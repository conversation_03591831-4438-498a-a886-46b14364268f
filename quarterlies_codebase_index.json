{"project_info": {"name": "Quarterlies", "description": "Comprehensive business management application for contractors and small businesses", "version": "1.0.0+1", "flutter_sdk": ">=3.7.2 <4.0.0", "architecture": "Clean Architecture with Provider state management", "offline_support": true, "main_features": ["Customer & Job Management", "Estimates, Contracts & Invoices", "Time & Expense Tracking", "Tax Payment Management", "Electronic Document Signing", "Voice Input & Search", "Offline-First Architecture", "Financial Reporting", "Mileage Tracking", "PDF Generation"]}, "entry_points": {"main": "lib/main.dart", "app_config": "lib/utils/app_config.dart", "models_index": "lib/models/models.dart", "navigation": "lib/screens/main_navigation_screen.dart"}, "routing": {"initial_route_logic": "Determined by auth state and auto-login result", "named_routes": {"/": "MainNavigationScreen", "/home": "MainNavigationScreen", "/main": "MainNavigationScreen", "/auth-wrapper": "AuthWrapperScreen", "/login": "LoginScreen", "/signup": "SignupScreen", "/password-reset": "PasswordResetScreen", "/update-password": "UpdatePasswordScreen", "/onboarding": "OnboardingFlowScreen", "/customers": "CustomerListScreen", "/jobs": "JobListScreen", "/mileage": "MileageListScreen"}, "deep_linking": {"method_channel": "com.quarterlies/deep_linking", "supported_routes": ["/reset-password", "/update-password"]}}, "navigation_structure": {"main_tabs": [{"index": 0, "name": "Dashboard", "icon": "Icons.dashboard", "screen": "DashboardScreen"}, {"index": 1, "name": "Home", "icon": "Icons.home", "screen": "HomeScreen"}, {"index": 2, "name": "Jobs", "icon": "Icons.work", "screen": "JobListScreen"}, {"index": 3, "name": "Invoices", "icon": "Icons.receipt_long", "screen": "InvoiceListScreen"}, {"index": 4, "name": "Reports", "icon": "Icons.bar_chart", "screen": "ReportsScreen"}, {"index": 5, "name": "Signatures", "icon": "Icons.draw", "screen": "SignedDocumentsScreen"}], "quick_add_options": ["Expense", "Customer", "Estimate", "Time Log", "Mileage"]}, "dependencies": {"core": {"flutter": "sdk", "supabase_flutter": "^2.9.0", "provider": "^6.1.5"}, "ui_ux": {"cupertino_icons": "^1.0.8", "fl_chart": "^1.0.0", "signature": "^5.4.0"}, "data_storage": {"sqflite": "^2.4.2", "sqflite_common_ffi": "^2.3.5", "shared_preferences": "^2.5.3", "flutter_secure_storage": "^9.2.4", "path_provider": "^2.1.5"}, "networking": {"dio": "^5.8.0", "http": "^1.1.0", "connectivity_plus": "^6.1.4"}, "media_files": {"image_picker": "^1.1.2", "pdf": "^3.11.3", "flutter_pdfview": "^1.4.0+1", "printing": "^5.14.2", "share_plus": "^11.0.0"}, "location_voice": {"geolocator": "^14.0.1", "geocoding": "^3.0.0", "flutter_sound": "^9.28.0", "speech_to_text": "^7.0.0", "permission_handler": "^12.0.0"}, "background_processing": {"workmanager": "^0.5.2", "background_fetch": "^1.2.1", "flutter_local_notifications": "^19.2.1"}, "utilities": {"uuid": "^4.5.1", "encrypt": "^5.0.3", "crypto": "^3.0.6", "intl": "^0.18.1", "path": "^1.9.1", "csv": "^6.0.0", "url_launcher": "^6.1.14", "device_info_plus": "^9.1.2", "package_info_plus": "^8.3.0", "html_unescape": "^2.0.0", "google_mlkit_text_recognition": "^0.15.0"}, "dev_dependencies": {"flutter_test": "sdk", "mockito": "^5.4.6", "build_runner": "^2.4.15", "mocktail": "^1.0.4", "fake_async": "^1.3.1", "flutter_lints": "^5.0.0"}}, "models": {"core_entities": {"customer.dart": {"class": "Customer", "description": "Customer information and contact details", "key_fields": ["id", "name", "email", "phone", "address", "notes"], "relationships": ["jobs", "invoices", "payments"]}, "job.dart": {"class": "Job", "description": "Job/project management with cost tracking", "key_fields": ["id", "customerId", "name", "description", "status", "estimatedPrice", "actualCost"], "relationships": ["customer", "estimates", "invoices", "expenses", "timeLogs"]}, "estimate.dart": {"class": "Estimate", "description": "Estimates with line items and templates", "key_fields": ["id", "jobId", "customerId", "items", "totalAmount", "status"], "relationships": ["job", "customer", "contract"]}, "contract.dart": {"class": "Contract", "description": "Contracts derived from estimates with electronic signing", "key_fields": ["id", "estimateId", "items", "totalAmount", "signedAt"], "relationships": ["estimate", "signedDocument"]}, "invoice.dart": {"class": "Invoice", "description": "Invoices with line items and payment tracking", "key_fields": ["id", "jobId", "customerId", "items", "totalAmount", "status", "dueDate"], "relationships": ["job", "customer", "payments"]}, "expense.dart": {"class": "Expense", "description": "Expenses and mileage tracking with IRS categories", "key_fields": ["id", "jobId", "category", "amount", "description", "receiptUrl"], "relationships": ["job"]}, "time_log.dart": {"class": "TimeLog", "description": "Time tracking with labor cost calculation", "key_fields": ["id", "jobId", "startTime", "endTime", "hourlyRate", "description"], "relationships": ["job"]}, "payment.dart": {"class": "Payment", "description": "Payment records linked to invoices", "key_fields": ["id", "invoiceId", "amount", "paymentDate", "method"], "relationships": ["invoice"]}, "tax_payment.dart": {"class": "TaxPayment", "description": "Quarterly tax payment tracking", "key_fields": ["id", "quarter", "year", "amount", "paymentDate", "confirmationNumber"], "relationships": []}, "mileage.dart": {"class": "Mileage", "description": "Mileage tracking for tax purposes (extends Expense)", "key_fields": ["id", "jobId", "startLocation", "endLocation", "miles", "rate"], "relationships": ["job"]}}, "supporting_models": {"user_profile.dart": "Personal user information and onboarding status", "user_settings.dart": "App preferences and business settings", "document_signing_request.dart": "Electronic signature requests", "signed_document.dart": "Completed electronic signature records", "sync_status.dart": "Sync status enumeration for offline support", "invoice_status.dart": "Invoice status enumeration", "expense_category.dart": "IRS Schedule C expense categories", "financial_models.dart": "Financial calculations and reporting models", "report_models.dart": "Report generation data models", "tax_export_data.dart": "Tax export and reporting data structures", "address.dart": "Address model for customers and jobs"}}, "providers": {"state_management": {"customer_provider.dart": {"class": "CustomerProvider", "description": "Customer data state management", "key_methods": ["initialize", "getCustomers", "addCustomer", "updateCustomer", "deleteCustomer"]}, "job_provider.dart": {"class": "JobProvider", "description": "Job data state management", "key_methods": ["initialize", "getJobs", "addJob", "updateJob", "deleteJob"]}, "financial_provider.dart": {"class": "FinancialProvider", "description": "Financial data and calculations", "key_methods": ["initialize", "getFinancialSummary", "calculateProfitLoss"]}, "invoice_provider.dart": {"class": "InvoiceProvider", "description": "Invoice data state management", "key_methods": ["initialize", "getInvoices", "addInvoice", "updateInvoice"]}, "expense_provider.dart": {"class": "ExpenseProvider", "description": "Expense data state management", "key_methods": ["initialize", "getExpenses", "addExpense", "updateExpense"]}, "tax_payment_provider.dart": {"class": "TaxPaymentProvider", "description": "Tax payment data state management", "key_methods": ["initialize", "getTaxPayments", "addTaxPayment"]}, "display_settings_provider.dart": {"class": "DisplaySettingsProvider", "description": "UI display mode and settings (Field/Office Mode)", "key_methods": ["initialize", "setDisplayMode", "toggleOfficeMode"]}, "loading_state_provider.dart": {"class": "LoadingStateProvider", "description": "Centralized loading state management", "key_methods": ["setLoading", "setSyncLoading", "setOcrLoading", "setPdfLoading"]}}}, "services": {"core_services": {"supabase_service.dart": {"class": "SupabaseService", "description": "Main database service with 110+ methods for all CRUD operations", "key_methods": ["getCustomers", "addCustomer", "getJobs", "addJob", "getInvoices", "addInvoice"], "features": ["Pagination", "Error handling", "Retry mechanism", "RLS support"]}, "auth_service.dart": {"class": "AuthService", "description": "Authentication and user management", "key_methods": ["signIn", "signUp", "signOut", "resetPassword", "attemptAutoLogin"], "features": ["Remember me", "Auto-login", "Password validation", "Session management"]}, "data_repository.dart": {"class": "DataRepository", "description": "Offline-first data access layer", "key_methods": ["getCustomers", "addCustomer", "syncData", "isOnline"], "features": ["Offline support", "Sync management", "Cache management", "Conflict resolution"]}}, "sync_services": {"sync_manager.dart": {"class": "SyncManager", "description": "Manages data synchronization between local and remote", "key_methods": ["syncData", "resolveConflicts", "performSync"], "features": ["Conflict resolution", "Background sync", "Retry logic"]}, "sync_service.dart": {"class": "SyncService", "description": "Sync status broadcasting and management", "key_methods": ["updateSyncStatus", "dispose"], "features": ["Status streaming", "Singleton pattern"]}, "background_sync_service.dart": {"class": "BackgroundSyncService", "description": "Background synchronization service", "features": ["Background processing", "Scheduled sync"]}}, "storage_services": {"local_database_service.dart": {"class": "LocalDatabaseService", "description": "SQLite local database operations", "key_methods": ["insertCustomer", "getCustomers", "updateCustomer", "deleteCustomer"], "features": ["SQLite operations", "Schema management", "Data persistence"]}, "cache_manager.dart": {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "LRU cache management for performance", "key_methods": ["recordAccess", "evictLeastRecentlyUsed", "clearCache"], "features": ["LRU eviction", "Access tracking", "Memory management"]}, "local_document_storage_service.dart": {"class": "LocalDocumentStorageService", "description": "Local document and file storage", "features": ["File management", "Document storage", "Offline access"]}}, "pdf_services": {"invoice_pdf_service.dart": {"class": "InvoicePDFService", "description": "Invoice PDF generation", "key_methods": ["generateInvoicePDF", "saveInvoicePDF"], "features": ["PDF generation", "Template support", "Branding"]}, "estimate_pdf_service.dart": {"class": "EstimatePDFService", "description": "Estimate PDF generation", "key_methods": ["generateEstimatePDF", "saveEstimatePDF"], "features": ["PDF generation", "Template support"]}, "contract_pdf_service.dart": {"class": "ContractPDFService", "description": "Contract PDF generation", "key_methods": ["generateContractPDF", "saveContractPDF"], "features": ["PDF generation", "Signature support"]}, "pdf_merger_service.dart": {"class": "PDFMergerService", "description": "PDF merging and manipulation", "features": ["PDF merging", "Document combination"]}}, "communication_services": {"email_service.dart": {"class": "EmailService", "description": "Email sending via Brevo API", "key_methods": ["sendEmail", "sendTemplateEmail", "sendSigningRequest"], "features": ["Template support", "Brevo integration", "Error handling"]}, "offline_email_service.dart": {"class": "OfflineEmailService", "description": "Offline email queuing and sending", "features": ["Email queuing", "Offline support", "Auto-send when online"]}, "notification_service.dart": {"class": "NotificationService", "description": "Local notifications and alerts", "features": ["Push notifications", "Scheduled notifications", "Alert management"]}}, "voice_location_services": {"voice_recording_service.dart": {"class": "VoiceRecordingService", "description": "Voice recording and transcription", "key_methods": ["startRecording", "stopRecording", "transcribeAudio"], "features": ["Voice recording", "Speech-to-text", "Audio file management"]}, "voice_search_service.dart": {"class": "VoiceSearchService", "description": "Voice-powered search functionality", "features": ["Voice search", "Natural language processing"]}, "address_service.dart": {"class": "AddressService", "description": "Address autocomplete and geocoding", "features": ["Mapbox integration", "Address validation", "Geocoding"]}, "mileage_tracking_service.dart": {"class": "MileageTrackingService", "description": "GPS-based mileage tracking", "features": ["GPS tracking", "Automatic mileage calculation", "IRS rate calculation"]}}, "utility_services": {"ocr_service.dart": {"class": "OCRService", "description": "Optical character recognition for receipts", "features": ["Text recognition", "Receipt processing", "Data extraction"]}, "connectivity_service.dart": {"class": "ConnectivityService", "description": "Network connectivity monitoring", "features": ["Connection monitoring", "Online/offline detection"]}, "encryption_service.dart": {"class": "EncryptionService", "description": "Data encryption and security", "features": ["Data encryption", "Secure storage", "Key management"]}, "security_provider.dart": {"class": "SecurityProvider", "description": "Security services provider", "features": ["Security initialization", "Service coordination"]}, "credential_manager.dart": {"class": "CredentialManager", "description": "Secure credential management", "features": ["Credential storage", "Secure access"]}}, "reporting_services": {"report_service.dart": {"class": "ReportService", "description": "Business reporting and analytics", "features": ["Financial reports", "Tax reports", "Business analytics"]}, "tax_export_service.dart": {"class": "TaxExportService", "description": "Tax data export and formatting", "features": ["Schedule C export", "Tax data formatting", "CSV export"]}, "budget_service.dart": {"class": "BudgetService", "description": "Budget tracking and analysis", "features": ["Budget vs actuals", "Financial forecasting"]}, "dashboard_service.dart": {"class": "DashboardService", "description": "Dashboard data aggregation", "features": ["Data aggregation", "KPI calculation"]}, "offline_dashboard_service.dart": {"class": "OfflineDashboardService", "description": "Offline dashboard data", "features": ["Offline analytics", "Cached reporting"]}}}, "screens": {"authentication": {"auth_wrapper_screen.dart": {"class": "AuthWrapperScreen", "description": "Checks onboarding status and redirects appropriately", "navigation": "Entry point after login, routes to /main or /onboarding"}, "login_screen.dart": {"class": "LoginScreen", "description": "User login with remember me functionality", "features": ["Email/password login", "Remember me", "Auto-login"]}, "signup_screen.dart": {"class": "SignupScreen", "description": "User registration", "features": ["Account creation", "Email verification"]}, "password_reset_screen.dart": {"class": "PasswordResetScreen", "description": "Password reset functionality", "features": ["Email-based reset", "Deep link support"]}, "update_password_screen.dart": {"class": "UpdatePasswordScreen", "description": "Password update after reset", "features": ["Password update", "Deep link handling"]}}, "main_navigation": {"main_navigation_screen.dart": {"class": "MainNavigationScreen", "description": "Main app navigation with bottom tabs", "tabs": ["Dashboard", "Home", "Jobs", "Invoices", "Reports", "Signatures"], "features": ["Bottom navigation", "Quick add FAB", "Offline status banner"]}, "dashboard_screen.dart": {"class": "DashboardScreen", "description": "Financial overview and key metrics", "features": ["Financial summary", "Charts", "Quick stats", "Field/Office mode layouts"]}, "home_screen.dart": {"class": "HomeScreen", "description": "Main hub with navigation cards", "features": ["Navigation cards", "Quick access", "Feature overview"]}}, "customer_management": {"customer_list_screen.dart": {"class": "CustomerListScreen", "description": "Customer listing with search and pagination", "features": ["Search", "Pagination", "Add customer", "Customer details"]}, "customer_detail_screen.dart": {"class": "CustomerDetailScreen", "description": "Customer details and related data", "features": ["Customer info", "Related jobs", "Edit customer", "Delete customer"]}, "customer_form_screen.dart": {"class": "CustomerFormScreen", "description": "Add/edit customer form", "features": ["Form validation", "Address autocomplete", "Voice input"]}}, "job_management": {"job_list_screen.dart": {"class": "JobListScreen", "description": "Job listing with search and filters", "features": ["Search", "Filters", "Status tracking", "Add job"]}, "job_detail_screen.dart": {"class": "JobDetailScreen", "description": "Job details with related data", "features": ["Job info", "Estimates", "Expenses", "Time logs", "Invoices", "Payments"]}, "job_form_screen.dart": {"class": "JobFormScreen", "description": "Add/edit job form", "features": ["Form validation", "Customer selection", "Address input"]}}, "estimates_contracts": {"estimate_list_screen.dart": {"class": "EstimateListScreen", "description": "Estimate listing and management", "features": ["Search", "Status filters", "PDF generation"]}, "estimate_detail_screen.dart": {"class": "EstimateDetailScreen", "description": "Estimate details and actions", "features": ["Line items", "PDF view", "Convert to contract", "Send for signing"]}, "estimate_form_screen.dart": {"class": "EstimateFormScreen", "description": "Create/edit estimates", "features": ["Line item management", "Templates", "Calculations"]}, "contract_list_screen.dart": {"class": "ContractListScreen", "description": "Contract listing and management", "features": ["Search", "Status tracking", "Signed documents"]}, "contract_detail_screen.dart": {"class": "ContractDetailScreen", "description": "Contract details and signing status", "features": ["Contract view", "Signing status", "Download signed PDF"]}, "contract_form_screen.dart": {"class": "ContractFormScreen", "description": "Create/edit contracts", "features": ["Based on estimates", "Line item editing", "Terms and conditions"]}}, "invoicing_payments": {"invoice_list_screen.dart": {"class": "InvoiceListScreen", "description": "Invoice listing with status tracking", "features": ["Search", "Status filters", "Payment tracking", "Overdue alerts"]}, "invoice_detail_screen.dart": {"class": "InvoiceDetailScreen", "description": "Invoice details and payment history", "features": ["Line items", "Payment history", "PDF generation", "Send invoice"]}, "invoice_form_screen.dart": {"class": "InvoiceFormScreen", "description": "Create/edit invoices", "features": ["Line item management", "Job data integration", "Payment terms"]}, "invoice_item_form_screen.dart": {"class": "InvoiceItemFormScreen", "description": "Add/edit invoice line items", "features": ["Item details", "Pricing", "Quantity"]}, "payment_list_screen.dart": {"class": "PaymentListScreen", "description": "Payment tracking and history", "features": ["Payment history", "Search", "Payment methods"]}, "payment_detail_screen.dart": {"class": "PaymentDetailScreen", "description": "Payment details and related invoices", "features": ["Payment info", "Related invoices", "Receipt generation"]}, "payment_form_screen.dart": {"class": "PaymentFormScreen", "description": "Record payments", "features": ["Payment entry", "Invoice selection", "Payment methods"]}}, "expense_tracking": {"expense_list_screen.dart": {"class": "ExpenseListScreen", "description": "Expense listing and categorization", "features": ["Search", "Category filters", "Receipt photos", "Tax categories"]}, "expense_detail_screen.dart": {"class": "ExpenseDetailScreen", "description": "Expense details and receipt management", "features": ["Expense info", "Receipt photos", "Tax categorization"]}, "expense_form_screen.dart": {"class": "ExpenseFormScreen", "description": "Add/edit expenses", "features": ["OCR receipt scanning", "Category selection", "Job assignment"]}, "expense_conflict_screen.dart": {"class": "ExpenseConflictScreen", "description": "Resolve expense sync conflicts", "features": ["Conflict resolution", "Data comparison", "Merge options"]}, "overhead_management_screen.dart": {"class": "OverheadManagementScreen", "description": "Overhead expense management", "features": ["Overhead tracking", "Job allocation", "Cost analysis"]}, "overhead_analytics_screen.dart": {"class": "OverheadAnalyticsScreen", "description": "Overhead expense analytics", "features": ["Cost analysis", "Allocation reports", "Trends"]}}, "time_mileage": {"time_log_list_screen.dart": {"class": "TimeLogListScreen", "description": "Time tracking and labor cost management", "features": ["Time entries", "Labor cost calculation", "Job filtering"]}, "time_log_detail_screen.dart": {"class": "TimeLogDetailScreen", "description": "Time log details and editing", "features": ["Time details", "Labor costs", "Voice notes"]}, "time_log_form_screen.dart": {"class": "TimeLogFormScreen", "description": "Add/edit time logs", "features": ["Time entry", "Hourly rates", "Job selection", "Voice notes"]}, "mileage_list_screen.dart": {"class": "MileageListScreen", "description": "Mileage tracking for tax purposes", "features": ["Mileage entries", "IRS rate calculation", "GPS tracking"]}, "mileage_form_screen.dart": {"class": "MileageFormScreen", "description": "Add/edit mileage entries", "features": ["Route entry", "Automatic calculation", "GPS integration"]}}, "document_signing": {"create_signing_request_screen.dart": {"class": "CreateSigningRequestScreen", "description": "Create electronic signature requests", "features": ["Document selection", "Signer details", "Email workflow"]}, "document_signing_screen.dart": {"class": "DocumentSigningScreen", "description": "Document signing interface", "features": ["Electronic signing", "Signature capture", "Document preview"]}, "signed_documents_screen.dart": {"class": "SignedDocumentsScreen", "description": "Signed document management", "features": ["Document listing", "Download signed PDFs", "Status tracking"]}}, "tax_reporting": {"tax_payment_list_screen.dart": {"class": "TaxPaymentListScreen", "description": "Quarterly tax payment tracking", "features": ["Payment history", "Quarterly view", "Payment reminders"]}, "tax_payment_detail_screen.dart": {"class": "TaxPaymentDetailScreen", "description": "Tax payment details", "features": ["Payment info", "Confirmation numbers", "Receipt generation"]}, "tax_payment_form_screen.dart": {"class": "TaxPaymentFormScreen", "description": "Record tax payments", "features": ["Payment entry", "Quarter selection", "Confirmation tracking"]}, "tax_export_screen.dart": {"class": "TaxExportScreen", "description": "Tax data export for filing", "features": ["Schedule C export", "CSV generation", "Tax period selection"]}}, "reports_analytics": {"reports/index.dart": {"class": "ReportsScreen", "description": "Business reporting and analytics hub", "features": ["Financial reports", "Profit/loss", "Tax reports", "Custom date ranges"]}}, "settings_onboarding": {"onboarding/onboarding_flow_screen.dart": {"class": "OnboardingFlowScreen", "description": "Multi-step onboarding process", "features": ["Step-by-step setup", "Business info", "Signature creation"]}, "onboarding/welcome_step.dart": {"class": "WelcomeStep", "description": "Welcome and introduction step", "features": ["App introduction", "Feature overview"]}, "onboarding/personal_info_step.dart": {"class": "PersonalInfoStep", "description": "Personal information collection", "features": ["User details", "Contact information"]}, "onboarding/business_info_step.dart": {"class": "BusinessInfoStep", "description": "Business information setup", "features": ["Business details", "Tax information"]}, "onboarding/signature_step.dart": {"class": "SignatureStep", "description": "User signature creation", "features": ["Signature capture", "Signature storage"]}, "onboarding/settings_review_step.dart": {"class": "SettingsReviewStep", "description": "Settings review and confirmation", "features": ["Settings summary", "Final confirmation"]}, "settings/settings_screen.dart": {"class": "SettingsScreen", "description": "App settings and preferences", "features": ["User preferences", "Business settings", "Display options"]}, "settings/signature_settings_screen.dart": {"class": "SignatureSettingsScreen", "description": "Signature management", "features": ["Signature update", "Signature preview"]}}, "utility_screens": {"search/voice_search_screen.dart": {"class": "VoiceSearchScreen", "description": "Voice-powered search interface", "features": ["Voice recognition", "Search results", "Natural language processing"]}, "document_viewer_screen.dart": {"class": "DocumentViewerScreen", "description": "PDF and document viewing", "features": ["PDF viewing", "Document navigation", "Zoom controls"]}}}, "widgets": {"navigation": {"bottom_nav_bar.dart": {"widgets": ["BottomNavBar", "QuickAddButton"], "description": "Main navigation bar with quick add functionality", "features": ["Tab navigation", "Quick add modal", "Recent data integration"]}}, "layout_adaptive": {"adaptive_list_tile.dart": {"widgets": ["AdaptiveListTile"], "description": "Responsive list tile for Field/Office modes", "features": ["Display mode adaptation", "Touch target optimization"]}, "adaptive_form_section.dart": {"widgets": ["AdaptiveFormSection"], "description": "Responsive form sections", "features": ["Field/Office mode layouts", "Form organization"]}, "adaptive_detail_section.dart": {"widgets": ["AdaptiveDetailSection"], "description": "Responsive detail sections", "features": ["Collapsible sections", "Display mode adaptation"]}}, "input_controls": {"custom_widgets.dart": {"widgets": ["CustomTextField", "CustomButton"], "description": "Reusable form controls with field-friendly design", "features": ["Large touch targets", "High contrast", "Validation support"]}, "voice_search_field.dart": {"widgets": ["VoiceSearchField"], "description": "Voice-enabled search input", "features": ["Voice recognition", "Search integration"]}, "address_autocomplete_field.dart": {"widgets": ["AddressAutocompleteField"], "description": "Address input with autocomplete", "features": ["Mapbox integration", "Address validation"]}, "signature_creation_widget.dart": {"widgets": ["SignatureCreationWidget"], "description": "Signature capture interface", "features": ["Touch signature", "Signature storage"]}}, "data_display": {"paginated_list_view.dart": {"widgets": ["PaginatedListView"], "description": "Paginated list with loading states", "features": ["Pagination", "Loading indicators", "Error handling"]}, "searchable_paginated_list_view.dart": {"widgets": ["SearchablePaginatedListView"], "description": "Searchable paginated list", "features": ["Search integration", "Pagination", "Filtering"]}, "budget_chart.dart": {"widgets": ["BudgetChart"], "description": "Budget vs actuals visualization", "features": ["Chart display", "Financial data visualization"]}, "collapsible_section.dart": {"widgets": ["CollapsibleSection"], "description": "Expandable content sections", "features": ["Expand/collapse", "Content organization"]}}, "status_feedback": {"loading_widgets.dart": {"widgets": ["QuarterliesLoadingIndicator", "LoadingOverlay"], "description": "Loading states and indicators", "features": ["Offline-aware loading", "Progress indicators"]}, "error_display_widgets.dart": {"widgets": ["ErrorDisplayWidget", "ErrorDisplay"], "description": "Error display and handling", "features": ["User-friendly errors", "Retry functionality", "Technical details"]}, "feedback_widgets.dart": {"widgets": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SuccessMessage"], "description": "User feedback and notifications", "features": ["Success messages", "User feedback", "Toast notifications"]}, "offline_status_banner.dart": {"widgets": ["OfflineStatusBanner"], "description": "Offline status indicator", "features": ["Connection status", "Sync indicators"]}, "sync_status_indicator.dart": {"widgets": ["SyncStatusIndicator"], "description": "Data synchronization status", "features": ["Sync progress", "Status indicators"]}}, "specialized": {"conflict_resolution_dialog.dart": {"widgets": ["ConflictResolutionDialog"], "description": "Data conflict resolution interface", "features": ["Conflict comparison", "Resolution options"]}, "offline_search_filter.dart": {"widgets": ["OfflineSearchFilter"], "description": "Offline-capable search and filtering", "features": ["Local search", "Filter options"]}}}, "utilities": {"configuration": {"app_config.dart": {"description": "Application configuration and environment setup", "features": ["Supabase configuration", "Environment variables", "Secure config"]}, "app_constants.dart": {"description": "Application constants and configuration values", "features": ["App constants", "Configuration values"]}}, "theming": {"field_friendly_theme.dart": {"description": "Field-friendly theme with high contrast and accessibility", "features": ["Field/Office modes", "High contrast colors", "WCAG compliance", "Glare resistance"]}}, "error_handling": {"error_handler.dart": {"description": "Comprehensive error handling and categorization", "features": ["Error categorization", "User-friendly messages", "Logging", "Retry logic"]}, "retry_mechanism.dart": {"description": "Retry logic with exponential backoff", "features": ["Exponential backoff", "Jitter", "Configurable policies"]}}, "validation": {"input_validators.dart": {"description": "Form validation utilities", "features": ["Email validation", "Phone validation", "Required field validation"]}}, "ui_helpers": {"responsive_helper.dart": {"description": "Responsive design utilities", "features": ["Screen size detection", "Responsive layouts"]}, "feedback_messages.dart": {"description": "User feedback message utilities", "features": ["Success messages", "Error messages", "Toast notifications"]}}}, "architecture_notes": {"patterns": ["Clean Architecture with separation of concerns", "Provider pattern for state management", "Repository pattern for data access", "Service layer for business logic", "Offline-first architecture with sync capabilities"], "offline_support": {"description": "Comprehensive offline-first implementation", "features": ["Local SQLite database for all entities", "Automatic sync when connectivity restored", "Conflict resolution for concurrent edits", "Cache management with LRU eviction", "Offline-aware UI components"]}, "security": {"description": "Production-ready security implementation", "features": ["Encrypted local storage", "Secure credential management", "Row-level security (RLS) in Supabase", "Input validation and sanitization", "Audit logging"]}, "testing": {"test_structure": "Unit tests, widget tests, and integration tests", "mocking": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> for service mocking", "coverage": "Comprehensive test coverage for business logic"}}}