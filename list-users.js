/*
 * Supabase User Listing Script
 * 
 * This script lists all users in the Supabase database using the Admin API.
 */

const axios = require('axios');

// ============================================================================
// CONFIGURATION
// ============================================================================

const SUPABASE_URL = 'https://hoeagvrddekfmeqqkxca.supabase.co';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI4MzI1MCwiZXhwIjoyMDYxODU5MjUwfQ.u_AmabaIzNdbBLlpj3rh_7tYQTvdL5-7HXKuEXSlsDo';

// ============================================================================
// SCRIPT LOGIC
// ============================================================================

// Create axios instance with default headers
const supabaseAdmin = axios.create({
  baseURL: `${SUPABASE_URL}/auth/v1/admin`,
  headers: {
    'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
    'Content-Type': 'application/json',
    'apikey': SERVICE_ROLE_KEY
  }
});

/**
 * Lists all users in the database
 */
async function listAllUsers() {
  try {
    console.log('🔍 Fetching all users from Supabase...');
    
    const response = await supabaseAdmin.get('/users', {
      params: {
        page: 1,
        per_page: 100 // Get up to 100 users
      }
    });
    
    if (response.status === 200) {
      const users = response.data.users || response.data;
      console.log(`✅ Found ${users.length} user(s):`);
      console.log('');
      
      users.forEach((user, index) => {
        console.log(`👤 User ${index + 1}:`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Created: ${user.created_at}`);
        console.log(`   Email Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
        console.log(`   Last Sign In: ${user.last_sign_in_at || 'Never'}`);
        console.log('');
      });
      
      // Check specifically for our target email
      const targetUser = users.find(user => user.email === '<EMAIL>');
      if (targetUser) {
        console.log('🎯 Found target user:');
        console.log(`   ID: ${targetUser.id}`);
        console.log(`   Email: ${targetUser.email}`);
        console.log('   ✅ This user exists and should be accessible for password reset');
      } else {
        console.log('❌ Target user "<EMAIL>" not found in the list');
      }
      
    } else {
      throw new Error(`Unexpected response status: ${response.status}`);
    }
  } catch (error) {
    if (error.response?.status === 401) {
      throw new Error('Unauthorized - check your service role key');
    } else if (error.response?.status === 403) {
      throw new Error('Forbidden - service role key may not have admin privileges');
    } else {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }
  }
}

/**
 * Test the specific endpoint used in reset-password.js
 */
async function testUserByEmailEndpoint() {
  try {
    console.log('🧪 Testing the /users/by-email endpoint...');
    
    const response = await supabaseAdmin.get('/users/by-email', {
      params: { email: '<EMAIL>' }
    });
    
    console.log('✅ /users/by-email endpoint works!');
    console.log(`   User ID: ${response.data.id}`);
    console.log(`   Email: ${response.data.email}`);
    
  } catch (error) {
    console.log('❌ /users/by-email endpoint failed:');
    console.log(`   Status: ${error.response?.status}`);
    console.log(`   Error: ${error.message}`);
    
    if (error.response?.data) {
      console.log(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Starting user investigation...');
    console.log('');
    
    // First, list all users
    await listAllUsers();
    
    console.log('');
    console.log('=' .repeat(50));
    console.log('');
    
    // Then test the specific endpoint
    await testUserByEmailEndpoint();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  listAllUsers,
  testUserByEmailEndpoint
};
