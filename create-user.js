/*
 * Supabase User Creation Script
 * 
 * This script creates a new user using the Supabase Admin API.
 * 
 * SECURITY WARNING:
 * - The service role key has admin privileges and should NEVER be exposed in frontend applications
 * - Keep this key secret and only use it in secure server environments
 */

const axios = require('axios');

// ============================================================================
// CONFIGURATION - USING SAME VALUES FROM reset-password.js
// ============================================================================

// Your Supabase project URL
const SUPABASE_URL = 'https://hoeagvrddekfmeqqkxca.supabase.co';

// Your Supabase service role key
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI4MzI1MCwiZXhwIjoyMDYxODU5MjUwfQ.u_AmabaIzNdbBLlpj3rh_7tYQTvdL5-7HXKuEXSlsDo';

// User details to create
const USER_EMAIL = '<EMAIL>';
const USER_PASSWORD = '!ValleyCarpentry1!';

// ============================================================================
// SCRIPT LOGIC
// ============================================================================

// Create axios instance with default headers
const supabaseAdmin = axios.create({
  baseURL: `${SUPABASE_URL}/auth/v1/admin`,
  headers: {
    'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
    'Content-Type': 'application/json',
    'apikey': SERVICE_ROLE_KEY
  }
});

/**
 * Creates a new user using Supabase Admin API
 * @param {string} email - User email address
 * @param {string} password - User password
 * @returns {Promise<object>} Created user data
 */
async function createUser(email, password) {
  try {
    console.log(`👤 Creating new user with email: ${email}`);
    
    const response = await supabaseAdmin.post('/users', {
      email: email,
      password: password,
      email_confirm: true, // Auto-confirm email to avoid email verification step
      user_metadata: {
        created_by: 'admin_script',
        created_at: new Date().toISOString()
      }
    });
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ User created successfully!');
      console.log(`📧 User ID: ${response.data.id}`);
      console.log(`📧 Email: ${response.data.email}`);
      console.log(`📧 Email confirmed: ${response.data.email_confirmed_at ? 'Yes' : 'No'}`);
      console.log(`📧 Created at: ${response.data.created_at}`);
      return response.data;
    } else {
      throw new Error(`Unexpected response status: ${response.status}`);
    }
  } catch (error) {
    if (error.response?.status === 400) {
      const errorMsg = error.response.data?.msg || error.response.data?.message || 'Bad request';
      if (errorMsg.includes('already registered') || errorMsg.includes('already exists')) {
        throw new Error(`User with email '${email}' already exists`);
      } else {
        throw new Error(`Bad request: ${errorMsg}`);
      }
    } else if (error.response?.status === 401) {
      throw new Error('Unauthorized - check your service role key');
    } else if (error.response?.status === 403) {
      throw new Error('Forbidden - service role key may not have admin privileges');
    } else if (error.response?.status === 422) {
      const errorMsg = error.response.data?.msg || error.response.data?.message || 'Validation error';
      throw new Error(`Validation error: ${errorMsg}`);
    } else {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }
}

/**
 * Main function to create user
 */
async function createNewUser() {
  try {
    console.log('🚀 Starting user creation process...');
    console.log(`📧 Target email: ${USER_EMAIL}`);
    
    // Create the user
    const userData = await createUser(USER_EMAIL, USER_PASSWORD);
    
    console.log('🎉 User creation completed successfully!');
    console.log('💡 The user can now log in with the provided credentials');
    
    return userData;
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    // If user already exists, that's actually fine for our purposes
    if (error.message.includes('already exists')) {
      console.log('💡 User already exists - you can proceed with the password reset script');
      return null;
    }
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createNewUser();
}

module.exports = {
  createNewUser,
  createUser
};
