import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/email_service.dart';
import 'package:quarterlies/services/pdf_merger_service.dart';
import 'package:quarterlies/services/document_signature_service.dart';
import 'package:quarterlies/services/signature_certification_service.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

/// Service for handling electronic document signing using a free service
class DocumentSigningService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  // Singleton pattern
  static final DocumentSigningService _instance =
      DocumentSigningService._internal();
  factory DocumentSigningService() => _instance;
  DocumentSigningService._internal();

  /// Create a signing request for a document
  ///
  /// This method:
  /// 1. Uploads the PDF to Supabase storage
  /// 2. Creates a unique signing link
  /// 3. Stores the signing request in the database
  /// 4. Sends an email to the customer with the signing link
  /// 5. Returns the signing link
  Future<String> createSigningRequest({
    required Uint8List pdfBytes,
    required String documentType, // 'estimate' or 'contract'
    required String documentId,
    required String customerId,
    required String customerEmail,
    required String customerName,
    required Job job,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // 1. Upload PDF to Supabase storage
        final userId = _supabaseClient.auth.currentUser!.id;
        final requestId = const Uuid().v4();
        final fileName = '${documentType}_${documentId}_$requestId.pdf';
        final storagePath = 'signing_requests/$userId/$fileName';

        // Create a temporary file to upload
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/$fileName');
        await tempFile.writeAsBytes(pdfBytes);

        // Upload to Supabase storage
        await _supabaseClient.storage
            .from('documents')
            .upload(
              storagePath,
              tempFile,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: false,
              ),
            );

        // Get the public URL for the document
        final documentUrl = _supabaseClient.storage
            .from('documents')
            .getPublicUrl(storagePath);

        // 2. Create a unique signing link with a longer expiration time (7 days)
        final signingId = const Uuid().v4();
        final expirationDate = DateTime.now().add(const Duration(days: 7));

        // 3. Store the signing request in the database with more metadata
        await _supabaseClient.from('document_signing_requests').insert({
          'id': signingId,
          'user_id': userId,
          'document_type': documentType,
          'document_id': documentId,
          'customer_id': customerId,
          'customer_email': customerEmail,
          'customer_name': customerName,
          'document_url': documentUrl,
          'status': 'pending',
          'created_at': DateTime.now().toIso8601String(),
          'expires_at': expirationDate.toIso8601String(),
          'job_id': job.id,
          'job_title': job.title,
          'job_address': job.address,
          'job_city': job.city,
          'job_state': job.state,
          'job_zip_code': job.zipCode,
        });

        // 4. Generate the signing link
        // This will be a deep link into our app's web version
        // Format: https://yourapp.com/sign?id=signingId
        final signingLink =
            'https://quarterlies-sign.vercel.app/sign?id=$signingId';

        // 5. Send an enhanced email to the customer with the signing link
        final emailService = EmailService();

        // Create a customer object with all available information
        final customer = Customer(
          id: customerId,
          name: customerName,
          email: customerEmail,
          userId: userId,
        );

        // Send the email with more context and information
        await emailService.sendSigningRequestEmail(
          documentType: documentType,
          documentId: documentId,
          signingLink: signingLink,
          customer: customer,
          job: job,
          expirationDate: expirationDate,
        );

        // Clean up the temporary file
        await tempFile.delete();

        return signingLink;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'createSigningRequest',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Check if a document has been signed
  Future<bool> isDocumentSigned(String documentId, String documentType) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('document_signing_requests')
                .select()
                .eq('document_id', documentId)
                .eq('document_type', documentType)
                .eq('status', 'signed')
                .limit(1)
                .maybeSingle();

        return response != null;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'isDocumentSigned',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return false as default for checking operations
      return false;
    }

    return result.data!;
  }

  /// Get all signing requests for the current user
  Future<List<Map<String, dynamic>>> getSigningRequests() async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;
      final response = await _supabaseClient
          .from('document_signing_requests')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting signing requests: $e');
      return [];
    }
  }

  /// Get a specific signing request by ID
  Future<Map<String, dynamic>?> getSigningRequestById(String id) async {
    try {
      final response =
          await _supabaseClient
              .from('document_signing_requests')
              .select()
              .eq('id', id)
              .limit(1)
              .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('Error getting signing request: $e');
      return null;
    }
  }

  /// Get the signed document URL
  Future<String?> getSignedDocumentUrl(
    String documentId,
    String documentType,
  ) async {
    try {
      final response =
          await _supabaseClient
              .from('document_signing_requests')
              .select()
              .eq('document_id', documentId)
              .eq('document_type', documentType)
              .eq('status', 'signed')
              .limit(1)
              .maybeSingle();

      if (response != null) {
        return response['signed_document_url'];
      }
      return null;
    } catch (e) {
      debugPrint('Error getting signed document URL: $e');
      return null;
    }
  }

  /// Handle document signing and send notifications
  ///
  /// This method:
  /// 1. Updates the signing request status to 'signed'
  /// 2. Applies customer signature to the document
  /// 3. Applies user (contractor) signature to the document (for contracts and estimates only)
  /// 4. Generates a signature certification document
  /// 5. Merges the certification with the signed document for the contractor
  /// 6. Sends email notifications to both the contractor and customer
  Future<bool> handleDocumentSigning({
    required String signingRequestId,
    required Uint8List signatureBytes,
    required String ipAddress,
    required String deviceInfo,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get the signing request details
        final signingRequest = await getSigningRequestById(signingRequestId);
        if (signingRequest == null) {
          debugPrint('Signing request not found: $signingRequestId');
          return false;
        }

        // Record the signing timestamp
        final signedAt = DateTime.now();

        // Extract necessary information
        final documentType = signingRequest['document_type'] as String;
        final documentId = signingRequest['document_id'] as String;
        final customerName = signingRequest['customer_name'] as String;
        final customerEmail = signingRequest['customer_email'] as String;
        final documentUrl = signingRequest['document_url'] as String;
        final userId = signingRequest['user_id'] as String;

        // Get the contractor's email
        final userResponse =
            await _supabaseClient
                .from('users')
                .select('email')
                .eq('id', userId)
                .single();
        final contractorEmail = userResponse['email'] as String;

        // Get the job title
        final jobResponse =
            await _supabaseClient
                .from('jobs')
                .select('title')
                .eq('id', documentId.split('_').first)
                .single();
        final jobName = jobResponse['title'] as String;

        // 1. Generate a signature certification document
        final certificationService = SignatureCertificationService();

        final certificationPdf = await certificationService
            .generateCertificationDocument(
              documentType: documentType,
              documentId: documentId,
              customerName: customerName,
              customerEmail: customerEmail,
              signingRequestId: signingRequestId,
              signedAt: signedAt,
              ipAddress: ipAddress,
              deviceInfo: deviceInfo,
            );

        // 2. Download the original document, add customer signature, add user signature (for contracts/estimates),
        // and create two versions:
        // - One for the customer (signed document with both signatures)
        // - One for the contractor (signed document with both signatures + certification)
        final signedDocumentUrls = await _addSignatureToDocument(
          documentUrl: documentUrl,
          customerSignatureBytes: signatureBytes,
          documentType: documentType,
          documentId: documentId,
          signingRequestId: signingRequestId,
          certificationPdf: certificationPdf,
        );

        final customerSignedDocumentUrl = signedDocumentUrls['customerUrl']!;
        final contractorSignedDocumentUrl =
            signedDocumentUrls['contractorUrl']!;

        // Upload the certification document
        final certificationUrl = await certificationService
            .uploadCertificationDocument(
              pdfBytes: certificationPdf,
              documentType: documentType,
              documentId: documentId,
              signingRequestId: signingRequestId,
            );

        // 3. Update the signing request status and store all document URLs
        await _supabaseClient
            .from('document_signing_requests')
            .update({
              'status': 'signed',
              'signed_at': signedAt.toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
              'signed_document_url':
                  customerSignedDocumentUrl, // Legacy field for backward compatibility
              'signed_document_customer_url': customerSignedDocumentUrl,
              'signed_document_contractor_url': contractorSignedDocumentUrl,
              'certification_url': certificationUrl,
              'signature_data': {
                'ip_address': ipAddress,
                'device_info': deviceInfo,
                'signed_at': signedAt.toIso8601String(),
              },
            })
            .eq('id', signingRequestId);

        // 4. Send enhanced email notifications
        final emailService = EmailService();

        // Get job details for more context in the emails
        final jobDetails =
            await _supabaseClient
                .from('jobs')
                .select('description, address, city, state, zip_code')
                .eq('id', documentId.split('_').first)
                .single();

        // Build the service address string if available
        String serviceAddress = '';
        if (jobDetails['address'] != null ||
            jobDetails['city'] != null ||
            jobDetails['state'] != null ||
            jobDetails['zip_code'] != null) {
          List<String> addressParts = [];
          if (jobDetails['address'] != null &&
              jobDetails['address'].isNotEmpty) {
            addressParts.add(jobDetails['address']);
          }

          List<String> cityStateParts = [];
          if (jobDetails['city'] != null && jobDetails['city'].isNotEmpty) {
            cityStateParts.add(jobDetails['city']);
          }
          if (jobDetails['state'] != null && jobDetails['state'].isNotEmpty) {
            cityStateParts.add(jobDetails['state']);
          }

          if (cityStateParts.isNotEmpty) {
            addressParts.add(cityStateParts.join(', '));
          }
          if (jobDetails['zip_code'] != null &&
              jobDetails['zip_code'].isNotEmpty) {
            addressParts.add(jobDetails['zip_code']);
          }

          serviceAddress = addressParts.join(', ');
        }

        // Send notification to the contractor with the version that includes certification
        await emailService.sendSignedDocumentNotification(
          documentType: documentType,
          documentId: documentId,
          customerName: customerName,
          jobName: jobName,
          signedDocumentUrl:
              contractorSignedDocumentUrl, // Send the contractor version
          contractorEmail: contractorEmail,
          serviceAddress: serviceAddress,
          jobDescription: jobDetails['description'],
          signedAt: signedAt,
        );

        // Send the signed document to the customer (without certification)
        await emailService.sendSignedDocumentToCustomer(
          documentType: documentType,
          documentId: documentId,
          customerName: customerName,
          customerEmail: customerEmail,
          jobName: jobName,
          signedDocumentUrl:
              customerSignedDocumentUrl, // Send the customer version
          serviceAddress: serviceAddress,
          jobDescription: jobDetails['description'],
          signedAt: signedAt,
        );

        return true;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'handleDocumentSigning',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return false;
    }

    return result.data!;
  }

  /// Add signatures to document and return the URLs of the signed documents
  ///
  /// This method:
  /// 1. Downloads the original document
  /// 2. Adds the customer signature to the document
  /// 3. Adds the user (contractor) signature to the document (for contracts and estimates only)
  /// 4. Creates two versions of the signed document:
  ///    - One for the customer (signed document with both signatures)
  ///    - One for the contractor (signed document with both signatures + certification)
  /// 5. Returns the URLs of both versions
  Future<Map<String, String>> _addSignatureToDocument({
    required String documentUrl,
    required Uint8List customerSignatureBytes,
    required String documentType,
    required String documentId,
    required String signingRequestId,
    required Uint8List certificationPdf,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Download the original document
        final response = await http.get(Uri.parse(documentUrl));
        if (response.statusCode != 200) {
          throw Exception(
            'Failed to download document: ${response.statusCode}',
          );
        }

        // Apply customer signature to the document
        // In a production implementation, we would use a PDF manipulation library
        // to add the signature image to the document at the correct position.
        // For now, we'll use the original document as the base
        Uint8List signedDocumentBytes = response.bodyBytes;

        // Apply user (contractor) signature for contracts and estimates only
        if (documentType == 'contract' || documentType == 'estimate') {
          final documentSignatureService = DocumentSignatureService();

          // Check if user has a signature and apply it
          if (await documentSignatureService.hasUserSignature()) {
            try {
              signedDocumentBytes = await documentSignatureService
                  .applyUserSignatureToPdf(
                    pdfBytes: signedDocumentBytes,
                    documentType: documentType,
                  );
              debugPrint('Applied user signature to $documentType');
            } catch (e) {
              debugPrint('Failed to apply user signature: $e');
              // Continue with customer signature only if user signature fails
            }
          } else {
            debugPrint('No user signature found for $documentType');
          }
        }

        // Create the PDF merger service
        final pdfMergerService = PdfMergerService();

        // Merge the signed document with the certification for the contractor
        final mergedPdf = await pdfMergerService
            .mergeSignedDocumentWithCertification(
              signedDocumentBytes: signedDocumentBytes,
              certificationBytes: certificationPdf,
            );

        final userId = _supabaseClient.auth.currentUser!.id;
        final tempDir = await getTemporaryDirectory();

        // 1. Upload the customer version (signed document only)
        final customerFileName =
            '${documentType}_${documentId}_${signingRequestId}_signed_customer.pdf';
        final customerStoragePath =
            'signed_documents/$userId/$customerFileName';

        // Create a temporary file for the customer version
        final customerTempFile = File('${tempDir.path}/$customerFileName');
        await customerTempFile.writeAsBytes(signedDocumentBytes);

        // Upload to Supabase storage
        await _supabaseClient.storage
            .from('documents')
            .upload(
              customerStoragePath,
              customerTempFile,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: false,
              ),
            );

        // Get the public URL for the customer version
        final customerSignedDocumentUrl = _supabaseClient.storage
            .from('documents')
            .getPublicUrl(customerStoragePath);

        // Clean up the temporary file
        await customerTempFile.delete();

        // 2. Upload the contractor version (signed document + certification)
        final contractorFileName =
            '${documentType}_${documentId}_${signingRequestId}_signed_contractor.pdf';
        final contractorStoragePath =
            'signed_documents/$userId/$contractorFileName';

        // Create a temporary file for the contractor version
        final contractorTempFile = File('${tempDir.path}/$contractorFileName');
        await contractorTempFile.writeAsBytes(mergedPdf);

        // Upload to Supabase storage
        await _supabaseClient.storage
            .from('documents')
            .upload(
              contractorStoragePath,
              contractorTempFile,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: false,
              ),
            );

        // Get the public URL for the contractor version
        final contractorSignedDocumentUrl = _supabaseClient.storage
            .from('documents')
            .getPublicUrl(contractorStoragePath);

        // Clean up the temporary file
        await contractorTempFile.delete();

        return {
          'customerUrl': customerSignedDocumentUrl,
          'contractorUrl': contractorSignedDocumentUrl,
        };
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'addSignatureToDocument',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Resend a signing request
  Future<void> resendSigningRequest(String signingRequestId) async {
    try {
      // Update the request status to trigger a resend
      await _supabaseClient
          .from('document_signing_requests')
          .update({
            'status': 'pending',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', signingRequestId);

      // In a production environment, this would trigger an edge function
      // to resend the signing email. For now, we'll just update the status.
      debugPrint('Signing request resent: $signingRequestId');
    } catch (e) {
      debugPrint('Error resending signing request: $e');
      throw Exception('Failed to resend signing request: $e');
    }
  }

  /// Cancel a signing request
  Future<void> cancelSigningRequest(String signingRequestId) async {
    try {
      // Update the request status to cancelled
      await _supabaseClient
          .from('document_signing_requests')
          .update({
            'status': 'cancelled',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', signingRequestId);

      debugPrint('Signing request cancelled: $signingRequestId');
    } catch (e) {
      debugPrint('Error cancelling signing request: $e');
      throw Exception('Failed to cancel signing request: $e');
    }
  }

  /// Resend a signed document to the customer
  Future<void> resendSignedDocumentToCustomer({
    required String signingRequestId,
    required String customerEmail,
    required String customerName,
    required String documentType,
    required String? documentUrl,
  }) async {
    try {
      if (documentUrl == null) {
        throw Exception('Signed document URL not available');
      }

      // In a production environment, this would trigger an edge function
      // to send the signed document email to the customer.
      // For now, we'll just log the action.
      debugPrint('Resending signed document to customer: $customerEmail');
      debugPrint('Document URL: $documentUrl');

      // Update the request to track the resend
      await _supabaseClient
          .from('document_signing_requests')
          .update({'updated_at': DateTime.now().toIso8601String()})
          .eq('id', signingRequestId);

      // Send email via edge function
      await _supabaseClient.functions.invoke(
        'send-signed-document-email',
        body: {
          'customerEmail': customerEmail,
          'customerName': customerName,
          'documentType': documentType,
          'documentUrl': documentUrl,
          'jobName':
              'Project', // Default value since not available in this context
          'jobDescription': null,
          'serviceAddress': null,
          'documentId': signingRequestId,
          'signedAt': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error resending signed document: $e');
      throw Exception('Failed to resend signed document: $e');
    }
  }
}
