import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// Service for generating signature certification documents
class SignatureCertificationService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  
  // Singleton pattern
  static final SignatureCertificationService _instance = SignatureCertificationService._internal();
  factory SignatureCertificationService() => _instance;
  SignatureCertificationService._internal();
  
  /// Generate a certification document for an electronic signature
  /// 
  /// This method creates a PDF document that certifies the authenticity of an electronic signature
  Future<Uint8List> generateCertificationDocument({
    required String documentType,
    required String documentId,
    required String customerName,
    required String customerEmail,
    required String signingRequestId,
    required DateTime signedAt,
    required String ipAddress,
    required String deviceInfo,
  }) async {
    final pdf = pw.Document();
    
    // Create a PDF theme with custom colors
    final theme = pw.ThemeData.withFont(
      base: pw.Font.helvetica(),
      bold: pw.Font.helveticaBold(),
    );
    
    // Generate a unique certificate ID
    final certificateId = const Uuid().v4();
    
    // Format the date
    final dateFormatter = DateFormat('MMMM d, yyyy');
    final timeFormatter = DateFormat('h:mm a');
    final formattedDate = dateFormatter.format(signedAt);
    final formattedTime = timeFormatter.format(signedAt);
    
    // Create the PDF document
    pdf.addPage(
      pw.MultiPage(
        theme: theme,
        pageFormat: PdfPageFormat.letter,
        margin: const pw.EdgeInsets.all(40),
        header: (context) => _buildHeader(),
        build: (context) => [
          _buildTitle(),
          pw.SizedBox(height: 20),
          _buildCertificateInfo(certificateId),
          pw.SizedBox(height: 20),
          _buildSignerInfo(customerName, customerEmail),
          pw.SizedBox(height: 20),
          _buildDocumentInfo(documentType, documentId),
          pw.SizedBox(height: 20),
          _buildSignatureInfo(formattedDate, formattedTime, ipAddress, deviceInfo),
          pw.SizedBox(height: 30),
          _buildLegalStatement(),
        ],
        footer: (context) => _buildFooter(context),
      ),
    );
    
    return pdf.save();
  }
  
  /// Upload the certification document to Supabase storage and return the URL
  Future<String> uploadCertificationDocument({
    required Uint8List pdfBytes,
    required String documentType,
    required String documentId,
    required String signingRequestId,
  }) async {
    try {
      // Create a unique file name
      final userId = _supabaseClient.auth.currentUser!.id;
      final fileName = '${documentType}_${documentId}_${signingRequestId}_certification.pdf';
      final storagePath = 'certifications/$userId/$fileName';
      
      // Create a temporary file to upload
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(pdfBytes);
      
      // Upload to Supabase storage
      await _supabaseClient.storage.from('documents').upload(
        storagePath,
        tempFile,
        fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
      );
      
      // Get the public URL for the document
      final certificationUrl = _supabaseClient.storage.from('documents').getPublicUrl(storagePath);
      
      // Clean up the temporary file
      await tempFile.delete();
      
      return certificationUrl;
    } catch (e) {
      debugPrint('Error uploading certification document: $e');
      throw Exception('Failed to upload certification document: $e');
    }
  }
  
  // Build the header section
  pw.Widget _buildHeader() {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(bottom: 20),
      child: pw.Text(
        'ELECTRONIC SIGNATURE CERTIFICATION',
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
    );
  }
  
  // Build the title section
  pw.Widget _buildTitle() {
    return pw.Container(
      alignment: pw.Alignment.center,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'CERTIFICATE OF COMPLETION',
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'Electronic Signature Verification',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey700,
            ),
          ),
        ],
      ),
    );
  }
  
  // Build the certificate info section
  pw.Widget _buildCertificateInfo(String certificateId) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'CERTIFICATE INFORMATION',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Text(
                'Certificate ID:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(certificateId),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'Certificate Issued:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(DateFormat('MMMM d, yyyy').format(DateTime.now())),
            ],
          ),
        ],
      ),
    );
  }
  
  // Build the signer info section
  pw.Widget _buildSignerInfo(String customerName, String customerEmail) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'SIGNER INFORMATION',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Text(
                'Name:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(customerName),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'Email:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(customerEmail),
            ],
          ),
        ],
      ),
    );
  }
  
  // Build the document info section
  pw.Widget _buildDocumentInfo(String documentType, String documentId) {
    final documentTypeCapitalized = documentType.substring(0, 1).toUpperCase() + documentType.substring(1);
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'DOCUMENT INFORMATION',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Text(
                'Document Type:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(documentTypeCapitalized),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'Document ID:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(documentId),
            ],
          ),
        ],
      ),
    );
  }
  
  // Build the signature info section
  pw.Widget _buildSignatureInfo(String date, String time, String ipAddress, String deviceInfo) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'SIGNATURE DETAILS',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Text(
                'Date Signed:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(date),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'Time Signed:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(time),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'IP Address:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(ipAddress),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            children: [
              pw.Text(
                'Device:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 10),
              pw.Text(deviceInfo),
            ],
          ),
        ],
      ),
    );
  }
  
  // Build the legal statement section
  pw.Widget _buildLegalStatement() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'LEGAL STATEMENT',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'This certificate serves as confirmation that the document referenced above was electronically signed by the identified signer. '
            'The electronic signature process complies with the Electronic Signatures in Global and National Commerce Act (E-SIGN Act) '
            'and the Uniform Electronic Transactions Act (UETA). The signature is legally binding and equivalent to a handwritten signature.',
            style: const pw.TextStyle(
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
  
  // Build the footer section
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 10),
      child: pw.Text(
        'Page ${context.pageNumber} of ${context.pagesCount}',
        style: const pw.TextStyle(
          fontSize: 10,
          color: PdfColors.grey700,
        ),
      ),
    );
  }
}
