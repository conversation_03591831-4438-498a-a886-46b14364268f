import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:workmanager/workmanager.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/utils/app_constants.dart';
import 'package:quarterlies/utils/error_handler.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final SupabaseService _supabaseService = SupabaseService();
  bool _isInitialized = false;

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  Future<void> initialize() async {
    if (_isInitialized) return;

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('app_icon');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestSoundPermission: true,
          requestBadgePermission: true,
          requestAlertPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notificationsPlugin.initialize(initializationSettings);

    _isInitialized = true;
  }

  /// Check for invoices that are coming due based on user settings
  Future<void> checkForDueInvoices() async {
    try {
      // Get user settings
      final userSettings = await _supabaseService.getUserSettings();

      // If notifications are disabled, don't proceed
      if (!userSettings.enableDueDateNotifications) return;

      // Get all open invoices
      final invoices = await _supabaseService.getInvoices();
      final openInvoices =
          invoices
              .where(
                (invoice) =>
                    invoice.status.toLowerCase() == 'open' ||
                    invoice.status.toLowerCase() == 'partially paid',
              )
              .toList();

      // Get the notification threshold date
      final now = DateTime.now();
      final notificationThreshold = now.add(
        Duration(days: userSettings.dueDateNotificationDays),
      );

      // Find invoices that are coming due within the notification threshold
      final comingDueInvoices =
          openInvoices.where((invoice) {
            // Check if the invoice due date is within the notification threshold
            return invoice.dueDate.isAfter(now) &&
                    invoice.dueDate.isBefore(notificationThreshold) ||
                invoice.dueDate.isAtSameMomentAs(notificationThreshold);
          }).toList();

      // Send notifications for each invoice coming due
      for (var invoice in comingDueInvoices) {
        await _sendInvoiceDueNotification(
          invoice,
          userSettings.dueDateNotificationDays,
        );
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'checkForDueInvoices'},
      );
      ErrorHandler.logError(appError);
      debugPrint('Error checking for due invoices: $e');
    }
  }

  /// Send a notification for an invoice that is coming due
  Future<void> _sendInvoiceDueNotification(
    Invoice invoice,
    int daysBeforeDue,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      // Get customer name for the notification
      final customer = await _supabaseService.getCustomerById(
        invoice.customerId,
      );
      final customerName = customer.name;

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'invoice_due_channel',
            'Invoice Due Notifications',
            importance: Importance.high,
            priority: Priority.high,
            channelDescription: 'Notifications for invoices coming due',
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Create a unique ID for the notification based on the invoice ID
      final notificationId = invoice.id.hashCode;

      await _notificationsPlugin.show(
        notificationId,
        'Invoice Coming Due',
        'Invoice #${invoice.id != null ? (invoice.id!.length >= 8 ? invoice.id!.substring(0, 8) : invoice.id!) : 'Unknown'} for $customerName is due in $daysBeforeDue days',
        platformChannelSpecifics,
        payload: invoice.id,
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'sendInvoiceDueNotification',
          'invoiceId': invoice.id,
          'daysBeforeDue': daysBeforeDue,
        },
      );
      ErrorHandler.logError(appError);
      debugPrint('Error sending invoice due notification: $e');
    }
  }

  /// Schedule a daily check for due invoices using WorkManager
  Future<void> scheduleDailyDueInvoiceCheck() async {
    if (!_isInitialized) await initialize();

    try {
      // Register a periodic task to check for due invoices daily
      await Workmanager().registerPeriodicTask(
        AppConstants.dailyInvoiceCheckTaskName,
        'checkDueInvoices',
        frequency: const Duration(hours: 24),
        constraints: Constraints(networkType: NetworkType.connected),
        existingWorkPolicy: ExistingWorkPolicy.keep,
      );

      debugPrint('Daily invoice check scheduled successfully');
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'scheduleDailyDueInvoiceCheck'},
      );
      ErrorHandler.logError(appError);
      debugPrint('Error scheduling daily due invoice check: $e');
      // Fallback to immediate check
      await checkForDueInvoices();
    }
  }

  /// Schedule daily checks for tax deadlines using WorkManager
  Future<void> scheduleDailyTaxDeadlineCheck() async {
    if (!_isInitialized) await initialize();

    try {
      // Register a periodic task to check for tax deadlines daily
      await Workmanager().registerPeriodicTask(
        AppConstants.dailyTaxDeadlineCheckTaskName,
        'checkTaxDeadlines',
        frequency: const Duration(hours: 24),
        constraints: Constraints(networkType: NetworkType.connected),
        existingWorkPolicy: ExistingWorkPolicy.keep,
      );

      debugPrint('Daily tax deadline check scheduled successfully');
    } catch (e) {
      debugPrint('Error scheduling daily tax deadline check: $e');
      // Fallback to immediate check
      await checkForTaxDeadlines();
    }
  }

  /// Check for upcoming tax payment deadlines and send notifications
  Future<void> checkForTaxDeadlines() async {
    try {
      final now = DateTime.now();
      final currentYear = now.year;

      // Calculate next tax deadline dates
      final taxDeadlines = _calculateTaxDeadlines(currentYear);

      // Check each deadline
      for (final deadline in taxDeadlines) {
        final daysUntilDeadline = deadline['date'].difference(now).inDays;

        // Send notifications at configured intervals before deadline
        if (AppConstants.taxDeadlineNotificationDays.contains(
          daysUntilDeadline,
        )) {
          await _sendTaxDeadlineNotification(
            deadline['period'],
            deadline['date'],
            daysUntilDeadline,
          );
        }
      }
    } catch (e) {
      debugPrint('Error checking for tax deadlines: $e');
    }
  }

  /// Calculate tax deadline dates for a given year
  List<Map<String, dynamic>> _calculateTaxDeadlines(int year) {
    return [
      {
        'period': 'Q1 $year',
        'date': DateTime(year, 4, 15), // April 15
      },
      {
        'period': 'Q2 $year',
        'date': DateTime(year, 6, 15), // June 15
      },
      {
        'period': 'Q3 $year',
        'date': DateTime(year, 9, 15), // September 15
      },
      {
        'period': 'Q4 $year',
        'date': DateTime(year + 1, 1, 15), // January 15 of next year
      },
    ];
  }

  /// Send a tax deadline notification
  Future<void> _sendTaxDeadlineNotification(
    String taxPeriod,
    DateTime deadline,
    int daysUntilDeadline,
  ) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'tax_deadline_channel',
            'Tax Deadline Notifications',
            importance: Importance.high,
            priority: Priority.high,
            channelDescription:
                'Notifications for upcoming tax payment deadlines',
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Create a unique ID for the notification
      final notificationId = '$taxPeriod-$daysUntilDeadline'.hashCode;

      String title;
      String body;

      if (daysUntilDeadline == 1) {
        title = 'Tax Payment Due Tomorrow!';
        body =
            '$taxPeriod quarterly tax payment is due tomorrow (${deadline.month}/${deadline.day}/${deadline.year})';
      } else {
        title = 'Tax Payment Reminder';
        body =
            '$taxPeriod quarterly tax payment is due in $daysUntilDeadline days (${deadline.month}/${deadline.day}/${deadline.year})';
      }

      await _notificationsPlugin.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: 'tax_deadline_$taxPeriod',
      );
    } catch (e) {
      debugPrint('Error sending tax deadline notification: $e');
    }
  }

  /// Show a general notification
  Future<void> showNotification(
    String title,
    String body,
    String channelId, {
    Map<String, dynamic>? payload,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      final AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            channelId,
            '$channelId notifications',
            importance: Importance.high,
            priority: Priority.high,
            channelDescription: 'Notifications for $channelId',
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      final NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Create a unique ID for the notification
      final notificationId = DateTime.now().millisecondsSinceEpoch.remainder(
        100000,
      );

      await _notificationsPlugin.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: payload?.toString(),
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }
}
