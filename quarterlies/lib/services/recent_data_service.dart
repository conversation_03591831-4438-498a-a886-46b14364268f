import 'package:flutter/foundation.dart' show debugPrint;
import 'package:supabase_flutter/supabase_flutter.dart';

class RecentDataService {
  final _supabase = Supabase.instance.client;

  // Get recent jobs with smart suggestions
  Future<List<Map<String, dynamic>>> getRecentJobs({int limit = 5}) async {
    try {
      // First get recently accessed jobs
      final recentJobs = await _supabase
          .from('jobs')
          .select('id, name, customer_id, customer:customers(name)')
          .order('last_accessed', ascending: false)
          .limit(limit);

      // If we don't have enough recent jobs, supplement with most used jobs
      if (recentJobs.length < limit) {
        final frequentJobs = await _supabase
            .from('jobs')
            .select('id, name, customer_id, customer:customers(name)')
            .order('usage_count', ascending: false)
            .limit(limit - recentJobs.length);

        // Combine lists, avoiding duplicates
        final recentJobIds = recentJobs.map((job) => job['id']).toSet();
        final uniqueFrequentJobs =
            frequentJobs
                .where((job) => !recentJobIds.contains(job['id']))
                .toList();

        return [...recentJobs, ...uniqueFrequentJobs];
      }

      return recentJobs;
    } catch (e) {
      // Return empty list on error
      return [];
    }
  }

  // Get recent customers with smart suggestions
  Future<List<Map<String, dynamic>>> getRecentCustomers({int limit = 5}) async {
    try {
      // First get recently accessed customers
      final recentCustomers = await _supabase
          .from('customers')
          .select('id, name, email, phone')
          .order('last_accessed', ascending: false)
          .limit(limit);

      // If we don't have enough recent customers, supplement with most used customers
      if (recentCustomers.length < limit) {
        final frequentCustomers = await _supabase
            .from('customers')
            .select('id, name, email, phone')
            .order('usage_count', ascending: false)
            .limit(limit - recentCustomers.length);

        // Combine lists, avoiding duplicates
        final recentCustomerIds =
            recentCustomers.map((customer) => customer['id']).toSet();
        final uniqueFrequentCustomers =
            frequentCustomers
                .where(
                  (customer) => !recentCustomerIds.contains(customer['id']),
                )
                .toList();

        return [...recentCustomers, ...uniqueFrequentCustomers];
      }

      return recentCustomers;
    } catch (e) {
      // Return empty list on error
      return [];
    }
  }

  // Update last accessed timestamp and increment usage count for a job
  Future<void> updateJobUsage(String jobId) async {
    try {
      await _supabase
          .from('jobs')
          .update({
            'last_accessed': DateTime.now().toIso8601String(),
            'usage_count': _supabase.rpc(
              'increment_usage_count',
              params: {'row_id': jobId},
            ),
          })
          .eq('id', jobId);
    } catch (e) {
      // Silently fail
    }
  }

  // Update last accessed timestamp and increment usage count for a customer
  Future<void> updateCustomerUsage(String customerId) async {
    try {
      await _supabase
          .from('customers')
          .update({
            'last_accessed': DateTime.now().toIso8601String(),
            'usage_count': _supabase.rpc(
              'increment_usage_count',
              params: {'row_id': customerId},
            ),
          })
          .eq('id', customerId);
    } catch (e) {
      // Silently fail
    }
  }

  // Get default values for a new expense based on recent entries
  Future<Map<String, dynamic>> getExpenseDefaults() async {
    try {
      final recentExpense =
          await _supabase
              .from('expenses')
              .select('category, payment_method')
              .order('created_at', ascending: false)
              .limit(1)
              .single();

      return {
        'category': recentExpense['category'],
        'payment_method': recentExpense['payment_method'],
      };
    } catch (e) {
      return {};
    }
  }

  // Update usage count for an expense category for the current user
  Future<void> updateExpenseCategoryUsage(String category) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      await _supabase.rpc(
        'increment_user_expense_category_usage',
        params: {'category_name': category, 'user_id': userId},
      );
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error updating expense category usage: $e');
    }
  }

  // Get expense categories sorted by usage frequency for the current user
  Future<List<Map<String, dynamic>>> getExpenseCategoriesByUsage() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _supabase.rpc(
        'get_user_expense_categories_by_usage',
        params: {'user_id': userId},
      );
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error getting expense categories by usage: $e');
      return [];
    }
  }

  // Get the most frequently used expense category for the current user
  Future<String?> getMostUsedExpenseCategory() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return null;

      final response = await _supabase.rpc(
        'get_user_most_used_expense_category',
        params: {'user_id': userId},
      );
      return response as String?;
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error getting most used expense category: $e');
      return null;
    }
  }

  // Reset all expense category usage counts for the current user
  Future<void> resetExpenseCategoryUsage() async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return;

      await _supabase.rpc(
        'reset_user_expense_category_usage',
        params: {'user_id': userId},
      );
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error resetting expense category usage: $e');
    }
  }

  // Get default values for a new time log based on recent entries
  Future<Map<String, dynamic>> getTimeLogDefaults() async {
    try {
      final recentTimeLog =
          await _supabase
              .from('time_logs')
              .select('hourly_rate')
              .order('created_at', ascending: false)
              .limit(1)
              .single();

      return {'hourly_rate': recentTimeLog['hourly_rate']};
    } catch (e) {
      return {};
    }
  }

  // Get default values for a new estimate based on recent entries
  Future<Map<String, dynamic>> getEstimateDefaults() async {
    try {
      // Get the most recent estimate
      final recentEstimate =
          await _supabase
              .from('estimates')
              .select('line_items')
              .order('created_at', ascending: false)
              .limit(1)
              .single();

      // Return line items as templates if available
      if (recentEstimate['line_items'] != null) {
        return {'line_items': recentEstimate['line_items']};
      }

      return {};
    } catch (e) {
      return {};
    }
  }
}
