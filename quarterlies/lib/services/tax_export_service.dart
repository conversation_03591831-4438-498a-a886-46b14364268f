import 'dart:io';
import 'package:csv/csv.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class TaxExportService {
  final DataRepository _dataRepository = DataRepository();

  /// Generate tax export data for the specified date range
  Future<TaxExportData> generateTaxExportData({
    required DateTime startDate,
    required DateTime endDate,
    required String periodDescription,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Fetch all relevant data for the date range
        final payments = await _dataRepository.getPaymentsByDateRange(
          startDate,
          endDate,
        );
        final expenses = await _dataRepository.getExpensesByDateRange(
          startDate,
          endDate,
        );
        final mileages = await getMileagesByDateRange(startDate, endDate);
        final taxPayments = await _dataRepository.getTaxPaymentsByDateRange(
          startDate,
          endDate,
        );

        // Get all jobs and customers for reference
        final jobs = await _dataRepository.getJobs();
        final customers = await _dataRepository.getCustomers();
        final invoices = await _dataRepository.getInvoices();

        // Create lookup maps for efficiency
        final jobMap = {for (var job in jobs) job.id: job};
        final customerMap = {
          for (var customer in customers) customer.id: customer,
        };
        final invoiceMap = {for (var invoice in invoices) invoice.id!: invoice};

        // Process income items from payments
        final incomeItems = <TaxIncomeItem>[];
        for (var payment in payments) {
          final invoice = invoiceMap[payment.invoiceId];
          final job = invoice != null ? jobMap[invoice.jobId] : null;
          final customer =
              invoice != null ? customerMap[invoice.customerId] : null;

          incomeItems.add(
            TaxIncomeItem(
              id: payment.id,
              date: payment.paymentDate,
              description: payment.notes ?? 'Payment received',
              customerName: customer?.name ?? 'Unknown Customer',
              jobName: job?.title ?? job?.description ?? 'Unknown Job',
              amount: payment.amount,
              source: 'payment',
              invoiceNumber: invoice?.id?.substring(0, 8),
            ),
          );
        }

        // Process expense items
        final expenseItems = <TaxExpenseItem>[];
        for (var expense in expenses) {
          // Skip mileage expenses as they're handled separately
          if (expense is Mileage) continue;

          final job = expense.jobId != null ? jobMap[expense.jobId!] : null;
          final customer = job != null ? customerMap[job.customerId] : null;

          expenseItems.add(
            TaxExpenseItem(
              id: expense.id,
              date: expense.date,
              description: expense.description,
              category: expense.category ?? 'Other',
              amount: expense.amount,
              jobName: job?.title ?? job?.description,
              customerName: customer?.name,
              isOverhead: expense.isOverhead,
              receiptUrl: expense.receiptPhotoUrl,
            ),
          );
        }

        // Process mileage items
        final mileageItems = <TaxMileageItem>[];
        for (var mileage in mileages) {
          final job = mileage.jobId != null ? jobMap[mileage.jobId!] : null;
          final customer = job != null ? customerMap[job.customerId] : null;

          mileageItems.add(
            TaxMileageItem(
              id: mileage.id,
              date: mileage.date,
              startLocation: mileage.startLocation,
              endLocation: mileage.endLocation,
              miles: mileage.miles,
              ratePerMile: mileage.ratePerMile,
              deductionAmount: mileage.amount,
              purpose: mileage.purpose,
              jobName: job?.title ?? job?.description,
              customerName: customer?.name,
            ),
          );
        }

        // Process tax payment items
        final taxPaymentItems = <TaxPaymentItem>[];
        for (var taxPayment in taxPayments) {
          taxPaymentItems.add(
            TaxPaymentItem(
              id: taxPayment.id,
              date: taxPayment.date,
              amount: taxPayment.amount,
              taxPeriod: taxPayment.taxPeriod,
              paymentMethod: taxPayment.paymentMethod,
              confirmationNumber: taxPayment.confirmationNumber,
              notes: taxPayment.notes,
            ),
          );
        }

        // Calculate summary
        final totalIncome = incomeItems.fold(
          0.0,
          (sum, item) => sum + item.amount,
        );
        final totalExpenses = expenseItems.fold(
          0.0,
          (sum, item) => sum + item.amount,
        );
        final totalMileageDeduction = mileageItems.fold(
          0.0,
          (sum, item) => sum + item.deductionAmount,
        );
        final totalTaxPayments = taxPaymentItems.fold(
          0.0,
          (sum, item) => sum + item.amount,
        );
        final netProfit = totalIncome - totalExpenses - totalMileageDeduction;
        final estimatedTaxOwed =
            netProfit > 0 ? netProfit * 0.25 : 0.0; // 25% estimate
        final taxBalance = estimatedTaxOwed - totalTaxPayments;

        final summary = TaxSummary(
          totalIncome: totalIncome,
          totalExpenses: totalExpenses,
          totalMileageDeduction: totalMileageDeduction,
          totalTaxPayments: totalTaxPayments,
          netProfit: netProfit,
          estimatedTaxOwed: estimatedTaxOwed,
          taxBalance: taxBalance,
        );

        return TaxExportData(
          startDate: startDate,
          endDate: endDate,
          periodDescription: periodDescription,
          incomeItems: incomeItems,
          expenseItems: expenseItems,
          mileageItems: mileageItems,
          taxPaymentItems: taxPaymentItems,
          summary: summary,
        );
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'generateTaxExportData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Export tax data as CSV file
  Future<String> exportToCsv(TaxExportData taxData) async {
    final result = await RetryMechanism.execute(
      () async {
        final directory = await getTemporaryDirectory();
        final fileName =
            'tax_export_${taxData.periodDescription.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.csv';
        final file = File('${directory.path}/$fileName');

        final csvData = <List<String>>[];

        // Add header with export info
        csvData.add(['Tax Export Report']);
        csvData.add(['Period:', taxData.periodDescription]);
        csvData.add([
          'Export Date:',
          DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()),
        ]);
        csvData.add([]); // Empty row

        // Add summary section
        csvData.add(['SUMMARY']);
        final summaryMap = taxData.summary.toMap();
        for (var entry in summaryMap.entries) {
          csvData.add([entry.key, '\$${entry.value.toStringAsFixed(2)}']);
        }
        csvData.add([]); // Empty row

        // Add income section
        if (taxData.incomeItems.isNotEmpty) {
          csvData.add(['INCOME']);
          csvData.add([
            'Date',
            'Description',
            'Customer',
            'Job',
            'Amount',
            'Source',
            'Invoice Number',
          ]);
          for (var item in taxData.incomeItems) {
            final row = item.toCsvRow();
            csvData.add([
              row['Date']!,
              row['Description']!,
              row['Customer']!,
              row['Job']!,
              '\$${row['Amount']!}',
              row['Source']!,
              row['Invoice Number']!,
            ]);
          }
          csvData.add([]); // Empty row
        }

        // Add expenses section
        if (taxData.expenseItems.isNotEmpty) {
          csvData.add(['EXPENSES']);
          csvData.add([
            'Date',
            'Description',
            'Category',
            'Amount',
            'Job',
            'Customer',
            'Type',
            'Receipt',
          ]);
          for (var item in taxData.expenseItems) {
            final row = item.toCsvRow();
            csvData.add([
              row['Date']!,
              row['Description']!,
              row['Category']!,
              '\$${row['Amount']!}',
              row['Job']!,
              row['Customer']!,
              row['Type']!,
              row['Receipt']!,
            ]);
          }
          csvData.add([]); // Empty row
        }

        // Add mileage section
        if (taxData.mileageItems.isNotEmpty) {
          csvData.add(['MILEAGE DEDUCTIONS']);
          csvData.add([
            'Date',
            'From',
            'To',
            'Miles',
            'Rate per Mile',
            'Deduction Amount',
            'Purpose',
            'Job',
            'Customer',
          ]);
          for (var item in taxData.mileageItems) {
            final row = item.toCsvRow();
            csvData.add([
              row['Date']!,
              row['From']!,
              row['To']!,
              row['Miles']!,
              '\$${row['Rate per Mile']!}',
              '\$${row['Deduction Amount']!}',
              row['Purpose']!,
              row['Job']!,
              row['Customer']!,
            ]);
          }
          csvData.add([]); // Empty row
        }

        // Add tax payments section
        if (taxData.taxPaymentItems.isNotEmpty) {
          csvData.add(['TAX PAYMENTS']);
          csvData.add([
            'Date',
            'Amount',
            'Tax Period',
            'Payment Method',
            'Confirmation Number',
            'Notes',
          ]);
          for (var item in taxData.taxPaymentItems) {
            final row = item.toCsvRow();
            csvData.add([
              row['Date']!,
              '\$${row['Amount']!}',
              row['Tax Period']!,
              row['Payment Method']!,
              row['Confirmation Number']!,
              row['Notes']!,
            ]);
          }
        }

        // Convert to CSV string and write to file
        final csvString = const ListToCsvConverter().convert(csvData);
        await file.writeAsString(csvString);

        return file.path;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'exportToCsv',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Export tax data as PDF file
  Future<String> exportToPdf(TaxExportData taxData) async {
    try {
      final pdf = pw.Document();

      // Create a PDF theme
      final theme = pw.ThemeData.withFont(
        base: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
      );

      pdf.addPage(
        pw.MultiPage(
          theme: theme,
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                // Header
                _buildPdfHeader(taxData),
                pw.SizedBox(height: 20),

                // Summary section
                _buildPdfSummary(taxData.summary),
                pw.SizedBox(height: 20),

                // Income section
                if (taxData.incomeItems.isNotEmpty) ...[
                  _buildPdfIncomeSection(taxData.incomeItems),
                  pw.SizedBox(height: 20),
                ],

                // Expenses section
                if (taxData.expenseItems.isNotEmpty) ...[
                  _buildPdfExpenseSection(taxData.expenseItems),
                  pw.SizedBox(height: 20),
                ],

                // Mileage section
                if (taxData.mileageItems.isNotEmpty) ...[
                  _buildPdfMileageSection(taxData.mileageItems),
                  pw.SizedBox(height: 20),
                ],

                // Tax payments section
                if (taxData.taxPaymentItems.isNotEmpty) ...[
                  _buildPdfTaxPaymentSection(taxData.taxPaymentItems),
                ],
              ],
          footer: (context) => _buildPdfFooter(context),
        ),
      );

      // Save PDF to file
      final directory = await getTemporaryDirectory();
      final fileName =
          'tax_export_${taxData.periodDescription.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      return file.path;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'exportToPdf',
          'periodDescription': taxData.periodDescription,
          'incomeItemsCount': taxData.incomeItems.length,
          'expenseItemsCount': taxData.expenseItems.length,
          'mileageItemsCount': taxData.mileageItems.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Share exported files
  Future<void> shareExportedFiles({
    required List<String> filePaths,
    required String periodDescription,
  }) async {
    try {
      final xFiles = filePaths.map((path) => XFile(path)).toList();

      await SharePlus.instance.share(
        ShareParams(
          files: xFiles,
          text: 'Tax Export Report for $periodDescription',
          subject: 'Tax Export - $periodDescription',
        ),
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'shareExportedFiles',
          'periodDescription': periodDescription,
          'fileCount': filePaths.length,
          'filePaths': filePaths,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Get mileages by date range (helper method)
  Future<List<Mileage>> getMileagesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final allExpenses = await _dataRepository.getExpensesByDateRange(
      startDate,
      endDate,
    );
    return allExpenses.whereType<Mileage>().toList();
  }

  // PDF building helper methods
  pw.Widget _buildPdfHeader(TaxExportData taxData) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Tax Export Report',
          style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          'Period: ${taxData.periodDescription}',
          style: const pw.TextStyle(fontSize: 16),
        ),
        pw.Text(
          'Export Date: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
          style: const pw.TextStyle(fontSize: 12),
        ),
        pw.Divider(thickness: 2),
      ],
    );
  }

  pw.Widget _buildPdfSummary(TaxSummary summary) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'SUMMARY',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            _buildPdfTableRow(
              'Total Income',
              '\$${summary.totalIncome.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Total Expenses',
              '\$${summary.totalExpenses.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Total Mileage Deduction',
              '\$${summary.totalMileageDeduction.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Net Profit',
              '\$${summary.netProfit.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Estimated Tax Owed (25%)',
              '\$${summary.estimatedTaxOwed.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Tax Payments Made',
              '\$${summary.totalTaxPayments.toStringAsFixed(2)}',
            ),
            _buildPdfTableRow(
              'Tax Balance',
              '\$${summary.taxBalance.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ],
    );
  }

  pw.TableRow _buildPdfTableRow(
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return pw.TableRow(
      children: [
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            label,
            style: pw.TextStyle(
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
            textAlign: pw.TextAlign.right,
          ),
        ),
      ],
    );
  }

  pw.Widget _buildPdfIncomeSection(List<TaxIncomeItem> incomeItems) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'INCOME',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Table(
          border: pw.TableBorder.all(),
          columnWidths: {
            0: const pw.FixedColumnWidth(80),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(1.5),
            3: const pw.FlexColumnWidth(1.5),
            4: const pw.FixedColumnWidth(80),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildPdfHeaderCell('Date'),
                _buildPdfHeaderCell('Description'),
                _buildPdfHeaderCell('Customer'),
                _buildPdfHeaderCell('Job'),
                _buildPdfHeaderCell('Amount'),
              ],
            ),
            ...incomeItems.map(
              (item) => pw.TableRow(
                children: [
                  _buildPdfCell(DateFormat('MM/dd/yyyy').format(item.date)),
                  _buildPdfCell(item.description),
                  _buildPdfCell(item.customerName),
                  _buildPdfCell(item.jobName),
                  _buildPdfCell(
                    '\$${item.amount.toStringAsFixed(2)}',
                    align: pw.TextAlign.right,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  pw.Widget _buildPdfExpenseSection(List<TaxExpenseItem> expenseItems) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'EXPENSES',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Table(
          border: pw.TableBorder.all(),
          columnWidths: {
            0: const pw.FixedColumnWidth(80),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(1),
            3: const pw.FixedColumnWidth(80),
            4: const pw.FlexColumnWidth(1),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildPdfHeaderCell('Date'),
                _buildPdfHeaderCell('Description'),
                _buildPdfHeaderCell('Category'),
                _buildPdfHeaderCell('Amount'),
                _buildPdfHeaderCell('Type'),
              ],
            ),
            ...expenseItems.map(
              (item) => pw.TableRow(
                children: [
                  _buildPdfCell(DateFormat('MM/dd/yyyy').format(item.date)),
                  _buildPdfCell(item.description),
                  _buildPdfCell(item.category),
                  _buildPdfCell(
                    '\$${item.amount.toStringAsFixed(2)}',
                    align: pw.TextAlign.right,
                  ),
                  _buildPdfCell(item.isOverhead ? 'Overhead' : 'Job'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  pw.Widget _buildPdfMileageSection(List<TaxMileageItem> mileageItems) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'MILEAGE DEDUCTIONS',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Table(
          border: pw.TableBorder.all(),
          columnWidths: {
            0: const pw.FixedColumnWidth(80),
            1: const pw.FlexColumnWidth(1.5),
            2: const pw.FlexColumnWidth(1.5),
            3: const pw.FixedColumnWidth(60),
            4: const pw.FixedColumnWidth(80),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildPdfHeaderCell('Date'),
                _buildPdfHeaderCell('From'),
                _buildPdfHeaderCell('To'),
                _buildPdfHeaderCell('Miles'),
                _buildPdfHeaderCell('Deduction'),
              ],
            ),
            ...mileageItems.map(
              (item) => pw.TableRow(
                children: [
                  _buildPdfCell(DateFormat('MM/dd/yyyy').format(item.date)),
                  _buildPdfCell(item.startLocation),
                  _buildPdfCell(item.endLocation),
                  _buildPdfCell(
                    item.miles.toStringAsFixed(1),
                    align: pw.TextAlign.right,
                  ),
                  _buildPdfCell(
                    '\$${item.deductionAmount.toStringAsFixed(2)}',
                    align: pw.TextAlign.right,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  pw.Widget _buildPdfTaxPaymentSection(List<TaxPaymentItem> taxPaymentItems) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'TAX PAYMENTS',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Table(
          border: pw.TableBorder.all(),
          columnWidths: {
            0: const pw.FixedColumnWidth(80),
            1: const pw.FixedColumnWidth(80),
            2: const pw.FlexColumnWidth(1),
            3: const pw.FlexColumnWidth(1),
          },
          children: [
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey300),
              children: [
                _buildPdfHeaderCell('Date'),
                _buildPdfHeaderCell('Amount'),
                _buildPdfHeaderCell('Tax Period'),
                _buildPdfHeaderCell('Method'),
              ],
            ),
            ...taxPaymentItems.map(
              (item) => pw.TableRow(
                children: [
                  _buildPdfCell(DateFormat('MM/dd/yyyy').format(item.date)),
                  _buildPdfCell(
                    '\$${item.amount.toStringAsFixed(2)}',
                    align: pw.TextAlign.right,
                  ),
                  _buildPdfCell(item.taxPeriod),
                  _buildPdfCell(item.paymentMethod ?? ''),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  pw.Widget _buildPdfHeaderCell(String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  pw.Widget _buildPdfCell(String text, {pw.TextAlign? align}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(text, textAlign: align ?? pw.TextAlign.left),
    );
  }

  pw.Widget _buildPdfFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 1.0 * PdfPageFormat.cm),
      child: pw.Text(
        'Page ${context.pageNumber} of ${context.pagesCount}',
        style: const pw.TextStyle(fontSize: 10),
      ),
    );
  }
}
