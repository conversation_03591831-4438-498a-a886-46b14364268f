import '../models/models.dart';
import 'supabase_service.dart';
import '../utils/error_handler.dart';
import '../utils/retry_mechanism.dart';

// Extension to SupabaseService for dashboard operations
extension DashboardService on SupabaseService {
  // Get all expenses since a specific date
  Future<List<Expense>> getExpensesSince(DateTime date) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await client
            .from('expenses')
            .select()
            .eq('user_id', client.auth.currentUser!.id)
            .gte('date', date.toIso8601String())
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesSince',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all payments since a specific date
  Future<List<Payment>> getPaymentsSince(DateTime date) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await client
            .from('payments')
            .select()
            .eq('user_id', client.auth.currentUser!.id)
            .gte('date', date.toIso8601String())
            .order('date', ascending: false);

        return response.map((json) => Payment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getPaymentsSince',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get active jobs (not completed or cancelled)
  Future<List<Job>> getActiveJobs() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await client
            .from('jobs')
            .select('*, customers(*)')
            .eq('user_id', client.auth.currentUser!.id)
            .not('status', 'eq', 'Completed')
            .not('status', 'eq', 'Cancelled')
            .order('created_at', ascending: false);

        return response.map((json) => Job.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getActiveJobs',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get estimates by status
  Future<List<Estimate>> getEstimatesByStatus(String status) async {
    try {
      final response = await client
          .from('estimates')
          .select()
          .eq('user_id', client.auth.currentUser!.id)
          .eq('status', status)
          .order('created_at', ascending: false);

      return response.map((json) => Estimate.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get estimates by status: $e');
    }
  }

  // Get invoices by status
  Future<List<Invoice>> getInvoicesByStatus(String status) async {
    try {
      final response = await client
          .from('invoices')
          .select()
          .eq('user_id', client.auth.currentUser!.id)
          .eq('status', status)
          .order('created_at', ascending: false);

      return response.map((json) => Invoice.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get invoices by status: $e');
    }
  }

  // Get overdue invoices
  Future<List<Invoice>> getOverdueInvoices() async {
    final now = DateTime.now();

    try {
      final response = await client
          .from('invoices')
          .select()
          .eq('user_id', client.auth.currentUser!.id)
          .eq('status', 'Sent')
          .lt('due_date', now.toIso8601String())
          .order('due_date', ascending: true);

      return response.map((json) => Invoice.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get overdue invoices: $e');
    }
  }
}
