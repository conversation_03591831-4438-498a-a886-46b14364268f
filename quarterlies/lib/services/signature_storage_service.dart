import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

/// Service for managing user signature storage in Supabase
class SignatureStorageService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  // Singleton pattern
  static final SignatureStorageService _instance =
      SignatureStorageService._internal();
  factory SignatureStorageService() => _instance;
  SignatureStorageService._internal();

  /// Upload user signature to Supabase storage and return the URL
  Future<String> uploadUserSignature(Uint8List signatureBytes) async {
    final result = await RetryMechanism.execute(
      () async {
        final userId = _supabaseClient.auth.currentUser!.id;
        final signatureId = const Uuid().v4();
        final fileName = 'signature_$signatureId.png';
        final storagePath = 'signatures/$userId/$fileName';

        // Create a temporary file to upload
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/$fileName');
        await tempFile.writeAsBytes(signatureBytes);

        // Upload to Supabase storage
        await _supabaseClient.storage
            .from('documents')
            .upload(
              storagePath,
              tempFile,
              fileOptions: const FileOptions(
                cacheControl: '3600',
                upsert: false,
              ),
            );

        // Get the public URL for the signature
        final signatureUrl = _supabaseClient.storage
            .from('documents')
            .getPublicUrl(storagePath);

        // Clean up the temporary file
        await tempFile.delete();

        return signatureUrl;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'uploadUserSignature',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Update user signature (delete old one and upload new one)
  Future<String> updateUserSignature(
    Uint8List signatureBytes,
    String? oldSignatureUrl,
  ) async {
    try {
      // Delete old signature if it exists
      if (oldSignatureUrl != null) {
        await deleteUserSignature(oldSignatureUrl);
      }

      // Upload new signature
      return await uploadUserSignature(signatureBytes);
    } catch (e) {
      debugPrint('Error updating user signature: $e');
      throw Exception('Failed to update signature: $e');
    }
  }

  /// Delete user signature from Supabase storage
  Future<void> deleteUserSignature(String signatureUrl) async {
    try {
      // Extract the storage path from the URL
      final uri = Uri.parse(signatureUrl);
      final pathSegments = uri.pathSegments;

      // Find the path after 'documents'
      final documentsIndex = pathSegments.indexOf('documents');
      if (documentsIndex == -1 || documentsIndex >= pathSegments.length - 1) {
        throw Exception('Invalid signature URL format');
      }

      // Reconstruct the storage path
      final storagePath = pathSegments.sublist(documentsIndex + 1).join('/');

      // Delete from Supabase storage
      await _supabaseClient.storage.from('documents').remove([storagePath]);

      debugPrint('Successfully deleted signature: $storagePath');
    } catch (e) {
      debugPrint('Error deleting user signature: $e');
      // Don't throw here as this is cleanup - log the error but continue
    }
  }

  /// Download signature bytes from URL
  Future<Uint8List?> downloadSignature(String signatureUrl) async {
    final result = await RetryMechanism.execute(
      () async {
        // For Supabase storage URLs, we can download directly
        final response = await _supabaseClient.storage
            .from('documents')
            .download(_extractStoragePath(signatureUrl));

        return response;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'downloadSignature',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return null; // Return null instead of throwing to allow graceful degradation
    }

    return result.data;
  }

  /// Extract storage path from Supabase URL
  String _extractStoragePath(String url) {
    final uri = Uri.parse(url);
    final pathSegments = uri.pathSegments;

    // Find the path after 'documents'
    final documentsIndex = pathSegments.indexOf('documents');
    if (documentsIndex == -1 || documentsIndex >= pathSegments.length - 1) {
      throw Exception('Invalid signature URL format');
    }

    // Return the storage path
    return pathSegments.sublist(documentsIndex + 1).join('/');
  }

  /// Check if signature URL is valid and accessible
  Future<bool> isSignatureValid(String signatureUrl) async {
    try {
      final bytes = await downloadSignature(signatureUrl);
      return bytes != null && bytes.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get signature file size in bytes
  Future<int?> getSignatureSize(String signatureUrl) async {
    try {
      final bytes = await downloadSignature(signatureUrl);
      return bytes?.length;
    } catch (e) {
      debugPrint('Error getting signature size: $e');
      return null;
    }
  }

  /// Validate signature bytes (basic validation)
  bool isValidSignatureBytes(Uint8List bytes) {
    // Check if it's a valid PNG file (starts with PNG signature)
    if (bytes.length < 8) return false;

    // PNG signature: 89 50 4E 47 0D 0A 1A 0A
    final pngSignature = [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A];

    for (int i = 0; i < pngSignature.length; i++) {
      if (bytes[i] != pngSignature[i]) {
        return false;
      }
    }

    return true;
  }

  /// Get storage usage for user signatures
  Future<Map<String, dynamic>> getSignatureStorageInfo() async {
    final result = await RetryMechanism.execute(
      () async {
        final userId = _supabaseClient.auth.currentUser!.id;

        // List all signature files for the user
        final files = await _supabaseClient.storage
            .from('documents')
            .list(path: 'signatures/$userId');

        int totalSize = 0;
        int fileCount = files.length;

        for (final file in files) {
          if (file.metadata != null && file.metadata!['size'] != null) {
            totalSize += file.metadata!['size'] as int;
          }
        }

        return {
          'fileCount': fileCount,
          'totalSize': totalSize,
          'totalSizeFormatted': _formatBytes(totalSize),
        };
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'getSignatureStorageInfo',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return {'fileCount': 0, 'totalSize': 0, 'totalSizeFormatted': '0 B'};
    }

    return result.data!;
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Clean up old signature files (keep only the latest)
  Future<void> cleanupOldSignatures() async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;

      // List all signature files for the user
      final files = await _supabaseClient.storage
          .from('documents')
          .list(path: 'signatures/$userId');

      if (files.length <= 1) return; // Keep at least one file

      // Sort by creation date (newest first)
      files.sort((a, b) {
        final aTime = DateTime.parse(
          a.createdAt ?? DateTime.now().toIso8601String(),
        );
        final bTime = DateTime.parse(
          b.createdAt ?? DateTime.now().toIso8601String(),
        );
        return bTime.compareTo(aTime);
      });

      // Delete all but the newest file
      final filesToDelete =
          files
              .skip(1)
              .map((file) => 'signatures/$userId/${file.name}')
              .toList();

      if (filesToDelete.isNotEmpty) {
        await _supabaseClient.storage.from('documents').remove(filesToDelete);

        debugPrint('Cleaned up ${filesToDelete.length} old signature files');
      }
    } catch (e) {
      debugPrint('Error cleaning up old signatures: $e');
      // Don't throw - this is cleanup
    }
  }
}
