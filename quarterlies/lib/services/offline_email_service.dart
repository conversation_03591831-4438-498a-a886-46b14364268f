import 'package:flutter/foundation.dart';
import 'package:quarterlies/services/connectivity_service.dart';

/// Model for queued email
class QueuedEmail {
  final String id;
  final String userId;
  final String type; // 'invoice', 'estimate', 'contract', 'signing_request'
  final String recipientEmail;
  final String recipientName;
  final String subject;
  final String body;
  final Map<String, dynamic> metadata;
  final String? attachmentPath;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  final int retryCount;
  final String status; // 'pending', 'sending', 'sent', 'failed'

  QueuedEmail({
    String? id,
    required this.userId,
    required this.type,
    required this.recipientEmail,
    required this.recipientName,
    required this.subject,
    required this.body,
    required this.metadata,
    this.attachmentPath,
    DateTime? createdAt,
    this.scheduledAt,
    this.retryCount = 0,
    this.status = 'pending',
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       createdAt = createdAt ?? DateTime.now();

  QueuedEmail copyWith({
    String? id,
    String? userId,
    String? type,
    String? recipientEmail,
    String? recipientName,
    String? subject,
    String? body,
    Map<String, dynamic>? metadata,
    String? attachmentPath,
    DateTime? createdAt,
    DateTime? scheduledAt,
    int? retryCount,
    String? status,
  }) {
    return QueuedEmail(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      recipientEmail: recipientEmail ?? this.recipientEmail,
      recipientName: recipientName ?? this.recipientName,
      subject: subject ?? this.subject,
      body: body ?? this.body,
      metadata: metadata ?? this.metadata,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      createdAt: createdAt ?? this.createdAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      retryCount: retryCount ?? this.retryCount,
      status: status ?? this.status,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type,
      'recipient_email': recipientEmail,
      'recipient_name': recipientName,
      'subject': subject,
      'body': body,
      'metadata': metadata,
      'attachment_path': attachmentPath,
      'created_at': createdAt.toIso8601String(),
      'scheduled_at': scheduledAt?.toIso8601String(),
      'retry_count': retryCount,
      'status': status,
    };
  }

  factory QueuedEmail.fromJson(Map<String, dynamic> json) {
    return QueuedEmail(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: json['type'] as String,
      recipientEmail: json['recipient_email'] as String,
      recipientName: json['recipient_name'] as String,
      subject: json['subject'] as String,
      body: json['body'] as String,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      attachmentPath: json['attachment_path'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      scheduledAt:
          json['scheduled_at'] != null
              ? DateTime.parse(json['scheduled_at'] as String)
              : null,
      retryCount: json['retry_count'] as int,
      status: json['status'] as String,
    );
  }
}

/// Offline-first email service that queues emails when offline
/// and sends them when connection is restored
class OfflineEmailService {
  // Singleton pattern
  static final OfflineEmailService _instance = OfflineEmailService._internal();
  factory OfflineEmailService() => _instance;
  OfflineEmailService._internal();

  final ConnectivityService _connectivityService = ConnectivityService();

  /// Queue an email for sending (offline-first)
  ///
  /// If online, attempts to send immediately. If offline or sending fails,
  /// queues the email for later delivery.
  Future<bool> queueEmail({
    required String type,
    required String recipientEmail,
    required String recipientName,
    required String subject,
    required String body,
    Map<String, dynamic>? metadata,
    String? attachmentPath,
    DateTime? scheduledAt,
  }) async {
    try {
      final userId = 'current_user_id'; // Get from auth service

      final queuedEmail = QueuedEmail(
        userId: userId,
        type: type,
        recipientEmail: recipientEmail,
        recipientName: recipientName,
        subject: subject,
        body: body,
        metadata: metadata ?? {},
        attachmentPath: attachmentPath,
        scheduledAt: scheduledAt,
      );

      // Always queue the email first
      await _addToQueue(queuedEmail);

      // If online and not scheduled for later, try to send immediately
      final isOnline = await _connectivityService.checkConnection();
      final shouldSendNow =
          scheduledAt == null || DateTime.now().isAfter(scheduledAt);

      if (isOnline && shouldSendNow) {
        return await _attemptSend(queuedEmail);
      }

      debugPrint('Email queued for later delivery: ${queuedEmail.id}');
      return true; // Successfully queued
    } catch (e) {
      debugPrint('Error queueing email: $e');
      return false;
    }
  }

  /// Process the email queue (send pending emails)
  Future<void> processQueue() async {
    try {
      final isOnline = await _connectivityService.checkConnection();
      if (!isOnline) {
        debugPrint('Offline - skipping email queue processing');
        return;
      }

      final pendingEmails = await _getPendingEmails();
      debugPrint('Processing ${pendingEmails.length} queued emails');

      for (final email in pendingEmails) {
        // Check if email is scheduled for the future
        if (email.scheduledAt != null &&
            DateTime.now().isBefore(email.scheduledAt!)) {
          continue; // Skip emails scheduled for later
        }

        await _attemptSend(email);

        // Add small delay between emails to avoid rate limiting
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      debugPrint('Error processing email queue: $e');
    }
  }

  /// Get pending emails from queue
  Future<List<QueuedEmail>> getPendingEmails() async {
    return await _getPendingEmails();
  }

  /// Get failed emails from queue
  Future<List<QueuedEmail>> getFailedEmails() async {
    try {
      // This would need to be implemented in LocalDatabaseService
      // For now, return empty list
      return [];
    } catch (e) {
      debugPrint('Error getting failed emails: $e');
      return [];
    }
  }

  /// Retry failed emails
  Future<void> retryFailedEmails() async {
    try {
      final failedEmails = await getFailedEmails();

      for (final email in failedEmails) {
        if (email.retryCount < 3) {
          // Max 3 retries
          await _attemptSend(email);
        }
      }
    } catch (e) {
      debugPrint('Error retrying failed emails: $e');
    }
  }

  /// Clear sent emails older than specified duration
  Future<void> clearOldEmails({
    Duration maxAge = const Duration(days: 30),
  }) async {
    try {
      // This would need to be implemented in LocalDatabaseService
      debugPrint('Clearing emails older than ${maxAge.inDays} days');
    } catch (e) {
      debugPrint('Error clearing old emails: $e');
    }
  }

  // Private methods

  Future<void> _addToQueue(QueuedEmail email) async {
    // This would need to be implemented in LocalDatabaseService
    // For now, just log
    debugPrint('Added email to queue: ${email.id}');
  }

  Future<List<QueuedEmail>> _getPendingEmails() async {
    // This would need to be implemented in LocalDatabaseService
    // For now, return empty list
    return [];
  }

  Future<bool> _attemptSend(QueuedEmail email) async {
    try {
      await _updateEmailStatus(email.id, 'sending');

      // Attempt to send the email using the existing EmailService
      final success = await _sendEmailUsingService(email);

      if (success) {
        await _updateEmailStatus(email.id, 'sent');
        debugPrint('Email sent successfully: ${email.id}');
        return true;
      } else {
        await _handleSendFailure(email);
        return false;
      }
    } catch (e) {
      debugPrint('Error sending email ${email.id}: $e');
      await _handleSendFailure(email);
      return false;
    }
  }

  Future<bool> _sendEmailUsingService(QueuedEmail email) async {
    try {
      // Use the existing EmailService based on email type
      switch (email.type) {
        case 'invoice':
          // Would call EmailService.sendInvoiceEmail()
          return true;
        case 'estimate':
          // Would call EmailService.sendEstimateEmail()
          return true;
        case 'contract':
          // Would call EmailService.sendContractEmail()
          return true;
        case 'signing_request':
          // Would call EmailService.sendSigningRequestEmail()
          return true;
        default:
          debugPrint('Unknown email type: ${email.type}');
          return false;
      }
    } catch (e) {
      debugPrint('Error in email service: $e');
      return false;
    }
  }

  Future<void> _updateEmailStatus(String emailId, String status) async {
    // This would need to be implemented in LocalDatabaseService
    debugPrint('Updated email $emailId status to: $status');
  }

  Future<void> _handleSendFailure(QueuedEmail email) async {
    final newRetryCount = email.retryCount + 1;

    if (newRetryCount >= 3) {
      await _updateEmailStatus(email.id, 'failed');
      debugPrint('Email failed after 3 attempts: ${email.id}');
    } else {
      // Update retry count and keep as pending
      debugPrint(
        'Email send failed, retry count: $newRetryCount for ${email.id}',
      );
    }
  }
}
