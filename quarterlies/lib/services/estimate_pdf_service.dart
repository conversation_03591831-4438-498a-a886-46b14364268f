import 'dart:io';
import 'dart:typed_data';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
// Import specific models to avoid conflicts
import 'package:quarterlies/models/estimate.dart';
import 'package:quarterlies/models/job.dart' show Job;
import 'package:quarterlies/models/customer.dart' show Customer;
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';

class EstimatePdfService {
  // Reference to LoadingStateProvider for UI updates
  LoadingStateProvider? _loadingStateProvider;

  // Set the LoadingStateProvider for UI updates
  void setLoadingStateProvider(LoadingStateProvider provider) {
    _loadingStateProvider = provider;
  }

  /// Generate a PDF document from an estimate
  Future<Uint8List> generateEstimatePdf({
    required Estimate estimate,
    required Customer customer,
    required Job job,
  }) async {
    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithPdfLoading(() async {
        return await _performGenerateEstimatePdf(
          estimate: estimate,
          customer: customer,
          job: job,
        );
      }, documentType: 'Estimate');
    } else {
      return await _performGenerateEstimatePdf(
        estimate: estimate,
        customer: customer,
        job: job,
      );
    }
  }

  /// Internal PDF generation implementation
  Future<Uint8List> _performGenerateEstimatePdf({
    required Estimate estimate,
    required Customer customer,
    required Job job,
  }) async {
    try {
      final pdf = pw.Document();

      // Create a PDF theme with custom colors
      final theme = pw.ThemeData.withFont(
        base: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
      );

      // Add a page to the PDF document
      pdf.addPage(
        pw.MultiPage(
          theme: theme,
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildHeader(estimate, customer),
                pw.SizedBox(height: 20),
                _buildEstimateInfo(estimate, job),
                pw.SizedBox(height: 20),
                _buildLineItems(estimate),
                pw.SizedBox(height: 20),
                _buildTotal(estimate),
                pw.SizedBox(height: 20),
                if (estimate.notes != null && estimate.notes!.isNotEmpty)
                  _buildNotes(estimate),
                pw.SizedBox(height: 20),
                _buildSignatureSection(),
              ],
          footer: (context) => _buildFooter(context),
        ),
      );

      return pdf.save();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'generateEstimatePdf',
          'estimateId': estimate.id,
          'customerId': customer.id,
          'jobId': job.id,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Build the signature section for estimate approval
  pw.Widget _buildSignatureSection() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Estimate Approval',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'By signing below, I approve this estimate and authorize the work to proceed.',
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          padding: const pw.EdgeInsets.all(10),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'ELECTRONIC SIGNATURE CONSENT',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                'By signing electronically, you confirm that you have read and agree to the terms of this estimate. '
                'Your electronic signature is legally binding as if you signed with pen and paper.',
                style: const pw.TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  width: 200,
                  height: 50,
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 1),
                    borderRadius: const pw.BorderRadius.all(
                      pw.Radius.circular(5),
                    ),
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text('Customer Signature'),
                pw.SizedBox(height: 15),
                pw.Container(
                  width: 200,
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  height: 1,
                ),
                pw.SizedBox(height: 5),
                pw.Text('Date'),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  width: 200,
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  height: 1,
                ),
                pw.SizedBox(height: 5),
                pw.Text('Print Name'),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Build the estimate header with company and customer information
  pw.Widget _buildHeader(Estimate estimate, Customer customer) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'ESTIMATE',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text('Estimate #${estimate.id.substring(0, 8)}'),
                pw.Text(
                  'Date: ${DateFormat('MM/dd/yyyy').format(estimate.createdAt)}',
                ),
                pw.Text(
                  'Valid Until: ${DateFormat('MM/dd/yyyy').format(estimate.createdAt.add(const Duration(days: 30)))}',
                ),
              ],
            ),
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Text(
                estimate.status.toUpperCase(),
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: _getStatusPdfColor(estimate.status),
                ),
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20),
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'From:',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text('Your Business Name'),
                  pw.Text('Your Address'),
                  pw.Text('Your City, State ZIP'),
                  pw.Text('Phone: Your Phone'),
                  pw.Text('Email: Your Email'),
                ],
              ),
            ),
            pw.Expanded(
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Bill To:',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.green50,
                      border: pw.Border.all(
                        color: PdfColors.green200,
                        width: 0.5,
                      ),
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          customer.name,
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        if (customer.address != null)
                          pw.Text(customer.address!),
                        pw.Text(
                          [
                                if (customer.city != null) customer.city,
                                if (customer.state != null) customer.state,
                                if (customer.zipCode != null) customer.zipCode,
                              ]
                              .where((part) => part != null && part.isNotEmpty)
                              .join(', '),
                        ),
                        if (customer.phone != null)
                          pw.Text('Phone: ${customer.phone}'),
                        if (customer.email != null)
                          pw.Text('Email: ${customer.email}'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the estimate information section
  pw.Widget _buildEstimateInfo(Estimate estimate, Job job) {
    // Build service address from job address components
    String buildServiceAddress() {
      final List<String> addressParts = [];

      if (job.address != null && job.address!.isNotEmpty) {
        addressParts.add(job.address!);
      }

      final List<String> cityStateParts = [];
      if (job.city != null && job.city!.isNotEmpty) {
        cityStateParts.add(job.city!);
      }
      if (job.state != null && job.state!.isNotEmpty) {
        cityStateParts.add(job.state!);
      }

      if (cityStateParts.isNotEmpty) {
        if (job.zipCode != null && job.zipCode!.isNotEmpty) {
          addressParts.add('${cityStateParts.join(', ')} ${job.zipCode}');
        } else {
          addressParts.add(cityStateParts.join(', '));
        }
      } else if (job.zipCode != null && job.zipCode!.isNotEmpty) {
        addressParts.add(job.zipCode!);
      }

      return addressParts.join('\n');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Job Information',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Text('Job: ${job.title}'),
        if (job.description != null) pw.Text('Description: ${job.description}'),

        // Add Service Address section with improved formatting
        if (job.address != null ||
            job.city != null ||
            job.state != null ||
            job.zipCode != null) ...[
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Service Address:',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 12,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  buildServiceAddress(),
                  style: const pw.TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
        ],

        pw.Divider(),
      ],
    );
  }

  /// Build the line items table
  pw.Widget _buildLineItems(Estimate estimate) {
    final headers = ['Description', 'Quantity', 'Unit', 'Unit Price', 'Total'];

    final lineItems = estimate.lineItems;

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Line Items',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        lineItems.isEmpty
            ? pw.Text('No line items')
            : pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              children: [
                // Table header
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                  children:
                      headers
                          .map(
                            (header) => pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text(
                                header,
                                style: pw.TextStyle(
                                  fontWeight: pw.FontWeight.bold,
                                ),
                                textAlign:
                                    header == 'Description'
                                        ? pw.TextAlign.left
                                        : pw.TextAlign.right,
                              ),
                            ),
                          )
                          .toList(),
                ),
                // Table data
                ...lineItems.map(
                  (item) => pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(item.description),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          item.quantity.toString(),
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          item.unit,
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '\$${item.unitPrice.toStringAsFixed(2)}',
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
      ],
    );
  }

  /// Build the total section
  pw.Widget _buildTotal(Estimate estimate) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Subtotal: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text('\$${_calculateSubtotal(estimate).toStringAsFixed(2)}'),
            ],
          ),
          // Tax calculation is now done per line item
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Tax: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text('\$${_calculateTaxTotal(estimate).toStringAsFixed(2)}'),
            ],
          ),
          pw.Divider(thickness: 0.5),
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Total Amount: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                '\$${estimate.totalAmount.toStringAsFixed(2)}',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the notes section
  pw.Widget _buildNotes(Estimate estimate) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Notes',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(width: 0.5),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          child: pw.Text(estimate.notes!),
        ),
      ],
    );
  }

  /// Build the footer
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Divider(),
        pw.SizedBox(height: 5),
        pw.Text('Thank you for considering our services!'),
        pw.SizedBox(height: 5),
        pw.Text(
          'Page ${context.pageNumber} of ${context.pagesCount}',
          style: const pw.TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  /// Get the PDF color for the estimate status
  PdfColor _getStatusPdfColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return PdfColors.orange;
      case 'approved':
        return PdfColors.green;
      case 'rejected':
        return PdfColors.red;
      case 'expired':
        return PdfColors.grey;
      default:
        return PdfColors.blue;
    }
  }

  /// Calculate subtotal from line items
  double _calculateSubtotal(Estimate estimate) {
    return estimate.lineItems.fold(
      0.0,
      (sum, item) => sum + (item.quantity * item.unitPrice),
    );
  }

  /// Calculate tax total from line items
  double _calculateTaxTotal(Estimate estimate) {
    return estimate.lineItems.fold(0.0, (sum, item) => sum + item.taxAmount);
  }

  /// Save the PDF to a permanent file and return the file path
  Future<File> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    try {
      // Get the permanent app documents directory instead of temporary
      final directory = await getApplicationDocumentsDirectory();
      final pdfsDir = Directory('${directory.path}/pdfs');

      // Create PDFs directory if it doesn't exist
      if (!await pdfsDir.exists()) {
        await pdfsDir.create(recursive: true);
      }

      // Create a platform-appropriate file path in the PDFs directory
      final filePath = '${pdfsDir.path}${Platform.pathSeparator}$fileName';

      // Create the file and write the PDF bytes
      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      return file;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'savePdfToFile',
          'fileName': fileName,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
