import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/error_handler.dart';

/// Service for generating business reports with offline-first architecture
class ReportService {
  final DataRepository _dataRepository;

  ReportService(this._dataRepository);

  /// Generate a report based on type and filters
  Future<ReportData> generateReport({
    required ReportType reportType,
    required ReportFilters filters,
  }) async {
    switch (reportType) {
      case ReportType.profitAndLoss:
        return await _generateProfitLossReport(filters);
      case ReportType.expenseBreakdown:
        return await _generateExpenseBreakdownReport(filters);
      case ReportType.jobProfitability:
        return await _generateJobProfitabilityReport(filters);
      case ReportType.invoiceAging:
        return await _generateInvoiceAgingReport(filters);
      case ReportType.mileageSummary:
        return await _generateMileageSummaryReport(filters);
      case ReportType.taxSummary:
        return await _generateTaxSummaryReport(filters);
    }
  }

  /// Generate a job-specific report
  Future<JobReportData> generateJobReport({
    required String jobId,
    required ReportFilters filters,
  }) async {
    try {
      // Get job details
      final job = await _dataRepository.getJobById(jobId);
      if (job == null) {
        throw Exception('Job not found');
      }

      // Get job cost summary (includes all financial data)
      final costSummary = await _dataRepository.getJobCostSummary(jobId);

      // Get related data within date range
      final dateRange = filters.effectiveDateRange;
      final expenses = await _dataRepository.getExpensesByJob(jobId);
      final timeLogs = await _dataRepository.getTimeLogsByJob(jobId);
      final invoices = await _dataRepository.getInvoices();
      final payments = await _dataRepository.getPaymentsByJob(jobId);
      final estimates = await _dataRepository.getEstimatesByJobId(jobId);

      // Filter by date range
      final filteredExpenses =
          expenses
              .where(
                (e) =>
                    e.date.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    e.date.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      final filteredTimeLogs =
          timeLogs
              .where(
                (t) =>
                    t.date.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    t.date.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      final filteredPayments =
          payments
              .where(
                (p) =>
                    p.dateReceived.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    p.dateReceived.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      // Convert to map format for report
      final jobDetails = {
        'id': job.id,
        'title': job.title,
        'description': job.description ?? '',
        'status': job.status,
        'customer_name': '', // Will be populated from customer data
        'estimated_price': job.estimatedPrice ?? 0.0,
        'estimated_expenses_budget': job.estimatedExpensesBudget ?? 0.0,
        'address': job.address ?? '',
        'city': job.city ?? '',
        'state': job.state ?? '',
        'zip_code': job.zipCode ?? '',
        'start_date': job.startDate?.toIso8601String(),
        'end_date': job.endDate?.toIso8601String(),
      };

      // Get customer details
      try {
        final customer = await _dataRepository.getCustomerById(job.customerId);
        if (customer != null) {
          jobDetails['customer_name'] = customer.name;
        }
      } catch (e) {
        debugPrint('Error getting customer for job report: $e');
      }

      return JobReportData(
        reportType:
            ReportType.jobProfitability, // Use job profitability as base type
        filters: filters,
        generatedAt: DateTime.now(),
        jobDetails: jobDetails,
        financialSummary: costSummary,
        expenses:
            filteredExpenses
                .map(
                  (e) => {
                    'id': e.id,
                    'amount': e.amount,
                    'description': e.description,
                    'category': e.category,
                    'date': e.date.toIso8601String(),
                    'is_overhead': e.jobId == null,
                  },
                )
                .toList(),
        timeLogs:
            filteredTimeLogs
                .map(
                  (t) => {
                    'id': t.id,
                    'hours': t.hours,
                    'hourly_rate': t.hourlyRate,
                    'labor_cost': t.laborCost,
                    'notes': t.notes ?? '',
                    'date': t.date.toIso8601String(),
                  },
                )
                .toList(),
        invoices:
            invoices
                .where((i) => i.jobId == jobId)
                .map(
                  (i) => {
                    'id': i.id,
                    'total_amount': i.totalAmount,
                    'amount_paid': i.amountPaid,
                    'status': i.status,
                    'issue_date': i.issueDate.toIso8601String(),
                    'due_date': i.dueDate.toIso8601String(),
                  },
                )
                .toList(),
        payments:
            filteredPayments
                .map(
                  (p) => {
                    'id': p.id,
                    'amount_received': p.amountReceived,
                    'date_received': p.dateReceived.toIso8601String(),
                    'payment_method': p.paymentMethod,
                    'notes': p.notes ?? '',
                  },
                )
                .toList(),
        estimates:
            estimates
                .map(
                  (e) => {
                    'id': e.id,
                    'total_amount': e.totalAmount,
                    'status': e.status,
                    'created_at': e.createdAt.toIso8601String(),
                  },
                )
                .toList(),
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'generateJobReport',
          'jobId': jobId,
          'filters': filters.toString(),
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Generate PDF for a report
  Future<Uint8List> generateReportPdf(ReportData reportData) async {
    try {
      final pdf = pw.Document();

      // Create a PDF theme
      final theme = pw.ThemeData.withFont(
        base: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
      );

      switch (reportData.reportType) {
        case ReportType.profitAndLoss:
          final data = reportData as ProfitLossReportData;
          pdf.addPage(await _buildProfitLossPdf(data, theme));
          break;
        case ReportType.expenseBreakdown:
          final data = reportData as ExpenseBreakdownReportData;
          pdf.addPage(await _buildExpenseBreakdownPdf(data, theme));
          break;
        case ReportType.jobProfitability:
          if (reportData is JobReportData) {
            pdf.addPage(await _buildJobReportPdf(reportData, theme));
          } else {
            final data = reportData as JobProfitabilityReportData;
            pdf.addPage(await _buildJobProfitabilityPdf(data, theme));
          }
          break;
        case ReportType.invoiceAging:
          final data = reportData as InvoiceAgingReportData;
          pdf.addPage(await _buildInvoiceAgingPdf(data, theme));
          break;
        case ReportType.mileageSummary:
          final data = reportData as MileageSummaryReportData;
          pdf.addPage(await _buildMileageSummaryPdf(data, theme));
          break;
        case ReportType.taxSummary:
          final data = reportData as TaxSummaryReportData;
          pdf.addPage(await _buildTaxSummaryPdf(data, theme));
          break;
      }

      return pdf.save();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'generateReportPdf',
          'reportType': reportData.reportType.toString(),
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Save PDF to file and return file path
  Future<String> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    try {
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);
      return filePath;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'savePdfToFile',
          'fileName': fileName,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Private methods for generating specific report types
  Future<ProfitLossReportData> _generateProfitLossReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get financial data for the period
      final payments = await _dataRepository.getPaymentsByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      final expenses = await _dataRepository.getExpensesByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      final timeLogs = await _getTimeLogsByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      // Filter by selected jobs if not including all jobs
      List<Payment> filteredPayments = payments;
      List<Expense> filteredExpenses = expenses;
      List<TimeLog> filteredTimeLogs = timeLogs;

      if (!filters.includeAllJobs && filters.selectedJobIds.isNotEmpty) {
        filteredPayments =
            payments
                .where((p) => filters.selectedJobIds.contains(p.jobId))
                .toList();
        filteredExpenses =
            expenses
                .where(
                  (e) =>
                      e.jobId != null &&
                      filters.selectedJobIds.contains(e.jobId),
                )
                .toList();
        filteredTimeLogs =
            timeLogs
                .where((t) => filters.selectedJobIds.contains(t.jobId))
                .toList();
      }

      // Calculate totals
      final totalIncome = filteredPayments.fold(
        0.0,
        (sum, p) => sum + p.amountReceived,
      );
      final totalExpenses = filteredExpenses.fold(
        0.0,
        (sum, e) => sum + e.amount,
      );
      final totalLaborCosts = filteredTimeLogs.fold(
        0.0,
        (sum, t) => sum + t.laborCost,
      );
      final netProfit = totalIncome - totalExpenses - totalLaborCosts;
      final profitMargin =
          totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0.0;

      // Group income by month
      final incomeByMonth = <String, double>{};
      for (final payment in filteredPayments) {
        final monthKey = DateFormat('yyyy-MM').format(payment.dateReceived);
        incomeByMonth[monthKey] =
            (incomeByMonth[monthKey] ?? 0.0) + payment.amountReceived;
      }

      // Group expenses by category
      final expensesByCategory = <String, double>{};
      for (final expense in filteredExpenses) {
        final category = expense.category ?? 'Uncategorized';
        expensesByCategory[category] =
            (expensesByCategory[category] ?? 0.0) + expense.amount;
      }

      // Get top expense categories
      final topExpenseCategories =
          expensesByCategory.entries
              .map((e) => {'category': e.key, 'amount': e.value})
              .toList()
            ..sort(
              (a, b) =>
                  (b['amount'] as double).compareTo(a['amount'] as double),
            );

      return ProfitLossReportData(
        reportType: ReportType.profitAndLoss,
        filters: filters,
        generatedAt: DateTime.now(),
        totalIncome: totalIncome,
        totalExpenses: totalExpenses,
        totalLaborCosts: totalLaborCosts,
        netProfit: netProfit,
        profitMargin: profitMargin,
        incomeByMonth: incomeByMonth,
        expensesByCategory: expensesByCategory,
        topExpenseCategories: topExpenseCategories,
      );
    } catch (e) {
      debugPrint('Error generating profit & loss report: $e');
      throw Exception('Failed to generate profit & loss report: $e');
    }
  }

  Future<ExpenseBreakdownReportData> _generateExpenseBreakdownReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get expenses for the period
      final expenses = await _dataRepository.getExpensesByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      // Filter by selected jobs if not including all jobs
      List<Expense> filteredExpenses = expenses;
      if (!filters.includeAllJobs && filters.selectedJobIds.isNotEmpty) {
        filteredExpenses =
            expenses
                .where(
                  (e) =>
                      e.jobId != null &&
                      filters.selectedJobIds.contains(e.jobId),
                )
                .toList();
      }

      // Separate job expenses from overhead expenses
      final jobExpenses =
          filteredExpenses.where((e) => e.jobId != null).toList();
      final overheadExpenses =
          filteredExpenses.where((e) => e.jobId == null).toList();

      // Group expenses by category
      final expensesByCategory = <String, double>{};
      final expenseDetails = <String, List<Map<String, dynamic>>>{};

      for (final expense in filteredExpenses) {
        final category = expense.category ?? 'Uncategorized';

        // Add to category totals
        expensesByCategory[category] =
            (expensesByCategory[category] ?? 0.0) + expense.amount;

        // Add to category details
        if (!expenseDetails.containsKey(category)) {
          expenseDetails[category] = [];
        }
        expenseDetails[category]!.add({
          'id': expense.id,
          'description': expense.description,
          'amount': expense.amount,
          'date': expense.date.toIso8601String(),
          'is_overhead': expense.jobId == null,
          'job_id': expense.jobId,
        });
      }

      final totalExpenses = filteredExpenses.fold(
        0.0,
        (sum, e) => sum + e.amount,
      );
      final totalOverheadExpenses = overheadExpenses.fold(
        0.0,
        (sum, e) => sum + e.amount,
      );
      final totalJobExpenses = jobExpenses.fold(
        0.0,
        (sum, e) => sum + e.amount,
      );

      return ExpenseBreakdownReportData(
        reportType: ReportType.expenseBreakdown,
        filters: filters,
        generatedAt: DateTime.now(),
        expensesByCategory: expensesByCategory,
        expenseDetails: expenseDetails,
        totalExpenses: totalExpenses,
        totalOverheadExpenses: totalOverheadExpenses,
        totalJobExpenses: totalJobExpenses,
      );
    } catch (e) {
      debugPrint('Error generating expense breakdown report: $e');
      throw Exception('Failed to generate expense breakdown report: $e');
    }
  }

  Future<JobProfitabilityReportData> _generateJobProfitabilityReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get all jobs or filtered jobs
      List<Job> jobs;
      if (filters.includeAllJobs) {
        jobs = await _dataRepository.getJobs();
      } else {
        jobs = [];
        for (final jobId in filters.selectedJobIds) {
          final job = await _dataRepository.getJobById(jobId);
          if (job != null) {
            jobs.add(job);
          }
        }
      }

      final jobProfitability = <Map<String, dynamic>>[];
      double totalRevenue = 0.0;
      double totalCosts = 0.0;

      for (final job in jobs) {
        // Get job cost summary
        final costSummary = await _dataRepository.getJobCostSummary(job.id);

        // Filter payments by date range
        final payments = await _dataRepository.getPaymentsByJob(job.id);
        final filteredPayments =
            payments
                .where(
                  (p) =>
                      p.dateReceived.isAfter(
                        dateRange.startDate.subtract(const Duration(days: 1)),
                      ) &&
                      p.dateReceived.isBefore(
                        dateRange.endDate.add(const Duration(days: 1)),
                      ),
                )
                .toList();

        final jobRevenue = filteredPayments.fold(
          0.0,
          (sum, p) => sum + p.amountReceived,
        );
        final jobCosts =
            costSummary['total_expenses'] + costSummary['total_labor_cost'];
        final jobProfit = jobRevenue - jobCosts;
        final profitMargin =
            jobRevenue > 0 ? (jobProfit / jobRevenue) * 100 : 0.0;

        jobProfitability.add({
          'job_id': job.id,
          'job_title': job.title,
          'revenue': jobRevenue,
          'costs': jobCosts,
          'profit': jobProfit,
          'profit_margin': profitMargin,
          'status': job.status,
        });

        totalRevenue += jobRevenue;
        totalCosts += jobCosts;
      }

      final totalProfit = totalRevenue - totalCosts;
      final averageProfitMargin =
          totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0.0;

      // Sort by profit descending
      jobProfitability.sort(
        (a, b) => (b['profit'] as double).compareTo(a['profit'] as double),
      );

      return JobProfitabilityReportData(
        reportType: ReportType.jobProfitability,
        filters: filters,
        generatedAt: DateTime.now(),
        jobProfitability: jobProfitability,
        totalRevenue: totalRevenue,
        totalCosts: totalCosts,
        totalProfit: totalProfit,
        averageProfitMargin: averageProfitMargin,
      );
    } catch (e) {
      debugPrint('Error generating job profitability report: $e');
      throw Exception('Failed to generate job profitability report: $e');
    }
  }

  Future<InvoiceAgingReportData> _generateInvoiceAgingReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get all invoices
      final allInvoices = await _dataRepository.getInvoices();

      // Filter by selected jobs if not including all jobs
      List<Invoice> invoices = allInvoices;
      if (!filters.includeAllJobs && filters.selectedJobIds.isNotEmpty) {
        invoices =
            allInvoices
                .where((i) => filters.selectedJobIds.contains(i.jobId))
                .toList();
      }

      // Filter by date range (issue date)
      invoices =
          invoices
              .where(
                (i) =>
                    i.issueDate.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    i.issueDate.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      final now = DateTime.now();
      final currentInvoices = <Map<String, dynamic>>[];
      final overdueInvoices = <Map<String, dynamic>>[];
      final agingBuckets = <String, double>{
        '0-30 days': 0.0,
        '31-60 days': 0.0,
        '61-90 days': 0.0,
        '90+ days': 0.0,
      };

      double totalOutstanding = 0.0;
      double totalOverdue = 0.0;

      for (final invoice in invoices) {
        // Skip paid invoices
        if (invoice.status.toLowerCase() == 'paid') continue;

        final outstanding = invoice.totalAmount - invoice.amountPaid;
        if (outstanding <= 0) continue;

        totalOutstanding += outstanding;

        final daysPastDue = now.difference(invoice.dueDate).inDays;
        final isOverdue = daysPastDue > 0;

        final invoiceData = {
          'id': invoice.id,
          'total_amount': invoice.totalAmount,
          'amount_paid': invoice.amountPaid,
          'outstanding': outstanding,
          'issue_date': invoice.issueDate.toIso8601String(),
          'due_date': invoice.dueDate.toIso8601String(),
          'days_past_due': daysPastDue,
          'status': invoice.status,
          'job_id': invoice.jobId,
        };

        if (isOverdue) {
          overdueInvoices.add(invoiceData);
          totalOverdue += outstanding;

          // Categorize by aging bucket
          if (daysPastDue <= 30) {
            agingBuckets['0-30 days'] =
                agingBuckets['0-30 days']! + outstanding;
          } else if (daysPastDue <= 60) {
            agingBuckets['31-60 days'] =
                agingBuckets['31-60 days']! + outstanding;
          } else if (daysPastDue <= 90) {
            agingBuckets['61-90 days'] =
                agingBuckets['61-90 days']! + outstanding;
          } else {
            agingBuckets['90+ days'] = agingBuckets['90+ days']! + outstanding;
          }
        } else {
          currentInvoices.add(invoiceData);
        }
      }

      // Sort by days past due (most overdue first)
      overdueInvoices.sort(
        (a, b) =>
            (b['days_past_due'] as int).compareTo(a['days_past_due'] as int),
      );

      return InvoiceAgingReportData(
        reportType: ReportType.invoiceAging,
        filters: filters,
        generatedAt: DateTime.now(),
        currentInvoices: currentInvoices,
        overdueInvoices: overdueInvoices,
        agingBuckets: agingBuckets,
        totalOutstanding: totalOutstanding,
        totalOverdue: totalOverdue,
      );
    } catch (e) {
      debugPrint('Error generating invoice aging report: $e');
      throw Exception('Failed to generate invoice aging report: $e');
    }
  }

  Future<MileageSummaryReportData> _generateMileageSummaryReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get mileage entries for the period
      final allMileage = await _dataRepository.search<Mileage>(keyword: '');

      // Filter by date range
      final mileageEntries =
          allMileage
              .where(
                (m) =>
                    m.date.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    m.date.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      // Filter by selected jobs if not including all jobs
      List<Mileage> filteredMileage = mileageEntries;
      if (!filters.includeAllJobs && filters.selectedJobIds.isNotEmpty) {
        filteredMileage =
            mileageEntries
                .where((m) => filters.selectedJobIds.contains(m.jobId))
                .toList();
      }

      // Calculate totals
      final totalMiles = filteredMileage.fold(0.0, (sum, m) => sum + m.miles);
      const irsRate = 0.655; // 2023 IRS standard mileage rate
      final totalDeduction = totalMiles * irsRate;

      // Group by job
      final mileageByJob = <String, double>{};
      for (final mileage in filteredMileage) {
        final jobId = mileage.jobId ?? 'No Job';
        mileageByJob[jobId] = (mileageByJob[jobId] ?? 0.0) + mileage.miles;
      }

      // Group by month
      final mileageByMonth = <String, double>{};
      for (final mileage in filteredMileage) {
        final monthKey = DateFormat('yyyy-MM').format(mileage.date);
        mileageByMonth[monthKey] =
            (mileageByMonth[monthKey] ?? 0.0) + mileage.miles;
      }

      // Convert to map format for report
      final mileageData =
          filteredMileage
              .map(
                (m) => {
                  'id': m.id,
                  'miles': m.miles,
                  'description': m.description,
                  'date': m.date.toIso8601String(),
                  'job_id': m.jobId,
                  'deduction': m.miles * irsRate,
                },
              )
              .toList();

      return MileageSummaryReportData(
        reportType: ReportType.mileageSummary,
        filters: filters,
        generatedAt: DateTime.now(),
        mileageEntries: mileageData,
        totalMiles: totalMiles,
        totalDeduction: totalDeduction,
        irsRate: irsRate,
        mileageByJob: mileageByJob,
        mileageByMonth: mileageByMonth,
      );
    } catch (e) {
      debugPrint('Error generating mileage summary report: $e');
      throw Exception('Failed to generate mileage summary report: $e');
    }
  }

  Future<TaxSummaryReportData> _generateTaxSummaryReport(
    ReportFilters filters,
  ) async {
    try {
      final dateRange = filters.effectiveDateRange;

      // Get financial data for the period
      final payments = await _dataRepository.getPaymentsByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      final expenses = await _dataRepository.getExpensesByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      final mileageEntries = await _dataRepository.search<Mileage>(keyword: '');
      final filteredMileage =
          mileageEntries
              .where(
                (m) =>
                    m.date.isAfter(
                      dateRange.startDate.subtract(const Duration(days: 1)),
                    ) &&
                    m.date.isBefore(
                      dateRange.endDate.add(const Duration(days: 1)),
                    ),
              )
              .toList();

      final taxPayments = await _dataRepository.getTaxPaymentsByDateRange(
        dateRange.startDate,
        dateRange.endDate,
      );

      // Filter by selected jobs if not including all jobs
      List<Payment> filteredPayments = payments;
      List<Expense> filteredExpenses = expenses;
      List<Mileage> filteredMileageForJobs = filteredMileage;

      if (!filters.includeAllJobs && filters.selectedJobIds.isNotEmpty) {
        filteredPayments =
            payments
                .where((p) => filters.selectedJobIds.contains(p.jobId))
                .toList();
        filteredExpenses =
            expenses
                .where(
                  (e) =>
                      e.jobId != null &&
                      filters.selectedJobIds.contains(e.jobId),
                )
                .toList();
        filteredMileageForJobs =
            filteredMileage
                .where(
                  (m) =>
                      m.jobId != null &&
                      filters.selectedJobIds.contains(m.jobId),
                )
                .toList();
      }

      // Calculate totals
      final totalIncome = filteredPayments.fold(
        0.0,
        (sum, p) => sum + p.amountReceived,
      );
      final totalExpenses = filteredExpenses.fold(
        0.0,
        (sum, e) => sum + e.amount,
      );
      final totalMileageDeduction = filteredMileageForJobs.fold(
        0.0,
        (sum, m) => sum + (m.miles * 0.655),
      );
      final totalTaxPaid = taxPayments.fold(0.0, (sum, t) => sum + t.amount);

      // Calculate estimated taxes (simplified)
      final netIncome = totalIncome - totalExpenses - totalMileageDeduction;
      final estimatedTaxRate = 0.25; // 25% estimated tax rate
      final estimatedTaxOwed =
          netIncome > 0 ? netIncome * estimatedTaxRate : 0.0;
      final taxBalance = estimatedTaxOwed - totalTaxPaid;

      // Group expenses by Schedule C categories
      final scheduleC = <String, double>{};
      for (final expense in filteredExpenses) {
        final category = expense.category ?? 'Other Business Expenses';
        scheduleC[category] = (scheduleC[category] ?? 0.0) + expense.amount;
      }

      // Add mileage deduction to Schedule C
      if (totalMileageDeduction > 0) {
        scheduleC['Car and Truck Expenses'] =
            (scheduleC['Car and Truck Expenses'] ?? 0.0) +
            totalMileageDeduction;
      }

      // Quarterly breakdown
      final quarterlyData = <String, Map<String, double>>{};
      for (int quarter = 1; quarter <= 4; quarter++) {
        final quarterStart = DateTime(
          dateRange.startDate.year,
          (quarter - 1) * 3 + 1,
          1,
        );
        final quarterEnd = DateTime(
          dateRange.startDate.year,
          quarter * 3 + 1,
          0,
        );

        final quarterPayments =
            filteredPayments
                .where(
                  (p) =>
                      p.dateReceived.isAfter(
                        quarterStart.subtract(const Duration(days: 1)),
                      ) &&
                      p.dateReceived.isBefore(
                        quarterEnd.add(const Duration(days: 1)),
                      ),
                )
                .toList();

        final quarterExpenses =
            filteredExpenses
                .where(
                  (e) =>
                      e.date.isAfter(
                        quarterStart.subtract(const Duration(days: 1)),
                      ) &&
                      e.date.isBefore(quarterEnd.add(const Duration(days: 1))),
                )
                .toList();

        final quarterMileage =
            filteredMileageForJobs
                .where(
                  (m) =>
                      m.date.isAfter(
                        quarterStart.subtract(const Duration(days: 1)),
                      ) &&
                      m.date.isBefore(quarterEnd.add(const Duration(days: 1))),
                )
                .toList();

        final quarterTaxPayments =
            taxPayments
                .where(
                  (t) =>
                      t.date.isAfter(
                        quarterStart.subtract(const Duration(days: 1)),
                      ) &&
                      t.date.isBefore(quarterEnd.add(const Duration(days: 1))),
                )
                .toList();

        final qIncome = quarterPayments.fold(
          0.0,
          (sum, p) => sum + p.amountReceived,
        );
        final qExpenses = quarterExpenses.fold(0.0, (sum, e) => sum + e.amount);
        final qMileageDeduction = quarterMileage.fold(
          0.0,
          (sum, m) => sum + (m.miles * 0.655),
        );
        final qTaxPaid = quarterTaxPayments.fold(
          0.0,
          (sum, t) => sum + t.amount,
        );
        final qNetIncome = qIncome - qExpenses - qMileageDeduction;
        final qEstimatedTax =
            qNetIncome > 0 ? qNetIncome * estimatedTaxRate : 0.0;

        quarterlyData['Q$quarter'] = {
          'income': qIncome,
          'expenses': qExpenses,
          'mileage_deduction': qMileageDeduction,
          'net_income': qNetIncome,
          'estimated_tax': qEstimatedTax,
          'tax_paid': qTaxPaid,
        };
      }

      // Convert tax payments to the expected format
      final taxPaymentsList =
          taxPayments
              .map(
                (t) => {
                  'id': t.id,
                  'date': t.date.toIso8601String(),
                  'amount': t.amount,
                  'tax_period': t.taxPeriod,
                  'payment_method': t.paymentMethod,
                  'confirmation_number': t.confirmationNumber,
                  'notes': t.notes,
                },
              )
              .toList();

      return TaxSummaryReportData(
        reportType: ReportType.taxSummary,
        filters: filters,
        generatedAt: DateTime.now(),
        totalIncome: totalIncome,
        totalDeductions: totalExpenses + totalMileageDeduction,
        netProfit: netIncome,
        estimatedTaxOwed: estimatedTaxOwed,
        taxPaymentsMade: totalTaxPaid,
        taxBalance: taxBalance,
        quarterlyBreakdown: quarterlyData.map(
          (key, value) => MapEntry(key, value['net_income'] ?? 0.0),
        ),
        taxPayments: taxPaymentsList,
      );
    } catch (e) {
      debugPrint('Error generating tax summary report: $e');
      throw Exception('Failed to generate tax summary report: $e');
    }
  }

  // PDF building methods will be added in subsequent chunks
  Future<pw.Page> _buildProfitLossPdf(
    ProfitLossReportData data,
    pw.ThemeData theme,
  ) async {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      theme: theme,
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Container(
              padding: const pw.EdgeInsets.only(bottom: 20),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(width: 2, color: PdfColors.blue),
                ),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'Profit & Loss Report',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue,
                    ),
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'Generated: ${DateFormat('MM/dd/yyyy').format(data.generatedAt)}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                      pw.Text(
                        'Period: ${data.filters.effectiveDateRange.description}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Summary Section
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
              ),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Total Income',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green,
                          ),
                        ),
                        pw.Text(
                          '\$${data.totalIncome.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Total Expenses',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.red,
                          ),
                        ),
                        pw.Text(
                          '\$${data.totalExpenses.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Labor Costs',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.orange,
                          ),
                        ),
                        pw.Text(
                          '\$${data.totalLaborCosts.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Net Profit',
                          style: pw.TextStyle(
                            fontSize: 14,
                            fontWeight: pw.FontWeight.bold,
                            color:
                                data.netProfit >= 0
                                    ? PdfColors.green
                                    : PdfColors.red,
                          ),
                        ),
                        pw.Text(
                          '\$${data.netProfit.toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                            color:
                                data.netProfit >= 0
                                    ? PdfColors.green
                                    : PdfColors.red,
                          ),
                        ),
                        pw.Text(
                          '${data.profitMargin.toStringAsFixed(1)}% margin',
                          style: const pw.TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Top Expense Categories
            if (data.topExpenseCategories.isNotEmpty) ...[
              pw.Text(
                'Top Expense Categories',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Container(
                padding: const pw.EdgeInsets.all(12),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: const pw.BorderRadius.all(
                    pw.Radius.circular(4),
                  ),
                ),
                child: pw.Column(
                  children:
                      data.topExpenseCategories.take(5).map((category) {
                        final amount = category['amount'] as double;
                        final percentage =
                            data.totalExpenses > 0
                                ? (amount / data.totalExpenses) * 100
                                : 0.0;
                        return pw.Container(
                          padding: const pw.EdgeInsets.symmetric(vertical: 4),
                          child: pw.Row(
                            mainAxisAlignment:
                                pw.MainAxisAlignment.spaceBetween,
                            children: [
                              pw.Text(category['category'] as String),
                              pw.Row(
                                children: [
                                  pw.Text('\$${amount.toStringAsFixed(2)}'),
                                  pw.SizedBox(width: 10),
                                  pw.Text(
                                    '(${percentage.toStringAsFixed(1)}%)',
                                    style: pw.TextStyle(
                                      fontSize: 10,
                                      color: PdfColors.grey600,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Future<pw.Page> _buildExpenseBreakdownPdf(
    ExpenseBreakdownReportData data,
    pw.ThemeData theme,
  ) async {
    throw UnimplementedError('PDF generation not yet implemented');
  }

  Future<pw.Page> _buildJobProfitabilityPdf(
    JobProfitabilityReportData data,
    pw.ThemeData theme,
  ) async {
    throw UnimplementedError('PDF generation not yet implemented');
  }

  Future<pw.Page> _buildJobReportPdf(
    JobReportData data,
    pw.ThemeData theme,
  ) async {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      theme: theme,
      build: (pw.Context context) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Container(
              padding: const pw.EdgeInsets.only(bottom: 20),
              decoration: const pw.BoxDecoration(
                border: pw.Border(
                  bottom: pw.BorderSide(width: 2, color: PdfColors.blue),
                ),
              ),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Job Report',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        data.jobDetails['title'] ?? 'Unknown Job',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'Generated: ${DateFormat('MM/dd/yyyy').format(data.generatedAt)}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                      pw.Text(
                        'Period: ${data.filters.effectiveDateRange.description}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Job Details Section
            pw.Text(
              'Job Information',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue,
              ),
            ),
            pw.SizedBox(height: 10),
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: pw.Text(
                          'Customer: ${data.jobDetails['customer_name'] ?? 'Unknown'}',
                        ),
                      ),
                      pw.Expanded(
                        child: pw.Text(
                          'Status: ${data.jobDetails['status'] ?? 'Unknown'}',
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 8),
                  if (data.jobDetails['address'] != null &&
                      (data.jobDetails['address'] as String).isNotEmpty)
                    pw.Text('Service Address: ${data.jobDetails['address']}'),
                  if (data.jobDetails['description'] != null &&
                      (data.jobDetails['description'] as String)
                          .isNotEmpty) ...[
                    pw.SizedBox(height: 8),
                    pw.Text('Description: ${data.jobDetails['description']}'),
                  ],
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Financial Summary Section
            pw.Text(
              'Financial Summary',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue,
              ),
            ),
            pw.SizedBox(height: 10),
            pw.Container(
              padding: const pw.EdgeInsets.all(12),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
              ),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Total Income: \$${(data.financialSummary['total_income'] ?? 0.0).toStringAsFixed(2)}',
                        ),
                        pw.SizedBox(height: 4),
                        pw.Text(
                          'Total Expenses: \$${(data.financialSummary['total_expenses'] ?? 0.0).toStringAsFixed(2)}',
                        ),
                        pw.SizedBox(height: 4),
                        pw.Text(
                          'Labor Costs: \$${(data.financialSummary['total_labor_cost'] ?? 0.0).toStringAsFixed(2)}',
                        ),
                      ],
                    ),
                  ),
                  pw.Expanded(
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'Net Profit: \$${(data.financialSummary['profit_loss'] ?? 0.0).toStringAsFixed(2)}',
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold,
                            color:
                                (data.financialSummary['profit_loss'] ?? 0.0) >=
                                        0
                                    ? PdfColors.green
                                    : PdfColors.red,
                          ),
                        ),
                        pw.SizedBox(height: 4),
                        pw.Text(
                          'Estimated Budget: \$${(data.jobDetails['estimated_price'] ?? 0.0).toStringAsFixed(2)}',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // Summary counts
            pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.blue50,
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Text(
                          '${data.expenses.length}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.blue,
                          ),
                        ),
                        pw.Text(
                          'Expenses',
                          style: const pw.TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.green50,
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Text(
                          '${data.timeLogs.length}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.green,
                          ),
                        ),
                        pw.Text(
                          'Time Logs',
                          style: const pw.TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.orange50,
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Text(
                          '${data.invoices.length}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.orange,
                          ),
                        ),
                        pw.Text(
                          'Invoices',
                          style: const pw.TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.purple50,
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      children: [
                        pw.Text(
                          '${data.payments.length}',
                          style: pw.TextStyle(
                            fontSize: 18,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.purple,
                          ),
                        ),
                        pw.Text(
                          'Payments',
                          style: const pw.TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Future<pw.Page> _buildInvoiceAgingPdf(
    InvoiceAgingReportData data,
    pw.ThemeData theme,
  ) async {
    throw UnimplementedError('PDF generation not yet implemented');
  }

  Future<pw.Page> _buildMileageSummaryPdf(
    MileageSummaryReportData data,
    pw.ThemeData theme,
  ) async {
    throw UnimplementedError('PDF generation not yet implemented');
  }

  Future<pw.Page> _buildTaxSummaryPdf(
    TaxSummaryReportData data,
    pw.ThemeData theme,
  ) async {
    throw UnimplementedError('PDF generation not yet implemented');
  }

  // Helper method to get time logs by date range
  Future<List<TimeLog>> _getTimeLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final allTimeLogs = await _dataRepository.getTimeLogs();
    return allTimeLogs.where((timeLog) {
      return timeLog.date.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          timeLog.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }
}
