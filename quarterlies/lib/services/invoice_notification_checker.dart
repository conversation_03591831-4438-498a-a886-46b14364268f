import 'package:flutter/material.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/notification_service.dart';

/// Service to check for invoices that are coming due and send notifications
class InvoiceNotificationChecker {
  final SupabaseService _supabaseService = SupabaseService();
  final NotificationService _notificationService = NotificationService();

  /// Check for invoices that are coming due and send notifications if needed
  Future<void> checkForDueInvoices() async {
    try {
      // Get user settings
      final userSettings = await _supabaseService.getUserSettings();

      // If notifications are disabled, don't proceed
      if (!userSettings.enableDueDateNotifications) {
        debugPrint('Due date notifications are disabled in user settings');
        return;
      }

      debugPrint(
        'Checking for invoices due in ${userSettings.dueDateNotificationDays} days...',
      );

      // Get all open invoices
      final invoices = await _supabaseService.getInvoices();
      final openInvoices =
          invoices
              .where(
                (invoice) =>
                    invoice.status.toLowerCase() == 'open' ||
                    invoice.status.toLowerCase() == 'partially paid',
              )
              .toList();

      debugPrint('Found ${openInvoices.length} open invoices');

      // Get the notification threshold date
      final now = DateTime.now();
      final notificationThreshold = now.add(
        Duration(days: userSettings.dueDateNotificationDays),
      );

      // Find invoices that are coming due within the notification threshold
      final comingDueInvoices =
          openInvoices.where((invoice) {
            // Check if the invoice due date is within the notification threshold
            final dueDate = invoice.dueDate;
            final isWithinThreshold =
                dueDate.isAfter(now) &&
                (dueDate.isBefore(notificationThreshold) ||
                    dueDate.isAtSameMomentAs(notificationThreshold));

            // Calculate days until due
            final daysUntilDue = dueDate.difference(now).inDays;

            if (isWithinThreshold) {
              debugPrint(
                'Invoice ${invoice.id != null ? (invoice.id!.length >= 8 ? invoice.id!.substring(0, 8) : invoice.id!) : 'Unknown'} is due in $daysUntilDue days',
              );
            }

            return isWithinThreshold;
          }).toList();

      debugPrint('Found ${comingDueInvoices.length} invoices coming due soon');

      // Trigger notifications for all invoices coming due
      if (comingDueInvoices.isNotEmpty) {
        await _notificationService.checkForDueInvoices();
      }
    } catch (e) {
      debugPrint('Error checking for due invoices: $e');
    }
  }
}
