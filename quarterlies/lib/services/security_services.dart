import 'package:html_unescape/html_unescape.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'dart:io';
import 'package:crypto/crypto.dart';

// Validation service for financial data inputs
class ValidationService {
  // Validate currency amount
  bool isValidAmount(String amount) {
    return RegExp(r'^\$?\d+(\.\d{1,2})?$').hasMatch(amount);
  }

  // Validate date format
  bool isValidDate(String date) {
    try {
      DateTime.parse(date);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Validate date range
  bool isValidDateRange(String startDate, String endDate) {
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      return end.isAfter(start);
    } catch (e) {
      return false;
    }
  }

  // Validate payment information
  bool isValidCardNumber(String cardNumber) {
    // Remove spaces and dashes
    cardNumber = cardNumber.replaceAll(RegExp(r'[\s-]'), '');

    // Check if only digits
    if (!RegExp(r'^\d+$').hasMatch(cardNumber)) return false;

    // Luhn algorithm for card validation
    int sum = 0;
    bool alternate = false;
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int n = int.parse(cardNumber[i]);
      if (alternate) {
        n *= 2;
        if (n > 9) n = n - 9;
      }
      sum += n;
      alternate = !alternate;
    }
    return sum % 10 == 0;
  }

  // Validate CVV
  bool isValidCVV(String cvv) {
    return RegExp(r'^\d{3,4}$').hasMatch(cvv);
  }

  // Validate expiry date
  bool isValidExpiryDate(String expiryDate) {
    // Format MM/YY
    if (!RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$').hasMatch(expiryDate)) {
      return false;
    }

    final parts = expiryDate.split('/');
    final month = int.parse(parts[0]);
    final year = int.parse('20${parts[1]}');

    final now = DateTime.now();
    final expiryDateTime = DateTime(year, month + 1, 0);

    return expiryDateTime.isAfter(now);
  }
}

// Sanitization service for user inputs
class SanitizationService {
  final HtmlUnescape _htmlUnescape = HtmlUnescape();

  // Sanitize text input to prevent XSS
  String sanitizeInput(String input) {
    // Remove HTML tags
    String sanitized = input.replaceAll(RegExp(r'<[^>]*>'), '');

    // Decode HTML entities
    sanitized = _htmlUnescape.convert(sanitized);

    // Remove potentially dangerous characters
    sanitized = sanitized.replaceAll(RegExp(r'[\r\n\t]'), ' ');

    return sanitized.trim();
  }

  // Sanitize SQL input to prevent SQL injection
  String sanitizeSqlInput(String input) {
    // Escape single quotes
    String sanitized = input.replaceAll("'", "''");

    // Remove SQL comments
    sanitized = sanitized.replaceAll(RegExp(r'--.*'), '');
    sanitized = sanitized.replaceAll(RegExp(r'/\*.*?\*/', dotAll: true), '');

    return sanitized.trim();
  }

  // Sanitize filename to prevent path traversal
  String sanitizeFilename(String filename) {
    // Remove path traversal characters
    String sanitized = filename.replaceAll(RegExp(r'[/\\]'), '');

    // Remove control characters
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');

    return sanitized.trim();
  }
}

// Network security service for certificate pinning
class NetworkSecurityService {
  late Dio _dio;

  // Certificate fingerprints for trusted domains
  final Map<String, List<String>> _certificateFingerprints = {
    'supabase.co': [
      'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Replace with actual certificate hash
      'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=', // Backup certificate hash
    ],
    'api.mapbox.com': [
      'sha256/DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD=', // Replace with actual certificate hash
    ],
  };

  NetworkSecurityService() {
    _dio = Dio();
    _configureCertificatePinning();
  }

  void _configureCertificatePinning() {
    // Configure Dio with certificate pinning using an interceptor
    (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      client.badCertificateCallback = (
        X509Certificate cert,
        String host,
        int port,
      ) {
        // Get the certificate fingerprint
        final fingerprint = sha256.convert(cert.der).toString();

        // Check if the host is in our trusted domains
        if (_certificateFingerprints.containsKey(host)) {
          // Check if the fingerprint matches any of our trusted fingerprints for this host
          return _certificateFingerprints[host]!.contains(
            'sha256/$fingerprint',
          );
        }

        // For hosts not in our list, reject the certificate
        return false;
      };
      return client;
    };
  }

  // Use this Dio instance for all network requests
  Dio get client => _dio;
}

// Audit logging service
class AuditService {
  final String _baseUrl;
  final Dio _dio;

  AuditService(this._baseUrl, this._dio);

  // Log sensitive data access
  Future<void> logDataAccess({
    required String userId,
    required String tableName,
    required String recordId,
    required String action,
  }) async {
    try {
      await _dio.post(
        '$_baseUrl/rest/v1/audit_logs',
        data: {
          'user_id': userId,
          'action_type': action,
          'table_name': tableName,
          'record_id': recordId,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      // Log locally if server logging fails
      // Silently handle audit logging failures
      // In a production environment, this should use a proper logging system
    }
  }

  // Get user's audit history
  Future<List<Map<String, dynamic>>> getUserAuditHistory(String userId) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/rest/v1/audit_logs',
        queryParameters: {'user_id': 'eq.$userId', 'order': 'created_at.desc'},
      );

      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      rethrow;
    }
  }
}
