import 'package:flutter/foundation.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/services/cache_manager.dart';
import 'package:quarterlies/services/connectivity_service.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/signature_storage_service.dart';
import 'package:quarterlies/services/sync_manager.dart'; // Import SyncManager
import 'package:quarterlies/utils/error_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

/// DataRepository acts as a facade over both local and remote data sources,
/// providing a unified API for data access with offline support.
///
/// This service decides whether to use local database or Supabase based on
/// network connectivity and handles the appropriate data flow.
class DataRepository {
  // Singleton pattern
  static final DataRepository _instance = DataRepository._internal();
  factory DataRepository({LoadingStateProvider? loadingStateProvider}) {
    if (loadingStateProvider != null) {
      _instance._loadingStateProvider = loadingStateProvider;
    }
    return _instance;
  }
  DataRepository._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final ConnectivityService _connectivityService = ConnectivityService();
  final SyncManager _syncManager = SyncManager(); // Use SyncManager
  final CacheManager _cacheManager = CacheManager();
  final SupabaseClient _client = Supabase.instance.client;

  LoadingStateProvider? _loadingStateProvider;

  bool _isInitialized = false;

  // Initialize the repository
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize services
    _connectivityService.initialize();
    await _cacheManager.initialize();
    _syncManager.initialize(); // Initialize SyncManager

    _isInitialized = true;
  }

  // Check if we're online
  Future<bool> isOnline() async {
    return true; // Simulate always online
  }

  // Force a sync operation
  Future<void> syncData() async {
    return _syncManager.syncData(); // Call SyncManager's syncData
  }

  // Get sync status stream
  Stream<SyncStatus> get syncStatus =>
      _syncManager.syncStatusStream; // Get from SyncManager

  // Get connection status stream
  Stream<bool> get connectionStatus async* {
    yield true; // Simulate always online
  }

  // ==================== SEARCH FUNCTIONALITY ====================

  /// Generic search method that works with any entity type
  ///
  /// This method searches through cached data when offline, making it resilient
  /// to connectivity issues. It supports both keyword search and filtering.
  ///
  /// Parameters:
  /// - keyword: The search term to look for across relevant fields
  /// - filters: Optional map of filter criteria (e.g., {'status': 'unpaid'})
  Future<List<T>> search<T>({
    required String keyword,
    Map<String, dynamic>? filters,
  }) async {
    // Normalize keyword for case-insensitive search
    final normalizedKeyword = keyword.toLowerCase().trim();

    // Handle different entity types
    switch (T) {
      case Customer _:
        final customers = await _localDatabaseService.getCustomers();
        return _filterCustomers(customers, normalizedKeyword, filters)
            as List<T>;

      case Job _:
        final jobs = await _localDatabaseService.getJobs();
        return _filterJobs(jobs, normalizedKeyword, filters) as List<T>;

      case Invoice _:
        final invoices = await _localDatabaseService.getInvoices();
        return _filterInvoices(invoices, normalizedKeyword, filters) as List<T>;

      case Expense _:
        // Handle both regular expenses and mileage (which is a subclass of Expense)
        if (T == Mileage) {
          // Get all mileage records directly
          final mileages = await _localDatabaseService.getMileages();
          return _filterMileages(mileages, normalizedKeyword, filters)
              as List<T>;
        } else {
          final expenses = await _localDatabaseService.getExpenses();
          return _filterExpenses(expenses, normalizedKeyword, filters)
              as List<T>;
        }

      case TimeLog _:
        final timeLogs = await _localDatabaseService.getTimeLogs();
        return _filterTimeLogs(timeLogs, normalizedKeyword, filters) as List<T>;

      case TaxPayment _:
        final taxPayments = await _localDatabaseService.getTaxPayments();
        return _filterTaxPayments(taxPayments, normalizedKeyword, filters)
            as List<T>;

      case Estimate _:
        final estimates = await _localDatabaseService.getEstimates();
        return _filterEstimates(estimates, normalizedKeyword, filters)
            as List<T>;

      case Contract _:
        final contracts = await _localDatabaseService.getContracts();
        return _filterContracts(contracts, normalizedKeyword, filters)
            as List<T>;

      case DocumentSigningRequest _:
        final requests =
            await _localDatabaseService.getDocumentSigningRequests();
        return _filterDocumentSigningRequests(
              requests,
              normalizedKeyword,
              filters,
            )
            as List<T>;

      case SignedDocument _:
        final documents = await _localDatabaseService.getSignedDocuments();
        return _filterSignedDocuments(documents, normalizedKeyword, filters)
            as List<T>;

      default:
        throw UnimplementedError('Search not implemented for type $T');
    }
  }

  // Filter customers based on keyword and filters
  List<Customer> _filterCustomers(
    List<Customer> customers,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return customers.where((customer) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          customer.name.toLowerCase().contains(keyword) ||
          (customer.email?.toLowerCase().contains(keyword) ?? false) ||
          (customer.phone?.toLowerCase().contains(keyword) ?? false) ||
          (customer.notes?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        // Example filter implementation
        if (filters.containsKey('status')) {
          // No status field in Customer, but showing as example
          return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter jobs based on keyword and filters
  List<Job> _filterJobs(
    List<Job> jobs,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return jobs.where((job) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          job.title.toLowerCase().contains(keyword) ||
          (job.description?.toLowerCase().contains(keyword) ?? false) ||
          (job.address?.toLowerCase().contains(keyword) ?? false) ||
          (job.city?.toLowerCase().contains(keyword) ?? false) ||
          (job.state?.toLowerCase().contains(keyword) ?? false) ||
          (job.zipCode?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('status')) {
          final status = filters['status'];
          if (job.status != status) return false;
        }

        if (filters.containsKey('customer_id')) {
          final customerId = filters['customer_id'];
          if (job.customerId != customerId) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter invoices based on keyword and filters
  List<Invoice> _filterInvoices(
    List<Invoice> invoices,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return invoices.where((invoice) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          (invoice.id != null && invoice.id!.toLowerCase().contains(keyword)) ||
          invoice.totalAmount.toString().contains(keyword) ||
          (invoice.notes?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('status')) {
          final status = filters['status'];
          if (invoice.status != status) return false;
        }

        if (filters.containsKey('paid')) {
          final paid = filters['paid'];
          final isPaid = invoice.status == 'paid';
          if (paid != isPaid) return false;
        }

        if (filters.containsKey('customer_id')) {
          final customerId = filters['customer_id'];
          if (invoice.customerId != customerId) return false;
        }

        if (filters.containsKey('job_id')) {
          final jobId = filters['job_id'];
          if (invoice.jobId != jobId) return false;
        }

        if (filters.containsKey('date_range')) {
          final dateRange = filters['date_range'] as Map<String, DateTime>;
          final startDate = dateRange['start'];
          final endDate = dateRange['end'];

          if (startDate != null && invoice.issueDate.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && invoice.issueDate.isAfter(endDate)) {
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  // Filter expenses based on keyword and filters
  List<Expense> _filterExpenses(
    List<Expense> expenses,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return expenses.where((expense) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          expense.description.toLowerCase().contains(keyword) ||
          expense.amount.toString().contains(keyword) ||
          (expense.category?.toLowerCase().contains(keyword) ?? false) ||
          (expense.voiceNoteUrl?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('category')) {
          final category = filters['category'];
          if (expense.category != category) return false;
        }

        if (filters.containsKey('date_range')) {
          final dateRange = filters['date_range'] as Map<String, DateTime>;
          final startDate = dateRange['start'];
          final endDate = dateRange['end'];

          if (startDate != null && expense.date.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && expense.date.isAfter(endDate)) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter time logs based on keyword and filters
  List<TimeLog> _filterTimeLogs(
    List<TimeLog> timeLogs,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return timeLogs.where((timeLog) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          (timeLog.notes?.toLowerCase().contains(keyword) ?? false) ||
          timeLog.hours.toString().contains(keyword);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('job_id')) {
          final jobId = filters['job_id'];
          if (timeLog.jobId != jobId) return false;
        }

        if (filters.containsKey('date_range')) {
          final dateRange = filters['date_range'] as Map<String, DateTime>;
          final startDate = dateRange['start'];
          final endDate = dateRange['end'];

          if (startDate != null && timeLog.date.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && timeLog.date.isAfter(endDate)) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter mileages based on keyword and filters
  List<Mileage> _filterMileages(
    List<Mileage> mileages,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return mileages.where((mileage) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          mileage.description.toLowerCase().contains(keyword) ||
          mileage.miles.toString().contains(keyword) ||
          mileage.startLocation.toLowerCase().contains(keyword) ||
          mileage.endLocation.toLowerCase().contains(keyword);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('job_id')) {
          final jobId = filters['job_id'];
          if (mileage.jobId != jobId) return false;
        }

        if (filters.containsKey('date_range')) {
          final dateRange = filters['date_range'] as Map<String, DateTime>;
          final startDate = dateRange['start'];
          final endDate = dateRange['end'];

          if (startDate != null && mileage.date.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && mileage.date.isAfter(endDate)) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter tax payments based on keyword and filters
  List<TaxPayment> _filterTaxPayments(
    List<TaxPayment> taxPayments,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return taxPayments.where((taxPayment) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          taxPayment.taxPeriod.toLowerCase().contains(keyword) ||
          taxPayment.amount.toString().contains(keyword) ||
          (taxPayment.paymentMethod?.toLowerCase().contains(keyword) ??
              false) ||
          (taxPayment.confirmationNumber?.toLowerCase().contains(keyword) ??
              false) ||
          (taxPayment.notes?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('date_range')) {
          final dateRange = filters['date_range'] as Map<String, DateTime>;
          final startDate = dateRange['start'];
          final endDate = dateRange['end'];

          if (startDate != null && taxPayment.date.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && taxPayment.date.isAfter(endDate)) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter estimates based on keyword and filters
  List<Estimate> _filterEstimates(
    List<Estimate> estimates,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return estimates.where((estimate) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          estimate.id.toLowerCase().contains(keyword) ||
          estimate.totalAmount.toString().contains(keyword) ||
          (estimate.notes?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('status')) {
          if (estimate.status.toLowerCase() !=
              filters['status'].toString().toLowerCase()) {
            return false;
          }
        }
        if (filters.containsKey('job_id')) {
          if (estimate.jobId != filters['job_id']) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter contracts based on keyword and filters
  List<Contract> _filterContracts(
    List<Contract> contracts,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return contracts.where((contract) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          contract.id.toLowerCase().contains(keyword) ||
          contract.totalAmount.toString().contains(keyword) ||
          (contract.notes?.toLowerCase().contains(keyword) ?? false);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('status')) {
          if (contract.status.toLowerCase() !=
              filters['status'].toString().toLowerCase()) {
            return false;
          }
        }
        if (filters.containsKey('job_id')) {
          if (contract.jobId != filters['job_id']) return false;
        }
      }

      return true;
    }).toList();
  }

  // Filter document signing requests based on keyword and filters
  List<DocumentSigningRequest> _filterDocumentSigningRequests(
    List<DocumentSigningRequest> requests,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return requests.where((request) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          request.documentType.toLowerCase().contains(keyword) ||
          request.customerEmail.toLowerCase().contains(keyword) ||
          request.customerName.toLowerCase().contains(keyword);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('status')) {
          if (request.status.toLowerCase() !=
              filters['status'].toString().toLowerCase()) {
            return false;
          }
        }
        if (filters.containsKey('document_type')) {
          if (request.documentType.toLowerCase() !=
              filters['document_type'].toString().toLowerCase()) {
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  // Filter signed documents based on keyword and filters
  List<SignedDocument> _filterSignedDocuments(
    List<SignedDocument> documents,
    String keyword,
    Map<String, dynamic>? filters,
  ) {
    return documents.where((document) {
      // Keyword search
      final matchesKeyword =
          keyword.isEmpty ||
          document.documentType.toLowerCase().contains(keyword) ||
          document.customerName.toLowerCase().contains(keyword) ||
          document.customerEmail.toLowerCase().contains(keyword);

      if (!matchesKeyword) return false;

      // Apply filters if any
      if (filters != null && filters.isNotEmpty) {
        if (filters.containsKey('document_type')) {
          if (document.documentType.toLowerCase() !=
              filters['document_type'].toString().toLowerCase()) {
            return false;
          }
        }
        if (filters.containsKey('customer_email')) {
          if (document.customerEmail.toLowerCase() !=
              filters['customer_email'].toString().toLowerCase()) {
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  // ==================== CUSTOMER OPERATIONS ====================

  Future<void> addCustomer(Customer customer) async {
    // Always save to local database first
    await _localDatabaseService.insertCustomer(customer);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addCustomer(customer);
        await _localDatabaseService.updateCustomerSyncStatus(
          customer.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync customer: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  Future<List<Customer>> getCustomers() async {
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithLoading(
        'getCustomers',
        () async {
          if (await isOnline()) {
            try {
              // If online, get from Supabase and update local cache
              final customers = await _supabaseService.getCustomers();

              // Update local database
              for (var customer in customers) {
                await _localDatabaseService.insertCustomer(
                  customer,
                  syncStatus: 'synced',
                );
              }

              return customers;
            } catch (e) {
              final appError = AppError.fromException(
                e,
                context: {
                  'operation': 'getCustomers',
                  'service': 'DataRepository',
                },
              );
              ErrorHandler.logError(appError);
              // Fall back to local database
            }
          }

          // Get from local database
          return await _localDatabaseService.getCustomers();
        },
        message: 'Loading customers...',
        errorMessage: 'Failed to load customers',
      );
    } else {
      // Fallback to original implementation
      if (await isOnline()) {
        try {
          // If online, get from Supabase and update local cache
          final customers = await _supabaseService.getCustomers();

          // Update local database
          for (var customer in customers) {
            await _localDatabaseService.insertCustomer(
              customer,
              syncStatus: 'synced',
            );
          }

          return customers;
        } catch (e) {
          final appError = AppError.fromException(
            e,
            context: {'operation': 'getCustomers', 'service': 'DataRepository'},
          );
          ErrorHandler.logError(appError);
          // Fall back to local database
        }
      }

      // Get from local database
      return await _localDatabaseService.getCustomers();
    }
  }

  /// Get customers with pagination support
  Future<List<Customer>> getCustomersPaginated(
    int page,
    int pageSize, [
    String? searchQuery,
  ]) async {
    final offset = page * pageSize;

    // If search query is provided, use search functionality
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final allCustomers = await search<Customer>(keyword: searchQuery);

      // Apply pagination manually to the search results
      final startIndex = offset;
      final endIndex =
          offset + pageSize > allCustomers.length
              ? allCustomers.length
              : offset + pageSize;

      if (startIndex >= allCustomers.length) {
        return [];
      }

      return allCustomers.sublist(startIndex, endIndex);
    }

    // Regular pagination without search
    if (await isOnline()) {
      try {
        // If online, get from Supabase with pagination
        final customers = await _supabaseService.getCustomersPaginated(
          offset,
          pageSize,
        );

        // Update local database
        for (var customer in customers) {
          await _localDatabaseService.insertCustomer(
            customer,
            syncStatus: 'synced',
          );

          // Record access in cache manager
          _cacheManager.recordCustomerAccess(customer.id);
        }

        return customers;
      } catch (e) {
        debugPrint(
          'Failed to get paginated customers from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database with pagination
    return await _localDatabaseService.getCustomersPaginated(offset, pageSize);
  }

  /// Get total number of customers
  Future<int> getCustomersCount([String? searchQuery]) async {
    // If search query is provided, use search functionality
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final allCustomers = await search<Customer>(keyword: searchQuery);
      return allCustomers.length;
    }

    // Regular count without search
    if (await isOnline()) {
      try {
        return await _supabaseService.getCustomersCount();
      } catch (e) {
        debugPrint(
          'Failed to get customers count from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    return await _localDatabaseService.getCustomersCount();
  }

  /// Get total number of jobs
  Future<int> getJobsCount() async {
    if (await isOnline()) {
      try {
        // Use existing getJobs method and count locally for now
        final jobs = await _supabaseService.getJobs();
        return jobs.length;
      } catch (e) {
        debugPrint('Failed to get jobs count from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Count from local database
    final jobs = await _localDatabaseService.getJobs();
    return jobs.length;
  }

  // ==================== PAGINATION METHODS ====================

  /// Get jobs with pagination support
  Future<List<Job>> getJobsPaginated(
    int page,
    int pageSize, [
    String? searchQuery,
  ]) async {
    final offset = page * pageSize;

    // If search query is provided, use search functionality
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final allJobs = await search<Job>(keyword: searchQuery);

      // Apply pagination manually to the search results
      final startIndex = offset;
      final endIndex =
          offset + pageSize > allJobs.length
              ? allJobs.length
              : offset + pageSize;

      if (startIndex >= allJobs.length) {
        return [];
      }

      return allJobs.sublist(startIndex, endIndex);
    }

    // Regular pagination without search
    if (await isOnline()) {
      try {
        // If online, get from Supabase with pagination
        final jobs = await _supabaseService.getJobsPaginated(offset, pageSize);

        // Update local database
        for (var job in jobs) {
          await _localDatabaseService.insertJob(job, syncStatus: 'synced');
          _cacheManager.recordJobAccess(job.id);
        }

        return jobs;
      } catch (e) {
        debugPrint(
          'Failed to get paginated jobs from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database with pagination
    return await _localDatabaseService.getJobsPaginated(offset, pageSize);
  }

  /// Get invoices with pagination support
  Future<List<Invoice>> getInvoicesPaginated(
    int page,
    int pageSize, [
    String? searchQuery,
  ]) async {
    final offset = page * pageSize;

    // If search query is provided, use search functionality
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final allInvoices = await search<Invoice>(keyword: searchQuery);

      // Apply pagination manually to the search results
      final startIndex = offset;
      final endIndex =
          offset + pageSize > allInvoices.length
              ? allInvoices.length
              : offset + pageSize;

      if (startIndex >= allInvoices.length) {
        return [];
      }

      return allInvoices.sublist(startIndex, endIndex);
    }

    // Regular pagination without search
    if (await isOnline()) {
      try {
        // If online, get from Supabase with pagination
        final invoices = await _supabaseService.getInvoicesPaginated(
          offset,
          pageSize,
        );

        // Update local database
        for (var invoice in invoices) {
          await _localDatabaseService.insertInvoice(
            invoice,
            syncStatus: 'synced',
          );
          _cacheManager.recordInvoiceAccess(invoice.id!);
        }

        return invoices;
      } catch (e) {
        debugPrint(
          'Failed to get paginated invoices from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database with pagination
    return await _localDatabaseService.getInvoicesPaginated(offset, pageSize);
  }

  /// Get expenses with pagination support
  Future<List<Expense>> getExpensesPaginated(
    int page,
    int pageSize, [
    String? searchQuery,
  ]) async {
    final offset = page * pageSize;

    // If search query is provided, use search functionality
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final allExpenses = await search<Expense>(keyword: searchQuery);

      // Apply pagination manually to the search results
      final startIndex = offset;
      final endIndex =
          offset + pageSize > allExpenses.length
              ? allExpenses.length
              : offset + pageSize;

      if (startIndex >= allExpenses.length) {
        return [];
      }

      return allExpenses.sublist(startIndex, endIndex);
    }

    // Regular pagination without search
    if (await isOnline()) {
      try {
        // If online, get from Supabase with pagination
        final expenses = await _supabaseService.getExpensesPaginated(
          offset,
          pageSize,
        );

        // Update local database
        for (var expense in expenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return expenses;
      } catch (e) {
        debugPrint(
          'Failed to get paginated expenses from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database with pagination
    return await _localDatabaseService.getExpensesPaginated(offset, pageSize);
  }

  Future<Customer?> getCustomerById(String id) async {
    // Record access in cache manager
    _cacheManager.recordCustomerAccess(id);

    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final customer = await _supabaseService.getCustomerById(id);

        // Update local cache
        await _localDatabaseService.insertCustomer(
          customer,
          syncStatus: 'synced',
        );

        return customer;
      } catch (e) {
        debugPrint('Failed to get customer from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getCustomerById(id);
  }

  // Update customer (offline-first)
  Future<void> updateCustomer(Customer customer) async {
    // Always save to local database first
    await _localDatabaseService.updateCustomer(customer);

    // Record access in cache manager
    _cacheManager.recordCustomerAccess(customer.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateCustomer(customer);
        await _localDatabaseService.updateCustomerSyncStatus(
          customer.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync customer update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // Delete customer (offline-first)
  Future<void> deleteCustomer(String customerId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteCustomer(customerId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteCustomer(customerId);
      } catch (e) {
        debugPrint('Failed to sync customer deletion: ${e.toString()}');
        // In a real implementation, you might want to mark for deletion sync
      }
    }
  }

  // ==================== JOB OPERATIONS ====================

  Future<void> addJob(Job job) async {
    // Always save to local database first
    await _localDatabaseService.insertJob(job);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addJob(job);
        await _localDatabaseService.updateCustomerSyncStatus(job.id, 'synced');
      } catch (e) {
        debugPrint('Failed to sync job: ${e.toString()}');
      }
    }
  }

  Future<List<Job>> getJobs() async {
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithLoading(
        'getJobs',
        () async {
          if (await isOnline()) {
            try {
              // If online, get from Supabase
              final jobs = await _supabaseService.getJobs();

              // Update local cache
              for (var job in jobs) {
                await _localDatabaseService.insertJob(
                  job,
                  syncStatus: 'synced',
                );
              }

              return jobs;
            } catch (e) {
              debugPrint('Failed to get jobs from Supabase: ${e.toString()}');
            }
          }

          // Get from local database
          return await _localDatabaseService.getJobs();
        },
        message: 'Loading jobs...',
        errorMessage: 'Failed to load jobs',
      );
    } else {
      // Fallback to original implementation
      if (await isOnline()) {
        try {
          // If online, get from Supabase
          final jobs = await _supabaseService.getJobs();

          // Update local cache
          for (var job in jobs) {
            await _localDatabaseService.insertJob(job, syncStatus: 'synced');
          }

          return jobs;
        } catch (e) {
          debugPrint('Failed to get jobs from Supabase: ${e.toString()}');
        }
      }

      // Get from local database
      return await _localDatabaseService.getJobs();
    }
  }

  Future<Job?> getJobById(String id) async {
    // Record access in cache manager
    _cacheManager.recordJobAccess(id);

    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final job = await _supabaseService.getJobById(id);

        // Update local cache
        await _localDatabaseService.insertJob(job, syncStatus: 'synced');

        return job;
      } catch (e) {
        debugPrint('Failed to get job from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getJobById(id);
  }

  // Update job (offline-first)
  Future<void> updateJob(Job job) async {
    // Always save to local database first
    await _localDatabaseService.updateJob(job);

    // Record access in cache manager
    _cacheManager.recordJobAccess(job.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateJob(job);
        await _localDatabaseService.updateJobSyncStatus(job.id, 'synced');
      } catch (e) {
        debugPrint('Failed to sync job update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // Delete job (offline-first)
  Future<void> deleteJob(String jobId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteJob(jobId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteJob(jobId);
      } catch (e) {
        debugPrint('Failed to sync job deletion: ${e.toString()}');
        // In a real implementation, you might want to mark for deletion sync
      }
    }
  }

  // ==================== INVOICE OPERATIONS ====================

  Future<void> addInvoice(Invoice invoice) async {
    // Always save to local database first
    await _localDatabaseService.insertInvoice(invoice);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addInvoice(invoice);
        if (invoice.id != null) {
          await _localDatabaseService.updateInvoiceSyncStatus(
            invoice.id!,
            'synced',
          );
        }
      } catch (e) {
        debugPrint('Failed to sync invoice: ${e.toString()}');
      }
    }
  }

  Future<List<Invoice>> getInvoices() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final invoices = await _supabaseService.getInvoices();

        // Update local cache
        for (var invoice in invoices) {
          await _localDatabaseService.insertInvoice(
            invoice,
            syncStatus: 'synced',
          );
        }

        return invoices;
      } catch (e) {
        debugPrint('Failed to get invoices from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getInvoices();
  }

  Future<Invoice?> getInvoiceById(String id) async {
    // Record access in cache manager
    _cacheManager.recordInvoiceAccess(id);

    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final invoice = await _supabaseService.getInvoiceById(id);

        // Update local cache
        if (invoice != null) {
          await _localDatabaseService.insertInvoice(
            invoice,
            syncStatus: 'synced',
          );
        }

        return invoice;
      } catch (e) {
        debugPrint('Failed to get invoice from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getInvoiceById(id);
  }

  // Update invoice (offline-first)
  Future<void> updateInvoice(Invoice invoice) async {
    // Always save to local database first
    await _localDatabaseService.updateInvoice(invoice);

    // Record access in cache manager
    _cacheManager.recordInvoiceAccess(invoice.id!);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateInvoice(invoice);
        await _localDatabaseService.updateInvoiceSyncStatus(
          invoice.id!,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync invoice update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // Delete invoice (offline-first)
  Future<void> deleteInvoice(String invoiceId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteInvoice(invoiceId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteInvoice(invoiceId);
      } catch (e) {
        debugPrint('Failed to sync invoice deletion: ${e.toString()}');
        // In a real implementation, you might want to mark for deletion sync
      }
    }
  }

  // ==================== PAYMENT OPERATIONS ====================

  Future<void> addPayment(Payment payment) async {
    // Always save to local database first
    await _localDatabaseService.insertPayment(payment);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addPayment(payment);
        await _localDatabaseService.updatePaymentSyncStatus(
          payment.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync payment: ${e.toString()}');
      }
    }
  }

  Future<List<Payment>> getPayments() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final payments = await _supabaseService.getPayments();

        // Update local cache
        for (var payment in payments) {
          await _localDatabaseService.insertPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return payments;
      } catch (e) {
        debugPrint('Failed to get payments from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getPayments();
  }

  Future<List<Payment>> getPaymentsByInvoice(String invoiceId) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final payments = await _supabaseService.getPaymentsByInvoice(invoiceId);

        // Update local cache
        for (var payment in payments) {
          await _localDatabaseService.insertPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return payments;
      } catch (e) {
        debugPrint('Failed to get payments from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getPaymentsByInvoice(invoiceId);
  }

  Future<List<Payment>> getPaymentsByJob(String jobId) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final payments = await _supabaseService.getPaymentsByJob(jobId);

        // Update local cache
        for (var payment in payments) {
          await _localDatabaseService.insertPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return payments;
      } catch (e) {
        debugPrint('Failed to get payments from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getPaymentsByJob(jobId);
  }

  // ==================== EXPENSE OPERATIONS ====================

  Future<void> addExpense(Expense expense) async {
    // Always save to local database first
    await _localDatabaseService.insertExpense(expense);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addExpense(expense);
        await _localDatabaseService.updateExpenseSyncStatus(
          expense.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync expense: ${e.toString()}');
      }
    }
  }

  Future<List<Expense>> getExpenses() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final expenses = await _supabaseService.getExpenses();

        // Update local cache
        for (var expense in expenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return expenses;
      } catch (e) {
        debugPrint('Failed to get expenses from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getExpenses();
  }

  Future<List<Expense>> getExpensesByJob(String jobId) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final expenses = await _supabaseService.getExpensesByJob(jobId);

        // Update local cache
        for (var expense in expenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return expenses;
      } catch (e) {
        debugPrint('Failed to get expenses from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getExpensesByJob(jobId);
  }

  // Get all overhead expenses (offline-first)
  Future<List<Expense>> getOverheadExpenses() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final expenses = await _supabaseService.getOverheadExpenses();

        // Update local cache
        for (var expense in expenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return expenses;
      } catch (e) {
        debugPrint(
          'Failed to get overhead expenses from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getOverheadExpenses();
  }

  // Get overhead expenses by date range (offline-first)
  Future<List<Expense>> getOverheadExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final expenses = await _supabaseService.getOverheadExpensesByDateRange(
          startDate,
          endDate,
        );

        // Update local cache
        for (var expense in expenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return expenses;
      } catch (e) {
        debugPrint(
          'Failed to get overhead expenses by date range from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getOverheadExpensesByDateRange(
      startDate,
      endDate,
    );
  }

  // Get expenses by date range (offline-first)
  Future<List<Expense>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase (no direct method, so get all and filter)
        final allExpenses = await _supabaseService.getExpenses();
        final filteredExpenses =
            allExpenses.where((expense) {
              return expense.date.isAfter(
                    startDate.subtract(const Duration(days: 1)),
                  ) &&
                  expense.date.isBefore(endDate.add(const Duration(days: 1)));
            }).toList();

        // Update local cache
        for (var expense in allExpenses) {
          await _localDatabaseService.insertExpense(
            expense,
            syncStatus: 'synced',
          );
        }

        return filteredExpenses;
      } catch (e) {
        debugPrint(
          'Failed to get expenses by date range from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getExpensesByDateRange(
      startDate,
      endDate,
    );
  }

  // Get payments by date range (offline-first)
  Future<List<Payment>> getPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase (no direct method, so get all and filter)
        final allPayments = await _supabaseService.getPayments();
        final filteredPayments =
            allPayments.where((payment) {
              return payment.paymentDate.isAfter(
                    startDate.subtract(const Duration(days: 1)),
                  ) &&
                  payment.paymentDate.isBefore(
                    endDate.add(const Duration(days: 1)),
                  );
            }).toList();

        // Update local cache
        for (var payment in allPayments) {
          await _localDatabaseService.insertPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return filteredPayments;
      } catch (e) {
        debugPrint(
          'Failed to get payments by date range from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getPaymentsByDateRange(
      startDate,
      endDate,
    );
  }

  // Update payment (offline-first)
  Future<void> updatePayment(Payment payment) async {
    // Always save to local database first
    await _localDatabaseService.updatePayment(payment);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updatePayment(payment);
        await _localDatabaseService.updatePaymentSyncStatus(
          payment.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync payment update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // Delete payment (offline-first)
  Future<void> deletePayment(String paymentId) async {
    // Always delete from local database first
    await _localDatabaseService.deletePayment(paymentId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deletePayment(paymentId);
      } catch (e) {
        debugPrint('Failed to sync payment deletion: ${e.toString()}');
        // In a real implementation, you might want to mark for deletion sync
      }
    }
  }

  // Update an existing expense
  Future<void> updateExpense(Expense expense) async {
    // Always save to local database first
    await _localDatabaseService.updateExpense(expense);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateExpense(expense);
        await _localDatabaseService.updateExpenseSyncStatus(
          expense.id,
          SyncStatus.synced,
        );
      } catch (e) {
        debugPrint('Failed to sync expense: ${e.toString()}');
      }
    }
  }

  // ==================== TIME LOG OPERATIONS ====================

  Future<void> addTimeLog(TimeLog timeLog) async {
    // Always save to local database first
    await _localDatabaseService.insertTimeLog(timeLog);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addTimeLog(timeLog);
        await _localDatabaseService.updateTimeLogSyncStatus(
          timeLog.id,
          SyncStatus.synced,
        );
      } catch (e) {
        debugPrint('Failed to sync time log: ${e.toString()}');
      }
    }
  }

  Future<List<TimeLog>> getTimeLogs() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final timeLogs = await _supabaseService.getTimeLogs();

        // Update local cache
        for (var timeLog in timeLogs) {
          await _localDatabaseService.insertTimeLog(
            timeLog,
            syncStatus: 'synced',
          );
        }

        return timeLogs;
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'getTimeLogs', 'service': 'DataRepository'},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get from local database
    return await _localDatabaseService.getTimeLogs();
  }

  Future<List<TimeLog>> getTimeLogsByJob(String jobId) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final timeLogs = await _supabaseService.getTimeLogsByJob(jobId);

        // Update local cache
        for (var timeLog in timeLogs) {
          await _localDatabaseService.insertTimeLog(
            timeLog,
            syncStatus: 'synced',
          );
        }

        return timeLogs;
      } catch (e) {
        debugPrint('Failed to get time logs from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getTimeLogsByJob(jobId);
  }

  // Update time log (offline-first)
  Future<void> updateTimeLog(TimeLog timeLog) async {
    // Always save to local database first
    await _localDatabaseService.updateTimeLog(timeLog);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateTimeLog(timeLog);
        await _localDatabaseService.updateTimeLogSyncStatus(
          timeLog.id,
          SyncStatus.synced.name,
        );
      } catch (e) {
        debugPrint('Failed to sync time log update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // Delete time log (offline-first)
  Future<void> deleteTimeLog(String timeLogId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteTimeLog(timeLogId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteTimeLog(timeLogId);
      } catch (e) {
        debugPrint('Failed to sync time log deletion: ${e.toString()}');
        // In a real implementation, you might want to mark for deletion sync
      }
    }
  }

  // Get network status stream
  Stream<bool> get networkStatus => _connectivityService.connectionStatus;

  // ==================== OVERHEAD ALLOCATION OPERATIONS ====================

  // Allocate overhead expenses across jobs based on each job's proportion of annual income (offline-first)
  Future<Map<String, double>> allocateOverheadExpenses({
    String method = 'proportional_income',
    int? year,
  }) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.allocateOverheadExpenses(
          method: method,
          year: year,
        );
      } catch (e) {
        debugPrint(
          'Failed to allocate overhead expenses from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Perform local allocation calculation
    return await _allocateOverheadExpensesLocally(method: method, year: year);
  }

  // Local overhead allocation calculation
  Future<Map<String, double>> _allocateOverheadExpensesLocally({
    String method = 'proportional_income',
    int? year,
  }) async {
    try {
      // Get all overhead expenses, optionally filtered by year
      List<Expense> overheadExpenses;
      if (year != null) {
        final startDate = DateTime(year, 1, 1);
        final endDate = DateTime(year, 12, 31);
        overheadExpenses = await getOverheadExpensesByDateRange(
          startDate,
          endDate,
        );
      } else {
        overheadExpenses = await getOverheadExpenses();
      }

      if (overheadExpenses.isEmpty) {
        return {};
      }

      // Calculate total overhead amount
      final totalOverheadAmount = overheadExpenses.fold(
        0.0,
        (sum, expense) => sum + expense.amount,
      );

      // Get all jobs
      final allJobs = await getJobs();
      if (allJobs.isEmpty) {
        return {};
      }

      final Map<String, double> allocationMap = {};

      if (method == 'proportional_income') {
        // Get all payments for the year (or all time if year not specified)
        List<Payment> yearPayments;
        if (year != null) {
          final startDate = DateTime(year, 1, 1);
          final endDate = DateTime(year, 12, 31);
          yearPayments = await getPaymentsByDateRange(startDate, endDate);
        } else {
          yearPayments = await getPayments();
        }

        // Group payments by job and calculate total income per job
        final Map<String, double> jobIncomes = {};
        double totalJobIncome = 0.0;

        for (final payment in yearPayments) {
          if (payment.jobId != null) {
            jobIncomes[payment.jobId!] =
                (jobIncomes[payment.jobId!] ?? 0.0) + payment.amountReceived;
            totalJobIncome += payment.amountReceived;
          }
        }

        // Allocate based on income proportion
        if (totalJobIncome > 0) {
          for (final jobId in jobIncomes.keys) {
            final proportion = jobIncomes[jobId]! / totalJobIncome;
            allocationMap[jobId] = totalOverheadAmount * proportion;
          }
        } else {
          // Fall back to equal allocation across all jobs if no income
          final amountPerJob = totalOverheadAmount / allJobs.length;
          for (final job in allJobs) {
            allocationMap[job.id] = amountPerJob;
          }
        }
      } else {
        // Default to equal allocation
        final amountPerJob = totalOverheadAmount / allJobs.length;
        for (final job in allJobs) {
          allocationMap[job.id] = amountPerJob;
        }
      }

      return allocationMap;
    } catch (e) {
      debugPrint(
        'Failed to allocate overhead expenses locally: ${e.toString()}',
      );
      return {};
    }
  }

  // Calculate total allocated overhead for a given year (offline-first)
  Future<double> getTotalAllocatedOverheadForYear(int year) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getTotalAllocatedOverheadForYear(year);
      } catch (e) {
        debugPrint(
          'Failed to get total allocated overhead from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Perform local calculation
    try {
      final Map<String, double> allocationMap = await allocateOverheadExpenses(
        year: year,
      );
      if (allocationMap.isEmpty) {
        return 0.0;
      }
      // Sum up all allocated amounts
      double total = 0.0;
      for (var amount in allocationMap.values) {
        total += amount;
      }
      return total;
    } catch (e) {
      debugPrint(
        'Failed to get total allocated overhead locally: ${e.toString()}',
      );
      return 0.0;
    }
  }

  // Get detailed job cost summary with overhead allocation (offline-first)
  Future<Map<String, dynamic>> getJobCostSummary(String jobId) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getJobCostSummary(jobId);
      } catch (e) {
        debugPrint(
          'Failed to get job cost summary from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Perform local calculation
    return await _getJobCostSummaryLocally(jobId);
  }

  // Local job cost summary calculation
  Future<Map<String, dynamic>> _getJobCostSummaryLocally(String jobId) async {
    try {
      // Get job details
      final job = await getJobById(jobId);
      if (job == null) {
        throw Exception('Job not found');
      }

      // Get all expenses for this job
      final allExpenses = await getExpensesByJob(jobId);

      // Group expenses by IRS Schedule C categories
      final Map<String, List<Expense>> expensesByCategory = {};

      for (var expense in allExpenses) {
        if (expense.category != null) {
          if (!expensesByCategory.containsKey(expense.category)) {
            expensesByCategory[expense.category!] = [];
          }
          expensesByCategory[expense.category!]!.add(expense);
        }
      }

      // Get all payments for this job
      final payments = await getPaymentsByJob(jobId);

      // Determine the years to use for overhead allocation based on job payments
      // We'll collect all years with income for this job
      Set<int> allocationYears = {};
      if (payments.isNotEmpty) {
        // Add all years with payments
        for (var payment in payments) {
          allocationYears.add(payment.paymentDate.year);
        }
      } else {
        // If no payments, check if there are expenses and use their years
        if (allExpenses.isNotEmpty) {
          for (var expense in allExpenses) {
            allocationYears.add(expense.date.year);
          }
        } else {
          // Default to current year if no payments or expenses
          allocationYears.add(DateTime.now().year);
        }
      }

      // Get allocated overhead expenses for this job for each year with income
      double allocatedOverhead = 0.0;
      for (var year in allocationYears) {
        final allocationMap = await allocateOverheadExpenses(year: year);
        allocatedOverhead += allocationMap[jobId] ?? 0.0;
      }

      // Get all time logs for this job
      final timeLogs = await getTimeLogsByJob(jobId);

      // Calculate totals by category
      Map<String, double> categoryTotals = {};

      // Initialize with all IRS Schedule C categories
      for (String category in ExpenseCategory.values) {
        categoryTotals[category] = 0.0;
      }

      // Calculate totals for each category
      for (var category in expensesByCategory.keys) {
        categoryTotals[category] = expensesByCategory[category]!.fold(
          0.0,
          (sum, expense) => sum + expense.amount,
        );
      }

      // Calculate total direct expenses from all categories
      double totalDirectExpenses = categoryTotals.values.fold(
        0.0,
        (sum, amount) => sum + amount,
      );
      double totalExpenses = totalDirectExpenses + allocatedOverhead;
      double totalLaborCost = timeLogs.fold(
        0.0,
        (sum, timeLog) => sum + timeLog.laborCost,
      );
      double totalHoursWorked = timeLogs.fold(
        0.0,
        (sum, timeLog) => sum + timeLog.hours,
      );
      double totalIncome = payments.fold(
        0.0,
        (sum, payment) => sum + payment.amountReceived,
      );

      // Time logs are for user only and should not be counted as expenses
      // They add to income, not expenses
      double netProfit = totalIncome - totalExpenses;
      double profitMargin =
          totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0;

      return {
        'job': job,
        'costs': {
          'categories': categoryTotals,
          'overhead_allocated': allocatedOverhead,
          'labor': totalLaborCost,
          'direct_expenses': totalDirectExpenses,
          'total_expenses': totalExpenses,
          'total_cost': totalExpenses + totalLaborCost,
        },
        'labor': {'hours_worked': totalHoursWorked, 'cost': totalLaborCost},
        'income': totalIncome,
        'profit': {'net_profit': netProfit, 'margin_percentage': profitMargin},
        'expense_details': {'by_category': expensesByCategory},
        'time_logs': timeLogs,
        'payments': payments,
      };
    } catch (e) {
      debugPrint('Failed to get job cost summary locally: ${e.toString()}');
      throw Exception('Failed to get job cost summary: $e');
    }
  }

  // ==================== FINANCIAL SUMMARY OPERATIONS ====================

  // Get year-to-date financial summary (offline-first)
  Future<Map<String, dynamic>> getYearToDateFinancials(int year) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getYearToDateFinancials(year);
      } catch (e) {
        debugPrint(
          'Failed to get YTD financials from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Perform local calculation
    return await _getYearToDateFinancialsLocally(year);
  }

  // Local year-to-date financial calculation
  Future<Map<String, dynamic>> _getYearToDateFinancialsLocally(int year) async {
    try {
      final startDate = DateTime(year, 1, 1);
      final endDate = DateTime(year, 12, 31);

      // Get all payments for the year
      final payments = await getPaymentsByDateRange(startDate, endDate);
      final totalIncome = payments.fold(
        0.0,
        (sum, payment) => sum + payment.amountReceived,
      );

      // Get all job expenses for the year
      final jobExpenses = await getExpensesByDateRange(startDate, endDate);
      final directJobExpenses = jobExpenses
          .where((expense) => !expense.isOverhead)
          .fold(0.0, (sum, expense) => sum + expense.amount);

      // Get allocated overhead for the year
      final totalAllocatedOverhead = await getTotalAllocatedOverheadForYear(
        year,
      );

      // Calculate totals
      final totalExpenses = directJobExpenses + totalAllocatedOverhead;
      final netProfit = totalIncome - totalExpenses;

      // Get all expenses for the year (for detailed breakdown)
      final allExpenses = await getExpensesByDateRange(startDate, endDate);

      return {
        'total_income': totalIncome,
        'direct_job_expenses': directJobExpenses,
        'total_allocated_overhead': totalAllocatedOverhead,
        'total_expenses': totalExpenses,
        'net_profit_allocated': netProfit,
        'all_year_actual_expenses_list': allExpenses,
      };
    } catch (e) {
      debugPrint('Failed to calculate YTD financials locally: ${e.toString()}');
      return {
        'total_income': 0.0,
        'direct_job_expenses': 0.0,
        'total_allocated_overhead': 0.0,
        'total_expenses': 0.0,
        'net_profit_allocated': 0.0,
        'all_year_actual_expenses_list': <Expense>[],
      };
    }
  }

  // Get quarterly financial summary (offline-first)
  Future<Map<String, dynamic>> getQuarterlyFinancials(
    int year,
    int quarter,
  ) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getQuarterlyFinancials(year, quarter);
      } catch (e) {
        debugPrint(
          'Failed to get quarterly financials from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Perform local calculation
    return await _getQuarterlyFinancialsLocally(year, quarter);
  }

  // Local quarterly financial calculation
  Future<Map<String, dynamic>> _getQuarterlyFinancialsLocally(
    int year,
    int quarter,
  ) async {
    try {
      // Calculate quarter date range
      final startMonth = (quarter - 1) * 3 + 1;
      final endMonth = quarter * 3;
      final startDate = DateTime(year, startMonth, 1);
      final endDate = DateTime(
        year,
        endMonth + 1,
        1,
      ).subtract(const Duration(days: 1));

      // Get payments for the quarter
      final payments = await getPaymentsByDateRange(startDate, endDate);
      final totalIncome = payments.fold(
        0.0,
        (sum, payment) => sum + payment.amountReceived,
      );

      // Get job expenses for the quarter
      final jobExpenses = await getExpensesByDateRange(startDate, endDate);
      final directJobExpenses = jobExpenses
          .where((expense) => !expense.isOverhead)
          .fold(0.0, (sum, expense) => sum + expense.amount);

      // Get allocated overhead for the year (overhead is allocated annually)
      final yearlyAllocatedOverhead = await getTotalAllocatedOverheadForYear(
        year,
      );
      // Allocate quarterly portion (1/4 of annual)
      final quarterlyAllocatedOverhead = yearlyAllocatedOverhead / 4;

      // Calculate totals
      final totalExpenses = directJobExpenses + quarterlyAllocatedOverhead;
      final netProfit = totalIncome - totalExpenses;

      // Simple tax estimate (25% of net profit)
      final estimatedTax = netProfit > 0 ? netProfit * 0.25 : 0.0;

      // Get tax payments for the quarter
      final taxPayments = await getTaxPaymentsByDateRange(startDate, endDate);
      final taxPaid = taxPayments.fold(
        0.0,
        (sum, payment) => sum + payment.amount,
      );

      return {
        'total_income': totalIncome,
        'total_expenses': totalExpenses,
        'net_profit': netProfit,
        'estimated_tax': estimatedTax,
        'tax_paid': taxPaid,
      };
    } catch (e) {
      debugPrint(
        'Failed to calculate quarterly financials locally: ${e.toString()}',
      );
      return {
        'total_income': 0.0,
        'total_expenses': 0.0,
        'net_profit': 0.0,
        'estimated_tax': 0.0,
        'tax_paid': 0.0,
      };
    }
  }

  // Get active jobs (offline-first)
  Future<List<Job>> getActiveJobs() async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getActiveJobs();
      } catch (e) {
        debugPrint('Failed to get active jobs from Supabase: ${e.toString()}');
        // Fall back to local calculation
      }
    }

    // Get from local database
    final allJobs = await getJobs();
    return allJobs
        .where(
          (job) =>
              job.status.toLowerCase() != 'completed' &&
              job.status.toLowerCase() != 'cancelled',
        )
        .toList();
  }

  // Get expenses since a date (offline-first)
  Future<List<Expense>> getExpensesSince(DateTime startDate) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getExpensesSince(startDate);
      } catch (e) {
        debugPrint(
          'Failed to get expenses since date from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database
    final endDate = DateTime.now().add(
      const Duration(days: 1),
    ); // Include today
    return await getExpensesByDateRange(startDate, endDate);
  }

  /// Get invoices since a specific date
  Future<List<Invoice>> getInvoicesSince(DateTime startDate) async {
    if (await isOnline()) {
      try {
        // Use existing getInvoices method and filter locally for now
        final allInvoices = await _supabaseService.getInvoices();
        return allInvoices
            .where(
              (invoice) =>
                  invoice.issueDate.isAfter(startDate) ||
                  invoice.issueDate.isAtSameMomentAs(startDate),
            )
            .toList();
      } catch (e) {
        debugPrint(
          'Failed to get invoices since date from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter by date
    final allInvoices = await getInvoices();
    return allInvoices
        .where(
          (invoice) =>
              invoice.issueDate.isAfter(startDate) ||
              invoice.issueDate.isAtSameMomentAs(startDate),
        )
        .toList();
  }

  /// Get invoices by job ID
  Future<List<Invoice>> getInvoicesByJob(String jobId) async {
    if (await isOnline()) {
      try {
        return await _supabaseService.getInvoicesByJob(jobId);
      } catch (e) {
        debugPrint(
          'Failed to get invoices by job from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter by job
    final allInvoices = await getInvoices();
    return allInvoices.where((invoice) => invoice.jobId == jobId).toList();
  }

  /// Get expense by ID
  Future<Expense?> getExpenseById(String id) async {
    if (await isOnline()) {
      try {
        // Use existing getExpenses method and find by ID for now
        final allExpenses = await _supabaseService.getExpenses();
        try {
          return allExpenses.firstWhere((expense) => expense.id == id);
        } catch (e) {
          return null;
        }
      } catch (e) {
        debugPrint(
          'Failed to get expense by ID from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database and find by ID
    final allExpenses = await getExpenses();
    try {
      return allExpenses.firstWhere((expense) => expense.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Delete expense
  Future<void> deleteExpense(String expenseId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteExpense(expenseId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteExpense(expenseId);
      } catch (e) {
        debugPrint('Failed to sync expense deletion: ${e.toString()}');
      }
    }
  }

  // Get estimates by status (offline-first)
  Future<List<Estimate>> getEstimatesByStatus(String status) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getEstimatesByStatus(status);
      } catch (e) {
        debugPrint(
          'Failed to get estimates by status from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter
    final allEstimates = await getEstimates();
    return allEstimates
        .where(
          (estimate) => estimate.status.toLowerCase() == status.toLowerCase(),
        )
        .toList();
  }

  // Get invoices by status (offline-first)
  Future<List<Invoice>> getInvoicesByStatus(String status) async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getInvoicesByStatus(status);
      } catch (e) {
        debugPrint(
          'Failed to get invoices by status from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter
    final allInvoices = await getInvoices();
    return allInvoices
        .where(
          (invoice) => invoice.status.toLowerCase() == status.toLowerCase(),
        )
        .toList();
  }

  // Get overdue invoices (offline-first)
  Future<List<Invoice>> getOverdueInvoices() async {
    if (await isOnline()) {
      try {
        // If online, use Supabase service
        return await _supabaseService.getOverdueInvoices();
      } catch (e) {
        debugPrint(
          'Failed to get overdue invoices from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter for overdue
    final allInvoices = await getInvoices();
    final now = DateTime.now();
    return allInvoices
        .where(
          (invoice) =>
              invoice.status.toLowerCase() != 'paid' &&
              invoice.status.toLowerCase() != 'cancelled' &&
              invoice.dueDate.isBefore(now),
        )
        .toList();
  }

  // ==================== TAX PAYMENT OPERATIONS ====================

  Future<void> addTaxPayment(TaxPayment payment) async {
    // Always save to local database first
    await _localDatabaseService.insertTaxPayment(payment);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addTaxPayment(payment);
        await _localDatabaseService.updateTaxPaymentSyncStatus(
          payment.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync tax payment: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  Future<void> updateTaxPayment(TaxPayment payment) async {
    // Always update in local database first
    await _localDatabaseService.updateTaxPayment(payment);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateTaxPayment(payment);
        await _localDatabaseService.updateTaxPaymentSyncStatus(
          payment.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync updated tax payment: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  Future<List<TaxPayment>> getTaxPayments() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final taxPayments = await _supabaseService.getTaxPayments(
          year: DateTime.now().year,
        );

        // Update local cache
        for (var payment in taxPayments) {
          await _localDatabaseService.insertTaxPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return taxPayments;
      } catch (e) {
        debugPrint('Failed to get tax payments from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getTaxPayments();
  }

  Future<List<TaxPayment>> getTaxPaymentsByPeriod(String taxPeriod) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        // Parse the tax period to get year and quarter
        final parts = taxPeriod.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final quarter = int.tryParse(parts[1]);

          if (year != null) {
            final taxPayments = await _supabaseService.getTaxPayments(
              year: year,
              quarter: quarter,
            );

            // Update local cache
            for (var payment in taxPayments) {
              await _localDatabaseService.insertTaxPayment(
                payment,
                syncStatus: 'synced',
              );
            }

            return taxPayments;
          }
        }

        throw Exception('Invalid tax period format: $taxPeriod');
      } catch (e) {
        debugPrint('Failed to get tax payments from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getTaxPaymentsByPeriod(taxPeriod);
  }

  // Get tax payments by date range (offline-first)
  Future<List<TaxPayment>> getTaxPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase (no direct method, so get all and filter)
        final allTaxPayments = await _supabaseService.getTaxPayments(
          year: DateTime.now().year,
        );
        final filteredPayments =
            allTaxPayments.where((payment) {
              return payment.date.isAfter(
                    startDate.subtract(const Duration(days: 1)),
                  ) &&
                  payment.date.isBefore(endDate.add(const Duration(days: 1)));
            }).toList();

        // Update local cache
        for (var payment in allTaxPayments) {
          await _localDatabaseService.insertTaxPayment(
            payment,
            syncStatus: 'synced',
          );
        }

        return filteredPayments;
      } catch (e) {
        debugPrint(
          'Failed to get tax payments by date range from Supabase: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getTaxPaymentsByDateRange(
      startDate,
      endDate,
    );
  }

  /// Get tax payment by ID
  Future<TaxPayment?> getTaxPaymentById(String id) async {
    if (await isOnline()) {
      try {
        return await _supabaseService.getTaxPaymentById(id);
      } catch (e) {
        debugPrint(
          'Failed to get tax payment by ID from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database - use search functionality since there's no direct method
    final allTaxPayments = await getTaxPayments();
    try {
      return allTaxPayments.firstWhere((payment) => payment.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Delete tax payment
  Future<void> deleteTaxPayment(String taxPaymentId) async {
    // Always delete from local database first
    await _localDatabaseService.deleteTaxPayment(taxPaymentId);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteTaxPayment(taxPaymentId);
      } catch (e) {
        debugPrint('Failed to sync tax payment deletion: ${e.toString()}');
      }
    }
  }

  /// Get tax payments since a specific date
  Future<List<TaxPayment>> getTaxPaymentsSince(DateTime startDate) async {
    if (await isOnline()) {
      try {
        // Use existing getTaxPayments method and filter locally for now
        final allTaxPayments = await _supabaseService.getTaxPayments(
          year: startDate.year,
        );
        return allTaxPayments
            .where(
              (payment) =>
                  payment.date.isAfter(startDate) ||
                  payment.date.isAtSameMomentAs(startDate),
            )
            .toList();
      } catch (e) {
        debugPrint(
          'Failed to get tax payments since date from Supabase: ${e.toString()}',
        );
        // Fall back to local calculation
      }
    }

    // Get from local database and filter by date
    final allTaxPayments = await getTaxPayments();
    return allTaxPayments
        .where(
          (payment) =>
              payment.date.isAfter(startDate) ||
              payment.date.isAtSameMomentAs(startDate),
        )
        .toList();
  }

  // Create an estimate template from an existing estimate
  Future<void> createEstimateTemplate(
    Estimate estimate,
    String templateName,
  ) async {
    try {
      // In a real implementation, this would save the template to the database
      debugPrint(
        'Creating estimate template: $templateName from estimate ${estimate.id}',
      );

      // Create a copy of the estimate with a new ID and the template name
      final template = estimate.copyWith(
        id: const Uuid().v4(),
        status: 'template',
        notes: templateName,
      );

      // If online, try to sync immediately
      if (await isOnline()) {
        try {
          // This would be implemented in a real app
          debugPrint('Template created: ${template.id}');
        } catch (e) {
          debugPrint('Failed to sync template: ${e.toString()}');
        }
      }

      // Simulate successful creation
      return Future.value();
    } catch (e) {
      debugPrint('Error creating estimate template: ${e.toString()}');
      rethrow;
    }
  }

  Future<String> getCurrentUserId() async {
    // Replace this with the actual implementation to fetch the current user ID
    return 'mockUserId'; // Example placeholder
  }

  // ==================== ESTIMATE OPERATIONS ====================

  /// Add a new estimate (offline-first)
  Future<void> addEstimate(Estimate estimate) async {
    // Always save to local database first
    await _localDatabaseService.insertEstimate(estimate);

    // Record access in cache manager
    _cacheManager.recordEstimateAccess(estimate.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addEstimate(estimate);
        await _localDatabaseService.updateEstimateSyncStatus(
          estimate.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync estimate: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  /// Get all estimates (offline-first)
  Future<List<Estimate>> getEstimates() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final estimates = await _supabaseService.getEstimates();

        // Update local database
        for (var estimate in estimates) {
          await _localDatabaseService.insertEstimate(
            estimate,
            syncStatus: 'synced',
          );
        }

        return estimates;
      } catch (e) {
        debugPrint('Failed to get estimates from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getEstimates();
  }

  /// Get an estimate by ID (offline-first)
  Future<Estimate?> getEstimateById(String id) async {
    // Record access in cache manager
    _cacheManager.recordEstimateAccess(id);

    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final estimate = await _supabaseService.getEstimateById(id);

        await _localDatabaseService.insertEstimate(
          estimate,
          syncStatus: 'synced',
        );
        return estimate;
      } catch (e) {
        debugPrint('Failed to get estimate from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getEstimateById(id);
  }

  /// Get estimates by job ID (offline-first)
  Future<List<Estimate>> getEstimatesByJobId(String jobId) async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final estimates = await _supabaseService.getEstimatesByJob(jobId);

        // Update local database
        for (var estimate in estimates) {
          await _localDatabaseService.insertEstimate(
            estimate,
            syncStatus: 'synced',
          );
        }

        return estimates;
      } catch (e) {
        debugPrint(
          'Failed to get estimates by job ID from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getEstimatesByJobId(jobId);
  }

  /// Get estimate templates (offline-first)
  Future<List<Estimate>> getEstimateTemplates() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final templates = await _supabaseService.getEstimateTemplates();

        // Update local database
        for (var template in templates) {
          await _localDatabaseService.insertEstimate(
            template,
            syncStatus: 'synced',
          );
        }

        return templates;
      } catch (e) {
        debugPrint(
          'Failed to get estimate templates from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getEstimateTemplates();
  }

  /// Update an estimate (offline-first)
  Future<void> updateEstimate(Estimate estimate) async {
    // Update locally first
    final updatedEstimate = estimate.copyWith(
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.pending,
    );

    await _localDatabaseService.insertEstimate(updatedEstimate);

    // Record access in cache manager
    _cacheManager.recordEstimateAccess(estimate.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateEstimate(updatedEstimate);
        await _localDatabaseService.updateEstimateSyncStatus(
          estimate.id,
          SyncStatus.synced.name,
        );
      } catch (e) {
        debugPrint('Failed to sync estimate update: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  /// Delete an estimate (offline-first)
  Future<void> deleteEstimate(String id) async {
    // Delete locally first
    await _localDatabaseService.deleteEstimate(id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.deleteEstimate(id);
      } catch (e) {
        debugPrint('Failed to sync estimate deletion: ${e.toString()}');
        // The local deletion has already happened, so we'll rely on sync to handle this
      }
    }
  }

  // Get the current IRS mileage rate
  Future<double?> getMileageRate() async {
    if (await isOnline()) {
      try {
        return await _supabaseService.getMileageRate();
      } catch (e) {
        debugPrint('Failed to get mileage rate from Supabase: ${e.toString()}');
        // Fall back to default rate
        return double.tryParse(MileageConstants.defaultIrsRate);
      }
    }

    // Return default rate when offline
    return double.tryParse(MileageConstants.defaultIrsRate);
  }

  // ==================== CONTRACT OPERATIONS ====================

  /// Add a new contract
  Future<void> addContract(Contract contract) async {
    // Always save to local database first
    await _localDatabaseService.insertContract(contract);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.addContract(contract);
        await _localDatabaseService.updateContractSyncStatus(
          contract.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync contract: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  /// Get all contracts
  Future<List<Contract>> getContracts() async {
    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final contracts = await _supabaseService.getContracts();

        // Update local cache
        for (var contract in contracts) {
          await _localDatabaseService.insertContract(
            contract,
            syncStatus: 'synced',
          );
        }

        return contracts;
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'getContracts', 'service': 'DataRepository'},
        );
        ErrorHandler.logError(appError);
      }
    }

    // Get from local database
    return await _localDatabaseService.getContracts();
  }

  /// Get a contract by ID
  Future<Contract?> getContractById(String id) async {
    // Record access in cache manager
    _cacheManager.recordContractAccess(id);

    if (await isOnline()) {
      try {
        // If online, get from Supabase
        final contract = await _supabaseService.getContractById(id);

        // Update local cache
        await _localDatabaseService.insertContract(
          contract,
          syncStatus: 'synced',
        );

        return contract;
      } catch (e) {
        debugPrint('Failed to get contract from Supabase: ${e.toString()}');
      }
    }

    // Get from local database
    return await _localDatabaseService.getContractById(id);
  }

  /// Update an existing contract
  Future<void> updateContract(Contract contract) async {
    // Always save to local database first
    await _localDatabaseService.updateContract(contract);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateContract(contract);
        await _localDatabaseService.updateContractSyncStatus(
          contract.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync contract: ${e.toString()}');
      }
    }
  }

  /// Create a contract from an approved estimate
  Future<Contract> createContractFromEstimate(Estimate estimate) async {
    // Create a new contract from the estimate
    final contract = Contract.fromEstimate(estimate);

    // Save the contract
    await addContract(contract);

    return contract;
  }

  // ==================== DOCUMENT SIGNING OPERATIONS ====================

  /// Add a new document signing request (offline-first)
  Future<void> addDocumentSigningRequest(DocumentSigningRequest request) async {
    // Always save to local database first
    await _localDatabaseService.insertDocumentSigningRequest(request);

    // Record access in cache manager
    _cacheManager.recordDocumentSigningRequestAccess(request.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        // Sync to server
        await _supabaseService.addDocumentSigningRequest(request);
        await _localDatabaseService.updateDocumentSigningRequestSyncStatus(
          request.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync document signing request: ${e.toString()}');
      }
    }
  }

  /// Get all document signing requests (offline-first)
  Future<List<DocumentSigningRequest>> getDocumentSigningRequests() async {
    if (await isOnline()) {
      try {
        // Get from server and update local cache
        final requests = await _supabaseService.getDocumentSigningRequests();

        // Update local cache
        for (var request in requests) {
          await _localDatabaseService.insertDocumentSigningRequest(
            request,
            syncStatus: 'synced',
          );
        }

        return requests;
      } catch (e) {
        debugPrint(
          'Failed to get document signing requests from server: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getDocumentSigningRequests();
  }

  /// Get a document signing request by ID (offline-first)
  Future<DocumentSigningRequest?> getDocumentSigningRequestById(
    String id,
  ) async {
    // Record access in cache manager
    _cacheManager.recordDocumentSigningRequestAccess(id);

    if (await isOnline()) {
      try {
        // Get from server and update local cache
        final request = await _supabaseService.getDocumentSigningRequestById(
          id,
        );

        if (request != null) {
          // Update local cache
          await _localDatabaseService.insertDocumentSigningRequest(
            request,
            syncStatus: 'synced',
          );

          return request;
        }
      } catch (e) {
        debugPrint(
          'Failed to get document signing request from server: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getDocumentSigningRequestById(id);
  }

  /// Add a new signed document (offline-first)
  Future<void> addSignedDocument(SignedDocument document) async {
    // Always save to local database first
    await _localDatabaseService.insertSignedDocument(document);

    // Record access in cache manager
    _cacheManager.recordSignedDocumentAccess(document.id);

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        // Sync to server
        await _supabaseService.addSignedDocument(document);
        await _localDatabaseService.updateSignedDocumentSyncStatus(
          document.id,
          'synced',
        );
      } catch (e) {
        debugPrint('Failed to sync signed document: ${e.toString()}');
      }
    }
  }

  /// Get all signed documents (offline-first)
  Future<List<SignedDocument>> getSignedDocuments() async {
    if (await isOnline()) {
      try {
        // Get from server and update local cache
        final documents = await _supabaseService.getSignedDocuments();

        // Update local cache
        for (var document in documents) {
          await _localDatabaseService.insertSignedDocument(
            document,
            syncStatus: 'synced',
          );
        }

        return documents;
      } catch (e) {
        debugPrint(
          'Failed to get signed documents from server: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getSignedDocuments();
  }

  /// Get a signed document by ID (offline-first)
  Future<SignedDocument?> getSignedDocumentById(String id) async {
    // Record access in cache manager
    _cacheManager.recordSignedDocumentAccess(id);

    if (await isOnline()) {
      try {
        // Get from server and update local cache
        final document = await _supabaseService.getSignedDocumentById(id);

        if (document != null) {
          // Update local cache
          await _localDatabaseService.insertSignedDocument(
            document,
            syncStatus: 'synced',
          );

          return document;
        }
      } catch (e) {
        debugPrint(
          'Failed to get signed document from server: ${e.toString()}',
        );
      }
    }

    // Get from local database
    return await _localDatabaseService.getSignedDocumentById(id);
  }

  // ==================== USER SETTINGS OPERATIONS ====================

  /// Get user settings (offline-first)
  Future<UserSettings> getUserSettings() async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final settings = await _supabaseService.getUserSettings();

        // Update local database
        await _localDatabaseService.insertUserSettings(
          settings,
          syncStatus: 'synced',
        );

        return settings;
      } catch (e) {
        debugPrint(
          'Failed to get user settings from Supabase: ${e.toString()}',
        );
        // Fall back to local database
      }
    }

    // Get from local database
    final localSettings = await _localDatabaseService.getUserSettings(userId);

    if (localSettings != null) {
      return localSettings;
    }

    // If no settings exist locally, create default settings
    final defaultSettings = UserSettings(
      userId: userId,
      defaultLiveJobCostSync: false,
    );

    // Save default settings locally
    await _localDatabaseService.insertUserSettings(defaultSettings);

    return defaultSettings;
  }

  /// Update user settings (offline-first)
  Future<void> updateUserSettings(UserSettings settings) async {
    // Update locally first
    final updatedSettings = settings.copyWith(updatedAt: DateTime.now());

    await _localDatabaseService.insertUserSettings(
      updatedSettings,
      syncStatus: SyncStatus.pending.name,
    );

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateUserSettings(updatedSettings);
        await _localDatabaseService.updateUserSettingsSyncStatus(
          settings.id,
          SyncStatus.synced.name,
        );
      } catch (e) {
        debugPrint('Failed to sync user settings: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  // ==================== USER PROFILE OPERATIONS ====================

  /// Get user profile (offline-first)
  Future<UserProfile?> getUserProfile() async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    if (await isOnline()) {
      try {
        // If online, get from Supabase and update local cache
        final profile = await _supabaseService.getUserProfile();

        if (profile != null) {
          // Update local database
          await _localDatabaseService.insertUserProfile(
            profile,
            syncStatus: 'synced',
          );

          return profile;
        }
      } catch (e) {
        debugPrint('Failed to get user profile from Supabase: ${e.toString()}');
        // Fall back to local database
      }
    }

    // Get from local database
    return await _localDatabaseService.getUserProfile(userId);
  }

  /// Create user profile (offline-first)
  Future<void> createUserProfile(UserProfile profile) async {
    // Create locally first
    await _localDatabaseService.insertUserProfile(
      profile,
      syncStatus: SyncStatus.pending.name,
    );

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.createUserProfile(profile);
        await _localDatabaseService.updateUserProfileSyncStatus(
          profile.id,
          SyncStatus.synced.name,
        );
      } catch (e) {
        debugPrint('Failed to sync user profile: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  /// Update user profile (offline-first)
  Future<void> updateUserProfile(UserProfile profile) async {
    // Update locally first
    final updatedProfile = profile.copyWith(updatedAt: DateTime.now());

    await _localDatabaseService.insertUserProfile(
      updatedProfile,
      syncStatus: SyncStatus.pending.name,
    );

    // If online, try to sync immediately
    if (await isOnline()) {
      try {
        await _supabaseService.updateUserProfile(updatedProfile);
        await _localDatabaseService.updateUserProfileSyncStatus(
          profile.id,
          SyncStatus.synced.name,
        );
      } catch (e) {
        debugPrint('Failed to sync user profile: ${e.toString()}');
        // Keep as pending in local database
      }
    }
  }

  /// Check if user has completed onboarding
  Future<bool> isOnboardingComplete() async {
    try {
      final profile = await getUserProfile();
      return profile?.isOnboardingComplete ?? false;
    } catch (e) {
      debugPrint('Failed to check onboarding status: ${e.toString()}');
      return false;
    }
  }

  // ==================== USER SIGNATURE OPERATIONS ====================

  /// Store user signature locally (offline-first)
  Future<void> storeUserSignatureLocally(Uint8List signatureBytes) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Store signature locally with pending sync status
      await _localDatabaseService.storeUserSignature(
        userId: userId,
        signatureBytes: signatureBytes,
        syncStatus: SyncStatus.pending.name,
      );

      debugPrint('User signature stored locally');
    } catch (e) {
      debugPrint('Failed to store user signature locally: $e');
      throw Exception('Failed to store signature locally: $e');
    }
  }

  /// Get user signature bytes (offline-first)
  Future<Uint8List?> getUserSignatureBytes() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Always try local first
      final localSignature = await _localDatabaseService.getUserSignature(
        userId,
      );
      if (localSignature != null) {
        return localSignature;
      }

      // If online and no local signature, try to get from server
      if (await isOnline()) {
        try {
          final profile = await _supabaseService.getUserProfile();
          if (profile?.signatureImageUrl != null) {
            // Download signature from server and store locally
            final signatureStorageService = SignatureStorageService();
            final signatureBytes = await signatureStorageService
                .downloadSignature(profile!.signatureImageUrl!);

            if (signatureBytes != null) {
              // Store locally for future offline access
              await _localDatabaseService.storeUserSignature(
                userId: userId,
                signatureBytes: signatureBytes,
                syncStatus: SyncStatus.synced.name,
              );
              return signatureBytes;
            }
          }
        } catch (e) {
          debugPrint('Failed to download signature from server: $e');
        }
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get user signature: $e');
      return null;
    }
  }

  /// Check if user has a signature (offline-first)
  Future<bool> hasUserSignature() async {
    try {
      final signatureBytes = await getUserSignatureBytes();
      return signatureBytes != null && signatureBytes.isNotEmpty;
    } catch (e) {
      debugPrint('Failed to check user signature: $e');
      return false;
    }
  }
}
