import 'package:flutter/foundation.dart';
import '../models/expense.dart';
import '../models/time_log.dart';
import 'data_repository.dart';

/// Service for handling budget calculations and analysis
class BudgetService {
  final DataRepository _dataRepository;

  BudgetService(this._dataRepository);

  /// Calculate budget data for a specific job
  Future<BudgetData> getBudgetData(String jobId) async {
    try {
      // Get job details
      final job = await _dataRepository.getJobById(jobId);
      if (job == null) {
        throw Exception('Job not found');
      }

      // Get expenses and time logs for this job (offline-first)
      final expenses = await _dataRepository.getExpensesByJob(jobId);
      final timeLogs = await _dataRepository.getTimeLogsByJob(jobId);

      // Calculate actual costs
      final totalExpenses = expenses.fold(
        0.0,
        (sum, expense) => sum + expense.amount,
      );
      final totalLaborCost = timeLogs.fold(
        0.0,
        (sum, timeLog) => sum + timeLog.laborCost,
      );
      final totalActualCosts = totalExpenses + totalLaborCost;

      // Get budget (estimated expenses budget)
      final budget = job.estimatedExpensesBudget ?? 0.0;

      // Calculate budget status
      final budgetStatus = _calculateBudgetStatus(budget, totalActualCosts);

      return BudgetData(
        jobId: jobId,
        budget: budget,
        totalActualCosts: totalActualCosts,
        totalExpenses: totalExpenses,
        totalLaborCost: totalLaborCost,
        budgetStatus: budgetStatus,
        expenses: expenses,
        timeLogs: timeLogs,
      );
    } catch (e) {
      debugPrint('Error getting budget data: $e');
      rethrow;
    }
  }

  /// Calculate budget status based on actual costs vs budget
  BudgetStatus _calculateBudgetStatus(double budget, double actualCosts) {
    if (budget <= 0) {
      return BudgetStatus.noBudget;
    }

    final percentage = (actualCosts / budget) * 100;

    if (percentage >= 100) {
      return BudgetStatus.overBudget;
    } else if (percentage >= 80) {
      return BudgetStatus.nearBudget;
    } else {
      return BudgetStatus.underBudget;
    }
  }

  /// Get budget alert message based on status
  String getBudgetAlertMessage(BudgetStatus status, double percentage) {
    switch (status) {
      case BudgetStatus.overBudget:
        return 'Over budget by ${(percentage - 100).toStringAsFixed(1)}%';
      case BudgetStatus.nearBudget:
        return 'Approaching budget limit (${percentage.toStringAsFixed(1)}% used)';
      case BudgetStatus.underBudget:
        return 'Within budget (${percentage.toStringAsFixed(1)}% used)';
      case BudgetStatus.noBudget:
        return 'No budget set for this job';
    }
  }
}

/// Data class for budget information
class BudgetData {
  final String jobId;
  final double budget;
  final double totalActualCosts;
  final double totalExpenses;
  final double totalLaborCost;
  final BudgetStatus budgetStatus;
  final List<Expense> expenses;
  final List<TimeLog> timeLogs;

  BudgetData({
    required this.jobId,
    required this.budget,
    required this.totalActualCosts,
    required this.totalExpenses,
    required this.totalLaborCost,
    required this.budgetStatus,
    required this.expenses,
    required this.timeLogs,
  });

  /// Calculate percentage of budget used
  double get budgetUsedPercentage {
    if (budget <= 0) return 0.0;
    return (totalActualCosts / budget) * 100;
  }

  /// Calculate remaining budget
  double get remainingBudget {
    return budget - totalActualCosts;
  }

  /// Check if budget is set
  bool get hasBudget => budget > 0;
}

/// Enum for budget status
enum BudgetStatus { underBudget, nearBudget, overBudget, noBudget }
