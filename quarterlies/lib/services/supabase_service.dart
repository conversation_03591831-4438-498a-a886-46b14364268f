import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/models.dart'; // This already includes mileage.dart
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class SupabaseService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final SupabaseClient _client = Supabase.instance.client;

  // Public getter for the Supabase client
  SupabaseClient get client => _supabaseClient;

  // ==================== CONTRACT OPERATIONS ====================

  // Add a new contract
  Future<void> addContract(Contract contract) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('contracts')
                .insert(contract.toJson())
                .select('id')
                .single();

        // If contract has line items, insert them with the contract ID
        if (contract.lineItems.isNotEmpty) {
          final contractWithId = contract.copyWith(id: response['id']);

          // Insert each line item
          for (var item in contractWithId.lineItems) {
            final itemMap = item.toJson();
            itemMap['contract_id'] = contractWithId.id;

            await _supabaseClient.from('contract_items').insert(itemMap);
          }
        }
        return response;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addContract',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all contracts for the current user
  Future<List<Contract>> getContracts() async {
    final result = await RetryMechanism.execute(
      () async {
        final currentUser = _supabaseClient.auth.currentUser;
        if (currentUser == null) {
          throw Exception('User not authenticated');
        }

        final response = await _supabaseClient
            .from('contracts')
            .select('*, contract_items(*)')
            .eq('user_id', currentUser.id)
            .order('created_at', ascending: false);

        return response.map((json) {
          // Extract line items from the response
          final lineItems = json['contract_items'] ?? [];

          // Remove contract_items from the main JSON
          json.remove('contract_items');

          // Add line_items in the format expected by Contract.fromJson
          json['line_items'] = lineItems;

          return Contract.fromJson(json);
        }).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getContracts',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get contracts with pagination support
  Future<List<Contract>> getContractsPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('contracts')
            .select('*, contract_items(*)')
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) {
          // Extract line items from the response
          final lineItems = json['contract_items'] ?? [];

          // Remove contract_items from the main JSON
          json.remove('contract_items');

          // Add line_items in the format expected by Contract.fromJson
          json['line_items'] = lineItems;

          return Contract.fromJson(json);
        }).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getContractsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get a contract by ID
  Future<Contract> getContractById(String id) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('contracts')
                .select('*, contract_items(*)')
                .eq('id', id)
                .single();

        // Extract line items from the response
        final lineItems = response['contract_items'] ?? [];

        // Remove contract_items from the main JSON
        response.remove('contract_items');

        // Add line_items in the format expected by Contract.fromJson
        response['line_items'] = lineItems;

        return Contract.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getContractById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing contract
  Future<void> updateContract(Contract contract) async {
    final result = await RetryMechanism.execute(
      () async {
        // Update the contract
        await _supabaseClient
            .from('contracts')
            .update(contract.toJson())
            .eq('id', contract.id);

        // Delete existing line items
        await _supabaseClient
            .from('contract_items')
            .delete()
            .eq('contract_id', contract.id);

        // Insert updated line items
        for (var item in contract.lineItems) {
          final itemMap = item.toJson();
          itemMap['contract_id'] = contract.id;

          await _supabaseClient.from('contract_items').insert(itemMap);
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateContract',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // ==================== MILEAGE OPERATIONS ====================

  // Add a new mileage entry
  Future<void> addMileage(Mileage mileage) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('expenses').insert(mileage.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addMileage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all mileage entries for the current user
  Future<List<Mileage>> getMileageEntries() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('type', 'Mileage')
            .order('date', ascending: false);

        return response.map((json) => Mileage.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getMileageEntries',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get mileage entries for a specific job
  Future<List<Mileage>> getMileageByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('job_id', jobId)
            .eq('type', 'Mileage')
            .order('date', ascending: false);

        return response.map((json) => Mileage.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getMileageByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing mileage entry
  Future<void> updateMileage(Mileage mileage) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('expenses')
            .update(mileage.toJson())
            .eq('id', mileage.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateMileage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a mileage entry
  Future<void> deleteMileage(String mileageId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('expenses')
            .delete()
            .eq('id', mileageId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteMileage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get the current IRS mileage rate
  Future<double?> getMileageRate() async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('app_settings')
                .select('value')
                .eq('key', 'irs_mileage_rate')
                .single();

        return double.tryParse(response['value']);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getMileageRate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return null if not found, will use default rate
      return null;
    }

    return result.data;
  }

  // ==================== CUSTOMER OPERATIONS ====================

  // Add a new customer
  Future<void> addCustomer(Customer customer) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('customers').insert(customer.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addCustomer',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all customers for the current user
  Future<List<Customer>> getCustomers() async {
    final result = await RetryMechanism.execute(
      () async {
        final currentUser = _supabaseClient.auth.currentUser;
        if (currentUser == null) {
          throw Exception('User not authenticated');
        }

        final response = await _supabaseClient
            .from('customers')
            .select()
            .eq('user_id', currentUser.id)
            .order('name');

        return response.map((json) => Customer.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomers',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get customers with pagination support
  Future<List<Customer>> getCustomersPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('customers')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('name')
            .range(offset, offset + limit - 1);

        return response.map((json) => Customer.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomersPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get total number of customers
  Future<int> getCustomersCount() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('customers')
            .select('id')
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        return response.length;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomersCount',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing customer
  Future<void> updateCustomer(Customer customer) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('customers')
            .update(customer.toJson())
            .eq('id', customer.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateCustomer',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a customer
  Future<void> deleteCustomer(String customerId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('customers')
            .delete()
            .eq('id', customerId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteCustomer',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get a single customer by ID
  Future<Customer> getCustomerById(String customerId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _client
                .from('customers')
                .select()
                .eq('id', customerId)
                .single();

        return Customer.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomerById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== JOB OPERATIONS ====================

  // Add a new job
  Future<void> addJob(Job job) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('jobs').insert(job.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all jobs for the current user
  Future<List<Job>> getJobs() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('jobs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Job.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getJobs',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get jobs with pagination support
  Future<List<Job>> getJobsPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('jobs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => Job.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getJobsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== TIME LOG OPERATIONS ====================

  // Add a new time log
  Future<void> addTimeLog(TimeLog timeLog) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('time_logs').insert(timeLog.toJson());

        // Update job's actual labor cost
        await updateJobLaborCost(timeLog.jobId);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addTimeLog',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all time logs for the current user
  Future<List<TimeLog>> getTimeLogs() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date', ascending: false);

        return response.map((json) => TimeLog.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTimeLogs',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get time logs for a specific job
  Future<List<TimeLog>> getTimeLogsByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('job_id', jobId)
            .order('date', ascending: false);

        return response.map((json) => TimeLog.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTimeLogsByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get time logs with pagination support
  Future<List<TimeLog>> getTimeLogsPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => TimeLog.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTimeLogsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing time log
  Future<void> updateTimeLog(TimeLog timeLog) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('time_logs')
            .update(timeLog.toJson())
            .eq('id', timeLog.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        // Update job's actual labor cost
        await updateJobLaborCost(timeLog.jobId);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateTimeLog',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a time log
  Future<void> deleteTimeLog(String timeLogId) async {
    final result = await RetryMechanism.execute(
      () async {
        // First get the time log to know which job to update
        final timeLog =
            await _supabaseClient
                .from('time_logs')
                .select()
                .eq('id', timeLogId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        final jobId = timeLog['job_id'];

        // Delete the time log
        await _supabaseClient
            .from('time_logs')
            .delete()
            .eq('id', timeLogId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        // Update job's actual labor cost
        await updateJobLaborCost(jobId);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteTimeLog',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Helper method to update a job's actual labor cost based on time logs
  Future<void> updateJobLaborCost(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get all time logs for the job
        final timeLogs = await getTimeLogsByJob(jobId);

        // Calculate total labor cost
        double totalLaborCost = 0.0;
        for (var timeLog in timeLogs) {
          totalLaborCost += timeLog.laborCost;
        }

        // Get the current job
        final job = await getJobById(jobId);

        // Update the job with new labor cost
        final updatedJob = job.copyWith(actualLaborCost: totalLaborCost);
        await updateJob(updatedJob);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobLaborCost',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get jobs for a specific customer
  Future<List<Job>> getJobsByCustomer(String customerId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('jobs')
            .select()
            .eq('customer_id', customerId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Job.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getJobsByCustomer',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing job
  Future<void> updateJob(Job job) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update(job.toJson())
            .eq('id', job.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update just the notes/description of a job
  Future<void> updateJobNotes(String jobId, String notes) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({
              'description': notes,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobNotes',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a job
  Future<void> deleteJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .delete()
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get a single job by ID
  Future<Job> getJobById(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('jobs')
                .select()
                .eq('id', jobId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return Job.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getJobById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== ESTIMATE OPERATIONS ====================

  // Add a new estimate
  Future<void> addEstimate(Estimate estimate) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('estimates').insert(estimate.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addEstimate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all estimates for the current user
  Future<List<Estimate>> getEstimates() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimates',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get estimates with pagination support
  Future<List<Estimate>> getEstimatesPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimatesPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get estimates for a specific job
  Future<List<Estimate>> getEstimatesByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('job_id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimatesByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing estimate
  Future<void> updateEstimate(Estimate estimate) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('estimates')
            .update(estimate.toJson())
            .eq('id', estimate.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateEstimate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete an estimate
  Future<void> deleteEstimate(String estimateId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('estimates')
            .delete()
            .eq('id', estimateId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteEstimate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get a single estimate by ID
  Future<Estimate> getEstimateById(String estimateId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('estimates')
                .select()
                .eq('id', estimateId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return Estimate.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimateById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get estimate templates
  Future<List<Estimate>> getEstimateTemplates() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('is_template', true)
            .order('created_at', ascending: false);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimateTemplates',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Create an estimate template from an existing estimate
  Future<String> createEstimateTemplate(
    Estimate estimate,
    String templateName,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Create a new template based on the existing estimate
        final template = estimate.copyWith(
          id: null, // Generate a new ID
          isTemplate: true,
          templateName: templateName,
          status: 'template',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Insert the template into the database
        final response =
            await _supabaseClient
                .from('estimates')
                .insert(template.toJson())
                .select('id')
                .single();

        return response['id'] as String;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'createEstimateTemplate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== EXPENSE OPERATIONS ====================

  // Add a new expense
  Future<void> addExpense(Expense expense) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('expenses').insert(expense.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addExpense',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all expenses for the current user
  Future<List<Expense>> getExpenses() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpenses',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get expenses with pagination support
  Future<List<Expense>> getExpensesPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get expenses for a specific job
  Future<List<Expense>> getExpensesByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('job_id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all overhead expenses (not tied to a specific job)
  Future<List<Expense>> getOverheadExpenses() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('is_overhead', true)
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getOverheadExpenses',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get expenses by IRS Schedule C category
  Future<List<Expense>> getExpensesByCategory(
    String category, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        var query = _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('category', category);

        // Add date range filters if provided
        if (startDate != null) {
          query = query.gte('date', startDate.toIso8601String());
        }

        if (endDate != null) {
          query = query.lte('date', endDate.toIso8601String());
        }

        final response = await query.order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesByCategory',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all expenses grouped by IRS Schedule C categories for tax reporting
  Future<Map<String, List<Expense>>> getExpensesByScheduleC({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get all expenses for the period
        var query = _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        // Add date range filters if provided
        if (startDate != null) {
          query = query.gte('date', startDate.toIso8601String());
        }

        if (endDate != null) {
          query = query.lte('date', endDate.toIso8601String());
        }

        final response = await query.order('date', ascending: false);
        final expenses =
            response.map((json) => Expense.fromJson(json)).toList();

        // Group expenses by category
        final Map<String, List<Expense>> categorizedExpenses = {};

        // Initialize all categories with empty lists
        for (var category in ExpenseCategory.values) {
          categorizedExpenses[category] = [];
        }

        // Add a category for uncategorized expenses
        categorizedExpenses['Uncategorized'] = [];

        // Group expenses by category
        for (var expense in expenses) {
          final category = expense.category;
          if (category != null && categorizedExpenses.containsKey(category)) {
            categorizedExpenses[category]!.add(expense);
          } else {
            categorizedExpenses['Uncategorized']!.add(expense);
          }
        }

        return categorizedExpenses;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesByScheduleC',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get overhead expenses for a specific date range
  Future<List<Expense>> getOverheadExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('is_overhead', true)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String())
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getOverheadExpensesByDateRange',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing expense
  Future<void> updateExpense(Expense expense) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('expenses')
            .update(expense.toJson())
            .eq('id', expense.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateExpense',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete an expense
  Future<void> deleteExpense(String expenseId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('expenses')
            .delete()
            .eq('id', expenseId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteExpense',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get a single expense by ID
  Future<Expense> getExpenseById(String expenseId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('expenses')
                .select()
                .eq('id', expenseId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return Expense.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpenseById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== PAYMENT OPERATIONS ====================

  // Add a new payment
  Future<void> addPayment(Payment payment) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('payments').insert(payment.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addPayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all payments for the current user
  Future<List<Payment>> getPayments() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false);

        return response.map((json) => Payment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getPayments',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get payments with pagination support
  Future<List<Payment>> getPaymentsPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => Payment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getPaymentsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get payments for a specific job
  Future<List<Payment>> getPaymentsByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('payments')
            .select()
            .eq('job_id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false);

        return response.map((json) => Payment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getPaymentsByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get payments for a specific invoice
  Future<List<Payment>> getPaymentsByInvoice(String invoiceId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('payments')
            .select()
            .eq('invoice_id', invoiceId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false);

        return response.map((json) => Payment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getPaymentsByInvoice',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing payment
  Future<void> updatePayment(Payment payment) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('payments')
            .update(payment.toJson())
            .eq('id', payment.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updatePayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a payment
  Future<void> deletePayment(String paymentId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('payments')
            .delete()
            .eq('id', paymentId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deletePayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // ==================== USER SETTINGS OPERATIONS ====================

  // Get user settings
  Future<UserSettings> getUserSettings() async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('user_settings')
                .select()
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return UserSettings.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getUserSettings',
    );

    if (!result.isSuccess) {
      // If no settings exist, create default settings
      final defaultSettings = UserSettings(
        userId: _supabaseClient.auth.currentUser!.id,
        defaultLiveJobCostSync: true,
      );

      final createResult = await RetryMechanism.execute(
        () async {
          await _supabaseClient
              .from('user_settings')
              .insert(defaultSettings.toJson());
          return defaultSettings;
        },
        config: RetryMechanism.databaseRetryConfig(),
        operationName: 'createDefaultUserSettings',
      );

      if (!createResult.isSuccess) {
        final appError = createResult.error!;
        ErrorHandler.logError(appError);
        throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
      }

      return createResult.data!;
    }

    return result.data!;
  }

  // Update user settings
  Future<void> updateUserSettings(UserSettings settings) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('user_settings')
            .update(settings.toJson())
            .eq('id', settings.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateUserSettings',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job live cost sync setting
  Future<void> updateJobLiveCostSync(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'live_cost_sync_enabled': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobLiveCostSync',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job sync estimate items setting
  Future<void> updateJobSyncEstimateItems(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'sync_estimate_items': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSyncEstimateItems',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job sync expenses setting (controls whether other expenses are included in invoices)
  Future<void> updateJobSyncExpenses(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'sync_expenses': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSyncExpenses',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job sync mileage setting (controls whether mileage entries are included in invoices)
  Future<void> updateJobSyncMileage(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'sync_mileage': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSyncMileage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job summarize mileage setting (controls whether mileage is shown as a summary or individual entries)
  Future<void> updateJobSummarizeMileage(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'summarize_mileage': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSummarizeMileage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job summarize hours setting (controls whether hours are shown as a summary or individual entries)
  Future<void> updateJobSummarizeHours(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'summarize_hours': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSummarizeHours',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update job sync labor costs setting (controls whether time logs are included in invoices)
  Future<void> updateJobSyncLaborCosts(String jobId, bool enabled) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('jobs')
            .update({'sync_labor_costs': enabled})
            .eq('id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateJobSyncLaborCosts',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // ==================== INVOICE OPERATIONS ====================

  // Add a new invoice
  Future<void> addInvoice(Invoice invoice) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('invoices')
                .insert(invoice.toJson())
                .select('id')
                .single();

        // Always update the tracking fields for items in the invoice
        if (invoice.lineItems != null) {
          // Create a copy of the invoice with the generated ID
          final invoiceWithId = invoice.copyWith(id: response['id']);
          await _markItemsAsInvoiced(invoiceWithId);
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addInvoice',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all invoices for the current user
  Future<List<Invoice>> getInvoices() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoices',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get invoices with pagination support
  Future<List<Invoice>> getInvoicesPaginated(int offset, int limit) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoicesPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get invoices for a specific job
  Future<List<Invoice>> getInvoicesByJob(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('job_id', jobId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoicesByJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get invoices for a specific customer
  Future<List<Invoice>> getInvoicesByCustomer(String customerId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('customer_id', customerId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('issue_date', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoicesByCustomer',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing invoice
  Future<void> updateInvoice(
    dynamic invoiceOrId, {
    Map<String, dynamic>? data,
    bool markItemsAsInvoiced = true,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        if (invoiceOrId is Invoice) {
          // Original method implementation for Invoice object
          await _supabaseClient
              .from('invoices')
              .update(invoiceOrId.toJson())
              .eq('id', invoiceOrId.id!)
              .eq('user_id', _supabaseClient.auth.currentUser!.id);

          // Always update the tracking fields for items in the invoice
          if (markItemsAsInvoiced && invoiceOrId.lineItems != null) {
            await _markItemsAsInvoiced(invoiceOrId);
          }
        } else if (invoiceOrId is String && data != null) {
          // Overload for String ID and Map data
          await _supabaseClient
              .from('invoices')
              .update(data)
              .eq('id', invoiceOrId)
              .eq('user_id', _supabaseClient.auth.currentUser!.id);

          // If we need to mark items as invoiced, we need to get the full invoice
          if (markItemsAsInvoiced) {
            final invoice = await getInvoiceById(invoiceOrId);
            if (invoice != null) {
              await _markItemsAsInvoiced(invoice);
            }
          }
        } else {
          throw Exception('Invalid parameters for updateInvoice');
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateInvoice',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Mark items as invoiced when an invoice is created or updated
  Future<void> _markItemsAsInvoiced(Invoice invoice) async {
    final result = await RetryMechanism.execute(
      () async {
        final jobId = invoice.jobId;
        final invoiceId = invoice.id;
        final invoiceNumber =
            invoice.id != null && invoice.id!.length >= 8
                ? invoice.id!.substring(0, 8)
                : 'Unknown';
        final isPaid = invoice.status.toLowerCase() == 'paid';

        // Get all expenses for this job
        final expenses = await getExpensesByJob(jobId);

        // Get all time logs for this job
        final timeLogs = await getTimeLogsByJob(jobId);

        // First, clear any pending invoice references for items that are no longer in this invoice
        // This handles the case where items were removed from the invoice
        await _clearRemovedItemsFromInvoice(invoice, expenses, timeLogs);

        // Process regular expenses and mileage
        for (final item in invoice.lineItems!) {
          // Skip excluded items - they should not be marked as invoiced
          if (item.excluded == true) continue;

          // Store the source ID in the invoice item for better tracking
          if (item.sourceId != null) {
            // If we have a source ID, use it for direct matching
            if (item.type == 'expense' || item.type == 'mileage') {
              // Find the expense by ID
              final matchingExpense = expenses.firstWhere(
                (expense) => expense.id == item.sourceId,
                orElse:
                    () => Expense(
                      userId: '',
                      jobId: '',
                      description: '',
                      amount: 0,
                      date: DateTime.now(),
                    ),
              );

              if (matchingExpense.id.isNotEmpty) {
                final updatedExpense =
                    isPaid
                        ? matchingExpense.copyWith(
                          invoicedInId: invoiceId,
                          pendingInvoiceId: null,
                          pendingInvoiceNumber: null,
                        )
                        : matchingExpense.copyWith(
                          pendingInvoiceId: invoiceId,
                          pendingInvoiceNumber: invoiceNumber,
                        );
                await updateExpense(updatedExpense);
              }
            } else if (item.type == 'labor') {
              // Find the time log by ID
              final matchingTimeLog = timeLogs.firstWhere(
                (timeLog) => timeLog.id == item.sourceId,
                orElse:
                    () => TimeLog(
                      userId: '',
                      jobId: '',
                      date: DateTime.now(),
                      hours: 0,
                      hourlyRate: 0,
                      laborCost: 0,
                    ),
              );

              if (matchingTimeLog.id.isNotEmpty) {
                final updatedTimeLog =
                    isPaid
                        ? matchingTimeLog.copyWith(
                          invoicedInId: invoiceId,
                          pendingInvoiceId: null,
                          pendingInvoiceNumber: null,
                        )
                        : matchingTimeLog.copyWith(
                          pendingInvoiceId: invoiceId,
                          pendingInvoiceNumber: invoiceNumber,
                        );
                await updateTimeLog(updatedTimeLog);
              }
            }
          } else {
            // If we don't have a source ID, use the legacy matching logic
            if (item.type == 'expense') {
              // Find matching expenses
              final matchingExpenses =
                  expenses
                      .where(
                        (expense) =>
                            expense is! Mileage &&
                            item.description.contains(expense.description) &&
                            (item.unitPrice - expense.amount).abs() < 0.01 &&
                            (isPaid ? expense.invoicedInId == null : true),
                      )
                      .toList();

              // Update each matching expense
              for (final expense in matchingExpenses) {
                final updatedExpense =
                    isPaid
                        ? expense.copyWith(
                          invoicedInId: invoiceId,
                          pendingInvoiceId: null,
                          pendingInvoiceNumber: null,
                        )
                        : expense.copyWith(
                          pendingInvoiceId: invoiceId,
                          pendingInvoiceNumber: invoiceNumber,
                        );
                await updateExpense(updatedExpense);
              }
            } else if (item.type == 'mileage') {
              if (item.description.contains('Mileage: Total for')) {
                // This is a summary mileage item - mark all mileage entries
                final mileageEntries =
                    expenses
                        .where(
                          (expense) =>
                              expense is Mileage &&
                              !expense.isOverhead &&
                              (isPaid ? expense.invoicedInId == null : true),
                        )
                        .toList();

                // Update each mileage entry
                for (final mileage in mileageEntries) {
                  final updatedMileage =
                      isPaid
                          ? mileage.copyWith(
                            invoicedInId: invoiceId,
                            pendingInvoiceId: null,
                            pendingInvoiceNumber: null,
                          )
                          : mileage.copyWith(
                            pendingInvoiceId: invoiceId,
                            pendingInvoiceNumber: invoiceNumber,
                          );
                  await updateExpense(updatedMileage);
                }
              } else {
                // This is an individual mileage item
                final matchingMileage =
                    expenses
                        .where(
                          (expense) =>
                              expense is Mileage &&
                              item.description.contains(expense.description) &&
                              (item.unitPrice - expense.amount).abs() < 0.01 &&
                              (isPaid ? expense.invoicedInId == null : true),
                        )
                        .toList();

                // Update each matching mileage entry
                for (final mileage in matchingMileage) {
                  final updatedMileage =
                      isPaid
                          ? mileage.copyWith(
                            invoicedInId: invoiceId,
                            pendingInvoiceId: null,
                            pendingInvoiceNumber: null,
                          )
                          : mileage.copyWith(
                            pendingInvoiceId: invoiceId,
                            pendingInvoiceNumber: invoiceNumber,
                          );
                  await updateExpense(updatedMileage);
                }
              }
            } else if (item.type == 'labor') {
              if (item.description.contains('Labor: Total hours')) {
                // This is a summary labor item - mark all time logs
                final timeLogEntries =
                    timeLogs
                        .where(
                          (timeLog) =>
                              isPaid ? timeLog.invoicedInId == null : true,
                        )
                        .toList();

                // Update each time log
                for (final timeLog in timeLogEntries) {
                  final updatedTimeLog =
                      isPaid
                          ? timeLog.copyWith(
                            invoicedInId: invoiceId,
                            pendingInvoiceId: null,
                            pendingInvoiceNumber: null,
                          )
                          : timeLog.copyWith(
                            pendingInvoiceId: invoiceId,
                            pendingInvoiceNumber: invoiceNumber,
                          );
                  await updateTimeLog(updatedTimeLog);
                }
              } else {
                // This is an individual labor item
                final matchingTimeLogs =
                    timeLogs
                        .where(
                          (timeLog) =>
                              ((timeLog.notes != null &&
                                      item.description.contains(
                                        timeLog.notes!,
                                      )) ||
                                  item.description.contains(
                                    timeLog.id.substring(0, 8),
                                  )) &&
                              (item.quantity - timeLog.hours).abs() < 0.01 &&
                              (item.unitPrice - timeLog.hourlyRate).abs() <
                                  0.01 &&
                              (isPaid ? timeLog.invoicedInId == null : true),
                        )
                        .toList();

                // Update each matching time log
                for (final timeLog in matchingTimeLogs) {
                  final updatedTimeLog =
                      isPaid
                          ? timeLog.copyWith(
                            invoicedInId: invoiceId,
                            pendingInvoiceId: null,
                            pendingInvoiceNumber: null,
                          )
                          : timeLog.copyWith(
                            pendingInvoiceId: invoiceId,
                            pendingInvoiceNumber: invoiceNumber,
                          );
                  await updateTimeLog(updatedTimeLog);
                }
              }
            }
          }
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: '_markItemsAsInvoiced',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Clear pending invoice references for items that were removed from an invoice
  Future<void> _clearRemovedItemsFromInvoice(
    Invoice invoice,
    List<Expense> expenses,
    List<TimeLog> timeLogs,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final invoiceId = invoice.id;
        if (invoiceId == null) return;

        // Find expenses that reference this invoice but are not in the line items
        final expensesToClear =
            expenses.where((expense) {
              // Skip if not referencing this invoice
              if (expense.pendingInvoiceId != invoiceId) return false;

              // Check if this expense is still in the line items
              bool stillInLineItems = false;
              if (invoice.lineItems != null) {
                for (final item in invoice.lineItems!) {
                  // Skip excluded items
                  if (item.excluded == true) continue;

                  // Direct match by source ID
                  if (item.sourceId == expense.id) {
                    stillInLineItems = true;
                    break;
                  }

                  // Legacy matching by description and amount
                  if ((item.type == 'expense' && expense is! Mileage) ||
                      (item.type == 'mileage' && expense is Mileage)) {
                    if (item.description.contains(expense.description) &&
                        (item.unitPrice - expense.amount).abs() < 0.01) {
                      stillInLineItems = true;
                      break;
                    }
                  }
                }
              }

              // If not in line items, clear the reference
              return !stillInLineItems;
            }).toList();

        // Clear references for expenses
        for (final expense in expensesToClear) {
          final updatedExpense = expense.copyWith(
            pendingInvoiceId: null,
            pendingInvoiceNumber: null,
          );
          await updateExpense(updatedExpense);
        }

        // Find time logs that reference this invoice but are not in the line items
        final timeLogsToClear =
            timeLogs.where((timeLog) {
              // Skip if not referencing this invoice
              if (timeLog.pendingInvoiceId != invoiceId) return false;

              // Check if this time log is still in the line items
              bool stillInLineItems = false;
              if (invoice.lineItems != null) {
                for (final item in invoice.lineItems!) {
                  // Skip excluded items
                  if (item.excluded == true) continue;

                  // Direct match by source ID
                  if (item.sourceId == timeLog.id) {
                    stillInLineItems = true;
                    break;
                  }

                  // Legacy matching by description, hours, and rate
                  if (item.type == 'labor') {
                    if (((timeLog.notes != null &&
                                item.description.contains(timeLog.notes!)) ||
                            item.description.contains(
                              timeLog.id.substring(0, 8),
                            )) &&
                        (item.quantity - timeLog.hours).abs() < 0.01 &&
                        (item.unitPrice - timeLog.hourlyRate).abs() < 0.01) {
                      stillInLineItems = true;
                      break;
                    }
                  }
                }
              }

              // If not in line items, clear the reference
              return !stillInLineItems;
            }).toList();

        // Clear references for time logs
        for (final timeLog in timeLogsToClear) {
          final updatedTimeLog = timeLog.copyWith(
            pendingInvoiceId: null,
            pendingInvoiceNumber: null,
          );
          await updateTimeLog(updatedTimeLog);
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: '_clearRemovedItemsFromInvoice',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Don't throw here as this is a helper method - just log the error
    }
  }

  // Get invoice by ID
  Future<Invoice?> getInvoiceById(String invoiceId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('invoices')
                .select()
                .eq('id', invoiceId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return Invoice.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoiceById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return null;
    }

    return result.data;
  }

  // Get invoice templates
  Future<List<Invoice>> getInvoiceTemplates() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('is_template', true)
            .order('created_at', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoiceTemplates',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Create an invoice template from an existing invoice
  Future<String> createInvoiceTemplate(
    Invoice invoice,
    String templateName,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Create a new template based on the existing invoice
        final template = Invoice(
          userId: invoice.userId,
          jobId: invoice.jobId,
          customerId: invoice.customerId,
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: invoice.totalAmount,
          amountPaid: 0.0,
          status: 'template',
          lineItems: invoice.lineItems,
          notes: invoice.notes,
          isTemplate: true,
          templateName: templateName,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Insert the template into the database
        final response =
            await _supabaseClient
                .from('invoices')
                .insert(template.toJson())
                .select('id')
                .single();

        return response['id'];
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'createInvoiceTemplate',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Generate suggested invoice line items from job expenses and time logs
  // This provides a starting point for the user, who can then choose which items to include
  // Note: Time logs and mileage are not automatically expenses - they are tracked separately
  // and can optionally be added to invoices based on user preference
  Future<Map<String, List<InvoiceItem>>> generateInvoiceItemsFromJob(
    String jobId,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get the job to check sync settings
        final job = await getJobById(jobId);

        // Get all invoices for this job to check which expenses/labor are already paid
        final invoices = await getInvoicesByJob(jobId);

        // Extract all line items from paid invoices to exclude them
        final paidLineItems = <InvoiceItem>[];
        final paidInvoices = <Invoice>[];

        // Track open invoices for reference
        final openInvoices = <Invoice>[];

        for (final invoice in invoices) {
          if (invoice.status.toLowerCase() == 'paid' &&
              invoice.lineItems != null) {
            // Only consider paid invoices for exclusion
            paidLineItems.addAll(invoice.lineItems!);
            paidInvoices.add(invoice);
          } else if (InvoiceStatus.isActive(invoice.status.toLowerCase()) &&
              invoice.lineItems != null) {
            // Track active invoices (draft, open, sent, partially_paid, overdue)
            openInvoices.add(invoice);
          }
        }

        // Also check for items in open invoices that are marked as excluded
        // These should still be available for inclusion in new invoices
        final excludedLineItems = <InvoiceItem>[];
        for (final invoice in openInvoices) {
          if (invoice.lineItems != null) {
            excludedLineItems.addAll(
              invoice.lineItems!.where((item) => item.excluded == true),
            );
          }
        }

        // Create two separate lists for available items and items in open invoices
        List<InvoiceItem> availableItems = [];
        List<InvoiceItem> inOpenInvoiceItems = [];

        // Handle regular expenses (excluding mileage)
        if (job.syncExpenses) {
          // Get expenses for this job
          final expenses = await getExpensesByJob(jobId);

          // Filter out mileage expenses, overhead expenses, and expenses that are already in paid invoices
          final availableExpenses =
              expenses.where((expense) {
                // Skip mileage expenses - they're handled separately
                if (expense is Mileage) {
                  return false;
                }

                // Skip overhead expenses - these should never be included in invoices
                if (expense.isOverhead) {
                  return false;
                }

                // Skip if already in a paid invoice
                if (expense.invoicedInId != null) {
                  return false;
                }

                // Skip if in an open invoice (these will go to the other list)
                if (expense.pendingInvoiceId != null) {
                  return false;
                }

                return true;
              }).toList();

          // Find expenses that are in open invoices
          final expensesInOpenInvoices =
              expenses.where((expense) {
                // Skip mileage expenses - they're handled separately
                if (expense is Mileage) {
                  return false;
                }

                // Skip overhead expenses - these should never be included in invoices
                if (expense.isOverhead) {
                  return false;
                }

                // Skip if already in a paid invoice
                if (expense.invoicedInId != null) {
                  return false;
                }

                // Include if in an open invoice
                return expense.pendingInvoiceId != null;
              }).toList();

          // Create invoice items from available regular expenses
          final availableExpenseItems =
              availableExpenses.map((expense) {
                return InvoiceItem(
                  description: 'Expense: ${expense.description}',
                  quantity: 1,
                  unit: 'item',
                  unitPrice: expense.amount,
                  type: 'expense',
                );
              }).toList();

          // Create invoice items from expenses in open invoices
          final openInvoiceExpenseItems =
              expensesInOpenInvoices.map((expense) {
                return InvoiceItem(
                  description:
                      'Expense: ${expense.description} (In Invoice #${expense.pendingInvoiceNumber})',
                  quantity: 1,
                  unit: 'item',
                  unitPrice: expense.amount,
                  type: 'expense',
                  metadata: {'pendingInvoiceId': expense.pendingInvoiceId},
                );
              }).toList();

          availableItems.addAll(availableExpenseItems);
          inOpenInvoiceItems.addAll(openInvoiceExpenseItems);
        }

        // Handle mileage expenses separately
        if (job.syncMileage) {
          // Get expenses for this job
          final expenses = await getExpensesByJob(jobId);

          // Filter for available mileage expenses (not in any invoice)
          final availableMileage =
              expenses.where((expense) {
                // Only include mileage expenses
                if (expense is! Mileage) {
                  return false;
                }

                // Skip overhead expenses - these should never be included in invoices
                if (expense.isOverhead) {
                  return false;
                }

                // Skip if already in a paid invoice
                if (expense.invoicedInId != null) {
                  return false;
                }

                // Skip if in an open invoice (these will go to the other list)
                if (expense.pendingInvoiceId != null) {
                  return false;
                }

                return true;
              }).toList();

          // Find mileage expenses that are in open invoices
          final mileageInOpenInvoices =
              expenses.where((expense) {
                // Only include mileage expenses
                if (expense is! Mileage) {
                  return false;
                }

                // Skip overhead expenses - these should never be included in invoices
                if (expense.isOverhead) {
                  return false;
                }

                // Skip if already in a paid invoice
                if (expense.invoicedInId != null) {
                  return false;
                }

                // Include if in an open invoice
                return expense.pendingInvoiceId != null;
              }).toList();

          // Create invoice items from available mileage
          if (job.summarizeMileage) {
            // Create a single summary line item for all available mileage
            if (availableMileage.isNotEmpty) {
              final totalMileageAmount = availableMileage.fold(
                0.0,
                (sum, expense) => sum + expense.amount,
              );

              final totalMiles = availableMileage.fold(
                0.0,
                (sum, expense) =>
                    sum + (expense is Mileage ? expense.miles : 0),
              );

              availableItems.add(
                InvoiceItem(
                  description:
                      'Mileage: Total for ${availableMileage.length} trips (${totalMiles.toStringAsFixed(1)} miles)',
                  quantity: 1,
                  unit: 'item',
                  unitPrice: totalMileageAmount,
                  type: 'mileage', // Use a specific type for mileage
                ),
              );
            }

            // Create a single summary line item for mileage in open invoices
            if (mileageInOpenInvoices.isNotEmpty) {
              // Group by invoice number
              final invoiceGroups = <String, List<Expense>>{};
              for (final expense in mileageInOpenInvoices) {
                final invoiceNumber = expense.pendingInvoiceNumber ?? 'Unknown';
                if (!invoiceGroups.containsKey(invoiceNumber)) {
                  invoiceGroups[invoiceNumber] = [];
                }
                invoiceGroups[invoiceNumber]!.add(expense);
              }

              // Create a summary item for each invoice
              for (final entry in invoiceGroups.entries) {
                final invoiceNumber = entry.key;
                final expenses = entry.value;
                final invoiceId = expenses.first.pendingInvoiceId;

                final totalAmount = expenses.fold(
                  0.0,
                  (sum, expense) => sum + expense.amount,
                );

                final totalMiles = expenses.fold(
                  0.0,
                  (sum, expense) =>
                      sum + (expense is Mileage ? expense.miles : 0),
                );

                inOpenInvoiceItems.add(
                  InvoiceItem(
                    description:
                        'Mileage: Total for ${expenses.length} trips (${totalMiles.toStringAsFixed(1)} miles) (In Invoice #$invoiceNumber)',
                    quantity: 1,
                    unit: 'item',
                    unitPrice: totalAmount,
                    type: 'mileage',
                    metadata: {'pendingInvoiceId': invoiceId},
                  ),
                );
              }
            }
          } else {
            // Create individual line items for each available mileage entry
            final availableMileageItems =
                availableMileage.map((expense) {
                  return InvoiceItem(
                    description: 'Mileage: ${expense.description}',
                    quantity: 1,
                    unit: 'item',
                    unitPrice: expense.amount,
                    type: 'mileage',
                  );
                }).toList();

            // Create individual line items for each mileage entry in open invoices
            final openInvoiceMileageItems =
                mileageInOpenInvoices.map((expense) {
                  return InvoiceItem(
                    description:
                        'Mileage: ${expense.description} (In Invoice #${expense.pendingInvoiceNumber})',
                    quantity: 1,
                    unit: 'item',
                    unitPrice: expense.amount,
                    type: 'mileage',
                    metadata: {'pendingInvoiceId': expense.pendingInvoiceId},
                  );
                }).toList();

            availableItems.addAll(availableMileageItems);
            inOpenInvoiceItems.addAll(openInvoiceMileageItems);
          }
        }

        // Only include labor costs if sync is enabled for this job
        // Note: Time logs are for tracking time spent on a job and can optionally be added to invoices
        if (job.syncLaborCosts) {
          // Get time logs for this job
          final timeLogs = await getTimeLogsByJob(jobId);

          // Filter for available time logs (not in any invoice)
          final availableTimeLogs =
              timeLogs.where((timeLog) {
                // Skip if already in a paid invoice
                if (timeLog.invoicedInId != null) {
                  return false;
                }

                // Skip if in an open invoice (these will go to the other list)
                if (timeLog.pendingInvoiceId != null) {
                  return false;
                }

                return true;
              }).toList();

          // Find time logs that are in open invoices
          final timeLogsInOpenInvoices =
              timeLogs.where((timeLog) {
                // Skip if already in a paid invoice
                if (timeLog.invoicedInId != null) {
                  return false;
                }

                // Include if in an open invoice
                return timeLog.pendingInvoiceId != null;
              }).toList();

          // Create invoice items from available time logs
          if (job.summarizeHours) {
            // Create a single summary line item for all available labor
            if (availableTimeLogs.isNotEmpty) {
              final totalHours = availableTimeLogs.fold(
                0.0,
                (sum, timeLog) => sum + timeLog.hours,
              );

              final totalLaborCost = availableTimeLogs.fold(
                0.0,
                (sum, timeLog) => sum + timeLog.laborCost,
              );

              // Calculate the effective hourly rate (total cost / total hours)
              final avgHourlyRate =
                  totalHours > 0
                      ? (totalLaborCost / totalHours)
                      : availableTimeLogs.first.hourlyRate;

              availableItems.add(
                InvoiceItem(
                  description:
                      'Labor: Total hours for ${job.title} (${totalHours.toStringAsFixed(1)} hours)',
                  quantity: totalHours,
                  unit: 'hour',
                  unitPrice: avgHourlyRate,
                  type: 'labor',
                ),
              );
            }

            // Create summary line items for time logs in open invoices, grouped by invoice
            if (timeLogsInOpenInvoices.isNotEmpty) {
              // Group by invoice number
              final invoiceGroups = <String, List<TimeLog>>{};
              for (final timeLog in timeLogsInOpenInvoices) {
                final invoiceNumber = timeLog.pendingInvoiceNumber ?? 'Unknown';
                if (!invoiceGroups.containsKey(invoiceNumber)) {
                  invoiceGroups[invoiceNumber] = [];
                }
                invoiceGroups[invoiceNumber]!.add(timeLog);
              }

              // Create a summary item for each invoice
              for (final entry in invoiceGroups.entries) {
                final invoiceNumber = entry.key;
                final timeLogs = entry.value;
                final invoiceId = timeLogs.first.pendingInvoiceId;

                final totalHours = timeLogs.fold(
                  0.0,
                  (sum, timeLog) => sum + timeLog.hours,
                );

                final totalLaborCost = timeLogs.fold(
                  0.0,
                  (sum, timeLog) => sum + timeLog.laborCost,
                );

                // Calculate the effective hourly rate
                final effectiveHourlyRate =
                    totalHours > 0
                        ? (totalLaborCost / totalHours)
                        : timeLogs.first.hourlyRate;

                inOpenInvoiceItems.add(
                  InvoiceItem(
                    description:
                        'Labor: Total hours for ${job.title} (${totalHours.toStringAsFixed(1)} hours) (In Invoice #$invoiceNumber)',
                    quantity: totalHours,
                    unit: 'hour',
                    unitPrice: effectiveHourlyRate,
                    type: 'labor',
                    metadata: {'pendingInvoiceId': invoiceId},
                  ),
                );
              }
            }
          } else {
            // Create individual line items for each available time log
            final availableTimeLogItems =
                availableTimeLogs
                    .map(
                      (timeLog) => InvoiceItem(
                        description:
                            'Labor: ${timeLog.notes ?? "Time log ${timeLog.id.substring(0, 8)}"} (Optional - from time tracking)',
                        quantity: timeLog.hours,
                        unit: 'hour',
                        unitPrice: timeLog.hourlyRate,
                        type: 'labor',
                      ),
                    )
                    .toList();

            // Create individual line items for each time log in open invoices
            final openInvoiceTimeLogItems =
                timeLogsInOpenInvoices
                    .map(
                      (timeLog) => InvoiceItem(
                        description:
                            'Labor: ${timeLog.notes ?? "Time log ${timeLog.id.substring(0, 8)}"} (In Invoice #${timeLog.pendingInvoiceNumber})',
                        quantity: timeLog.hours,
                        unit: 'hour',
                        unitPrice: timeLog.hourlyRate,
                        type: 'labor',
                        metadata: {
                          'pendingInvoiceId': timeLog.pendingInvoiceId,
                        },
                      ),
                    )
                    .toList();

            availableItems.addAll(availableTimeLogItems);
            inOpenInvoiceItems.addAll(openInvoiceTimeLogItems);
          }
        }

        // Only include estimate line items if sync is enabled for this job
        if (job.syncEstimateItems) {
          // Get estimates for this job
          final estimates = await getEstimatesByJob(jobId);

          // Only use approved estimates
          final approvedEstimates =
              estimates.where((est) => est.status == 'accepted').toList();

          if (approvedEstimates.isNotEmpty) {
            // Use the most recent approved estimate
            final latestEstimate = approvedEstimates.reduce(
              (a, b) =>
                  (a.updatedAt != null &&
                          b.updatedAt != null &&
                          a.updatedAt!.isAfter(b.updatedAt!))
                      ? a
                      : b,
            );

            // Filter for estimate items that are not in any invoice
            final availableEstimateItems =
                latestEstimate.lineItems.where((estimateItem) {
                  // Check if this estimate item is already in a paid invoice
                  bool isAlreadyPaid = paidLineItems.any(
                    (item) =>
                        item.type == 'estimate' &&
                        item.description == estimateItem.description &&
                        (item.unitPrice - estimateItem.unitPrice).abs() <
                            0.01 &&
                        (item.quantity - estimateItem.quantity).abs() < 0.01 &&
                        item.excluded != true,
                  );

                  if (isAlreadyPaid) {
                    return false;
                  }

                  // Check if this item is in any open invoice and not marked as excluded
                  for (final invoice in openInvoices) {
                    if (invoice.lineItems != null) {
                      bool isInOpenInvoice = invoice.lineItems!.any(
                        (item) =>
                            item.type == 'estimate' &&
                            item.description == estimateItem.description &&
                            (item.unitPrice - estimateItem.unitPrice).abs() <
                                0.01 &&
                            (item.quantity - estimateItem.quantity).abs() <
                                0.01 &&
                            item.excluded != true,
                      );

                      if (isInOpenInvoice) {
                        return false;
                      }
                    }
                  }

                  // Include items that are not in any invoice
                  return true;
                }).toList();

            // Find estimate items that are in open invoices
            final estimateItemsInOpenInvoices = <Map<String, dynamic>>[];
            for (final estimateItem in latestEstimate.lineItems) {
              // Skip if already in a paid invoice
              bool isAlreadyPaid = paidLineItems.any(
                (item) =>
                    item.type == 'estimate' &&
                    item.description == estimateItem.description &&
                    (item.unitPrice - estimateItem.unitPrice).abs() < 0.01 &&
                    (item.quantity - estimateItem.quantity).abs() < 0.01 &&
                    item.excluded != true,
              );

              if (isAlreadyPaid) {
                continue;
              }

              // Check if in any open invoice
              for (final invoice in openInvoices) {
                if (invoice.lineItems != null) {
                  for (final item in invoice.lineItems!) {
                    if (item.type == 'estimate' &&
                        item.description == estimateItem.description &&
                        (item.unitPrice - estimateItem.unitPrice).abs() <
                            0.01 &&
                        (item.quantity - estimateItem.quantity).abs() < 0.01 &&
                        item.excluded != true) {
                      // Found in an open invoice
                      estimateItemsInOpenInvoices.add({
                        'estimateItem': estimateItem,
                        'invoiceId': invoice.id,
                        'invoiceNumber':
                            invoice.id != null && invoice.id!.length >= 8
                                ? invoice.id!.substring(0, 8)
                                : 'Unknown',
                      });
                      break;
                    }
                  }
                }
              }
            }

            // Create invoice items from available estimate items
            final availableEstimateLineItems =
                availableEstimateItems
                    .map(
                      (estimateItem) => InvoiceItem(
                        description: estimateItem.description,
                        quantity: estimateItem.quantity,
                        unit: estimateItem.unit,
                        unitPrice: estimateItem.unitPrice,
                        taxRate: estimateItem.taxRate,
                        type: 'estimate',
                      ),
                    )
                    .toList();

            // Create invoice items from estimate items in open invoices
            final openInvoiceEstimateLineItems =
                estimateItemsInOpenInvoices
                    .map(
                      (itemData) => InvoiceItem(
                        description:
                            '${itemData['estimateItem'].description} (In Invoice #${itemData['invoiceNumber']})',
                        quantity: itemData['estimateItem'].quantity,
                        unit: itemData['estimateItem'].unit,
                        unitPrice: itemData['estimateItem'].unitPrice,
                        taxRate: itemData['estimateItem'].taxRate,
                        type: 'estimate',
                        metadata: {'pendingInvoiceId': itemData['invoiceId']},
                      ),
                    )
                    .toList();

            availableItems.addAll(availableEstimateLineItems);
            inOpenInvoiceItems.addAll(openInvoiceEstimateLineItems);
          }
        }

        // Return both categories of items
        return {
          'availableItems': availableItems,
          'inOpenInvoiceItems': inOpenInvoiceItems,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'generateInvoiceItemsFromJob',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Delete an invoice
  Future<void> deleteInvoice(String invoiceId) async {
    final result = await RetryMechanism.execute(
      () async {
        if (invoiceId.isEmpty) {
          debugPrint('Cannot delete invoice with empty ID');
          return;
        }

        // First get the invoice to check its status and job ID
        final invoice = await getInvoiceById(invoiceId);

        if (invoice == null) {
          debugPrint('Invoice not found: $invoiceId');
          return;
        }

        // Delete the invoice
        await _supabaseClient
            .from('invoices')
            .delete()
            .eq('id', invoiceId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        // Clear the pendingInvoiceId field for any items that were in this invoice
        if (invoice.lineItems != null && invoice.jobId.isNotEmpty) {
          // Get all expenses for this job
          final expenses = await getExpensesByJob(invoice.jobId);

          // Get all time logs for this job
          final timeLogs = await getTimeLogsByJob(invoice.jobId);

          // Update expenses
          for (final expense in expenses) {
            if (expense.pendingInvoiceId == invoiceId) {
              final updatedExpense = expense.copyWith(
                pendingInvoiceId: null,
                pendingInvoiceNumber: null,
              );
              await updateExpense(updatedExpense);
            }
          }

          // Update time logs
          for (final timeLog in timeLogs) {
            if (timeLog.pendingInvoiceId == invoiceId) {
              final updatedTimeLog = timeLog.copyWith(
                pendingInvoiceId: null,
                pendingInvoiceNumber: null,
              );
              await updateTimeLog(updatedTimeLog);
            }
          }
        }
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteInvoice',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // ==================== TAX PAYMENT OPERATIONS ====================

  // Add a new tax payment
  Future<void> addTaxPayment(TaxPayment payment) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('tax_payments').insert(payment.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addTaxPayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get all tax payments for the current user (without filters)
  Future<List<TaxPayment>> getAllTaxPayments() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('tax_payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false);

        return response.map((json) => TaxPayment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getAllTaxPayments',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get tax payments filtered by year and quarter
  Future<List<TaxPayment>> getTaxPayments({
    required int year,
    int? quarter,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        var query = _supabaseClient
            .from('tax_payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('tax_year', year);

        if (quarter != null) {
          query = query.eq('tax_quarter', quarter);
        }

        final response = await query.order('payment_date', ascending: false);

        return response.map((json) => TaxPayment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTaxPayments',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get tax payments with pagination support
  Future<List<TaxPayment>> getTaxPaymentsPaginated(
    int offset,
    int limit,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('tax_payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('payment_date', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => TaxPayment.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTaxPaymentsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Update an existing tax payment
  Future<void> updateTaxPayment(TaxPayment payment) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('tax_payments')
            .update(payment.toJson())
            .eq('id', payment.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateTaxPayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Delete a tax payment
  Future<void> deleteTaxPayment(String paymentId) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('tax_payments')
            .delete()
            .eq('id', paymentId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteTaxPayment',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get tax payment by ID
  Future<TaxPayment> getTaxPaymentById(String paymentId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('tax_payments')
                .select()
                .eq('id', paymentId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();
        return TaxPayment.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTaxPaymentById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== ADVANCED DATA RETRIEVAL AND AGGREGATION ====================

  // Call a Supabase database function (RPC)
  Future<dynamic> callDatabaseFunction(
    String functionName,
    Map<String, dynamic> params,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient.rpc(
          functionName,
          params: params,
        );
        return response;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'callDatabaseFunction_$functionName',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data;
  }

  // Get all data for a specific tax period (optimized with joins)
  Future<Map<String, dynamic>> getTaxPeriodData(int year, int quarter) async {
    final result = await RetryMechanism.execute(
      () async {
        // Determine quarter date range
        DateTime startDate;
        DateTime endDate;

        switch (quarter) {
          case 1:
            startDate = DateTime(year, 1, 1);
            endDate = DateTime(year, 3, 31);
            break;
          case 2:
            startDate = DateTime(year, 4, 1);
            endDate = DateTime(year, 6, 30);
            break;
          case 3:
            startDate = DateTime(year, 7, 1);
            endDate = DateTime(year, 9, 30);
            break;
          case 4:
            startDate = DateTime(year, 10, 1);
            endDate = DateTime(year, 12, 31);
            break;
          default:
            throw Exception('Invalid quarter: $quarter');
        }

        // Use a more optimized approach with a single query for payments
        // This could be implemented as a Supabase database function for better performance
        final paymentsData = await _supabaseClient
            .from('payments')
            .select('*, invoices!inner(*)')
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('payment_date', startDate.toIso8601String())
            .lte('payment_date', endDate.toIso8601String());

        // Get expenses with job information
        final expensesData = await _supabaseClient
            .from('expenses')
            .select('*, jobs!inner(*)')
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String());

        // Get tax payments for this period
        final taxPayments = await getTaxPayments(year: year, quarter: quarter);

        // Process the data
        final quarterlyFinancials = await getQuarterlyFinancials(year, quarter);

        return {
          'period': {
            'year': year,
            'quarter': quarter,
            'start_date': startDate,
            'end_date': endDate,
          },
          'payments_data': paymentsData,
          'expenses_data': expensesData,
          'tax_payments': taxPayments,
          'financials': quarterlyFinancials,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTaxPeriodData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all related data for a customer with optimized queries
  Future<Map<String, dynamic>> getCustomerDataOptimized(
    String customerId,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get customer details
        final customer = await getCustomerById(customerId);

        // Get jobs with related invoices in a single query
        final jobsWithInvoices = await _supabaseClient
            .from('jobs')
            .select('*, invoices(*)')
            .eq('customer_id', customerId)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        // Get payments with related invoices in a single query
        final paymentsWithInvoices = await _supabaseClient
            .from('payments')
            .select('*, invoices(*)')
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .inFilter(
              'job_id',
              jobsWithInvoices.map((job) => job['id']).toList(),
            );

        return {
          'customer': customer,
          'jobs_with_invoices': jobsWithInvoices,
          'payments_with_invoices': paymentsWithInvoices,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomerDataOptimized',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all data linked to a specific customer (jobs, estimates, invoices, payments)
  Future<Map<String, dynamic>> getCustomerFullData(String customerId) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get customer details
        final customer = await getCustomerById(customerId);

        // Get all jobs for this customer
        final jobs = await getJobsByCustomer(customerId);

        // Get all invoices for this customer
        final invoices = await getInvoicesByCustomer(customerId);

        // Get all payments for this customer's jobs
        List<Payment> allPayments = [];
        for (final job in jobs) {
          final payments = await getPaymentsByJob(job.id);
          allPayments.addAll(payments);
        }

        // Get all estimates for this customer's jobs
        List<Estimate> allEstimates = [];
        for (final job in jobs) {
          final estimates = await getEstimatesByJob(job.id);
          allEstimates.addAll(estimates);
        }

        return {
          'customer': customer,
          'jobs': jobs,
          'invoices': invoices,
          'payments': allPayments,
          'estimates': allEstimates,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomerFullData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get all data linked to a specific job (estimates, expenses, time logs, payments)
  Future<Map<String, dynamic>> getJobFullData(String jobId) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get job details
        final job = await getJobById(jobId);

        // Get all estimates for this job
        final estimates = await getEstimatesByJob(jobId);

        // Get all expenses for this job
        final expenses = await getExpensesByJob(jobId);

        // Get all time logs for this job
        final timeLogs = await getTimeLogsByJob(jobId);

        // Get all payments for this job
        final payments = await getPaymentsByJob(jobId);

        // Get all invoices for this job
        final invoices = await getInvoicesByJob(jobId);

        return {
          'job': job,
          'estimates': estimates,
          'expenses': expenses,
          'time_logs': timeLogs,
          'payments': payments,
          'invoices': invoices,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getJobFullData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get open estimates, invoices, and overdue payments for the Dashboard
  Future<Map<String, dynamic>> getDashboardData() async {
    final result = await RetryMechanism.execute(
      () async {
        // Get open estimates (status = 'draft' or 'sent')
        final openEstimates = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .inFilter('status', ['draft', 'sent'])
            .order('created_at', ascending: false);

        // Get open invoices (not fully paid)
        final openInvoices = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .neq('status', 'paid')
            .order('created_at', ascending: false);

        // Get overdue invoices (due_date < today and not paid)
        final today = DateTime.now();
        final overdueInvoices = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .neq('status', 'paid')
            .lt('due_date', today.toIso8601String())
            .order('due_date');

        // Get recent payments
        final recentPayments = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('date_received', ascending: false)
            .limit(5);

        // Get active jobs
        final activeJobs = await _supabaseClient
            .from('jobs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('status', 'active')
            .order('created_at', ascending: false);

        return {
          'open_estimates':
              openEstimates.map((json) => Estimate.fromJson(json)).toList(),
          'open_invoices':
              openInvoices.map((json) => Invoice.fromJson(json)).toList(),
          'overdue_invoices':
              overdueInvoices.map((json) => Invoice.fromJson(json)).toList(),
          'recent_payments':
              recentPayments.map((json) => Payment.fromJson(json)).toList(),
          'active_jobs': activeJobs.map((json) => Job.fromJson(json)).toList(),
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getDashboardData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Query expenses by specific type (materials, tools, subcontractor)
  Future<List<Expense>> getExpensesByType(String type) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('type', type)
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesByType',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Query expenses by specific category for a job
  Future<List<Expense>> getExpensesByJobAndCategory(
    String jobId,
    String category,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('job_id', jobId)
            .eq('category', category)
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesByJobAndCategory',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Calculate Year-to-date income, expenses, and net profit
  Future<Map<String, dynamic>> getYearToDateFinancials(int year) async {
    final result = await RetryMechanism.execute(
      () async {
        final startDate = DateTime(year, 1, 1);
        final endDate = DateTime(year, 12, 31);

        // Get all payments received in the year
        final paymentsResponse = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date_received', startDate.toIso8601String())
            .lte('date_received', endDate.toIso8601String());

        final payments =
            paymentsResponse.map((json) => Payment.fromJson(json)).toList();

        // Get all expenses in the year
        final expensesResponse = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String());

        final allYearExpenses =
            expensesResponse.map((json) => Expense.fromJson(json)).toList();

        // Get all time logs in the year
        final timeLogsResponse = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String());

        final timeLogs =
            timeLogsResponse.map((json) => TimeLog.fromJson(json)).toList();

        // Calculate totals
        double totalIncome = payments.fold(
          0.0,
          (sum, payment) => sum + payment.amountReceived,
        );

        // Actual total expenses (direct job + actual overhead spending)
        double actualTotalExpenses = allYearExpenses.fold(
          0.0,
          (sum, expense) => sum + expense.amount,
        );

        // Direct job expenses
        double directJobExpenses = allYearExpenses
            .where((e) => !e.isOverhead)
            .fold(0.0, (sum, expense) => sum + expense.amount);

        // Actual overhead spending
        double actualOverheadSpending = allYearExpenses
            .where((e) => e.isOverhead)
            .fold(0.0, (sum, expense) => sum + expense.amount);

        double totalLaborCost = timeLogs.fold(
          0.0,
          (sum, timeLog) => sum + timeLog.laborCost,
        );

        // Time logs are for the user only, which is a means for them to charge the customer
        // They add to income, not expenses, and should not be counted in expense calculations

        // Net profit based on actual expenses
        double netProfitActual = totalIncome - actualTotalExpenses;

        // Get total allocated overhead for the year
        double totalAllocatedOverhead = await getTotalAllocatedOverheadForYear(
          year,
        );

        // Net profit based on allocated overhead
        double totalExpensesWithAllocatedOverhead =
            directJobExpenses + totalAllocatedOverhead;
        double netProfitAllocated =
            totalIncome - totalExpensesWithAllocatedOverhead;

        return {
          'total_income': totalIncome,
          'direct_job_expenses': directJobExpenses,
          'actual_overhead_spending': actualOverheadSpending,
          'total_allocated_overhead': totalAllocatedOverhead,
          'total_labor_cost': totalLaborCost,
          'net_profit_actual': netProfitActual,
          'net_profit_allocated': netProfitAllocated,
          'all_year_actual_expenses_list': allYearExpenses,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getYearToDateFinancials',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Calculate quarterly financials for tax estimation
  Future<Map<String, dynamic>> getQuarterlyFinancials(
    int year,
    int quarter,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Determine quarter date range
        DateTime startDate;
        DateTime endDate;

        switch (quarter) {
          case 1:
            startDate = DateTime(year, 1, 1);
            endDate = DateTime(year, 3, 31);
            break;
          case 2:
            startDate = DateTime(year, 4, 1);
            endDate = DateTime(year, 6, 30);
            break;
          case 3:
            startDate = DateTime(year, 7, 1);
            endDate = DateTime(year, 9, 30);
            break;
          case 4:
            startDate = DateTime(year, 10, 1);
            endDate = DateTime(year, 12, 31);
            break;
          default:
            throw Exception('Invalid quarter: $quarter');
        }

        // Get all payments received in the quarter
        final paymentsResponse = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date_received', startDate.toIso8601String())
            .lte('date_received', endDate.toIso8601String());

        final payments =
            paymentsResponse.map((json) => Payment.fromJson(json)).toList();

        // Get all expenses in the quarter
        final expensesResponse = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String());

        final expenses =
            expensesResponse.map((json) => Expense.fromJson(json)).toList();

        // Get all direct job expenses for the quarter
        final directJobExpenses =
            expenses.where((expense) => !expense.isOverhead).toList();

        // Get all time logs in the quarter
        final timeLogsResponse = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String());

        final timeLogs =
            timeLogsResponse.map((json) => TimeLog.fromJson(json)).toList();

        // Get tax payments for this quarter
        final taxPayments = await getTaxPayments(year: year, quarter: quarter);

        // Calculate total income for the quarter
        double totalIncome = payments.fold(
          0.0,
          (sum, payment) => sum + payment.amountReceived,
        );

        // Group expenses by IRS Schedule C categories
        Map<String, double> categoryTotals = {};

        // Initialize with all IRS Schedule C categories
        for (String category in ExpenseCategory.values) {
          categoryTotals[category] = 0.0;
        }

        // Calculate totals for each category
        for (var expense in directJobExpenses) {
          if (expense.category != null) {
            categoryTotals[expense.category!] =
                (categoryTotals[expense.category!] ?? 0.0) + expense.amount;
          }
        }

        // Calculate total direct job expenses for the quarter
        double totalDirectJobExpenses = categoryTotals.values.fold(
          0.0,
          (sum, amount) => sum + amount,
        );

        // Calculate allocated overhead for the quarter using dynamic overhead allocations
        double totalQuarterlyAllocatedOverhead = 0.0;

        // 1. Get annual overhead allocation for all jobs for the current 'year'
        final Map<String, double> annualOverheadAllocationMap =
            await allocateOverheadExpenses(year: year);

        // 2. Get all payments for the entire 'year' to determine annual income per job
        final DateTime yearStartDate = DateTime(year, 1, 1);
        final DateTime yearEndDate = DateTime(year, 12, 31);
        final annualPaymentsResponse = await _supabaseClient
            .from('payments')
            .select('job_id, amount_received') // Select only necessary fields
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date_received', yearStartDate.toIso8601String())
            .lte('date_received', yearEndDate.toIso8601String());

        final Map<String, double> jobAnnualIncomeMap = {};
        for (final paymentData in annualPaymentsResponse) {
          final payment = Payment.fromJson(
            paymentData,
          ); // Assuming Payment.fromJson can handle partial data or adjust select accordingly
          if (payment.jobId != null) {
            jobAnnualIncomeMap[payment.jobId!] =
                (jobAnnualIncomeMap[payment.jobId!] ?? 0.0) +
                payment.amountReceived;
          }
        }

        // 3. Get payments for the current quarter (already fetched as 'payments')
        //    and calculate income per job for this quarter.
        final Map<String, double> jobQuarterlyIncomeMap = {};
        for (final payment in payments) {
          // 'payments' are quarterly payments
          if (payment.jobId != null) {
            jobQuarterlyIncomeMap[payment.jobId!] =
                (jobQuarterlyIncomeMap[payment.jobId!] ?? 0.0) +
                payment.amountReceived;
          }
        }

        // 4. Apportion annual allocated overhead to the quarter for each job
        for (final jobId in jobQuarterlyIncomeMap.keys) {
          final double jobIncomeThisQuarter = jobQuarterlyIncomeMap[jobId]!;
          final double jobAnnualIncome = jobAnnualIncomeMap[jobId] ?? 0.0;
          final double annualAllocatedOverheadForThisJob =
              annualOverheadAllocationMap[jobId] ?? 0.0;

          if (jobAnnualIncome > 0 && annualAllocatedOverheadForThisJob > 0) {
            final double proportion = jobIncomeThisQuarter / jobAnnualIncome;
            totalQuarterlyAllocatedOverhead +=
                proportion * annualAllocatedOverheadForThisJob;
          }
        }

        // End of new overhead calculation logic

        // Use the new totalQuarterlyAllocatedOverhead as totalOverheadExpenses
        double totalOverheadExpenses = totalQuarterlyAllocatedOverhead;

        // Calculate total expenses (job-specific + overhead)
        double totalJobExpenses = totalDirectJobExpenses;
        double totalExpenses = totalJobExpenses + totalOverheadExpenses;
        double totalLaborCost = timeLogs.fold(
          0.0,
          (sum, timeLog) => sum + timeLog.laborCost,
        );
        // Time logs are for the user only, which is a means for them to charge the customer
        // They add to income, not expenses, and should not be counted in expense calculations
        double netProfit = totalIncome - totalExpenses;

        // Calculate estimated tax (simplified example - actual tax calculation would be more complex)
        double estimatedTaxRate =
            0.15; // 15% self-employment tax as a simple example
        double estimatedTax = math.max(0, netProfit * estimatedTaxRate);

        // Calculate total tax payments made for this quarter
        double totalTaxPaid = taxPayments.fold(
          0,
          (sum, payment) => sum + payment.amount,
        );

        // Calculate remaining tax due
        double remainingTaxDue = math.max(0, estimatedTax - totalTaxPaid);

        return {
          'quarter': quarter,
          'year': year,
          'start_date': startDate,
          'end_date': endDate,
          'total_income': totalIncome,
          'expenses': {
            'categories': categoryTotals,
            'overhead': totalOverheadExpenses,
            'job_specific_total': totalDirectJobExpenses,
            'total': totalExpenses,
          },
          'labor_cost': totalLaborCost,
          'net_profit': netProfit,
          'estimated_tax': estimatedTax,
          'tax_paid': totalTaxPaid,
          'remaining_tax_due': remainingTaxDue,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getQuarterlyFinancials',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Allocate overhead expenses across jobs based on each job's proportion of annual income
  Future<Map<String, double>> allocateOverheadExpenses({
    String method = 'proportional_income',
    int? year,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get all overhead expenses, optionally filtered by year
        List<Expense> overheadExpenses;
        if (year != null) {
          final startDate = DateTime(year, 1, 1);
          final endDate = DateTime(year, 12, 31);
          overheadExpenses = await getOverheadExpensesByDateRange(
            startDate,
            endDate,
          );
        } else {
          overheadExpenses = await getOverheadExpenses();
        }

        if (overheadExpenses.isEmpty) {
          return {};
        }

        // Calculate total overhead amount
        final totalOverheadAmount = overheadExpenses.fold(
          0.0,
          (sum, expense) => sum + expense.amount,
        );

        // Determine the year for filtering if not provided
        final expenseYear = year ?? DateTime.now().year;
        final startDate = DateTime(expenseYear, 1, 1);
        final endDate = DateTime(expenseYear, 12, 31);

        // Get all jobs (regardless of status)
        final jobsResponse = await _supabaseClient
            .from('jobs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id);

        final allJobs = jobsResponse.map((json) => Job.fromJson(json)).toList();
        if (allJobs.isEmpty) {
          return {};
        }

        // Allocation map to store job ID -> allocated amount
        final Map<String, double> allocationMap = {};

        // Always use proportional income allocation regardless of the method parameter
        // Allocate based on job income proportion for the same year as expenses
        double totalJobIncome = 0.0;
        final Map<String, double> jobIncomes = {};

        // Get all payments for the year
        final paymentsResponse = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('payment_date', startDate.toIso8601String())
            .lte('payment_date', endDate.toIso8601String());

        final yearPayments =
            paymentsResponse.map((json) => Payment.fromJson(json)).toList();

        // Group payments by job and calculate total income per job
        for (final payment in yearPayments) {
          if (payment.jobId != null) {
            jobIncomes[payment.jobId!] =
                (jobIncomes[payment.jobId!] ?? 0.0) + payment.amountReceived;
            totalJobIncome += payment.amountReceived;
          }
        }

        // Allocate based on income proportion
        if (totalJobIncome > 0) {
          for (final jobId in jobIncomes.keys) {
            final proportion = jobIncomes[jobId]! / totalJobIncome;
            allocationMap[jobId] = totalOverheadAmount * proportion;
          }
        } else {
          // Fall back to equal allocation across all jobs if no income
          if (allJobs.isEmpty) {
            return {};
          }

          final amountPerJob = totalOverheadAmount / allJobs.length;
          for (final job in allJobs) {
            allocationMap[job.id] = amountPerJob;
          }
        }

        return allocationMap;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'allocateOverheadExpenses',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data! as Map<String, double>;
  }

  // Calculate total allocated overhead for a given year
  Future<double> getTotalAllocatedOverheadForYear(int year) async {
    final result = await RetryMechanism.execute(
      () async {
        final Map<String, double> allocationMap =
            await allocateOverheadExpenses(year: year);
        if (allocationMap.isEmpty) {
          return 0.0;
        }
        // Sum up all allocated amounts
        double total = 0.0;
        for (var amount in allocationMap.values) {
          total += amount;
        }
        return total;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTotalAllocatedOverheadForYear',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get detailed job cost summary
  Future<Map<String, dynamic>> getJobCostSummary(String jobId) async {
    try {
      // Get job details
      final job = await getJobById(jobId);

      // Get all expenses for this job
      final allExpenses = await getExpensesByJob(jobId);

      // Group expenses by IRS Schedule C categories
      final Map<String, List<Expense>> expensesByCategory = {};

      for (var expense in allExpenses) {
        if (expense.category != null) {
          if (!expensesByCategory.containsKey(expense.category)) {
            expensesByCategory[expense.category!] = [];
          }
          expensesByCategory[expense.category!]!.add(expense);
        }
      }

      // Get all payments for this job
      final payments = await getPaymentsByJob(jobId);

      // Determine the years to use for overhead allocation based on job payments
      // We'll collect all years with income for this job
      Set<int> allocationYears = {};
      if (payments.isNotEmpty) {
        // Add all years with payments
        for (var payment in payments) {
          allocationYears.add(payment.paymentDate.year);
        }
      } else {
        // If no payments, check if there are expenses and use their years
        if (allExpenses.isNotEmpty) {
          for (var expense in allExpenses) {
            allocationYears.add(expense.date.year);
          }
        } else {
          // Default to current year if no payments or expenses
          allocationYears.add(DateTime.now().year);
        }
      }

      // Get allocated overhead expenses for this job for each year with income
      double allocatedOverhead = 0.0;
      for (var year in allocationYears) {
        final allocationMap = await allocateOverheadExpenses(year: year);
        allocatedOverhead += allocationMap[jobId] ?? 0.0;
      }

      // Get all time logs for this job
      final timeLogs = await getTimeLogsByJob(jobId);

      // Calculate totals by category
      Map<String, double> categoryTotals = {};

      // Initialize with all IRS Schedule C categories
      for (String category in ExpenseCategory.values) {
        categoryTotals[category] = 0.0;
      }

      // Calculate totals for each category
      for (var category in expensesByCategory.keys) {
        categoryTotals[category] = expensesByCategory[category]!.fold(
          0.0,
          (sum, expense) => sum + expense.amount,
        );
      }

      // Calculate total direct expenses from all categories
      double totalDirectExpenses = categoryTotals.values.fold(
        0.0,
        (sum, amount) => sum + amount,
      );
      double totalExpenses = totalDirectExpenses + allocatedOverhead;
      double totalLaborCost = timeLogs.fold(
        0.0,
        (sum, timeLog) => sum + timeLog.laborCost,
      );
      double totalHoursWorked = timeLogs.fold(
        0.0,
        (sum, timeLog) => sum + timeLog.hours,
      );
      double totalIncome = payments.fold(
        0.0,
        (sum, payment) => sum + payment.amountReceived,
      );

      // Time logs are for user only and should not be counted as expenses
      // They add to income, not expenses
      double netProfit = totalIncome - totalExpenses;
      double profitMargin =
          totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0;

      return {
        'job': job,
        'costs': {
          'categories': categoryTotals,
          'overhead_allocated': allocatedOverhead,
          'labor': totalLaborCost,
          'direct_expenses': totalDirectExpenses,
          'total_expenses': totalExpenses,
          'total_cost': totalExpenses + totalLaborCost,
        },
        'labor': {'hours_worked': totalHoursWorked, 'cost': totalLaborCost},
        'income': totalIncome,
        'profit': {'net_profit': netProfit, 'margin_percentage': profitMargin},
        'expense_details': {'by_category': expensesByCategory},
        'time_logs': timeLogs,
        'payments': payments,
      };
    } catch (e) {
      throw Exception('Failed to get job cost summary: $e');
    }
  }

  // Get financial data for a specific date range
  Future<Map<String, dynamic>> getFinancialDataForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        // Get all payments received in the period
        final paymentsResponse = await _supabaseClient
            .from('payments')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('payment_date', startDate.toIso8601String())
            .lte('payment_date', endDate.toIso8601String())
            .order('payment_date');

        final payments =
            paymentsResponse.map((json) => Payment.fromJson(json)).toList();

        // Get all expenses in the period
        final expensesResponse = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String())
            .order('date');

        final expenses =
            expensesResponse.map((json) => Expense.fromJson(json)).toList();

        // Get all time logs in the period
        final timeLogsResponse = await _supabaseClient
            .from('time_logs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', startDate.toIso8601String())
            .lte('date', endDate.toIso8601String())
            .order('date');

        final timeLogs =
            timeLogsResponse.map((json) => TimeLog.fromJson(json)).toList();

        // Get all invoices issued in the period
        final invoicesResponse = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('created_at', startDate.toIso8601String())
            .lte('created_at', endDate.toIso8601String())
            .order('created_at');

        final invoices =
            invoicesResponse.map((json) => Invoice.fromJson(json)).toList();

        // Calculate totals
        double totalIncome = payments.fold(
          0.0,
          (sum, payment) => sum + payment.amountReceived,
        );

        // Group expenses by IRS Schedule C categories
        Map<String, double> categoryTotals = {};

        // Initialize with all IRS Schedule C categories
        for (String category in ExpenseCategory.values) {
          categoryTotals[category] = 0.0;
        }

        // Calculate totals for each category
        for (var expense in expenses) {
          if (expense.category != null) {
            categoryTotals[expense.category!] =
                (categoryTotals[expense.category!] ?? 0.0) + expense.amount;
          }
        }

        double totalExpenses = categoryTotals.values.fold(
          0.0,
          (sum, amount) => sum + amount,
        );
        double totalLaborCost = timeLogs.fold(
          0.0,
          (sum, timeLog) => sum + timeLog.laborCost,
        );
        // Time logs are for the user only, which is a means for them to charge the customer
        // They add to income, not expenses, and should not be counted in expense calculations
        double netProfit = totalIncome - totalExpenses;

        return {
          'period': {'start_date': startDate, 'end_date': endDate},
          'income': {'total': totalIncome, 'payments': payments},
          'expenses': {
            'categories': categoryTotals,
            'total': totalExpenses,
            'details': expenses,
          },
          'labor': {'cost': totalLaborCost, 'details': timeLogs},
          'invoices': invoices,
          'net_profit': netProfit,
        };
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getFinancialDataForPeriod',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get open estimates (status = 'draft' or 'sent')
  Future<List<Estimate>> getOpenEstimates() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .inFilter('status', ['draft', 'sent'])
            .order('created_at', ascending: false);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getOpenEstimates',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get open invoices (not fully paid)
  Future<List<Invoice>> getOpenInvoices() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .neq('status', 'paid')
            .order('created_at', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getOpenInvoices',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get overdue invoices (due_date < today and not paid)
  Future<List<Invoice>> getOverdueInvoices() async {
    final result = await RetryMechanism.execute(
      () async {
        final today = DateTime.now();
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .neq('status', 'paid')
            .lt('due_date', today.toIso8601String())
            .order('due_date');

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getOverdueInvoices',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get monthly income and expenses for a specific year
  Future<List<Map<String, dynamic>>> getMonthlyFinancials(int year) async {
    final result = await RetryMechanism.execute(
      () async {
        List<Map<String, dynamic>> monthlyData = [];

        for (int month = 1; month <= 12; month++) {
          final startDate = DateTime(year, month, 1);
          final endDate = DateTime(year, month + 1, 0); // Last day of month

          // Get all payments received in the month
          final paymentsResponse = await _supabaseClient
              .from('payments')
              .select()
              .eq('user_id', _supabaseClient.auth.currentUser!.id)
              .gte('date_received', startDate.toIso8601String())
              .lte('date_received', endDate.toIso8601String());

          final payments =
              paymentsResponse.map((json) => Payment.fromJson(json)).toList();

          // Get all expenses in the month
          final expensesResponse = await _supabaseClient
              .from('expenses')
              .select()
              .eq('user_id', _supabaseClient.auth.currentUser!.id)
              .gte('date', startDate.toIso8601String())
              .lte('date', endDate.toIso8601String());

          final expenses =
              expensesResponse.map((json) => Expense.fromJson(json)).toList();

          // Get all time logs in the month
          final timeLogsResponse = await _supabaseClient
              .from('time_logs')
              .select()
              .eq('user_id', _supabaseClient.auth.currentUser!.id)
              .gte('date', startDate.toIso8601String())
              .lte('date', endDate.toIso8601String());

          final timeLogs =
              timeLogsResponse.map((json) => TimeLog.fromJson(json)).toList();

          // Calculate totals
          double totalIncome = payments.fold(
            0,
            (sum, payment) => sum + payment.amountReceived,
          );
          double totalExpenses = expenses.fold(
            0,
            (sum, expense) => sum + expense.amount,
          );
          double totalLaborCost = timeLogs.fold(
            0.0,
            (sum, timeLog) => sum + timeLog.laborCost,
          );
          double netProfit = totalIncome - totalExpenses - totalLaborCost;

          monthlyData.add({
            'year': year,
            'month': month,
            'start_date': startDate,
            'end_date': endDate,
            'total_income': totalIncome,
            'total_expenses': totalExpenses,
            'total_labor_cost': totalLaborCost,
            'net_profit': netProfit,
          });
        }

        return monthlyData;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getMonthlyFinancials',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get a time log by ID
  Future<TimeLog?> getTimeLogById(String timeLogId) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('time_logs')
                .select()
                .eq('id', timeLogId)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return TimeLog.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getTimeLogById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return null if not found instead of throwing
      return null;
    }

    return result.data;
  }

  // Get all expenses since a specific date
  Future<List<Expense>> getExpensesSince(DateTime date) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('expenses')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .gte('date', date.toIso8601String())
            .order('date', ascending: false);

        return response.map((json) => Expense.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getExpensesSince',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get active jobs (not completed or cancelled)
  Future<List<Job>> getActiveJobs() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('jobs')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .not('status', 'eq', 'Completed')
            .not('status', 'eq', 'Cancelled')
            .order('created_at', ascending: false);

        return response.map((json) => Job.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getActiveJobs',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get estimates by status
  Future<List<Estimate>> getEstimatesByStatus(String status) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('estimates')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('status', status)
            .order('created_at', ascending: false);

        return response.map((json) => Estimate.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getEstimatesByStatus',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Get invoices by status
  Future<List<Invoice>> getInvoicesByStatus(String status) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('invoices')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .eq('status', status)
            .order('created_at', ascending: false);

        return response.map((json) => Invoice.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getInvoicesByStatus',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // ==================== DOCUMENT SIGNING OPERATIONS ====================

  /// Add a new document signing request
  Future<void> addDocumentSigningRequest(DocumentSigningRequest request) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('document_signing_requests')
            .insert(request.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addDocumentSigningRequest',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Get all document signing requests for the current user
  Future<List<DocumentSigningRequest>> getDocumentSigningRequests() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('document_signing_requests')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false);

        return response
            .map((json) => DocumentSigningRequest.fromJson(json))
            .toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getDocumentSigningRequests',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Get document signing requests with pagination support
  Future<List<DocumentSigningRequest>> getDocumentSigningRequestsPaginated(
    int offset,
    int limit,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('document_signing_requests')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('created_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response
            .map((json) => DocumentSigningRequest.fromJson(json))
            .toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getDocumentSigningRequestsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Get a document signing request by ID
  Future<DocumentSigningRequest?> getDocumentSigningRequestById(
    String id,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('document_signing_requests')
                .select()
                .eq('id', id)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .maybeSingle();

        if (response == null) return null;
        return DocumentSigningRequest.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getDocumentSigningRequestById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data;
  }

  /// Update a document signing request
  Future<void> updateDocumentSigningRequest(
    DocumentSigningRequest request,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('document_signing_requests')
            .update(request.toJson())
            .eq('id', request.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateDocumentSigningRequest',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Delete a document signing request
  Future<void> deleteDocumentSigningRequest(String id) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('document_signing_requests')
            .delete()
            .eq('id', id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'deleteDocumentSigningRequest',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // ==================== SIGNED DOCUMENT OPERATIONS ====================

  /// Add a new signed document
  Future<void> addSignedDocument(SignedDocument document) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('signed_documents')
            .insert(document.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'addSignedDocument',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Get all signed documents for the current user
  Future<List<SignedDocument>> getSignedDocuments() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('signed_documents')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('signed_at', ascending: false);

        return response.map((json) => SignedDocument.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getSignedDocuments',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Get signed documents with pagination support
  Future<List<SignedDocument>> getSignedDocumentsPaginated(
    int offset,
    int limit,
  ) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient
            .from('signed_documents')
            .select()
            .eq('user_id', _supabaseClient.auth.currentUser!.id)
            .order('signed_at', ascending: false)
            .range(offset, offset + limit - 1);

        return response.map((json) => SignedDocument.fromJson(json)).toList();
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getSignedDocumentsPaginated',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Get a signed document by ID
  Future<SignedDocument?> getSignedDocumentById(String id) async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('signed_documents')
                .select()
                .eq('id', id)
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .maybeSingle();

        if (response == null) return null;
        return SignedDocument.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getSignedDocumentById',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data;
  }

  /// Update a signed document
  Future<void> updateSignedDocument(SignedDocument document) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('signed_documents')
            .update(document.toJson())
            .eq('id', document.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateSignedDocument',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Delete a signed document
  Future<void> deleteSignedDocument(String id) async {
    try {
      await _supabaseClient
          .from('signed_documents')
          .delete()
          .eq('id', id)
          .eq('user_id', _supabaseClient.auth.currentUser!.id);
    } catch (e) {
      throw Exception('Failed to delete signed document: $e');
    }
  }

  /// Get signed documents by document type
  Future<List<SignedDocument>> getSignedDocumentsByType(
    String documentType,
  ) async {
    try {
      final response = await _supabaseClient
          .from('signed_documents')
          .select()
          .eq('user_id', _supabaseClient.auth.currentUser!.id)
          .eq('document_type', documentType)
          .order('signed_at', ascending: false);

      return response.map((json) => SignedDocument.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get signed documents by type: $e');
    }
  }

  /// Get signed documents for a specific job
  Future<List<SignedDocument>> getSignedDocumentsByJob(String jobId) async {
    try {
      final response = await _supabaseClient
          .from('signed_documents')
          .select()
          .eq('user_id', _supabaseClient.auth.currentUser!.id)
          .eq('job_id', jobId)
          .order('signed_at', ascending: false);

      return response.map((json) => SignedDocument.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get signed documents by job: $e');
    }
  }

  // ==================== USER PROFILE OPERATIONS ====================

  // Get user profile
  Future<UserProfile?> getUserProfile() async {
    final result = await RetryMechanism.execute(
      () async {
        final response =
            await _supabaseClient
                .from('user_profiles')
                .select()
                .eq('user_id', _supabaseClient.auth.currentUser!.id)
                .single();

        return UserProfile.fromJson(response);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getUserProfile',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // If no profile exists, return null instead of throwing
      return null;
    }

    return result.data;
  }

  // Create user profile
  Future<void> createUserProfile(UserProfile profile) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.from('user_profiles').insert(profile.toJson());
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'createUserProfile',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserProfile profile) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient
            .from('user_profiles')
            .update(profile.toJson())
            .eq('id', profile.id)
            .eq('user_id', _supabaseClient.auth.currentUser!.id);
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'updateUserProfile',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
