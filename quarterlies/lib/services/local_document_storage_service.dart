import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:quarterlies/utils/error_handler.dart';

/// Service for managing local document storage in permanent app directories
class LocalDocumentStorageService {
  // Singleton pattern
  static final LocalDocumentStorageService _instance =
      LocalDocumentStorageService._internal();
  factory LocalDocumentStorageService() => _instance;
  LocalDocumentStorageService._internal();

  static const String _documentsFolder = 'documents';
  static const String _signingRequestsFolder = 'signing_requests';
  static const String _signedDocumentsFolder = 'signed_documents';
  static const String _certificationsFolder = 'certifications';

  /// Get the app's documents directory
  Future<Directory> get _documentsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final documentsDir = Directory(path.join(appDir.path, _documentsFolder));

    if (!await documentsDir.exists()) {
      await documentsDir.create(recursive: true);
    }

    return documentsDir;
  }

  /// Get the signing requests directory
  Future<Directory> get _signingRequestsDirectory async {
    final documentsDir = await _documentsDirectory;
    final signingRequestsDir = Directory(
      path.join(documentsDir.path, _signingRequestsFolder),
    );

    if (!await signingRequestsDir.exists()) {
      await signingRequestsDir.create(recursive: true);
    }

    return signingRequestsDir;
  }

  /// Get the signed documents directory
  Future<Directory> get _signedDocumentsDirectory async {
    final documentsDir = await _documentsDirectory;
    final signedDocumentsDir = Directory(
      path.join(documentsDir.path, _signedDocumentsFolder),
    );

    if (!await signedDocumentsDir.exists()) {
      await signedDocumentsDir.create(recursive: true);
    }

    return signedDocumentsDir;
  }

  /// Get the certifications directory
  Future<Directory> get _certificationsDirectory async {
    final documentsDir = await _documentsDirectory;
    final certificationsDir = Directory(
      path.join(documentsDir.path, _certificationsFolder),
    );

    if (!await certificationsDir.exists()) {
      await certificationsDir.create(recursive: true);
    }

    return certificationsDir;
  }

  /// Store a PDF for a signing request
  Future<String> storeSigningRequestPdf({
    required Uint8List pdfBytes,
    required String documentType,
    required String documentId,
    required String requestId,
  }) async {
    try {
      final directory = await _signingRequestsDirectory;
      final fileName = '${documentType}_${documentId}_$requestId.pdf';
      final filePath = path.join(directory.path, fileName);

      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        debugPrint('Stored signing request PDF: $filePath');
      }
      return filePath;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'storeSigningRequestPdf',
          'documentType': documentType,
          'documentId': documentId,
          'requestId': requestId,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Store a signed document PDF (customer version)
  Future<String> storeSignedDocumentCustomerPdf({
    required Uint8List pdfBytes,
    required String documentType,
    required String documentId,
    required String signingRequestId,
  }) async {
    try {
      final directory = await _signedDocumentsDirectory;
      final fileName =
          '${documentType}_${documentId}_${signingRequestId}_signed_customer.pdf';
      final filePath = path.join(directory.path, fileName);

      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        debugPrint('Stored signed document customer PDF: $filePath');
      }
      return filePath;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'storeSignedDocumentCustomerPdf',
          'documentType': documentType,
          'documentId': documentId,
          'signingRequestId': signingRequestId,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Store a signed document PDF (contractor version with certification)
  Future<String> storeSignedDocumentContractorPdf({
    required Uint8List pdfBytes,
    required String documentType,
    required String documentId,
    required String signingRequestId,
  }) async {
    try {
      final directory = await _signedDocumentsDirectory;
      final fileName =
          '${documentType}_${documentId}_${signingRequestId}_signed_contractor.pdf';
      final filePath = path.join(directory.path, fileName);

      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      debugPrint('Stored signed document contractor PDF: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error storing signed document contractor PDF: $e');
      throw Exception('Failed to store signed document contractor PDF: $e');
    }
  }

  /// Store a certification PDF
  Future<String> storeCertificationPdf({
    required Uint8List pdfBytes,
    required String documentType,
    required String documentId,
    required String signingRequestId,
  }) async {
    try {
      final directory = await _certificationsDirectory;
      final fileName =
          '${documentType}_${documentId}_${signingRequestId}_certification.pdf';
      final filePath = path.join(directory.path, fileName);

      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      if (kDebugMode) {
        debugPrint('Stored certification PDF: $filePath');
      }
      return filePath;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'storeCertificationPdf',
          'documentType': documentType,
          'documentId': documentId,
          'signingRequestId': signingRequestId,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Read a PDF file from local storage
  Future<Uint8List?> readPdf(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'readPdf', 'filePath': filePath},
      );
      ErrorHandler.logError(appError);
      return null;
    }
  }

  /// Check if a PDF file exists
  Future<bool> pdfExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'pdfExists', 'filePath': filePath},
      );
      ErrorHandler.logError(appError);
      return false;
    }
  }

  /// Delete a PDF file
  Future<bool> deletePdf(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          debugPrint('Deleted PDF file: $filePath');
        }
        return true;
      }
      return false;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'deletePdf', 'filePath': filePath},
      );
      ErrorHandler.logError(appError);
      return false;
    }
  }

  /// Get the size of a PDF file in bytes
  Future<int?> getPdfSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        return stat.size;
      }
      return null;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'getPdfSize', 'filePath': filePath},
      );
      ErrorHandler.logError(appError);
      return null;
    }
  }

  /// Clear all stored documents (for testing or reset)
  Future<void> clearAllDocuments() async {
    try {
      final documentsDir = await _documentsDirectory;
      if (await documentsDir.exists()) {
        await documentsDir.delete(recursive: true);
        if (kDebugMode) {
          debugPrint('Cleared all local documents');
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'clearAllDocuments'},
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Get total storage used by documents in bytes
  Future<int> getTotalStorageUsed() async {
    try {
      final documentsDir = await _documentsDirectory;
      if (!await documentsDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in documentsDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'getTotalStorageUsed'},
      );
      ErrorHandler.logError(appError);
      return 0;
    }
  }

  /// Get a human-readable storage size string
  String formatStorageSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
