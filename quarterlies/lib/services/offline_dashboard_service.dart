import 'package:flutter/foundation.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/connectivity_service.dart';
import '../utils/error_handler.dart';
import '../utils/retry_mechanism.dart';

/// Offline-first dashboard service that provides business overview data
/// even when the app is offline by caching dashboard data locally
class OfflineDashboardService {
  // Singleton pattern
  static final OfflineDashboardService _instance =
      OfflineDashboardService._internal();
  factory OfflineDashboardService() => _instance;
  OfflineDashboardService._internal();

  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final SupabaseService _supabaseService = SupabaseService();
  final ConnectivityService _connectivityService = ConnectivityService();

  /// Get dashboard data (offline-first)
  ///
  /// Returns cached data immediately if offline, or fetches fresh data
  /// and updates cache if online
  Future<Map<String, dynamic>> getDashboardData() async {
    final result = await RetryMechanism.execute(
      () async {
        // Check if we're online
        final isOnline = await _connectivityService.checkConnection();

        if (isOnline) {
          // Try to get fresh data from server
          final serverData = await _supabaseService.getDashboardData();

          // Cache the fresh data locally for offline access
          await _cacheDashboardData(serverData);

          return serverData;
        }

        // Get cached data from local database
        return await _getCachedDashboardData();
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'getDashboardData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return cached data as fallback
      try {
        return await _getCachedDashboardData();
      } catch (e) {
        // Return empty dashboard data as final fallback
        return _getEmptyDashboardData();
      }
    }

    return result.data!;
  }

  /// Get cached dashboard data from local database
  Future<Map<String, dynamic>> _getCachedDashboardData() async {
    try {
      // Get open estimates (status = 'draft' or 'sent')
      final estimates = await _localDatabaseService.getEstimates();
      final openEstimates =
          estimates
              .where((e) => e.status == 'draft' || e.status == 'sent')
              .toList();

      // Get open invoices (not fully paid)
      final invoices = await _localDatabaseService.getInvoices();
      final openInvoices = invoices.where((i) => i.status != 'paid').toList();

      // Get overdue invoices (due_date < today and not paid)
      final today = DateTime.now();
      final overdueInvoices =
          invoices
              .where((i) => i.status != 'paid' && i.dueDate.isBefore(today))
              .toList();

      // Get recent payments (last 5)
      final payments = await _localDatabaseService.getPayments();
      final recentPayments = payments.take(5).toList();

      // Get active jobs
      final jobs = await _localDatabaseService.getJobs();
      final activeJobs = jobs.where((j) => j.status == 'active').toList();

      // Calculate totals
      final totalOpenEstimatesAmount = openEstimates.fold<double>(
        0,
        (sum, e) => sum + e.totalAmount,
      );

      final totalOpenInvoicesAmount = openInvoices.fold<double>(
        0,
        (sum, i) => sum + i.totalAmount,
      );

      final totalOverdueAmount = overdueInvoices.fold<double>(
        0,
        (sum, i) => sum + i.totalAmount,
      );

      final totalRecentPaymentsAmount = recentPayments.fold<double>(
        0,
        (sum, p) => sum + p.amount,
      );

      return {
        'openEstimates': openEstimates.map((e) => e.toJson()).toList(),
        'openInvoices': openInvoices.map((i) => i.toJson()).toList(),
        'overdueInvoices': overdueInvoices.map((i) => i.toJson()).toList(),
        'recentPayments': recentPayments.map((p) => p.toJson()).toList(),
        'activeJobs': activeJobs.map((j) => j.toJson()).toList(),
        'totals': {
          'openEstimates': totalOpenEstimatesAmount,
          'openInvoices': totalOpenInvoicesAmount,
          'overdueAmount': totalOverdueAmount,
          'recentPayments': totalRecentPaymentsAmount,
        },
        'counts': {
          'openEstimates': openEstimates.length,
          'openInvoices': openInvoices.length,
          'overdueInvoices': overdueInvoices.length,
          'activeJobs': activeJobs.length,
        },
        'lastUpdated': DateTime.now().toIso8601String(),
        'isOfflineData': true,
      };
    } catch (e) {
      debugPrint('Error getting cached dashboard data: $e');
      return _getEmptyDashboardData();
    }
  }

  /// Cache dashboard data locally (for future offline access)
  Future<void> _cacheDashboardData(Map<String, dynamic> dashboardData) async {
    try {
      // The dashboard data is already cached through individual entity operations
      // when estimates, invoices, payments, and jobs are synced.
      // This method could be used to cache additional dashboard-specific metadata
      // or summary data if needed in the future.

      debugPrint('Dashboard data cached successfully');
    } catch (e) {
      debugPrint('Error caching dashboard data: $e');
    }
  }

  /// Get empty dashboard data as fallback
  Map<String, dynamic> _getEmptyDashboardData() {
    return {
      'openEstimates': <Map<String, dynamic>>[],
      'openInvoices': <Map<String, dynamic>>[],
      'overdueInvoices': <Map<String, dynamic>>[],
      'recentPayments': <Map<String, dynamic>>[],
      'activeJobs': <Map<String, dynamic>>[],
      'totals': {
        'openEstimates': 0.0,
        'openInvoices': 0.0,
        'overdueAmount': 0.0,
        'recentPayments': 0.0,
      },
      'counts': {
        'openEstimates': 0,
        'openInvoices': 0,
        'overdueInvoices': 0,
        'activeJobs': 0,
      },
      'lastUpdated': DateTime.now().toIso8601String(),
      'isOfflineData': true,
      'isEmpty': true,
    };
  }

  /// Get business summary statistics (offline-first)
  Future<Map<String, dynamic>> getBusinessSummary() async {
    try {
      final dashboardData = await getDashboardData();

      return {
        'totalRevenue':
            dashboardData['totals']['openInvoices'] +
            dashboardData['totals']['recentPayments'],
        'pendingRevenue': dashboardData['totals']['openInvoices'],
        'overdueAmount': dashboardData['totals']['overdueAmount'],
        'estimatesValue': dashboardData['totals']['openEstimates'],
        'activeJobsCount': dashboardData['counts']['activeJobs'],
        'lastUpdated': dashboardData['lastUpdated'],
        'isOfflineData': dashboardData['isOfflineData'] ?? false,
      };
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'getBusinessSummary',
          'service': 'OfflineDashboardService',
        },
      );
      ErrorHandler.logError(appError);

      return {
        'totalRevenue': 0.0,
        'pendingRevenue': 0.0,
        'overdueAmount': 0.0,
        'estimatesValue': 0.0,
        'activeJobsCount': 0,
        'lastUpdated': DateTime.now().toIso8601String(),
        'isOfflineData': true,
        'error': true,
      };
    }
  }

  /// Force refresh dashboard data from server
  Future<Map<String, dynamic>> refreshDashboardData() async {
    final result = await RetryMechanism.execute(
      () async {
        final serverData = await _supabaseService.getDashboardData();
        await _cacheDashboardData(serverData);
        return serverData;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'refreshDashboardData',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return cached data if refresh fails
      return await _getCachedDashboardData();
    }

    return result.data!;
  }

  /// Check if dashboard data is stale (older than specified duration)
  Future<bool> isDashboardDataStale({
    Duration maxAge = const Duration(hours: 1),
  }) async {
    try {
      final dashboardData = await _getCachedDashboardData();
      final lastUpdatedStr = dashboardData['lastUpdated'] as String?;

      if (lastUpdatedStr == null) return true;

      final lastUpdated = DateTime.parse(lastUpdatedStr);
      final now = DateTime.now();

      return now.difference(lastUpdated) > maxAge;
    } catch (e) {
      debugPrint('Error checking dashboard data staleness: $e');
      return true; // Assume stale if we can't determine
    }
  }
}
