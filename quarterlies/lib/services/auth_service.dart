import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class AuthService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Keys for storing remember me data
  static const String _rememberMeKey = 'remember_me_enabled';
  static const String _rememberedEmailKey = 'remembered_email';
  static const String _rememberedPasswordKey = 'remembered_password';

  // Validate password strength
  bool isPasswordStrong(String password) {
    // Minimum 8 characters
    if (password.length < 8) return false;

    // Contains uppercase
    if (!RegExp(r'[A-Z]').hasMatch(password)) return false;

    // Contains lowercase
    if (!RegExp(r'[a-z]').hasMatch(password)) return false;

    // Contains number
    if (!RegExp(r'[0-9]').hasMatch(password)) return false;

    // Contains special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;

    return true;
  }

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    // Validate password strength client-side before sending to server
    if (!isPasswordStrong(password)) {
      final validationError = AppError(
        type: ErrorType.validation,
        severity: ErrorSeverity.medium,
        message: 'Password does not meet strength requirements',
        userFriendlyMessage:
            'Password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters',
        context: {'operation': 'signUp', 'email': email},
      );
      ErrorHandler.logError(validationError);
      throw Exception(validationError.userFriendlyMessage);
    }

    final result = await RetryMechanism.execute(
      () async {
        return await _supabaseClient.auth.signUp(
          email: email,
          password: password,
        );
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'signUp',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
    String? totpCode,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // First sign in with password
        final response = await _supabaseClient.auth.signInWithPassword(
          email: email,
          password: password,
        );

        // Check if MFA is required
        if (response.user != null && totpCode != null) {
          // Verify with TOTP if code is provided
          final factors = await _supabaseClient.auth.mfa.listFactors();
          final totpFactor = factors.totp.firstWhere(
            (factor) => factor.status == FactorStatus.verified,
            orElse: () => throw Exception('No verified TOTP factor found'),
          );

          // Challenge the factor
          final challenge = await _supabaseClient.auth.mfa.challenge(
            factorId: totpFactor.id,
          );

          // Verify the challenge with the provided code
          await _supabaseClient.auth.mfa.verify(
            factorId: totpFactor.id,
            challengeId: challenge.id,
            code: totpCode,
          );
        }

        return response;
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'signIn',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Sign in with MFA
  Future<AuthResponse> signInWithMFA({
    required String email,
    required String password,
    required String totpCode,
  }) async {
    return signIn(email: email, password: password, totpCode: totpCode);
  }

  // Sign out
  Future<void> signOut() async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.auth.signOut();
        // Clear remembered credentials on sign out
        await clearRememberedCredentials();
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'signOut',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Get current user
  User? get currentUser => _supabaseClient.auth.currentUser;

  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Enable MFA for the current user
  Future<String> enableMFA() async {
    final result = await RetryMechanism.execute(
      () async {
        // Generate TOTP secret
        final response = await _supabaseClient.auth.mfa.enroll();
        final factorId = response.id;
        final totpUri = response.totp.uri;

        // Store the factor ID for later use
        await _secureStorage.write(key: 'mfa_factor_id', value: factorId);

        // Return the URI for QR code generation
        return totpUri;
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'enableMFA',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  // Verify MFA setup with user-provided code
  Future<void> verifyMFA(String userProvidedCode) async {
    final result = await RetryMechanism.execute(
      () async {
        final factorId = await _secureStorage.read(key: 'mfa_factor_id');
        if (factorId == null) {
          throw Exception('MFA setup not initiated');
        }

        // Challenge the factor
        final challenge = await _supabaseClient.auth.mfa.challenge(
          factorId: factorId,
        );

        // Verify with the provided code
        await _supabaseClient.auth.mfa.verify(
          factorId: factorId,
          challengeId: challenge.id,
          code: userProvidedCode,
        );

        // Clear the stored factor ID after successful verification
        await _secureStorage.delete(key: 'mfa_factor_id');
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'verifyMFA',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Send password reset email
  Future<void> resetPassword(String email) async {
    final result = await RetryMechanism.execute(
      () async {
        await _supabaseClient.auth.resetPasswordForEmail(
          email,
          redirectTo: 'io.quarterlies.app://reset-password/',
        );
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'resetPassword',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Update password after reset
  Future<void> updatePassword(String newPassword) async {
    final result = await RetryMechanism.execute(
      () async {
        // Validate password strength client-side before sending to server
        if (!isPasswordStrong(newPassword)) {
          throw Exception(
            'Password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters',
          );
        }

        await _supabaseClient.auth.updateUser(
          UserAttributes(password: newPassword),
        );
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'updatePassword',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Check if user has MFA enabled
  Future<bool> hasMFAEnabled() async {
    final result = await RetryMechanism.execute(
      () async {
        final factors = await _supabaseClient.auth.mfa.listFactors();
        return factors.totp.any(
          (factor) => factor.status == FactorStatus.verified,
        );
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'hasMFAEnabled',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return false; // Return false on error instead of throwing
    }

    return result.data!;
  }

  // Remember Me functionality

  /// Save credentials for remember me functionality
  Future<void> saveRememberedCredentials({
    required String email,
    required String password,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rememberMeKey, true);
      await _secureStorage.write(key: _rememberedEmailKey, value: email);
      await _secureStorage.write(key: _rememberedPasswordKey, value: password);
    } catch (e) {
      // Silently fail - remember me is not critical functionality
    }
  }

  /// Clear remembered credentials
  Future<void> clearRememberedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rememberMeKey, false);
      await _secureStorage.delete(key: _rememberedEmailKey);
      await _secureStorage.delete(key: _rememberedPasswordKey);
    } catch (e) {
      // Silently fail - remember me is not critical functionality
    }
  }

  /// Check if remember me is enabled
  Future<bool> isRememberMeEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_rememberMeKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get remembered credentials
  Future<Map<String, String?>> getRememberedCredentials() async {
    try {
      final email = await _secureStorage.read(key: _rememberedEmailKey);
      final password = await _secureStorage.read(key: _rememberedPasswordKey);
      return {'email': email, 'password': password};
    } catch (e) {
      return {'email': null, 'password': null};
    }
  }

  /// Attempt automatic login using remembered credentials
  Future<bool> attemptAutoLogin() async {
    final result = await RetryMechanism.execute(
      () async {
        // Check if remember me is enabled
        if (!await isRememberMeEnabled()) {
          return false;
        }

        // Get remembered credentials
        final credentials = await getRememberedCredentials();
        final email = credentials['email'];
        final password = credentials['password'];

        if (email == null || password == null) {
          return false;
        }

        // Attempt sign in
        await signIn(email: email, password: password);
        return true;
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'attemptAutoLogin',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Clear invalid credentials on error
      await clearRememberedCredentials();
      return false;
    }

    return result.data!;
  }

  /// Enhanced sign in method with remember me support
  Future<AuthResponse> signInWithRememberMe({
    required String email,
    required String password,
    required bool rememberMe,
    String? totpCode,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Perform normal sign in
        final response = await signIn(
          email: email,
          password: password,
          totpCode: totpCode,
        );

        // Handle remember me functionality
        if (rememberMe && response.user != null) {
          await saveRememberedCredentials(email: email, password: password);
        } else {
          await clearRememberedCredentials();
        }

        return response;
      },
      config: RetryMechanism.authRetryConfig(),
      operationName: 'signInWithRememberMe',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Clear credentials on failed login
      await clearRememberedCredentials();
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }
}
