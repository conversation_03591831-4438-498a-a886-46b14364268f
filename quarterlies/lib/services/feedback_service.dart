import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../utils/app_constants.dart';

/// Enum for different types of feedback messages
enum FeedbackType { success, info, sync, operation, warning }

/// Enum for feedback priority levels
enum FeedbackPriority { low, normal, high, critical }

/// A comprehensive feedback service for user notifications
/// Handles offline-first aware messaging with different types and priorities
class FeedbackService {
  static final FeedbackService _instance = FeedbackService._internal();
  factory FeedbackService() => _instance;
  FeedbackService._internal();

  FlutterLocalNotificationsPlugin? _notificationsPlugin;

  /// Initialize the feedback service
  Future<void> initialize() async {
    _notificationsPlugin = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notificationsPlugin?.initialize(initializationSettings);
  }

  /// Show success feedback for data operations
  static void showSuccess(
    BuildContext context,
    String message, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    _showFeedback(
      context,
      message,
      FeedbackType.success,
      isOffline: isOffline,
      priority: priority,
    );
  }

  /// Show info feedback for general information
  static void showInfo(
    BuildContext context,
    String message, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    _showFeedback(
      context,
      message,
      FeedbackType.info,
      isOffline: isOffline,
      priority: priority,
    );
  }

  /// Show sync-related feedback
  static void showSync(
    BuildContext context,
    String message, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    _showFeedback(
      context,
      message,
      FeedbackType.sync,
      isOffline: isOffline,
      priority: priority,
    );
  }

  /// Show operation feedback for heavy operations
  static void showOperation(
    BuildContext context,
    String message, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    _showFeedback(
      context,
      message,
      FeedbackType.operation,
      isOffline: isOffline,
      priority: priority,
    );
  }

  /// Show warning feedback
  static void showWarning(
    BuildContext context,
    String message, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    _showFeedback(
      context,
      message,
      FeedbackType.warning,
      isOffline: isOffline,
      priority: priority,
    );
  }

  /// Internal method to show feedback with consistent styling
  static void _showFeedback(
    BuildContext context,
    String message,
    FeedbackType type, {
    bool isOffline = false,
    FeedbackPriority priority = FeedbackPriority.normal,
  }) {
    final messenger = ScaffoldMessenger.of(context);
    messenger.hideCurrentSnackBar();

    // Truncate message if too long
    String displayMessage = message;
    if (message.length > AppConstants.maxFeedbackMessageLength) {
      displayMessage =
          '${message.substring(0, AppConstants.maxFeedbackMessageLength - 3)}...';
    }

    // Add offline indicator if needed
    if (isOffline) {
      displayMessage = '$displayMessage (Offline)';
    }

    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(_getFeedbackIcon(type), color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              displayMessage,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      backgroundColor: _getFeedbackColor(type),
      duration: Duration(seconds: _getFeedbackDuration(type)),
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );

    messenger.showSnackBar(snackBar);
  }

  /// Get appropriate icon for feedback type
  static IconData _getFeedbackIcon(FeedbackType type) {
    switch (type) {
      case FeedbackType.success:
        return Icons.check_circle;
      case FeedbackType.info:
        return Icons.info;
      case FeedbackType.sync:
        return Icons.sync;
      case FeedbackType.operation:
        return Icons.settings;
      case FeedbackType.warning:
        return Icons.warning;
    }
  }

  /// Get appropriate color for feedback type
  static Color _getFeedbackColor(FeedbackType type) {
    switch (type) {
      case FeedbackType.success:
        return Colors.green;
      case FeedbackType.info:
        return Colors.blue;
      case FeedbackType.sync:
        return Colors.orange;
      case FeedbackType.operation:
        return Colors.purple;
      case FeedbackType.warning:
        return Colors.amber;
    }
  }

  /// Get appropriate duration for feedback type
  static int _getFeedbackDuration(FeedbackType type) {
    switch (type) {
      case FeedbackType.success:
        return AppConstants.successDisplayDurationSeconds;
      case FeedbackType.info:
        return AppConstants.infoDisplayDurationSeconds;
      case FeedbackType.sync:
        return AppConstants.syncDisplayDurationSeconds;
      case FeedbackType.operation:
        return AppConstants.operationDisplayDurationSeconds;
      case FeedbackType.warning:
        return AppConstants.infoDisplayDurationSeconds;
    }
  }

  /// Show persistent notification for background operations
  Future<void> showPersistentNotification({
    required String title,
    required String message,
    FeedbackType type = FeedbackType.info,
    String? payload,
  }) async {
    if (_notificationsPlugin == null) return;

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'feedback_channel',
          'App Feedback',
          channelDescription: 'Notifications for app operations and feedback',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
          ongoing: false,
          autoCancel: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _notificationsPlugin!.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      message,
      platformChannelSpecifics,
      payload: payload,
    );
  }
}
