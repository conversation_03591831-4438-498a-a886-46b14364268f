import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  // Singleton pattern
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final _connectionStatusController = StreamController<bool>.broadcast();

  // Stream that broadcasts connection status changes
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  // Initialize the service and start listening for connectivity changes
  void initialize() {
    _connectivity.onConnectivityChanged.listen((result) {
      // Handle the List<ConnectivityResult> by checking if any result indicates connectivity
      if (result.isNotEmpty) {
        _updateConnectionStatus(result.first);
      } else {
        _connectionStatusController.add(false);
      }
    });
    // Check initial connection state
    checkConnection();
  }

  // Check current connection status
  Future<bool> checkConnection() async {
    bool isConnected = false;
    try {
      final results = await _connectivity.checkConnectivity();
      // Handle the List<ConnectivityResult> by checking if any result indicates connectivity
      if (results.isNotEmpty) {
        isConnected = _isConnected(results.first);
      }
      _connectionStatusController.add(isConnected);
    } catch (e) {
      _connectionStatusController.add(false);
    }
    return isConnected;
  }

  // Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    final isConnected = _isConnected(result);
    _connectionStatusController.add(isConnected);
  }

  // Helper method to determine if there is an active connection
  bool _isConnected(ConnectivityResult result) {
    return result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;
  }

  // Dispose resources
  void dispose() {
    _connectionStatusController.close();
  }
}
