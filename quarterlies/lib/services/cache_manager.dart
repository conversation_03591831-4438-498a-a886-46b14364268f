import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:quarterlies/services/local_database_service.dart';

/// CacheManager implements an LRU (Least Recently Used) cache eviction policy
/// for managing local data storage.
///
/// It keeps track of entity access patterns and automatically removes the least
/// recently used data when storage limits are reached.
class CacheManager {
  // Singleton pattern
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();

  // LRU caches for different entity types
  final LinkedHashMap<String, DateTime> _customerAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _jobAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _invoiceAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _expenseAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _timeLogAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _taxPaymentAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _contractAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _documentSigningRequestAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _signedDocumentAccessTimes =
      LinkedHashMap<String, DateTime>();
  final LinkedHashMap<String, DateTime> _estimateAccessTimes =
      LinkedHashMap<String, DateTime>();

  // Maximum number of entities to keep in local storage
  static const int _maxCustomers = 500;
  static const int _maxJobs = 200;
  static const int _maxInvoices = 100;
  static const int _maxExpenses = 1000;
  static const int _maxTimeLogs = 1000;
  static const int _maxTaxPayments = 50;
  static const int _maxContracts = 100;
  static const int _maxDocumentSigningRequests = 50;
  static const int _maxSignedDocuments = 100;
  static const int _maxEstimates = 200;

  // Initialize the cache manager
  Future<void> initialize() async {
    // Load existing entities to populate access times
    await _loadExistingEntities();
  }

  // Load existing entities from the database to initialize access times
  Future<void> _loadExistingEntities() async {
    try {
      // Load customers
      final customers = await _localDatabaseService.getCustomers();
      for (var customer in customers) {
        _customerAccessTimes[customer.id] = DateTime.now();
      }

      // Load jobs
      final jobs = await _localDatabaseService.getJobs();
      for (var job in jobs) {
        _jobAccessTimes[job.id] = DateTime.now();
      }

      // Load invoices
      final invoices = await _localDatabaseService.getInvoices();
      for (var invoice in invoices) {
        if (invoice.id != null) {
          _invoiceAccessTimes[invoice.id!] = DateTime.now();
        }
      }

      // Load expenses
      final expenses = await _localDatabaseService.getExpenses();
      for (var expense in expenses) {
        _expenseAccessTimes[expense.id] = DateTime.now();
      }

      // Load time logs
      final timeLogs = await _localDatabaseService.getTimeLogs();
      for (var timeLog in timeLogs) {
        _timeLogAccessTimes[timeLog.id] = DateTime.now();
      }

      // Load tax payments
      final taxPayments = await _localDatabaseService.getTaxPayments();
      for (var taxPayment in taxPayments) {
        _taxPaymentAccessTimes[taxPayment.id] = DateTime.now();
      }

      // Load contracts
      final contracts = await _localDatabaseService.getContracts();
      for (var contract in contracts) {
        _contractAccessTimes[contract.id] = DateTime.now();
      }

      // Load document signing requests
      final documentSigningRequests =
          await _localDatabaseService.getDocumentSigningRequests();
      for (var request in documentSigningRequests) {
        _documentSigningRequestAccessTimes[request.id] = DateTime.now();
      }

      // Load signed documents
      final signedDocuments = await _localDatabaseService.getSignedDocuments();
      for (var document in signedDocuments) {
        _signedDocumentAccessTimes[document.id] = DateTime.now();
      }

      // Load estimates
      final estimates = await _localDatabaseService.getEstimates();
      for (var estimate in estimates) {
        _estimateAccessTimes[estimate.id] = DateTime.now();
      }
    } catch (e) {
      debugPrint('Error loading existing entities: ${e.toString()}');
    }
  }

  // Record access to a customer
  void recordCustomerAccess(String customerId) {
    _customerAccessTimes[customerId] = DateTime.now();
    _enforceCustomerLimit();
  }

  // Record access to a job
  void recordJobAccess(String jobId) {
    _jobAccessTimes[jobId] = DateTime.now();
    _enforceJobLimit();
  }

  // Record access to an invoice
  void recordInvoiceAccess(String invoiceId) {
    _invoiceAccessTimes[invoiceId] = DateTime.now();
    _enforceInvoiceLimit();
  }

  // Record access to an expense
  void recordExpenseAccess(String expenseId) {
    _expenseAccessTimes[expenseId] = DateTime.now();
    _enforceExpenseLimit();
  }

  // Record access to a time log
  void recordTimeLogAccess(String timeLogId) {
    _timeLogAccessTimes[timeLogId] = DateTime.now();
    _enforceTimeLogLimit();
  }

  /// Clear all cached data
  Future<void> clearAllCache() async {
    try {
      // Clear all access time maps
      _customerAccessTimes.clear();
      _jobAccessTimes.clear();
      _invoiceAccessTimes.clear();
      _expenseAccessTimes.clear();
      _timeLogAccessTimes.clear();
      _taxPaymentAccessTimes.clear();
      _contractAccessTimes.clear();
      _documentSigningRequestAccessTimes.clear();
      _signedDocumentAccessTimes.clear();
      _estimateAccessTimes.clear();

      // Clear all data from local database
      await _localDatabaseService.clearAllData();

      debugPrint('All cache data cleared successfully');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      rethrow;
    }
  }

  // Record access to a tax payment
  void recordTaxPaymentAccess(String taxPaymentId) {
    _taxPaymentAccessTimes[taxPaymentId] = DateTime.now();
    _enforceTaxPaymentLimit();
  }

  // Record access to a contract
  void recordContractAccess(String contractId) {
    _contractAccessTimes[contractId] = DateTime.now();
    _enforceContractLimit();
  }

  // Record access to a document signing request
  void recordDocumentSigningRequestAccess(String requestId) {
    _documentSigningRequestAccessTimes[requestId] = DateTime.now();
    _enforceDocumentSigningRequestLimit();
  }

  // Record access to a signed document
  void recordSignedDocumentAccess(String documentId) {
    _signedDocumentAccessTimes[documentId] = DateTime.now();
    _enforceSignedDocumentLimit();
  }

  // Record access to an estimate
  void recordEstimateAccess(String estimateId) {
    _estimateAccessTimes[estimateId] = DateTime.now();
    _enforceEstimateLimit();
  }

  // Enforce customer limit by removing least recently used customers
  Future<void> _enforceCustomerLimit() async {
    if (_customerAccessTimes.length <= _maxCustomers) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _customerAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxCustomers)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteCustomer(id);
      _customerAccessTimes.remove(id);
    }
  }

  // Enforce job limit by removing least recently used jobs
  Future<void> _enforceJobLimit() async {
    if (_jobAccessTimes.length <= _maxJobs) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _jobAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxJobs)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteJob(id);
      _jobAccessTimes.remove(id);
    }
  }

  // Enforce invoice limit by removing least recently used invoices
  Future<void> _enforceInvoiceLimit() async {
    if (_invoiceAccessTimes.length <= _maxInvoices) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _invoiceAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxInvoices)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteInvoice(id);
      _invoiceAccessTimes.remove(id);
    }
  }

  // Enforce expense limit by removing least recently used expenses
  Future<void> _enforceExpenseLimit() async {
    if (_expenseAccessTimes.length <= _maxExpenses) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _expenseAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxExpenses)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteExpense(id);
      _expenseAccessTimes.remove(id);
    }
  }

  // Enforce time log limit by removing least recently used time logs
  Future<void> _enforceTimeLogLimit() async {
    if (_timeLogAccessTimes.length <= _maxTimeLogs) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _timeLogAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxTimeLogs)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteTimeLog(id);
      _timeLogAccessTimes.remove(id);
    }
  }

  // Enforce tax payment limit by removing least recently used tax payments
  Future<void> _enforceTaxPaymentLimit() async {
    if (_taxPaymentAccessTimes.length <= _maxTaxPayments) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _taxPaymentAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxTaxPayments)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteTaxPayment(id);
      _taxPaymentAccessTimes.remove(id);
    }
  }

  // Enforce contract limit by removing least recently used contracts
  Future<void> _enforceContractLimit() async {
    if (_contractAccessTimes.length <= _maxContracts) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _contractAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxContracts)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteContract(id);
      _contractAccessTimes.remove(id);
    }
  }

  // Enforce document signing request limit by removing least recently used requests
  Future<void> _enforceDocumentSigningRequestLimit() async {
    if (_documentSigningRequestAccessTimes.length <=
        _maxDocumentSigningRequests) {
      return;
    }

    // Sort by access time (oldest first)
    final sortedEntries =
        _documentSigningRequestAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxDocumentSigningRequests)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteDocumentSigningRequest(id);
      _documentSigningRequestAccessTimes.remove(id);
    }
  }

  // Enforce signed document limit by removing least recently used documents
  Future<void> _enforceSignedDocumentLimit() async {
    if (_signedDocumentAccessTimes.length <= _maxSignedDocuments) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _signedDocumentAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxSignedDocuments)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteSignedDocument(id);
      _signedDocumentAccessTimes.remove(id);
    }
  }

  // Enforce estimate limit by removing least recently used estimates
  Future<void> _enforceEstimateLimit() async {
    if (_estimateAccessTimes.length <= _maxEstimates) return;

    // Sort by access time (oldest first)
    final sortedEntries =
        _estimateAccessTimes.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    // Get IDs to remove
    final idsToRemove =
        sortedEntries
            .take(sortedEntries.length - _maxEstimates)
            .map((e) => e.key)
            .toList();

    // Remove from database
    for (var id in idsToRemove) {
      await _localDatabaseService.deleteEstimate(id);
      _estimateAccessTimes.remove(id);
    }
  }
}
