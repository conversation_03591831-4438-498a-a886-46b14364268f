import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/error_handler.dart';
import '../utils/retry_mechanism.dart';

class CustomerService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  Future<List<Map<String, dynamic>>> getCustomers() async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await _supabaseClient.from('customers').select();
        final customers = List<Map<String, dynamic>>.from(response);

        // Cache the customers locally
        await _secureStorage.write(
          key: 'cached_customers',
          value: jsonEncode(customers),
        );

        return customers;
      },
      config: RetryMechanism.databaseRetryConfig(),
      operationName: 'getCustomers',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // If there's an error, try to return cached data
      return await getCachedCustomers();
    }

    return result.data!;
  }

  Future<List<Map<String, dynamic>>> getCachedCustomers() async {
    try {
      final cachedData = await _secureStorage.read(key: 'cached_customers');

      if (cachedData == null) {
        return [];
      }

      return List<Map<String, dynamic>>.from(jsonDecode(cachedData));
    } catch (e) {
      return [];
    }
  }
}
