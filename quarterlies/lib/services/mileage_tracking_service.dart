import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/services/notification_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MileageTrackingService {
  static final MileageTrackingService _instance =
      MileageTrackingService._internal();
  final SupabaseService _supabaseService = SupabaseService();
  final NotificationService _notificationService = NotificationService();

  StreamSubscription<Position>? _positionStreamSubscription;
  Position? _startPosition;
  DateTime? _startTime;
  String? _startAddress;
  Timer? _idleTimer;
  bool _isTracking = false;
  int _idleTimeoutMinutes = MileageConstants.defaultIdleTimeoutMinutes;

  // Factory constructor
  factory MileageTrackingService() {
    return _instance;
  }

  // Private constructor
  MileageTrackingService._internal();

  bool get isTracking => _isTracking;

  // Initialize the service with user settings
  Future<void> initialize() async {
    try {
      final userSettings = await _supabaseService.getUserSettings();
      _idleTimeoutMinutes =
          userSettings.mileageIdleTimeoutMinutes ??
          MileageConstants.defaultIdleTimeoutMinutes;
    } catch (e) {
      debugPrint('Error initializing mileage tracking service: $e');
      // Use default timeout if settings can't be loaded
      _idleTimeoutMinutes = MileageConstants.defaultIdleTimeoutMinutes;
    }
  }

  // Start tracking mileage
  Future<bool> startTracking() async {
    if (_isTracking) return true; // Already tracking

    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return false;
      }

      // Get current position as starting point
      _startPosition = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );
      _startTime = DateTime.now();

      // Get address for starting position
      try {
        final placemarks = await placemarkFromCoordinates(
          _startPosition!.latitude,
          _startPosition!.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          _startAddress = [
                placemark.street,
                placemark.locality,
                placemark.administrativeArea,
                placemark.postalCode,
              ]
              .where((element) => element != null && element.isNotEmpty)
              .join(', ');
        }
      } catch (e) {
        debugPrint('Error getting address: $e');
        _startAddress = 'Unknown location';
      }

      // Start listening to position updates
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
        ),
      ).listen(_onPositionUpdate);

      _isTracking = true;

      // Show notification that tracking has started
      await _notificationService.showNotification(
        'Mileage Tracking Active',
        'Tracking started from $_startAddress',
        'mileage_tracking',
      );

      return true;
    } catch (e) {
      debugPrint('Error starting mileage tracking: $e');
      return false;
    }
  }

  // Handle position updates
  void _onPositionUpdate(Position position) {
    // Reset idle timer on movement
    _resetIdleTimer();
  }

  // Reset the idle timer
  void _resetIdleTimer() {
    _idleTimer?.cancel();
    _idleTimer = Timer(Duration(minutes: _idleTimeoutMinutes), _onIdleTimeout);
  }

  // Handle idle timeout
  Future<void> _onIdleTimeout() async {
    if (!_isTracking) return;

    try {
      // Get final position
      final endPosition = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      // Calculate distance
      final distanceInMeters = Geolocator.distanceBetween(
        _startPosition!.latitude,
        _startPosition!.longitude,
        endPosition.latitude,
        endPosition.longitude,
      );

      // Convert to miles (1 meter = 0.000621371 miles)
      final distanceInMiles = distanceInMeters * 0.000621371;

      // Get end address
      String endAddress = 'Unknown location';
      try {
        final placemarks = await placemarkFromCoordinates(
          endPosition.latitude,
          endPosition.longitude,
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          endAddress = [
                placemark.street,
                placemark.locality,
                placemark.administrativeArea,
                placemark.postalCode,
              ]
              .where((element) => element != null && element.isNotEmpty)
              .join(', ');
        }
      } catch (e) {
        debugPrint('Error getting end address: $e');
      }

      // Stop tracking
      await stopTracking();

      // Show notification with trip summary
      await _notificationService.showNotification(
        'Mileage Tracking Completed',
        'Trip: $_startAddress to $endAddress\n${distanceInMiles.toStringAsFixed(1)} miles',
        'mileage_completed',
        payload: {
          'start_location': _startAddress ?? 'Unknown',
          'end_location': endAddress,
          'start_coordinates':
              '${_startPosition!.latitude},${_startPosition!.longitude}',
          'end_coordinates': '${endPosition.latitude},${endPosition.longitude}',
          'miles': distanceInMiles,
          'date': _startTime!.toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error handling idle timeout: $e');
      await stopTracking();
    }
  }

  // Stop tracking mileage
  Future<void> stopTracking() async {
    _positionStreamSubscription?.cancel();
    _idleTimer?.cancel();
    _isTracking = false;
    _startPosition = null;
    _startTime = null;
    _startAddress = null;
  }

  // Save a tracked mileage entry to a specific job
  Future<bool> saveTrackedMileageToJob(
    Map<String, dynamic> tripData,
    String jobId,
  ) async {
    try {
      // Get current mileage rate
      final rate =
          await _supabaseService.getMileageRate() ??
          double.tryParse(MileageConstants.defaultIrsRate) ??
          0.655;

      // Create mileage entry
      final mileage = Mileage(
        userId: Supabase.instance.client.auth.currentUser!.id,
        jobId: jobId,
        startLocation: tripData['start_location'],
        endLocation: tripData['end_location'],
        miles: tripData['miles'],
        ratePerMile: rate,
        date: DateTime.parse(tripData['date']),
        startCoordinates: tripData['start_coordinates'],
        endCoordinates: tripData['end_coordinates'],
        isAutoTracked: true,
      );

      // Save to database
      await _supabaseService.addMileage(mileage);
      return true;
    } catch (e) {
      debugPrint('Error saving tracked mileage: $e');
      return false;
    }
  }
}
