import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class VoiceRecordingService {
  static VoiceRecordingService? _instance;

  // Flutter Sound recorder
  final FlutterSoundRecorder _recorder;
  bool _isRecorderInitialized = false;
  String? _recordingPath;

  // Speech to text
  final stt.SpeechToText _speechToText;
  bool _isSpeechInitialized = false;
  String _transcribedText = '';

  // Supabase client for storage
  final SupabaseClient _supabaseClient;

  // Reference to LoadingStateProvider for UI updates
  LoadingStateProvider? _loadingStateProvider;

  // Factory constructor
  factory VoiceRecordingService({
    FlutterSoundRecorder? recorder,
    stt.SpeechToText? speechToText,
    SupabaseClient? supabaseClient,
  }) {
    return _instance ??= VoiceRecordingService._internal(
      recorder: recorder,
      speechToText: speechToText,
      supabaseClient: supabaseClient,
    );
  }

  // Private constructor
  VoiceRecordingService._internal({
    FlutterSoundRecorder? recorder,
    stt.SpeechToText? speechToText,
    SupabaseClient? supabaseClient,
  }) : _recorder = recorder ?? FlutterSoundRecorder(),
       _speechToText = speechToText ?? stt.SpeechToText(),
       _supabaseClient = supabaseClient ?? Supabase.instance.client;

  // Method to reset singleton for testing
  static void resetInstance() {
    _instance = null;
  }

  // Set the LoadingStateProvider for UI updates
  void setLoadingStateProvider(LoadingStateProvider provider) {
    _loadingStateProvider = provider;
  }

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Initialize recorder
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw Exception('Microphone permission not granted');
      }

      await _recorder.openRecorder();
      _isRecorderInitialized = true;

      // Initialize speech to text
      _isSpeechInitialized = await _speechToText.initialize();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'initializeVoiceRecording'},
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Dispose resources
  Future<void> dispose() async {
    if (_isRecorderInitialized) {
      await _recorder.closeRecorder();
      _isRecorderInitialized = false;
    }
  }

  // Start recording
  Future<void> startRecording() async {
    if (!_isRecorderInitialized) {
      await initialize();
    }

    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      await _loadingStateProvider!.executeWithVoiceLoading(
        () async {
          await _performStartRecording();
        },
        operationName: 'Starting recording...',
        isRecording: true,
      );
    } else {
      await _performStartRecording();
    }
  }

  // Internal start recording implementation
  Future<void> _performStartRecording() async {
    try {
      // Use permanent app documents directory instead of temporary
      final directory = await getApplicationDocumentsDirectory();
      final voiceNotesDir = Directory('${directory.path}/voice_notes');

      // Create voice notes directory if it doesn't exist
      if (!await voiceNotesDir.exists()) {
        await voiceNotesDir.create(recursive: true);
      }

      _recordingPath = '${voiceNotesDir.path}/${const Uuid().v4()}.aac';

      await _recorder.startRecorder(
        toFile: _recordingPath,
        codec: Codec.aacADTS,
        audioSource: AudioSource.microphone,
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'startRecording',
          'recordingPath': _recordingPath,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  // Stop recording and transcribe
  Future<String> stopRecording() async {
    if (!_isRecorderInitialized || !_recorder.isRecording) {
      return '';
    }

    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithVoiceLoading(
        () async {
          return await _performStopRecording();
        },
        operationName: 'Processing recording...',
        isRecording: false,
      );
    } else {
      return await _performStopRecording();
    }
  }

  // Internal stop recording implementation
  Future<String> _performStopRecording() async {
    try {
      await _recorder.stopRecorder();

      // Transcribe the recording
      if (_recordingPath != null) {
        return await transcribeAudio(_recordingPath!);
      }

      return '';
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'stopRecording',
          'recordingPath': _recordingPath,
        },
      );
      ErrorHandler.logError(appError);
      // Return empty string instead of throwing to allow graceful degradation
      return '';
    }
  }

  // Transcribe audio file
  Future<String> transcribeAudio(String filePath) async {
    // For now, we'll use on-device speech recognition
    // In a production app, you might want to use a more robust service like Google Cloud Speech-to-Text

    if (!_isSpeechInitialized) {
      return '';
    }

    try {
      // This is a simplified version - in a real app, you'd need to implement
      // a way to convert the audio file to a format that speech_to_text can process
      // or use a different library/service that can transcribe audio files

      // For demonstration purposes, we'll use live listening instead
      _transcribedText = '';

      await _speechToText.listen(
        onResult: (result) {
          _transcribedText = result.recognizedWords;
        },
        listenFor: const Duration(seconds: 30),
      );

      // Wait for transcription to complete
      await Future.delayed(const Duration(seconds: 2));

      return _transcribedText;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'transcribeAudio', 'filePath': filePath},
      );
      ErrorHandler.logError(appError);
      // Return empty string instead of throwing to allow graceful degradation
      return '';
    }
  }

  // Get the current recording path
  String? getRecordingPath() {
    return _recordingPath;
  }

  // Upload audio file to Supabase Storage
  Future<String?> uploadAudioToStorage(
    String filePath,
    String recordType,
    String recordId,
  ) async {
    if (filePath.isEmpty) {
      return null;
    }

    final result = await RetryMechanism.execute(
      () async {
        final file = File(filePath);
        if (!await file.exists()) {
          throw Exception('Audio file does not exist: $filePath');
        }

        final userId = _supabaseClient.auth.currentUser!.id;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = '$userId/$recordType/$recordId/$timestamp.aac';

        await _supabaseClient.storage
            .from('voice_notes')
            .upload(fileName, file);

        // Get the public URL
        final String publicUrl = _supabaseClient.storage
            .from('voice_notes')
            .getPublicUrl(fileName);

        return publicUrl;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'uploadAudioToStorage',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      // Return null instead of throwing to allow graceful degradation
      return null;
    }

    return result.data;
  }

  // Process transcribed text to extract information
  Map<String, dynamic> processTranscribedText(String text) {
    final Map<String, dynamic> extractedInfo = {};

    if (text.isEmpty) {
      return extractedInfo;
    }

    // Extract amount
    final amountRegex = RegExp(r'\$?(\d+(?:\.\d{1,2})?)');
    final amountMatch = amountRegex.firstMatch(text);
    if (amountMatch != null) {
      extractedInfo['amount'] = amountMatch.group(1);
    }

    // Extract quantity
    final quantityRegex = RegExp(
      r'(\d+(?:\.\d{1,2})?)\s+(?:units?|pieces?|hours?|items?|qty|quantity)',
      caseSensitive: false,
    );
    final quantityMatch = quantityRegex.firstMatch(text);
    if (quantityMatch != null) {
      extractedInfo['quantity'] = quantityMatch.group(1);
    }

    // Extract price or rate
    final priceRegex = RegExp(
      r'(?:price|rate|cost|at)\s+\$?(\d+(?:\.\d{1,2})?)',
      caseSensitive: false,
    );
    final priceMatch = priceRegex.firstMatch(text);
    if (priceMatch != null) {
      extractedInfo['price'] = priceMatch.group(1);
    }

    // Extract date
    final dateRegex = RegExp(
      r'(\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4})|' // MM/DD/YYYY or similar formats
      r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?(?:[,]\s*\d{4})?', // Month name formats
      caseSensitive: false,
    );
    final dateMatch = dateRegex.firstMatch(text);
    if (dateMatch != null) {
      final dateStr = dateMatch.group(0)!;
      try {
        DateTime? parsedDate;

        // Try different date formats
        for (final format in [
          'MM/dd/yyyy',
          'M/d/yyyy',
          'yyyy-MM-dd',
          'MMMM d, yyyy',
          'MMMM d yyyy',
        ]) {
          try {
            parsedDate = DateFormat(format).parse(dateStr);
            break;
          } catch (_) {
            // Continue to next format
          }
        }

        if (parsedDate != null) {
          extractedInfo['date'] = parsedDate;
        }
      } catch (e) {
        // If date parsing fails, don't add to extracted info
      }
    }

    // Extract job-related information
    final jobRegex = RegExp(
      r'(?:for|job|project)\s+([\w\s]+)',
      caseSensitive: false,
    );
    final jobMatch = jobRegex.firstMatch(text);
    if (jobMatch != null) {
      extractedInfo['jobName'] = jobMatch.group(1)?.trim();
    }

    // Extract email
    final emailRegex = RegExp(
      r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
    );
    final emailMatch = emailRegex.firstMatch(text);
    if (emailMatch != null) {
      extractedInfo['email'] = emailMatch.group(0);
    }

    // Extract phone number
    final phoneRegex = RegExp(
      r'\b(?:\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b',
    );
    final phoneMatch = phoneRegex.firstMatch(text);
    if (phoneMatch != null) {
      extractedInfo['phone'] = phoneMatch.group(0);
    }

    // Extract address components
    final addressRegex = RegExp(
      r'\b\d+\s+[A-Za-z0-9\s,]+(?:street|st|avenue|ave|road|rd|boulevard|blvd|lane|ln|drive|dr|way|place|pl|court|ct)\b',
      caseSensitive: false,
    );
    final addressMatch = addressRegex.firstMatch(text);
    if (addressMatch != null) {
      extractedInfo['address'] = addressMatch.group(0);
    }

    // Extract city, state, zip
    final cityStateZipRegex = RegExp(
      r'\b([A-Za-z\s]+),\s*([A-Z]{2})\s*(\d{5}(?:-\d{4})?)\b',
    );
    final cityStateZipMatch = cityStateZipRegex.firstMatch(text);
    if (cityStateZipMatch != null) {
      extractedInfo['city'] = cityStateZipMatch.group(1);
      extractedInfo['state'] = cityStateZipMatch.group(2);
      extractedInfo['zipCode'] = cityStateZipMatch.group(3);
    }

    // Extract description
    // Use the remaining text as description, or the whole text if no specific entities were found
    String description = text;

    // Remove extracted entities from description to clean it up
    if (amountMatch != null) {
      description = description.replaceAll(amountMatch.group(0)!, '');
    }
    if (dateMatch != null) {
      description = description.replaceAll(dateMatch.group(0)!, '');
    }
    if (jobMatch != null) {
      description = description.replaceAll(jobMatch.group(0)!, '');
    }
    if (emailMatch != null) {
      description = description.replaceAll(emailMatch.group(0)!, '');
    }
    if (phoneMatch != null) {
      description = description.replaceAll(phoneMatch.group(0)!, '');
    }
    if (addressMatch != null) {
      description = description.replaceAll(addressMatch.group(0)!, '');
    }
    if (cityStateZipMatch != null) {
      description = description.replaceAll(cityStateZipMatch.group(0)!, '');
    }
    if (quantityMatch != null) {
      description = description.replaceAll(quantityMatch.group(0)!, '');
    }
    if (priceMatch != null) {
      description = description.replaceAll(priceMatch.group(0)!, '');
    }

    // Clean up description
    description = description
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(
          RegExp(
            r'^(for|paid|spent|bought|purchased|customer|client|job|invoice|estimate|item)\s+',
            caseSensitive: false,
          ),
          '',
        );

    if (description.isNotEmpty) {
      extractedInfo['description'] = description;
    }

    return extractedInfo;
  }

  // Process transcribed text specifically for customer information
  Map<String, dynamic> processCustomerVoiceInput(String text) {
    final extractedInfo = processTranscribedText(text);

    // Extract customer name - look for patterns like "customer name is" or "new customer"
    final nameRegex = RegExp(
      r'(?:customer|client|name is|add)\s+([A-Za-z\s]+)(?:,|\.|$)',
      caseSensitive: false,
    );
    final nameMatch = nameRegex.firstMatch(text);
    if (nameMatch != null && nameMatch.group(1) != null) {
      String name = nameMatch.group(1)!.trim();
      // Clean up common words that might be captured
      name =
          name
              .replaceAll(
                RegExp(
                  r'\b(customer|client|new|named)\b',
                  caseSensitive: false,
                ),
                '',
              )
              .trim();
      if (name.isNotEmpty) {
        extractedInfo['name'] = name;
      }
    } else if (extractedInfo.containsKey('description')) {
      // If no explicit name pattern found, use the description as name
      extractedInfo['name'] = extractedInfo['description'];
    }

    return extractedInfo;
  }

  // Process transcribed text specifically for job information
  Map<String, dynamic> processJobVoiceInput(String text) {
    final extractedInfo = processTranscribedText(text);

    // Extract job title - look for patterns like "job title is" or "new job"
    final titleRegex = RegExp(
      r'(?:job|project|title is|add job)\s+([A-Za-z0-9\s]+)(?:,|\.|$)',
      caseSensitive: false,
    );

    // Extract job address - look for patterns like "job address is" or "location is"
    final addressRegex = RegExp(
      r'(?:job address|location|address is|at)\s+([A-Za-z0-9\s,]+)(?:,|\.|$)',
      caseSensitive: false,
    );
    final titleMatch = titleRegex.firstMatch(text);
    if (titleMatch != null && titleMatch.group(1) != null) {
      String title = titleMatch.group(1)!.trim();
      // Clean up common words that might be captured
      title =
          title
              .replaceAll(
                RegExp(r'\b(job|project|new|titled)\b', caseSensitive: false),
                '',
              )
              .trim();
      if (title.isNotEmpty) {
        extractedInfo['title'] = title;
      }
    } else if (extractedInfo.containsKey('description')) {
      // If no explicit title pattern found, use the description as title
      extractedInfo['title'] = extractedInfo['description'];
    }

    // Extract job address from the specific pattern
    final jobAddressMatch = addressRegex.firstMatch(text);
    if (jobAddressMatch != null && jobAddressMatch.group(1) != null) {
      extractedInfo['address'] = jobAddressMatch.group(1)!.trim();
    }

    // Extract job status if mentioned
    final statusRegex = RegExp(
      r'\b(estimate|active|completed|invoiced|paid)\b',
      caseSensitive: false,
    );
    final statusMatch = statusRegex.firstMatch(text);
    if (statusMatch != null) {
      extractedInfo['status'] = statusMatch.group(1)!.toLowerCase();
    }

    // If amount is found, treat it as estimated price
    if (extractedInfo.containsKey('amount')) {
      extractedInfo['estimatedPrice'] = extractedInfo['amount'];
    }

    return extractedInfo;
  }

  // Process transcribed text specifically for line item information
  Map<String, dynamic> processLineItemVoiceInput(String text) {
    final extractedInfo = processTranscribedText(text);

    // If no quantity was found but there's a number at the beginning, treat it as quantity
    if (!extractedInfo.containsKey('quantity')) {
      final leadingNumberRegex = RegExp(r'^\s*(\d+(?:\.\d+)?)\s+');
      final leadingNumberMatch = leadingNumberRegex.firstMatch(text);
      if (leadingNumberMatch != null) {
        extractedInfo['quantity'] = leadingNumberMatch.group(1);
      } else {
        // Default quantity to 1 if not specified
        extractedInfo['quantity'] = '1';
      }
    }

    // If price was found, use it as unit price
    if (extractedInfo.containsKey('price')) {
      extractedInfo['unitPrice'] = extractedInfo['price'];
    }

    // Extract unit if mentioned (each, hour, etc.)
    final unitRegex = RegExp(
      r'\b(each|hour|piece|item|unit|foot|feet|meter|yard|day)\b',
      caseSensitive: false,
    );
    final unitMatch = unitRegex.firstMatch(text);
    if (unitMatch != null) {
      extractedInfo['unit'] = unitMatch.group(1)!.toLowerCase();
    } else {
      // Default unit
      extractedInfo['unit'] = 'each';
    }

    return extractedInfo;
  }

  // Process transcribed text for search queries
  String processSearchQuery(String text) {
    // Remove filler words and extract the main search terms
    final cleanedText =
        text
            .toLowerCase()
            .replaceAll(
              RegExp(
                r'\b(search|find|look|for|up|the|a|an|show|me|get|fetch|retrieve)\b',
              ),
              ' ',
            )
            .replaceAll(RegExp(r'\s+'), ' ')
            .trim();

    // Check for job-specific search patterns
    final jobAddressRegex = RegExp(
      r'(?:job|work|project)(?:\s+at|in|on|near|with address)?\s+([A-Za-z0-9\s,]+)(?:,|\.|$)',
      caseSensitive: false,
    );

    final jobAddressMatch = jobAddressRegex.firstMatch(text);
    if (jobAddressMatch != null && jobAddressMatch.group(1) != null) {
      return jobAddressMatch.group(1)!.trim();
    }

    return cleanedText;
  }
}
