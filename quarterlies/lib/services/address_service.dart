import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

/// A service for address autocomplete functionality using Mapbox
class AddressService {
  // Mapbox access token - should be stored securely in environment variables
  static const String _accessToken = 'YOUR_MAPBOX_ACCESS_TOKEN';
  static const String _baseUrl =
      'https://api.mapbox.com/geocoding/v5/mapbox.places';

  /// Get address predictions based on input text using Mapbox
  Future<List<AddressPrediction>> getAddressPredictions(String input) async {
    if (input.isEmpty || input.length < 3) {
      return [];
    }

    final result = await RetryMechanism.execute(
      () async {
        final encodedInput = Uri.encodeComponent(input);
        final url =
            '$_baseUrl/$encodedInput.json?access_token=$_accessToken&country=US&types=address&limit=5';

        final response = await http.get(Uri.parse(url));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          final features = data['features'] as List;

          return features
              .map((feature) => AddressPrediction.fromMapboxFeature(feature))
              .toList();
        } else {
          throw Exception(
            'Address service returned error status: ${response.statusCode} - ${response.body}',
          );
        }
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'getAddressPredictions',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return [];
    }

    return result.data!;
  }

  /// Get detailed address information from a Mapbox place
  Future<AddressDetails?> getPlaceDetails(String placeId) async {
    if (placeId.isEmpty) {
      return null;
    }

    try {
      // For Mapbox, we can extract details directly from the place name
      // This is a simplified implementation - in a real app you might want
      // to use Mapbox's geocoding API for more detailed information
      return AddressDetails.fromPlaceId(placeId);
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'getPlaceDetails', 'placeId': placeId},
      );
      ErrorHandler.logError(appError);
      return null;
    }
  }
}

/// Model class for address predictions
class AddressPrediction {
  final String placeId;
  final String description;
  final double? latitude;
  final double? longitude;
  final String? city;
  final String? state;
  final String? zipCode;

  AddressPrediction({
    required this.placeId,
    required this.description,
    this.latitude,
    this.longitude,
    this.city,
    this.state,
    this.zipCode,
  });

  factory AddressPrediction.fromJson(Map<String, dynamic> json) {
    return AddressPrediction(
      placeId: json['place_id'] ?? '',
      description: json['description'] ?? '',
    );
  }

  factory AddressPrediction.fromMapboxFeature(Map<String, dynamic> feature) {
    final geometry = feature['geometry'];
    final coordinates = geometry?['coordinates'] as List?;
    final context = feature['context'] as List?;

    // Extract address components from context
    String? city;
    String? state;
    String? zipCode;

    if (context != null) {
      for (var item in context) {
        final id = item['id'] as String?;
        final text = item['text'] as String?;

        if (id != null && text != null) {
          if (id.startsWith('place.')) {
            city = text;
          } else if (id.startsWith('region.')) {
            state = text;
          } else if (id.startsWith('postcode.')) {
            zipCode = text;
          }
        }
      }
    }

    return AddressPrediction(
      placeId: feature['id'] ?? '',
      description: feature['place_name'] ?? '',
      latitude:
          coordinates != null && coordinates.length > 1
              ? coordinates[1]?.toDouble()
              : null,
      longitude:
          coordinates != null && coordinates.isNotEmpty
              ? coordinates[0]?.toDouble()
              : null,
      city: city,
      state: state,
      zipCode: zipCode,
    );
  }
}

/// Model class for address details
class AddressDetails {
  final String? streetNumber;
  final String? route;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;

  AddressDetails({
    this.streetNumber,
    this.route,
    this.city,
    this.state,
    this.zipCode,
    this.country,
  });

  factory AddressDetails.fromJson(Map<String, dynamic> json) {
    final components = json['address_components'] as List;
    String? streetNumber;
    String? route;
    String? city;
    String? state;
    String? zipCode;
    String? country;

    for (var component in components) {
      final types = component['types'] as List;

      if (types.contains('street_number')) {
        streetNumber = component['long_name'];
      } else if (types.contains('route')) {
        route = component['long_name'];
      } else if (types.contains('locality')) {
        city = component['long_name'];
      } else if (types.contains('administrative_area_level_1')) {
        state = component['short_name'];
      } else if (types.contains('postal_code')) {
        zipCode = component['long_name'];
      } else if (types.contains('country')) {
        country = component['long_name'];
      }
    }

    return AddressDetails(
      streetNumber: streetNumber,
      route: route,
      city: city,
      state: state,
      zipCode: zipCode,
      country: country,
    );
  }

  factory AddressDetails.fromPlaceId(String placeId) {
    // This is a simplified implementation for Mapbox
    // In a real app, you would parse the place_name or make additional API calls
    return AddressDetails(
      streetNumber: null,
      route: null,
      city: null,
      state: null,
      zipCode: null,
      country: 'USA',
    );
  }

  String get formattedAddress {
    final street = [streetNumber, route].where((e) => e != null).join(' ');
    return [street, city, state, zipCode].where((e) => e != null).join(', ');
  }
}
