import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:quarterlies/models/user_profile.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/error_handler.dart';

/// Service for applying user signatures to documents
/// This service handles the automatic application of the user's signature
/// to contracts and estimates after the customer has signed them
class DocumentSignatureService {
  final DataRepository _dataRepository = DataRepository();

  // Singleton pattern
  static final DocumentSignatureService _instance =
      DocumentSignatureService._internal();
  factory DocumentSignatureService() => _instance;
  DocumentSignatureService._internal();

  /// Apply user signature to a PDF document (offline-first)
  ///
  /// This method:
  /// 1. Gets the user's signature from local storage first
  /// 2. Falls back to remote storage if needed
  /// 3. Adds the signature to the PDF document
  /// 4. Returns the modified PDF bytes
  Future<Uint8List> applyUserSignatureToPdf({
    required Uint8List pdfBytes,
    required String documentType, // 'contract' or 'estimate'
    String? signaturePosition = 'bottom-right', // Position on the page
  }) async {
    try {
      // Get user signature bytes using offline-first approach
      final signatureBytes = await _dataRepository.getUserSignatureBytes();
      if (signatureBytes == null) {
        debugPrint('No user signature found, returning original PDF');
        return pdfBytes;
      }

      // Get user profile for signature details
      final userProfile = await _dataRepository.getUserProfile();
      if (userProfile == null) {
        debugPrint('No user profile found, returning original PDF');
        return pdfBytes;
      }

      // Apply signature to PDF
      return await _addSignatureToPdf(
        pdfBytes: pdfBytes,
        signatureBytes: signatureBytes,
        documentType: documentType,
        position: signaturePosition ?? 'bottom-right',
        userProfile: userProfile,
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'applyUserSignatureToPdf',
          'documentType': documentType,
          'signaturePosition': signaturePosition,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      // Return original PDF if signature application fails (graceful degradation)
      return pdfBytes;
    }
  }

  /// Check if user has a signature available (offline-first)
  Future<bool> hasUserSignature() async {
    try {
      return await _dataRepository.hasUserSignature();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'hasUserSignature'},
      );
      ErrorHandler.logError(appError);
      return false;
    }
  }

  /// Get user signature information
  Future<Map<String, dynamic>?> getUserSignatureInfo() async {
    try {
      final userProfile = await _dataRepository.getUserProfile();
      if (userProfile?.signatureImageUrl == null) {
        return null;
      }

      return {
        'hasSignature': true,
        'signatureUrl': userProfile!.signatureImageUrl,
        'userName': userProfile.fullName,
        'userEmail': userProfile.email,
      };
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'getUserSignatureInfo'},
      );
      ErrorHandler.logError(appError);
      return null;
    }
  }

  /// Add signature to PDF document
  Future<Uint8List> _addSignatureToPdf({
    required Uint8List pdfBytes,
    required Uint8List signatureBytes,
    required String documentType,
    required String position,
    required UserProfile userProfile,
  }) async {
    try {
      // Create a new PDF document
      final pdf = pw.Document();

      // Create signature image
      final signatureImage = pw.MemoryImage(signatureBytes);

      // Add a signature page to the PDF
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Container(
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'CONTRACTOR SIGNATURE',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.blue800,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'This ${documentType.toLowerCase()} has been reviewed and approved by the contractor.',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 30),

                // Signature section
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    // Signature image
                    pw.Container(
                      width: 200,
                      height: 80,
                      padding: const pw.EdgeInsets.all(8),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.grey400),
                        borderRadius: pw.BorderRadius.circular(4),
                      ),
                      child: pw.Image(signatureImage, fit: pw.BoxFit.contain),
                    ),

                    pw.SizedBox(width: 30),

                    // Signature details
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            'Contractor Information:',
                            style: pw.TextStyle(
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                          pw.SizedBox(height: 8),
                          if (userProfile.fullName.isNotEmpty) ...[
                            pw.Text(
                              'Name: ${userProfile.fullName}',
                              style: const pw.TextStyle(fontSize: 11),
                            ),
                            pw.SizedBox(height: 4),
                          ],
                          if (userProfile.email != null) ...[
                            pw.Text(
                              'Email: ${userProfile.email}',
                              style: const pw.TextStyle(fontSize: 11),
                            ),
                            pw.SizedBox(height: 4),
                          ],
                          pw.Text(
                            'Date: ${DateTime.now().toLocal().toString().split(' ')[0]}',
                            style: const pw.TextStyle(fontSize: 11),
                          ),
                          pw.SizedBox(height: 4),
                          pw.Text(
                            'Time: ${DateTime.now().toLocal().toString().split(' ')[1].substring(0, 8)}',
                            style: const pw.TextStyle(fontSize: 11),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                pw.SizedBox(height: 30),

                // Legal notice
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Digital Signature Notice',
                        style: pw.TextStyle(
                          fontSize: 10,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'This document has been digitally signed by the contractor using their registered signature. '
                        'The signature above represents the contractor\'s agreement to the terms and conditions '
                        'outlined in this ${documentType.toLowerCase()}.',
                        style: const pw.TextStyle(fontSize: 9),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      );

      // Save the signature page as bytes
      final signaturePageBytes = await pdf.save();

      // For now, return just the signature page
      // In a full implementation, you would merge this with the original PDF
      return signaturePageBytes;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': '_addSignatureToPdf',
          'documentType': documentType,
          'position': position,
          'pdfSize': pdfBytes.length,
          'signatureSize': signatureBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Merge signature page with original PDF
  Future<Uint8List> mergeSignatureWithPdf({
    required Uint8List originalPdfBytes,
    required Uint8List signaturePageBytes,
  }) async {
    try {
      // Create a new PDF document
      final mergedPdf = pw.Document();

      // Add original PDF content (simplified - in production you'd parse the original PDF)
      mergedPdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Text(
                'Original Document Content\n(This would contain the actual PDF content)',
                textAlign: pw.TextAlign.center,
                style: const pw.TextStyle(fontSize: 14),
              ),
            );
          },
        ),
      );

      // Add signature page
      // Note: In a full implementation, you would properly parse and merge PDFs
      // This is a simplified version for demonstration

      return await mergedPdf.save();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'mergeSignatureWithPdf',
          'originalPdfSize': originalPdfBytes.length,
          'signaturePageSize': signaturePageBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
