import 'dart:async';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:quarterlies/models/models.dart';
import 'package:flutter/foundation.dart';

class LocalDatabaseService {
  static final LocalDatabaseService _instance =
      LocalDatabaseService._internal();
  static Database? _database;

  // Singleton pattern
  factory LocalDatabaseService() => _instance;

  LocalDatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'quarterlies.db');

    return await openDatabase(
      path,
      version: 2,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    if (oldVersion < 2) {
      // Add glare resistance fields to user_settings table
      await db.execute('''
        ALTER TABLE user_settings
        ADD COLUMN automatic_brightness_detection INTEGER NOT NULL DEFAULT 1
      ''');

      await db.execute('''
        ALTER TABLE user_settings
        ADD COLUMN dynamic_color_adjustment INTEGER NOT NULL DEFAULT 1
      ''');

      await db.execute('''
        ALTER TABLE user_settings
        ADD COLUMN enhanced_contrast_mode INTEGER NOT NULL DEFAULT 1
      ''');

      // Add missing job fields
      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN estimated_price REAL
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN actual_income REAL DEFAULT 0.0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN actual_expenses REAL DEFAULT 0.0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN actual_labor_cost REAL DEFAULT 0.0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN live_cost_sync_enabled INTEGER NOT NULL DEFAULT 0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN sync_expenses INTEGER NOT NULL DEFAULT 0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN sync_mileage INTEGER NOT NULL DEFAULT 0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN sync_labor_costs INTEGER NOT NULL DEFAULT 0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN sync_estimate_items INTEGER NOT NULL DEFAULT 1
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN summarize_mileage INTEGER NOT NULL DEFAULT 1
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN summarize_hours INTEGER NOT NULL DEFAULT 0
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN default_invoice_due_days INTEGER
      ''');

      await db.execute('''
        ALTER TABLE jobs
        ADD COLUMN voice_note_url TEXT
      ''');
    }
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create tables for all entity types

    // Tax payments table
    await db.execute('''
      CREATE TABLE tax_payments (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        date TEXT NOT NULL,
        amount REAL NOT NULL,
        tax_period TEXT NOT NULL,
        payment_method TEXT,
        confirmation_number TEXT,
        notes TEXT,
        voice_note_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL
      )
    ''');

    // Customers table
    await db.execute('''
      CREATE TABLE customers (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL
      )
    ''');

    // Contracts table
    await db.execute('''
      CREATE TABLE contracts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        estimate_id TEXT,
        total_amount REAL NOT NULL,
        status TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Contract items table
    await db.execute('''
      CREATE TABLE contract_items (
        id TEXT PRIMARY KEY,
        contract_id TEXT NOT NULL,
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        unit TEXT,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (contract_id) REFERENCES contracts (id) ON DELETE CASCADE
      )
    ''');

    // Jobs table
    await db.execute('''
      CREATE TABLE jobs (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL,
        address TEXT,
        city TEXT,
        state TEXT,
        zip_code TEXT,
        start_date TEXT,
        end_date TEXT,
        estimated_price REAL,
        estimated_expenses_budget REAL,
        actual_income REAL DEFAULT 0.0,
        actual_expenses REAL DEFAULT 0.0,
        actual_labor_cost REAL DEFAULT 0.0,
        live_cost_sync_enabled INTEGER NOT NULL DEFAULT 0,
        sync_expenses INTEGER NOT NULL DEFAULT 0,
        sync_mileage INTEGER NOT NULL DEFAULT 0,
        sync_labor_costs INTEGER NOT NULL DEFAULT 0,
        sync_estimate_items INTEGER NOT NULL DEFAULT 1,
        summarize_mileage INTEGER NOT NULL DEFAULT 1,
        summarize_hours INTEGER NOT NULL DEFAULT 0,
        default_invoice_due_days INTEGER,
        voice_note_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Invoices table
    await db.execute('''
      CREATE TABLE invoices (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        issue_date TEXT NOT NULL,
        due_date TEXT NOT NULL,
        total_amount REAL NOT NULL,
        amount_paid REAL NOT NULL,
        status TEXT NOT NULL,
        notes TEXT,
        voice_note_url TEXT,
        is_template INTEGER NOT NULL,
        template_name TEXT,
        template_id TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Invoice items table
    await db.execute('''
      CREATE TABLE invoice_items (
        id TEXT PRIMARY KEY,
        invoice_id TEXT NOT NULL,
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit TEXT NOT NULL,
        unit_price REAL NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    // Payments table
    await db.execute('''
      CREATE TABLE payments (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT NOT NULL,
        invoice_id TEXT,
        amount REAL NOT NULL,
        payment_date TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE SET NULL
      )
    ''');

    // Expenses table
    await db.execute('''
      CREATE TABLE expenses (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT,
        type TEXT NOT NULL,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        receipt_photo_url TEXT,
        tags TEXT,
        is_overhead INTEGER NOT NULL,
        category TEXT,
        voice_note_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE SET NULL
      )
    ''');

    // Time logs table
    await db.execute('''
      CREATE TABLE time_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT NOT NULL,
        date TEXT NOT NULL,
        hours REAL NOT NULL,
        notes TEXT,
        hourly_rate REAL NOT NULL,
        labor_cost REAL NOT NULL,
        is_flat_rate INTEGER NOT NULL,
        voice_note_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE
      )
    ''');

    // Document signing requests table
    await db.execute('''
      CREATE TABLE document_signing_requests (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        document_type TEXT NOT NULL,
        document_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        job_id TEXT NOT NULL,
        signing_link TEXT NOT NULL,
        expiration_date TEXT NOT NULL,
        status TEXT NOT NULL,
        local_pdf_path TEXT,
        remote_pdf_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE
      )
    ''');

    // Signed documents table
    await db.execute('''
      CREATE TABLE signed_documents (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        signing_request_id TEXT NOT NULL,
        document_type TEXT NOT NULL,
        document_id TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        customer_email TEXT NOT NULL,
        signed_at TEXT NOT NULL,
        ip_address TEXT,
        device_info TEXT,
        customer_pdf_path TEXT,
        contractor_pdf_path TEXT,
        customer_pdf_url TEXT,
        contractor_pdf_url TEXT,
        certification_pdf_path TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (signing_request_id) REFERENCES document_signing_requests (id) ON DELETE CASCADE
      )
    ''');

    // Estimates table
    await db.execute('''
      CREATE TABLE estimates (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        job_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        total_amount REAL NOT NULL,
        status TEXT NOT NULL,
        notes TEXT,
        template_id TEXT,
        is_template INTEGER NOT NULL DEFAULT 0,
        template_name TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (job_id) REFERENCES jobs (id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE
      )
    ''');

    // Estimate items table
    await db.execute('''
      CREATE TABLE estimate_items (
        id TEXT PRIMARY KEY,
        estimate_id TEXT NOT NULL,
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total REAL NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL,
        FOREIGN KEY (estimate_id) REFERENCES estimates (id) ON DELETE CASCADE
      )
    ''');

    // User profiles table
    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        email TEXT,
        phone TEXT,
        address TEXT,
        city TEXT,
        state TEXT,
        zip_code TEXT,
        country TEXT,
        profile_photo_url TEXT,
        signature_image_url TEXT,
        is_onboarding_complete INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL
      )
    ''');

    // User signatures table for offline storage
    await db.execute('''
      CREATE TABLE user_signatures (
        user_id TEXT PRIMARY KEY,
        signature_bytes BLOB NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL
      )
    ''');

    // Create indexes for better query performance
    await _createIndexes(db);
  }

  /// Create database indexes for improved query performance
  Future<void> _createIndexes(Database db) async {
    // Indexes for frequently queried columns

    // Customer indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_sync_status ON customers(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)',
    );

    // Job indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON jobs(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_jobs_customer_id ON jobs(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_jobs_sync_status ON jobs(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at)',
    );

    // Invoice indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_job_id ON invoices(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON invoices(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_sync_status ON invoices(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_invoices_issue_date ON invoices(issue_date)',
    );

    // Expense indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON expenses(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_job_id ON expenses(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_sync_status ON expenses(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_expenses_is_overhead ON expenses(is_overhead)',
    );

    // Time log indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON time_logs(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_time_logs_job_id ON time_logs(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_time_logs_sync_status ON time_logs(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_time_logs_date ON time_logs(date)',
    );

    // Tax payment indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_tax_payments_user_id ON tax_payments(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_tax_payments_sync_status ON tax_payments(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_tax_payments_date ON tax_payments(date)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_tax_payments_tax_period ON tax_payments(tax_period)',
    );

    // Estimate indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_user_id ON estimates(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_job_id ON estimates(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_customer_id ON estimates(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_sync_status ON estimates(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_status ON estimates(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_estimates_created_at ON estimates(created_at)',
    );

    // Contract indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_contracts_user_id ON contracts(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_contracts_job_id ON contracts(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_contracts_sync_status ON contracts(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_contracts_created_at ON contracts(created_at)',
    );

    // Document signing request indexes
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_user_id ON document_signing_requests(user_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_customer_id ON document_signing_requests(customer_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_job_id ON document_signing_requests(job_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_sync_status ON document_signing_requests(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_status ON document_signing_requests(status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_doc_signing_document_id_type ON document_signing_requests(document_id, document_type)',
    );

    // User settings table
    await db.execute('''
      CREATE TABLE user_settings (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        default_live_job_cost_sync INTEGER NOT NULL DEFAULT 0,
        sync_expenses INTEGER NOT NULL DEFAULT 0,
        sync_mileage INTEGER NOT NULL DEFAULT 0,
        sync_labor_costs INTEGER NOT NULL DEFAULT 0,
        sync_estimate_items INTEGER NOT NULL DEFAULT 1,
        default_invoice_due_days INTEGER NOT NULL DEFAULT 30,
        enable_due_date_notifications INTEGER NOT NULL DEFAULT 1,
        due_date_notification_days INTEGER NOT NULL DEFAULT 3,
        enable_mileage_tracking INTEGER NOT NULL DEFAULT 0,
        mileage_idle_timeout_minutes INTEGER NOT NULL DEFAULT 5,
        enable_voice_input INTEGER NOT NULL DEFAULT 1,
        enable_offline_mode INTEGER NOT NULL DEFAULT 1,
        wifi_only_sync INTEGER NOT NULL DEFAULT 1,
        business_name TEXT,
        business_address TEXT,
        business_phone TEXT,
        business_email TEXT,
        business_logo TEXT,
        default_invoice_notes TEXT,
        default_invoice_terms TEXT,
        show_mileage_as_summary INTEGER NOT NULL DEFAULT 1,
        show_hours_as_individual INTEGER NOT NULL DEFAULT 1,
        automatic_brightness_detection INTEGER NOT NULL DEFAULT 1,
        dynamic_color_adjustment INTEGER NOT NULL DEFAULT 1,
        enhanced_contrast_mode INTEGER NOT NULL DEFAULT 1,
        display_mode TEXT NOT NULL DEFAULT 'field',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL
      )
    ''');
  }

  // ==================== CUSTOMER OPERATIONS ====================

  Future<void> insertCustomer(
    Customer customer, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final customerMap = customer.toJson();
    customerMap['sync_status'] = syncStatus;

    await db.insert(
      'customers',
      customerMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Customer>> getCustomers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('customers');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      // Remove sync_status before creating Customer object
      map.remove('sync_status');
      return Customer.fromJson(map);
    });
  }

  /// Get customers with pagination support
  Future<List<Customer>> getCustomersPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      limit: limit,
      offset: offset,
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      // Remove sync_status before creating Customer object
      map.remove('sync_status');
      return Customer.fromJson(map);
    });
  }

  /// Get total number of customers
  Future<int> getCustomersCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM customers');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  Future<Customer?> getCustomerById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return Customer.fromJson(map);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncCustomers() async {
    final db = await database;
    return await db.query(
      'customers',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateCustomerSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'customers',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateCustomer(Customer customer) async {
    final db = await database;
    final customerMap = customer.toJson();
    customerMap['sync_status'] = 'pending';

    await db.update(
      'customers',
      customerMap,
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  // ==================== JOB OPERATIONS ====================

  Future<void> insertJob(Job job, {String syncStatus = 'pending'}) async {
    final db = await database;
    final jobMap = job.toJson();
    jobMap['sync_status'] = syncStatus;

    await db.insert(
      'jobs',
      jobMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Job>> getJobs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('jobs');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Job.fromJson(map);
    });
  }

  Future<Job?> getJobById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'jobs',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return Job.fromJson(map);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncJobs() async {
    final db = await database;
    return await db.query(
      'jobs',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateJob(Job job) async {
    final db = await database;
    final jobMap = job.toJson();
    jobMap['sync_status'] = 'pending';

    await db.update('jobs', jobMap, where: 'id = ?', whereArgs: [job.id]);
  }

  Future<void> updateJobSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'jobs',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== INVOICE OPERATIONS ====================

  Future<void> insertInvoice(
    Invoice invoice, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final invoiceMap = invoice.toJson();
    invoiceMap['sync_status'] = syncStatus;

    // Remove line_items from the map as they'll be inserted separately
    final lineItems = invoiceMap.remove('line_items');

    await db.insert(
      'invoices',
      invoiceMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert line items if they exist
    if (lineItems != null) {
      for (var item in lineItems) {
        item['invoice_id'] = invoice.id;
        item['sync_status'] = syncStatus;
        await db.insert(
          'invoice_items',
          item,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  Future<List<Invoice>> getInvoices() async {
    final db = await database;
    final List<Map<String, dynamic>> invoiceMaps = await db.query('invoices');

    List<Invoice> invoices = [];

    for (var invoiceMap in invoiceMaps) {
      // Get line items for this invoice
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'invoice_items',
        where: 'invoice_id = ?',
        whereArgs: [invoiceMap['id']],
      );

      // Remove sync_status from all maps
      invoiceMap.remove('sync_status');
      for (var itemMap in itemMaps) {
        itemMap.remove('sync_status');
      }

      // Add line items to invoice map
      invoiceMap['line_items'] = itemMaps;

      invoices.add(Invoice.fromJson(invoiceMap));
    }

    return invoices;
  }

  Future<Invoice?> getInvoiceById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> invoiceMaps = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (invoiceMaps.isEmpty) return null;

    final invoiceMap = invoiceMaps.first;

    // Get line items for this invoice
    final List<Map<String, dynamic>> itemMaps = await db.query(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [id],
    );

    // Remove sync_status from all maps
    invoiceMap.remove('sync_status');
    for (var itemMap in itemMaps) {
      itemMap.remove('sync_status');
    }

    // Add line items to invoice map
    invoiceMap['line_items'] = itemMaps;

    return Invoice.fromJson(invoiceMap);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncInvoices() async {
    final db = await database;
    return await db.query(
      'invoices',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateInvoiceSyncStatus(String id, String status) async {
    if (id.isEmpty) {
      debugPrint(
        'Warning: Attempted to update sync status with empty invoice ID',
      );
      return;
    }

    final db = await database;
    await db.update(
      'invoices',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateInvoice(Invoice invoice) async {
    final db = await database;
    final invoiceMap = invoice.toJson();
    invoiceMap['sync_status'] = 'pending';

    // Remove line_items from the map as they'll be updated separately
    final lineItems = invoiceMap.remove('line_items');

    await db.update(
      'invoices',
      invoiceMap,
      where: 'id = ?',
      whereArgs: [invoice.id],
    );

    // Delete existing line items
    await db.delete(
      'invoice_items',
      where: 'invoice_id = ?',
      whereArgs: [invoice.id],
    );

    // Insert updated line items
    if (lineItems != null) {
      for (var item in lineItems) {
        item['invoice_id'] = invoice.id;
        item['sync_status'] = 'pending';
        await db.insert(
          'invoice_items',
          item,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  // ==================== PAYMENT OPERATIONS ====================

  Future<void> insertPayment(
    Payment payment, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final paymentMap = payment.toJson();
    paymentMap['sync_status'] = syncStatus;

    await db.insert(
      'payments',
      paymentMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Payment>> getPayments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('payments');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Payment.fromJson(map);
    });
  }

  Future<List<Payment>> getPaymentsByInvoice(String invoiceId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Payment.fromJson(map);
    });
  }

  Future<List<Payment>> getPaymentsByJob(String jobId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'job_id = ?',
      whereArgs: [jobId],
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Payment.fromJson(map);
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncPayments() async {
    final db = await database;
    return await db.query(
      'payments',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updatePaymentSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'payments',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updatePayment(Payment payment) async {
    final db = await database;
    final paymentMap = payment.toJson();
    paymentMap['sync_status'] = 'pending';

    await db.update(
      'payments',
      paymentMap,
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  // Get payments by date range (for allocation calculations)
  Future<List<Payment>> getPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      where: 'payment_date >= ? AND payment_date <= ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'payment_date DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Payment.fromJson(map);
    });
  }

  // ==================== EXPENSE OPERATIONS ====================

  Future<void> insertExpense(
    Expense expense, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final expenseMap = expense.toJson();
    expenseMap['sync_status'] = syncStatus;

    // Convert tags list to string for storage
    if (expenseMap['tags'] != null) {
      expenseMap['tags'] = expenseMap['tags'].join(',');
    }

    await db.insert(
      'expenses',
      expenseMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Expense>> getExpenses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('expenses');

    return List.generate(maps.length, (i) {
      final map = maps[i];

      // Convert tags string back to list
      if (map['tags'] != null && map['tags'].isNotEmpty) {
        map['tags'] = map['tags'].split(',');
      } else {
        map['tags'] = [];
      }

      map.remove('sync_status');
      return Expense.fromJson(map);
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncExpenses() async {
    final db = await database;
    return await db.query(
      'expenses',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateExpenseSyncStatus(String id, dynamic status) async {
    final db = await database;
    final statusValue = status is String ? status : status.toJson();
    await db.update(
      'expenses',
      {'sync_status': statusValue},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get expenses by job ID
  Future<List<Expense>> getExpensesByJob(String jobId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'job_id = ?',
      whereArgs: [jobId],
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];

      // Convert tags string back to list
      if (map['tags'] != null && map['tags'].isNotEmpty) {
        map['tags'] = map['tags'].split(',');
      } else {
        map['tags'] = [];
      }

      map.remove('sync_status');
      return Expense.fromJson(map);
    });
  }

  // Update an existing expense
  Future<void> updateExpense(Expense expense) async {
    final db = await database;
    final expenseMap = expense.toJson();
    expenseMap['sync_status'] = 'pending';

    // Convert tags list to string for storage
    if (expenseMap['tags'] != null) {
      expenseMap['tags'] = expenseMap['tags'].join(',');
    }

    await db.update(
      'expenses',
      expenseMap,
      where: 'id = ?',
      whereArgs: [expense.id],
    );
  }

  Future<List<Expense>> getExpensesWithConflicts() async {
    // Mock implementation: Replace with actual logic to fetch expenses with conflicts
    await Future.delayed(const Duration(seconds: 1)); // Simulate a delay
    return [
      Expense(
        id: '1',
        userId: 'mockUserId',
        description: 'Office Supplies',
        amount: 45.99,
        date: DateTime.now().subtract(const Duration(days: 2)),
        category: 'Office',
      ),
      Expense(
        id: '2',
        userId: 'mockUserId',
        description: 'Travel Expense',
        amount: 120.50,
        date: DateTime.now().subtract(const Duration(days: 5)),
        category: 'Travel',
      ),
    ];
  }

  // Get all overhead expenses (not tied to a specific job)
  Future<List<Expense>> getOverheadExpenses() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'is_overhead = ?',
      whereArgs: [1], // SQLite uses 1 for true
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];

      // Convert tags string back to list
      if (map['tags'] != null && map['tags'].isNotEmpty) {
        map['tags'] = map['tags'].split(',');
      } else {
        map['tags'] = [];
      }

      map.remove('sync_status');
      return Expense.fromJson(map);
    });
  }

  // Get overhead expenses for a specific date range
  Future<List<Expense>> getOverheadExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'is_overhead = ? AND date >= ? AND date <= ?',
      whereArgs: [
        1, // SQLite uses 1 for true
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];

      // Convert tags string back to list
      if (map['tags'] != null && map['tags'].isNotEmpty) {
        map['tags'] = map['tags'].split(',');
      } else {
        map['tags'] = [];
      }

      map.remove('sync_status');
      return Expense.fromJson(map);
    });
  }

  // Get expenses by date range (for allocation calculations)
  Future<List<Expense>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'date >= ? AND date <= ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];

      // Convert tags string back to list
      if (map['tags'] != null && map['tags'].isNotEmpty) {
        map['tags'] = map['tags'].split(',');
      } else {
        map['tags'] = [];
      }

      map.remove('sync_status');
      return Expense.fromJson(map);
    });
  }

  // ==================== TIME LOG OPERATIONS ====================

  Future<void> insertTimeLog(
    TimeLog timeLog, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final timeLogMap = timeLog.toJson();
    timeLogMap['sync_status'] = syncStatus;

    await db.insert(
      'time_logs',
      timeLogMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<TimeLog>> getTimeLogs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('time_logs');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return TimeLog.fromJson(map);
    });
  }

  Future<List<TimeLog>> getTimeLogsByJob(String jobId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'time_logs',
      where: 'job_id = ?',
      whereArgs: [jobId],
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return TimeLog.fromJson(map);
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncTimeLogs() async {
    final db = await database;
    return await db.query(
      'time_logs',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateTimeLogSyncStatus(String id, dynamic status) async {
    final db = await database;
    final statusValue = status is String ? status : status.toJson();
    await db.update(
      'time_logs',
      {'sync_status': statusValue},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get time logs with pending sync status
  Future<List<TimeLog>> getPendingTimeLogs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'time_logs',
      where: "sync_status = ? OR sync_status = ?",
      whereArgs: ['pending', 'error'],
    );

    return List.generate(maps.length, (i) {
      return TimeLog.fromJson(maps[i]);
    });
  }

  // Get a specific time log by ID
  Future<TimeLog?> getTimeLogById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'time_logs',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) {
      return null;
    }

    return TimeLog.fromJson(maps.first);
  }

  // Update an entire time log
  Future<void> updateTimeLog(TimeLog timeLog) async {
    final db = await database;
    await db.update(
      'time_logs',
      timeLog.toJson(),
      where: 'id = ?',
      whereArgs: [timeLog.id],
    );
  }

  // Clear all data (for testing or reset)
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('time_logs');
    await db.delete('expenses');
    await db.delete('payments');
    await db.delete('invoice_items');
    await db.delete('invoices');
    await db.delete('contract_items');
    await db.delete('contracts');
    await db.delete('estimate_items');
    await db.delete('estimates');
    await db.delete('jobs');
    await db.delete('customers');
    await db.delete('tax_payments');
    await db.delete('signed_documents');
    await db.delete('document_signing_requests');
    await db.delete('user_settings');
  }

  // ==================== DELETE OPERATIONS ====================

  // Delete a customer
  Future<void> deleteCustomer(String id) async {
    final db = await database;
    await db.delete('customers', where: 'id = ?', whereArgs: [id]);
  }

  // Delete a job
  Future<void> deleteJob(String id) async {
    final db = await database;
    await db.delete('jobs', where: 'id = ?', whereArgs: [id]);
  }

  // Delete an invoice
  Future<void> deleteInvoice(String id) async {
    final db = await database;
    // First delete all line items
    await db.delete('invoice_items', where: 'invoice_id = ?', whereArgs: [id]);
    // Then delete the invoice
    await db.delete('invoices', where: 'id = ?', whereArgs: [id]);
  }

  // Delete a contract
  Future<void> deleteContract(String id) async {
    final db = await database;
    // First delete all line items
    await db.delete(
      'contract_items',
      where: 'contract_id = ?',
      whereArgs: [id],
    );
    // Then delete the contract
    await db.delete('contracts', where: 'id = ?', whereArgs: [id]);
  }

  // Delete an expense
  Future<void> deleteExpense(String id) async {
    final db = await database;
    await db.delete('expenses', where: 'id = ?', whereArgs: [id]);
  }

  // Delete a time log
  Future<void> deleteTimeLog(String id) async {
    final db = await database;
    await db.delete('time_logs', where: 'id = ?', whereArgs: [id]);
  }

  // Delete a tax payment
  Future<void> deleteTaxPayment(String id) async {
    final db = await database;
    await db.delete('tax_payments', where: 'id = ?', whereArgs: [id]);
  }

  // Delete a payment
  Future<void> deletePayment(String id) async {
    final db = await database;
    await db.delete('payments', where: 'id = ?', whereArgs: [id]);
  }

  // ==================== CONTRACT OPERATIONS ====================

  Future<void> insertContract(
    Contract contract, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final contractMap = contract.toJson();
    contractMap['sync_status'] = syncStatus;

    // Remove line_items from the map as they'll be inserted separately
    final lineItems = contractMap.remove('line_items');

    await db.insert(
      'contracts',
      contractMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert line items if they exist
    if (lineItems != null) {
      for (var item in lineItems) {
        item['contract_id'] = contract.id;
        item['sync_status'] = syncStatus;
        await db.insert(
          'contract_items',
          item,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  Future<List<Contract>> getContracts() async {
    final db = await database;
    final List<Map<String, dynamic>> contractMaps = await db.query('contracts');

    List<Contract> contracts = [];

    for (var contractMap in contractMaps) {
      // Get line items for this contract
      final List<Map<String, dynamic>> itemMaps = await db.query(
        'contract_items',
        where: 'contract_id = ?',
        whereArgs: [contractMap['id']],
      );

      // Remove sync_status from all maps
      contractMap.remove('sync_status');
      for (var itemMap in itemMaps) {
        itemMap.remove('sync_status');
        itemMap.remove('contract_id');
      }

      // Add line items to contract map
      contractMap['line_items'] = itemMaps;

      contracts.add(Contract.fromJson(contractMap));
    }

    return contracts;
  }

  Future<Contract?> getContractById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> contractMaps = await db.query(
      'contracts',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (contractMaps.isEmpty) return null;

    final contractMap = contractMaps.first;

    // Get line items for this contract
    final List<Map<String, dynamic>> itemMaps = await db.query(
      'contract_items',
      where: 'contract_id = ?',
      whereArgs: [id],
    );

    // Remove sync_status from all maps
    contractMap.remove('sync_status');
    for (var itemMap in itemMaps) {
      itemMap.remove('sync_status');
      itemMap.remove('contract_id');
    }

    // Add line items to contract map
    contractMap['line_items'] = itemMaps;

    return Contract.fromJson(contractMap);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncContracts() async {
    final db = await database;
    return await db.query(
      'contracts',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  // Pagination methods for missing entities
  Future<List<Job>> getJobsPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'jobs',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Job.fromJson(maps[i]);
    });
  }

  Future<List<TimeLog>> getTimeLogsPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'time_logs',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return TimeLog.fromJson(maps[i]);
    });
  }

  Future<List<Payment>> getPaymentsPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'payments',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Payment.fromJson(maps[i]);
    });
  }

  Future<List<Invoice>> getInvoicesPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'invoices',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Invoice.fromJson(maps[i]);
    });
  }

  Future<List<Expense>> getExpensesPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Expense.fromJson(maps[i]);
    });
  }

  Future<List<TaxPayment>> getTaxPaymentsPaginated(
    int offset,
    int limit,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tax_payments',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return TaxPayment.fromJson(maps[i]);
    });
  }

  Future<List<Estimate>> getEstimatesPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'estimates',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Estimate.fromJson(maps[i]);
    });
  }

  Future<List<Contract>> getContractsPaginated(int offset, int limit) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'contracts',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Contract.fromJson(maps[i]);
    });
  }

  Future<List<DocumentSigningRequest>> getDocumentSigningRequestsPaginated(
    int offset,
    int limit,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_signing_requests',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return DocumentSigningRequest.fromJson(maps[i]);
    });
  }

  Future<List<SignedDocument>> getSignedDocumentsPaginated(
    int offset,
    int limit,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'signed_documents',
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return SignedDocument.fromJson(maps[i]);
    });
  }

  Future<void> updateContractSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'contracts',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateContract(Contract contract) async {
    final db = await database;
    final contractMap = contract.toJson();
    contractMap['sync_status'] = 'pending';

    // Remove line_items from the map as they'll be updated separately
    final lineItems = contractMap.remove('line_items');

    await db.update(
      'contracts',
      contractMap,
      where: 'id = ?',
      whereArgs: [contract.id],
    );

    // Delete existing line items
    await db.delete(
      'contract_items',
      where: 'contract_id = ?',
      whereArgs: [contract.id],
    );

    // Insert updated line items
    if (lineItems != null) {
      for (var item in lineItems) {
        item['contract_id'] = contract.id;
        item['sync_status'] = 'pending';
        await db.insert(
          'contract_items',
          item,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  // ==================== TAX PAYMENT OPERATIONS ====================

  Future<void> insertTaxPayment(
    TaxPayment payment, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final paymentMap = payment.toJson();
    paymentMap['sync_status'] = syncStatus;

    await db.insert(
      'tax_payments',
      paymentMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<TaxPayment>> getTaxPayments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('tax_payments');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return TaxPayment.fromJson(map);
    });
  }

  Future<List<TaxPayment>> getTaxPaymentsByPeriod(String taxPeriod) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tax_payments',
      where: 'tax_period = ?',
      whereArgs: [taxPeriod],
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return TaxPayment.fromJson(map);
    });
  }

  Future<void> updateTaxPayment(TaxPayment payment) async {
    final db = await database;
    final paymentMap = payment.toJson();
    paymentMap['sync_status'] = 'pending';

    await db.update(
      'tax_payments',
      paymentMap,
      where: 'id = ?',
      whereArgs: [payment.id],
    );
  }

  Future<List<Map<String, dynamic>>> getPendingSyncTaxPayments() async {
    final db = await database;
    return await db.query(
      'tax_payments',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateTaxPaymentSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'tax_payments',
      {'sync_status': status},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get tax payments by date range
  Future<List<TaxPayment>> getTaxPaymentsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tax_payments',
      where: 'payment_date >= ? AND payment_date <= ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'payment_date DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return TaxPayment.fromJson(map);
    });
  }

  Future<List<Mileage>> getMileages() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('mileages');

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return Mileage.fromJson(map);
    });
  }

  // ==================== DOCUMENT SIGNING REQUEST OPERATIONS ====================

  Future<void> insertDocumentSigningRequest(
    DocumentSigningRequest request, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final requestMap = request.toJson();
    requestMap['sync_status'] = syncStatus;

    await db.insert(
      'document_signing_requests',
      requestMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<DocumentSigningRequest>> getDocumentSigningRequests() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_signing_requests',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return DocumentSigningRequest.fromJson(map);
    });
  }

  Future<DocumentSigningRequest?> getDocumentSigningRequestById(
    String id,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_signing_requests',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return DocumentSigningRequest.fromJson(map);
  }

  Future<DocumentSigningRequest?> getDocumentSigningRequestByDocumentId(
    String documentId,
    String documentType,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'document_signing_requests',
      where: 'document_id = ? AND document_type = ?',
      whereArgs: [documentId, documentType],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return DocumentSigningRequest.fromJson(map);
  }

  Future<List<Map<String, dynamic>>>
  getPendingSyncDocumentSigningRequests() async {
    final db = await database;
    return await db.query(
      'document_signing_requests',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateDocumentSigningRequestSyncStatus(
    String id,
    String status,
  ) async {
    final db = await database;
    await db.update(
      'document_signing_requests',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateDocumentSigningRequestStatus(
    String id,
    String status,
  ) async {
    final db = await database;
    await db.update(
      'document_signing_requests',
      {'status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteDocumentSigningRequest(String id) async {
    final db = await database;
    await db.delete(
      'document_signing_requests',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== SIGNED DOCUMENT OPERATIONS ====================

  Future<void> insertSignedDocument(
    SignedDocument document, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final documentMap = document.toJson();
    documentMap['sync_status'] = syncStatus;

    await db.insert(
      'signed_documents',
      documentMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<SignedDocument>> getSignedDocuments() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'signed_documents',
      orderBy: 'signed_at DESC',
    );

    return List.generate(maps.length, (i) {
      final map = maps[i];
      map.remove('sync_status');
      return SignedDocument.fromJson(map);
    });
  }

  Future<SignedDocument?> getSignedDocumentById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'signed_documents',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return SignedDocument.fromJson(map);
  }

  Future<SignedDocument?> getSignedDocumentBySigningRequestId(
    String signingRequestId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'signed_documents',
      where: 'signing_request_id = ?',
      whereArgs: [signingRequestId],
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return SignedDocument.fromJson(map);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncSignedDocuments() async {
    final db = await database;
    return await db.query(
      'signed_documents',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateSignedDocumentSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'signed_documents',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteSignedDocument(String id) async {
    final db = await database;
    await db.delete('signed_documents', where: 'id = ?', whereArgs: [id]);
  }

  // ==================== ESTIMATE OPERATIONS ====================

  Future<void> insertEstimate(
    Estimate estimate, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final estimateMap = estimate.toJson();
    estimateMap['sync_status'] = syncStatus;

    // Remove line_items from the map as they'll be inserted separately
    final lineItems = estimateMap.remove('line_items');

    await db.insert(
      'estimates',
      estimateMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert line items if they exist
    if (lineItems != null) {
      for (var item in lineItems) {
        item['estimate_id'] = estimate.id;
        item['sync_status'] = syncStatus;
        await db.insert(
          'estimate_items',
          item,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  Future<List<Estimate>> getEstimates() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'estimates',
      orderBy: 'created_at DESC',
    );

    List<Estimate> estimates = [];
    for (var map in maps) {
      // Get line items for this estimate
      final lineItemMaps = await db.query(
        'estimate_items',
        where: 'estimate_id = ?',
        whereArgs: [map['id']],
      );

      // Convert line items to EstimateItem objects
      final lineItems =
          lineItemMaps.map((itemMap) {
            itemMap.remove('sync_status');
            return EstimateItem.fromJson(itemMap);
          }).toList();

      // Add line items to the estimate map
      map['line_items'] = lineItems.map((item) => item.toJson()).toList();
      map.remove('sync_status');

      estimates.add(Estimate.fromJson(map));
    }

    return estimates;
  }

  Future<Estimate?> getEstimateById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'estimates',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;

    // Get line items for this estimate
    final lineItemMaps = await db.query(
      'estimate_items',
      where: 'estimate_id = ?',
      whereArgs: [id],
    );

    // Convert line items to EstimateItem objects
    final lineItems =
        lineItemMaps.map((itemMap) {
          itemMap.remove('sync_status');
          return EstimateItem.fromJson(itemMap);
        }).toList();

    // Add line items to the estimate map
    map['line_items'] = lineItems.map((item) => item.toJson()).toList();
    map.remove('sync_status');

    return Estimate.fromJson(map);
  }

  Future<List<Estimate>> getEstimatesByJobId(String jobId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'estimates',
      where: 'job_id = ?',
      whereArgs: [jobId],
      orderBy: 'created_at DESC',
    );

    List<Estimate> estimates = [];
    for (var map in maps) {
      // Get line items for this estimate
      final lineItemMaps = await db.query(
        'estimate_items',
        where: 'estimate_id = ?',
        whereArgs: [map['id']],
      );

      // Convert line items to EstimateItem objects
      final lineItems =
          lineItemMaps.map((itemMap) {
            itemMap.remove('sync_status');
            return EstimateItem.fromJson(itemMap);
          }).toList();

      // Add line items to the estimate map
      map['line_items'] = lineItems.map((item) => item.toJson()).toList();
      map.remove('sync_status');

      estimates.add(Estimate.fromJson(map));
    }

    return estimates;
  }

  Future<List<Estimate>> getEstimateTemplates() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'estimates',
      where: 'is_template = ?',
      whereArgs: [1],
      orderBy: 'template_name ASC',
    );

    List<Estimate> templates = [];
    for (var map in maps) {
      // Get line items for this template
      final lineItemMaps = await db.query(
        'estimate_items',
        where: 'estimate_id = ?',
        whereArgs: [map['id']],
      );

      // Convert line items to EstimateItem objects
      final lineItems =
          lineItemMaps.map((itemMap) {
            itemMap.remove('sync_status');
            return EstimateItem.fromJson(itemMap);
          }).toList();

      // Add line items to the estimate map
      map['line_items'] = lineItems.map((item) => item.toJson()).toList();
      map.remove('sync_status');

      templates.add(Estimate.fromJson(map));
    }

    return templates;
  }

  Future<List<Map<String, dynamic>>> getPendingSyncEstimates() async {
    final db = await database;
    return await db.query(
      'estimates',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateEstimateSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'estimates',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteEstimate(String id) async {
    final db = await database;
    await db.delete('estimates', where: 'id = ?', whereArgs: [id]);
  }

  // ==================== USER SETTINGS OPERATIONS ====================

  Future<void> insertUserSettings(
    UserSettings settings, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final settingsMap = settings.toJson();
    settingsMap['sync_status'] = syncStatus;

    await db.insert(
      'user_settings',
      settingsMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserSettings?> getUserSettings(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_settings',
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return UserSettings.fromJson(map);
  }

  Future<UserSettings?> getUserSettingsById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_settings',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return UserSettings.fromJson(map);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncUserSettings() async {
    final db = await database;
    return await db.query(
      'user_settings',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateUserSettingsSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'user_settings',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteUserSettings(String id) async {
    final db = await database;
    await db.delete('user_settings', where: 'id = ?', whereArgs: [id]);
  }

  // ==================== USER PROFILE OPERATIONS ====================

  Future<void> insertUserProfile(
    UserProfile profile, {
    String syncStatus = 'pending',
  }) async {
    final db = await database;
    final profileMap = profile.toJson();
    profileMap['sync_status'] = syncStatus;

    await db.insert(
      'user_profiles',
      profileMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<UserProfile?> getUserProfile(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_profiles',
      where: 'user_id = ?',
      whereArgs: [userId],
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return UserProfile.fromJson(map);
  }

  Future<UserProfile?> getUserProfileById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_profiles',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    map.remove('sync_status');
    return UserProfile.fromJson(map);
  }

  Future<List<Map<String, dynamic>>> getPendingSyncUserProfiles() async {
    final db = await database;
    return await db.query(
      'user_profiles',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }

  Future<void> updateUserProfileSyncStatus(String id, String status) async {
    final db = await database;
    await db.update(
      'user_profiles',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteUserProfile(String id) async {
    final db = await database;
    await db.delete('user_profiles', where: 'id = ?', whereArgs: [id]);
  }

  // ==================== USER SIGNATURE OPERATIONS ====================

  /// Store user signature bytes locally (offline-first)
  Future<void> storeUserSignature({
    required String userId,
    required Uint8List signatureBytes,
    required String syncStatus,
  }) async {
    final db = await database;
    final now = DateTime.now().toIso8601String();

    await db.insert('user_signatures', {
      'user_id': userId,
      'signature_bytes': signatureBytes,
      'created_at': now,
      'updated_at': now,
      'sync_status': syncStatus,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// Get user signature bytes from local storage
  Future<Uint8List?> getUserSignature(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_signatures',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    if (maps.isEmpty) return null;

    final signatureData = maps.first['signature_bytes'];
    if (signatureData is Uint8List) {
      return signatureData;
    }
    return null;
  }

  /// Update user signature sync status
  Future<void> updateUserSignatureSyncStatus(
    String userId,
    String status,
  ) async {
    final db = await database;
    await db.update(
      'user_signatures',
      {'sync_status': status, 'updated_at': DateTime.now().toIso8601String()},
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }

  /// Delete user signature from local storage
  Future<void> deleteUserSignature(String userId) async {
    final db = await database;
    await db.delete(
      'user_signatures',
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }

  /// Get pending signature sync operations
  Future<List<Map<String, dynamic>>> getPendingSyncSignatures() async {
    final db = await database;
    return await db.query(
      'user_signatures',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );
  }
}
