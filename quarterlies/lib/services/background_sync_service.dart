import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart' as workmanager;
import 'package:background_fetch/background_fetch.dart' as background_fetch;
import 'package:quarterlies/services/sync_manager.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/app_constants.dart';
import 'package:quarterlies/utils/error_handler.dart';

/// BackgroundSyncService handles platform-specific background synchronization
/// using WorkManager for Android and Background App Refresh for iOS.
class BackgroundSyncService {
  // Singleton pattern
  static final BackgroundSyncService _instance =
      BackgroundSyncService._internal();
  factory BackgroundSyncService() => _instance;
  BackgroundSyncService._internal();

  // Constants for task identifiers
  static const String periodicSyncTaskName = AppConstants.periodicSyncTaskName;
  static const String oneTimeSyncTaskName = AppConstants.oneTimeSyncTaskName;

  // Initialize the background sync service
  Future<void> initialize() async {
    if (Platform.isAndroid) {
      await _initializeAndroid();
    } else if (Platform.isIOS) {
      await _initializeIOS();
    }
  }

  // Initialize WorkManager for Android
  Future<void> _initializeAndroid() async {
    await workmanager.Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );

    // Schedule periodic sync task
    await workmanager.Workmanager().registerPeriodicTask(
      periodicSyncTaskName,
      periodicSyncTaskName,
      frequency: Duration(minutes: AppConstants.backgroundSyncFrequencyMinutes),
      constraints: workmanager.Constraints(
        networkType: workmanager.NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
      existingWorkPolicy: workmanager.ExistingWorkPolicy.keep,
      backoffPolicy: workmanager.BackoffPolicy.linear,
      backoffPolicyDelay: const Duration(minutes: 5),
    );
  }

  // Initialize Background Fetch for iOS
  Future<void> _initializeIOS() async {
    await background_fetch.BackgroundFetch.configure(
      background_fetch.BackgroundFetchConfig(
        minimumFetchInterval: AppConstants.backgroundSyncFrequencyMinutes,
        stopOnTerminate: false,
        enableHeadless: true,
        requiresBatteryNotLow: true,
        requiresCharging: false,
        requiresStorageNotLow: false,
        requiresDeviceIdle: false,
        requiredNetworkType: background_fetch.NetworkType.ANY,
      ),
      _onBackgroundFetch,
      _onBackgroundFetchTimeout,
    );
  }

  // Schedule a one-time sync task (Android only)
  Future<void> scheduleOneTimeSync() async {
    if (Platform.isAndroid) {
      await workmanager.Workmanager().registerOneOffTask(
        oneTimeSyncTaskName,
        oneTimeSyncTaskName,
        constraints: workmanager.Constraints(
          networkType: workmanager.NetworkType.connected,
        ),
        existingWorkPolicy: workmanager.ExistingWorkPolicy.replace,
      );
    }
    // On iOS, we rely on the periodic background fetch
  }

  // Cancel all background sync tasks
  Future<void> cancelAllTasks() async {
    if (Platform.isAndroid) {
      await workmanager.Workmanager().cancelAll();
    } else if (Platform.isIOS) {
      await background_fetch.BackgroundFetch.stop();
    }
  }
}

// Background fetch callback for iOS
void _onBackgroundFetch(String taskId) async {
  try {
    if (kDebugMode) {
      debugPrint('[BackgroundFetch] Event received: $taskId');
    }

    // Perform sync operation
    final syncManager = SyncManager();
    await syncManager.syncData();

    // Signal completion
    background_fetch.BackgroundFetch.finish(taskId);
  } catch (e) {
    final appError = AppError.fromException(
      e,
      context: {'operation': '_onBackgroundFetch', 'taskId': taskId},
    );
    ErrorHandler.logError(appError);

    // Signal completion even on error to prevent system penalties
    background_fetch.BackgroundFetch.finish(taskId);
  }
}

// Background fetch timeout callback for iOS
void _onBackgroundFetchTimeout(String taskId) {
  final appError = AppError(
    type: ErrorType.timeout,
    severity: ErrorSeverity.medium,
    message: 'Background fetch task timed out',
    userFriendlyMessage: 'Background sync timed out. Will retry later.',
    context: {'operation': '_onBackgroundFetchTimeout', 'taskId': taskId},
  );
  ErrorHandler.logError(appError);
  background_fetch.BackgroundFetch.finish(taskId);
}

// WorkManager callback dispatcher for Android
@pragma('vm:entry-point')
void callbackDispatcher() {
  workmanager.Workmanager().executeTask((task, inputData) async {
    try {
      if (kDebugMode) {
        debugPrint('[WorkManager] Executing task: $task');
      }

      // Initialize data repository
      final dataRepository = DataRepository();
      await dataRepository.initialize();

      // Perform sync operation
      final syncManager = SyncManager();
      await syncManager.syncData();

      return true; // Task completed successfully
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'callbackDispatcher',
          'task': task,
          'inputData': inputData?.toString(),
        },
      );
      ErrorHandler.logError(appError);
      return false; // Task failed
    }
  });
}
