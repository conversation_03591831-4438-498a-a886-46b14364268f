import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';

class OcrService {
  final TextRecognizer _textRecognizer = TextRecognizer();
  final LoadingStateProvider? _loadingStateProvider;

  OcrService({LoadingStateProvider? loadingStateProvider})
    : _loadingStateProvider = loadingStateProvider;

  Future<OcrResult> processReceiptImage(File imageFile) async {
    final loadingProvider = _loadingStateProvider;
    if (loadingProvider != null) {
      return await loadingProvider.executeWithOcrLoading(() async {
        try {
          // Validate input file
          if (!await imageFile.exists()) {
            throw Exception('Image file does not exist');
          }

          final inputImage = InputImage.fromFile(imageFile);
          final RecognizedText recognizedText = await _textRecognizer
              .processImage(inputImage);

          return _extractReceiptData(recognizedText.text);
        } catch (e) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'processReceiptImage',
              'imagePath': imageFile.path,
              'imageExists': await imageFile.exists(),
            },
          );
          ErrorHandler.logError(appError);
          return OcrResult();
        }
      }, stage: 'Processing receipt image...');
    } else {
      // Fallback to original implementation
      try {
        // Validate input file
        if (!await imageFile.exists()) {
          throw Exception('Image file does not exist');
        }

        final inputImage = InputImage.fromFile(imageFile);
        final RecognizedText recognizedText = await _textRecognizer
            .processImage(inputImage);

        return _extractReceiptData(recognizedText.text);
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'processReceiptImage',
            'imagePath': imageFile.path,
            'imageExists': await imageFile.exists(),
          },
        );
        ErrorHandler.logError(appError);
        return OcrResult();
      }
    }
  }

  OcrResult _extractReceiptData(String text) {
    final result = OcrResult();
    final lines = text.split('\n');

    // Extract total amount
    result.amount = _extractAmount(text);

    // Extract date
    result.date = _extractDate(text);

    // Extract merchant/description
    result.description = _extractMerchant(lines);

    return result;
  }

  double? _extractAmount(String text) {
    // Common patterns for total amount
    final patterns = [
      RegExp(
        r'(?:total|amount|balance|sum)\s*(?:due|paid)?\s*[:\$]?\s*(\d+\.\d{2})',
        caseSensitive: false,
      ),
      RegExp(r'\$\s*(\d+\.\d{2})', caseSensitive: false),
      RegExp(r'(\d+\.\d{2})\s*(?:USD|\$)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.groupCount >= 1) {
        final amount = double.tryParse(match.group(1)!);
        if (amount != null && amount > 0) {
          return amount;
        }
      }
    }

    return null;
  }

  DateTime? _extractDate(String text) {
    // Common date formats: MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD
    final patterns = [
      RegExp(r'(\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4})', caseSensitive: false),
      RegExp(
        r'(?:date|time)\s*:?\s*(\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4})',
        caseSensitive: false,
      ),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.groupCount >= 1) {
        final dateStr = match.group(1)!;

        // Try different date formats
        try {
          // MM/DD/YYYY
          if (dateStr.contains('/')) {
            final parts = dateStr.split('/');
            if (parts.length == 3) {
              int? year = int.tryParse(parts[2]);
              int? month = int.tryParse(parts[0]);
              int? day = int.tryParse(parts[1]);

              // Handle 2-digit years
              if (year != null && year < 100) {
                year += 2000;
              }

              if (year != null && month != null && day != null) {
                if (month <= 12 && day <= 31) {
                  return DateTime(year, month, day);
                }
              }
            }
          }
          // YYYY-MM-DD
          else if (dateStr.contains('-')) {
            final parts = dateStr.split('-');
            if (parts.length == 3) {
              int? year = int.tryParse(parts[0]);
              int? month = int.tryParse(parts[1]);
              int? day = int.tryParse(parts[2]);

              if (year != null && month != null && day != null) {
                if (month <= 12 && day <= 31) {
                  return DateTime(year, month, day);
                }
              }
            }
          }
        } catch (e) {
          debugPrint('Error parsing date: $e');
        }
      }
    }

    return null;
  }

  String? _extractMerchant(List<String> lines) {
    // Usually the merchant name is in the first few lines
    if (lines.isNotEmpty) {
      // Skip empty lines
      for (final line in lines) {
        final trimmed = line.trim();
        if (trimmed.isNotEmpty && trimmed.length > 3) {
          return trimmed;
        }
      }
    }

    return null;
  }

  void dispose() {
    try {
      _textRecognizer.close();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'disposeOcrService'},
      );
      ErrorHandler.logError(appError);
      debugPrint('Error disposing OCR service: $e');
    }
  }
}

class OcrResult {
  double? amount;
  DateTime? date;
  String? description;

  OcrResult({this.amount, this.date, this.description});

  bool get hasData => amount != null || date != null || description != null;
}
