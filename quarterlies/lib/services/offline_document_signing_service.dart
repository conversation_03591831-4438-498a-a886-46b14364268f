import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/services/local_document_storage_service.dart';
import 'package:quarterlies/services/signature_certification_service.dart';
import 'package:quarterlies/services/pdf_merger_service.dart';
import 'package:quarterlies/services/cache_manager.dart';

/// Offline-first document signing service that follows the same patterns as other app services
class OfflineDocumentSigningService {
  // Singleton pattern
  static final OfflineDocumentSigningService _instance =
      OfflineDocumentSigningService._internal();
  factory OfflineDocumentSigningService() => _instance;
  OfflineDocumentSigningService._internal();

  final SupabaseClient _supabaseClient = Supabase.instance.client;
  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final LocalDocumentStorageService _documentStorageService =
      LocalDocumentStorageService();
  final SignatureCertificationService _certificationService =
      SignatureCertificationService();
  final PdfMergerService _pdfMergerService = PdfMergerService();
  final CacheManager _cacheManager = CacheManager();

  /// Create a signing request for a document (offline-first)
  ///
  /// This method:
  /// 1. Stores the PDF locally
  /// 2. Creates a local signing request record
  /// 3. Queues for sync when online
  /// 4. Returns the signing link (for future use when online)
  Future<String> createSigningRequest({
    required Uint8List pdfBytes,
    required String documentType, // 'estimate' or 'contract'
    required String documentId,
    required String customerId,
    required String customerEmail,
    required String customerName,
    required Job job,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;
      final requestId = const Uuid().v4();
      final signingId = const Uuid().v4();
      final expirationDate = DateTime.now().add(const Duration(days: 7));

      // 1. Store PDF locally
      final localPdfPath = await _documentStorageService.storeSigningRequestPdf(
        pdfBytes: pdfBytes,
        documentType: documentType,
        documentId: documentId,
        requestId: requestId,
      );

      // 2. Create signing link (will be used when online)
      final signingLink = 'https://quarterlies.com/sign/$signingId';

      // 3. Create local signing request record
      final signingRequest = DocumentSigningRequest(
        id: requestId,
        userId: userId,
        documentType: documentType,
        documentId: documentId,
        customerId: customerId,
        customerEmail: customerEmail,
        customerName: customerName,
        jobId: job.id,
        signingLink: signingLink,
        expirationDate: expirationDate,
        status: 'pending',
        localPdfPath: localPdfPath,
        syncStatus: SyncStatus.pending,
      );

      // 4. Store in local database
      await _localDatabaseService.insertDocumentSigningRequest(signingRequest);

      // 5. Record access for cache management
      _cacheManager.recordDocumentSigningRequestAccess(requestId);

      debugPrint('Created offline signing request: $requestId');
      return signingLink;
    } catch (e) {
      debugPrint('Error creating offline signing request: $e');
      throw Exception('Failed to create signing request: $e');
    }
  }

  /// Check if a document has been signed (offline-first)
  Future<bool> isDocumentSigned(String documentId, String documentType) async {
    try {
      // First check local database
      final localRequest = await _localDatabaseService
          .getDocumentSigningRequestByDocumentId(documentId, documentType);

      if (localRequest != null) {
        // Check if there's a signed document for this request
        final signedDocument = await _localDatabaseService
            .getSignedDocumentBySigningRequestId(localRequest.id);
        if (signedDocument != null) {
          return true;
        }
      }

      // If online, also check remote
      if (await _isOnline()) {
        try {
          final response =
              await _supabaseClient
                  .from('document_signing_requests')
                  .select()
                  .eq('document_id', documentId)
                  .eq('document_type', documentType)
                  .eq('status', 'signed')
                  .limit(1)
                  .maybeSingle();

          return response != null;
        } catch (e) {
          debugPrint('Error checking remote signing status: $e');
          // Fall back to local result
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if document is signed: $e');
      return false;
    }
  }

  /// Get signing request by document ID (offline-first)
  Future<DocumentSigningRequest?> getSigningRequestByDocumentId(
    String documentId,
    String documentType,
  ) async {
    try {
      // Record access for cache management
      final request = await _localDatabaseService
          .getDocumentSigningRequestByDocumentId(documentId, documentType);

      if (request != null) {
        _cacheManager.recordDocumentSigningRequestAccess(request.id);
      }

      return request;
    } catch (e) {
      debugPrint('Error getting signing request: $e');
      return null;
    }
  }

  /// Get all signing requests (offline-first)
  Future<List<DocumentSigningRequest>> getSigningRequests() async {
    try {
      return await _localDatabaseService.getDocumentSigningRequests();
    } catch (e) {
      debugPrint('Error getting signing requests: $e');
      return [];
    }
  }

  /// Get signed document by signing request ID (offline-first)
  Future<SignedDocument?> getSignedDocumentByRequestId(
    String signingRequestId,
  ) async {
    try {
      final document = await _localDatabaseService
          .getSignedDocumentBySigningRequestId(signingRequestId);

      if (document != null) {
        _cacheManager.recordSignedDocumentAccess(document.id);
      }

      return document;
    } catch (e) {
      debugPrint('Error getting signed document: $e');
      return null;
    }
  }

  /// Get all signed documents (offline-first)
  Future<List<SignedDocument>> getSignedDocuments() async {
    try {
      return await _localDatabaseService.getSignedDocuments();
    } catch (e) {
      debugPrint('Error getting signed documents: $e');
      return [];
    }
  }

  /// Read a locally stored PDF
  Future<Uint8List?> readLocalPdf(String localPath) async {
    try {
      return await _documentStorageService.readPdf(localPath);
    } catch (e) {
      debugPrint('Error reading local PDF: $e');
      return null;
    }
  }

  /// Process a completed signature (offline-first)
  ///
  /// This method handles when a document is signed and creates the signed document records
  Future<Map<String, String>?> processCompletedSignature({
    required String signingRequestId,
    required Uint8List signedDocumentBytes,
    required String customerName,
    required String customerEmail,
    required DateTime signedAt,
    String? ipAddress,
    String? deviceInfo,
  }) async {
    try {
      // Get the signing request
      final signingRequest = await _localDatabaseService
          .getDocumentSigningRequestById(signingRequestId);
      if (signingRequest == null) {
        throw Exception('Signing request not found');
      }

      // Generate certification document
      final certificationPdf = await _certificationService
          .generateCertificationDocument(
            documentType: signingRequest.documentType,
            documentId: signingRequest.documentId,
            customerName: customerName,
            customerEmail: customerEmail,
            signingRequestId: signingRequestId,
            signedAt: signedAt,
            ipAddress: ipAddress ?? 'Unknown',
            deviceInfo: deviceInfo ?? 'Unknown',
          );

      // Store certification locally
      final certificationPath = await _documentStorageService
          .storeCertificationPdf(
            pdfBytes: certificationPdf,
            documentType: signingRequest.documentType,
            documentId: signingRequest.documentId,
            signingRequestId: signingRequestId,
          );

      // Store customer version (signed document only)
      final customerPdfPath = await _documentStorageService
          .storeSignedDocumentCustomerPdf(
            pdfBytes: signedDocumentBytes,
            documentType: signingRequest.documentType,
            documentId: signingRequest.documentId,
            signingRequestId: signingRequestId,
          );

      // Merge signed document with certification for contractor
      final mergedPdf = await _pdfMergerService
          .mergeSignedDocumentWithCertification(
            signedDocumentBytes: signedDocumentBytes,
            certificationBytes: certificationPdf,
          );

      // Store contractor version (with certification)
      final contractorPdfPath = await _documentStorageService
          .storeSignedDocumentContractorPdf(
            pdfBytes: mergedPdf,
            documentType: signingRequest.documentType,
            documentId: signingRequest.documentId,
            signingRequestId: signingRequestId,
          );

      // Create signed document record
      final signedDocument = SignedDocument(
        userId: signingRequest.userId,
        signingRequestId: signingRequestId,
        documentType: signingRequest.documentType,
        documentId: signingRequest.documentId,
        customerName: customerName,
        customerEmail: customerEmail,
        signedAt: signedAt,
        ipAddress: ipAddress,
        deviceInfo: deviceInfo,
        customerPdfPath: customerPdfPath,
        contractorPdfPath: contractorPdfPath,
        certificationPdfPath: certificationPath,
        syncStatus: SyncStatus.pending,
      );

      // Store signed document in local database
      await _localDatabaseService.insertSignedDocument(signedDocument);

      // Update signing request status
      await _localDatabaseService.updateDocumentSigningRequestStatus(
        signingRequestId,
        'signed',
      );

      // Record access for cache management
      _cacheManager.recordSignedDocumentAccess(signedDocument.id);

      debugPrint('Processed completed signature offline: ${signedDocument.id}');

      return {
        'customerPath': customerPdfPath,
        'contractorPath': contractorPdfPath,
      };
    } catch (e) {
      debugPrint('Error processing completed signature: $e');
      throw Exception('Failed to process signature: $e');
    }
  }

  /// Simple online check (can be enhanced with actual connectivity checking)
  Future<bool> _isOnline() async {
    // This is a simplified check - in a real implementation you'd use connectivity_plus
    // or similar to check actual network connectivity
    return true;
  }
}
