import 'dart:async';
import 'package:quarterlies/models/customer.dart';
import 'package:quarterlies/models/job.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/utils/error_handler.dart';

/// A service for searching customers and jobs using voice input
class VoiceSearchService {
  final VoiceRecordingService _voiceRecordingService = VoiceRecordingService();
  final DataRepository _dataRepository = DataRepository();

  bool _isRecording = false;
  bool get isRecording => _isRecording;

  /// Stream controller for search results
  final _searchResultsController =
      StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get searchResults =>
      _searchResultsController.stream;

  /// Dispose resources
  void dispose() {
    _voiceRecordingService.dispose();
    _searchResultsController.close();
  }

  /// Start voice recording for search
  Future<void> startVoiceSearch() async {
    if (_isRecording) return;

    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      _isRecording = true;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'startVoiceSearch',
          'service': 'VoiceSearchService',
        },
      );
      ErrorHandler.logError(appError);

      _searchResultsController.add({
        'error': ErrorHandler.getUserFriendlyMessage(appError),
      });
    }
  }

  /// Stop recording and process the search query
  Future<void> stopVoiceSearch() async {
    if (!_isRecording) return;

    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      _isRecording = false;

      if (transcribedText.isEmpty) {
        _searchResultsController.add({'error': 'No speech detected'});
        return;
      }

      // Notify that we're processing
      _searchResultsController.add({
        'status': 'processing',
        'query': transcribedText,
      });

      // Process the search query
      await _processSearchQuery(transcribedText);
    } catch (e) {
      _isRecording = false;
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'stopVoiceSearch',
          'service': 'VoiceSearchService',
        },
      );
      ErrorHandler.logError(appError);

      _searchResultsController.add({
        'error': ErrorHandler.getUserFriendlyMessage(appError),
      });
    }
  }

  /// Process the search query and return results
  Future<void> _processSearchQuery(String query) async {
    // Normalize the query
    final normalizedQuery = query.toLowerCase().trim();

    try {
      // Search for customers
      final customers = await _searchCustomers(normalizedQuery);

      // Search for jobs
      final jobs = await _searchJobs(normalizedQuery);

      // Return the results
      _searchResultsController.add({
        'status': 'completed',
        'query': query,
        'customers': customers,
        'jobs': jobs,
      });
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'processSearchQuery',
          'service': 'VoiceSearchService',
          'query': query,
        },
      );
      ErrorHandler.logError(appError);

      _searchResultsController.add({
        'error': ErrorHandler.getUserFriendlyMessage(appError),
      });
    }
  }

  /// Search for customers by name or address
  Future<List<Customer>> _searchCustomers(String query) async {
    final allCustomers = await _dataRepository.getCustomers();

    return allCustomers.where((customer) {
      // Search by name
      if (customer.name.toLowerCase().contains(query)) {
        return true;
      }

      // Search by address
      if ((customer.address?.toLowerCase().contains(query) ?? false) ||
          (customer.city?.toLowerCase().contains(query) ?? false) ||
          (customer.state?.toLowerCase().contains(query) ?? false) ||
          (customer.zipCode?.toLowerCase().contains(query) ?? false)) {
        return true;
      }

      // Search by phone
      if (customer.phone?.toLowerCase().contains(query) ?? false) {
        return true;
      }

      // Search by email
      if (customer.email?.toLowerCase().contains(query) ?? false) {
        return true;
      }

      return false;
    }).toList();
  }

  /// Search for jobs by title, customer name, or address
  Future<List<Job>> _searchJobs(String query) async {
    final allJobs = await _dataRepository.getJobs();
    final allCustomers = await _dataRepository.getCustomers();

    return allJobs.where((job) {
      // Search by title
      if (job.title.toLowerCase().contains(query)) {
        return true;
      }

      // Search by address
      if ((job.address?.toLowerCase().contains(query) ?? false) ||
          (job.city?.toLowerCase().contains(query) ?? false) ||
          (job.state?.toLowerCase().contains(query) ?? false) ||
          (job.zipCode?.toLowerCase().contains(query) ?? false)) {
        return true;
      }

      // Search by customer name
      final customer = allCustomers.firstWhere(
        (c) => c.id == job.customerId,
        orElse: () => Customer(id: '', userId: '', name: ''),
      );

      if (customer.name.toLowerCase().contains(query)) {
        return true;
      }

      return false;
    }).toList();
  }

  /// Perform a text-based search (without voice)
  Future<void> textSearch(String query) async {
    if (query.isEmpty) {
      _searchResultsController.add({'status': 'empty'});
      return;
    }

    // Notify that we're processing
    _searchResultsController.add({'status': 'processing', 'query': query});

    // Process the search query
    await _processSearchQuery(query);
  }
}
