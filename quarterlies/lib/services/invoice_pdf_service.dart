import 'dart:io';
import 'dart:typed_data';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
// Import all models through the main barrel file
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';

class InvoicePdfService {
  // Reference to LoadingStateProvider for UI updates
  LoadingStateProvider? _loadingStateProvider;

  // Set the LoadingStateProvider for UI updates
  void setLoadingStateProvider(LoadingStateProvider provider) {
    _loadingStateProvider = provider;
  }

  /// Generate a PDF document from an invoice
  Future<Uint8List> generateInvoicePdf({
    required Invoice invoice,
    required Customer customer,
    required Job job,
    List<Payment>? payments,
  }) async {
    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithPdfLoading(() async {
        return await _performGenerateInvoicePdf(
          invoice: invoice,
          customer: customer,
          job: job,
          payments: payments,
        );
      }, documentType: 'Invoice');
    } else {
      return await _performGenerateInvoicePdf(
        invoice: invoice,
        customer: customer,
        job: job,
        payments: payments,
      );
    }
  }

  /// Internal PDF generation implementation
  Future<Uint8List> _performGenerateInvoicePdf({
    required Invoice invoice,
    required Customer customer,
    required Job job,
    List<Payment>? payments,
  }) async {
    final pdf = pw.Document();

    // Create a PDF theme with custom colors
    final theme = pw.ThemeData.withFont(
      base: pw.Font.helvetica(),
      bold: pw.Font.helveticaBold(),
    );

    // Add a page to the PDF document
    pdf.addPage(
      pw.MultiPage(
        theme: theme,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build:
            (pw.Context context) => [
              _buildHeader(invoice, customer),
              pw.SizedBox(height: 20),
              _buildInvoiceInfo(invoice, job),
              pw.SizedBox(height: 20),
              _buildLineItems(invoice),
              pw.SizedBox(height: 20),
              _buildTotal(invoice),
              if (payments != null && payments.isNotEmpty) ...[
                pw.SizedBox(height: 20),
                _buildPayments(payments),
              ],
              pw.SizedBox(height: 20),
              if (invoice.notes != null && invoice.notes!.isNotEmpty)
                _buildNotes(invoice),
            ],
        footer: (context) => _buildFooter(context),
      ),
    );

    return pdf.save();
  }

  /// Build the invoice header with company and customer information
  pw.Widget _buildHeader(Invoice invoice, Customer customer) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'INVOICE',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'Invoice #${invoice.id != null ? (invoice.id!.length >= 8 ? invoice.id!.substring(0, 8) : invoice.id!) : 'Unknown'}',
                ),
                pw.Text(
                  'Date: ${DateFormat('MM/dd/yyyy').format(invoice.issueDate)}',
                ),
                pw.Text(
                  'Due: ${DateFormat('MM/dd/yyyy').format(invoice.dueDate)}',
                ),
              ],
            ),
            pw.Container(
              padding: const pw.EdgeInsets.all(10),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(),
                borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
              ),
              child: pw.Text(
                invoice.status.toUpperCase(),
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: _getStatusPdfColor(invoice.status),
                ),
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20),
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'From:',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                  pw.Text('Your Business Name'),
                  pw.Text('Your Address'),
                  pw.Text('Your City, State ZIP'),
                  pw.Text('Phone: Your Phone'),
                  pw.Text('Email: Your Email'),
                ],
              ),
            ),
            pw.Expanded(
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Bill To:',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.green50,
                      border: pw.Border.all(
                        color: PdfColors.green200,
                        width: 0.5,
                      ),
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          customer.name,
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        if (customer.address != null)
                          pw.Text(customer.address!),
                        pw.Text(
                          [
                                if (customer.city != null) customer.city,
                                if (customer.state != null) customer.state,
                                if (customer.zipCode != null) customer.zipCode,
                              ]
                              .where((part) => part != null && part.isNotEmpty)
                              .join(', '),
                        ),
                        if (customer.phone != null)
                          pw.Text('Phone: ${customer.phone}'),
                        if (customer.email != null)
                          pw.Text('Email: ${customer.email}'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the invoice information section
  pw.Widget _buildInvoiceInfo(Invoice invoice, Job job) {
    // Build full address from job address components
    String buildServiceAddress() {
      final List<String> addressParts = [];

      if (job.address != null && job.address!.isNotEmpty) {
        addressParts.add(job.address!);
      }

      final List<String> cityStateParts = [];
      if (job.city != null && job.city!.isNotEmpty) {
        cityStateParts.add(job.city!);
      }
      if (job.state != null && job.state!.isNotEmpty) {
        cityStateParts.add(job.state!);
      }

      if (cityStateParts.isNotEmpty) {
        if (job.zipCode != null && job.zipCode!.isNotEmpty) {
          addressParts.add('${cityStateParts.join(', ')} ${job.zipCode}');
        } else {
          addressParts.add(cityStateParts.join(', '));
        }
      } else if (job.zipCode != null && job.zipCode!.isNotEmpty) {
        addressParts.add(job.zipCode!);
      }

      return addressParts.join('\n');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Job Information',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Text('Job: ${job.title}'),
        if (job.description != null) pw.Text('Description: ${job.description}'),

        // Add Service Address section with improved formatting
        if (job.address != null ||
            job.city != null ||
            job.state != null ||
            job.zipCode != null) ...[
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Service Address:',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 12,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  buildServiceAddress(),
                  style: const pw.TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
        ],

        pw.Divider(),
      ],
    );
  }

  /// Build the line items table
  pw.Widget _buildLineItems(Invoice invoice) {
    final headers = ['Description', 'Quantity', 'Unit', 'Unit Price', 'Total'];

    final lineItems = invoice.lineItems ?? [];

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Line Items',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        lineItems.isEmpty
            ? pw.Text('No line items')
            : pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              children: [
                // Table header
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey200),
                  children:
                      headers
                          .map(
                            (header) => pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text(
                                header,
                                style: pw.TextStyle(
                                  fontWeight: pw.FontWeight.bold,
                                ),
                                textAlign:
                                    header == 'Description'
                                        ? pw.TextAlign.left
                                        : pw.TextAlign.right,
                              ),
                            ),
                          )
                          .toList(),
                ),
                // Table data
                ...lineItems.map(
                  (item) => pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(item.description),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          item.quantity.toString(),
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          item.unit,
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '\$${item.unitPrice.toStringAsFixed(2)}',
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
      ],
    );
  }

  /// Build the total section
  pw.Widget _buildTotal(Invoice invoice) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Total Amount: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text('\$${invoice.totalAmount.toStringAsFixed(2)}'),
            ],
          ),
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Amount Paid: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text('\$${invoice.amountPaid.toStringAsFixed(2)}'),
            ],
          ),
          pw.Divider(thickness: 0.5),
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Balance Due: ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(
                '\$${invoice.balanceDue.toStringAsFixed(2)}',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color:
                      invoice.balanceDue > 0 ? PdfColors.red : PdfColors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the payments section
  pw.Widget _buildPayments(List<Payment> payments) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Payment History',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(width: 0.5),
          children: [
            // Table header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Date',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Method',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Amount',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
              ],
            ),
            // Table data
            ...payments.map(
              (payment) => pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      DateFormat('MM/dd/yyyy').format(payment.dateReceived),
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(payment.paymentMethod),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '\$${payment.amountReceived.toStringAsFixed(2)}',
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the notes section
  pw.Widget _buildNotes(Invoice invoice) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Notes',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(width: 0.5),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          child: pw.Text(invoice.notes!),
        ),
      ],
    );
  }

  /// Build the footer
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Divider(),
        pw.SizedBox(height: 5),
        pw.Text('Thank you for your business!'),
        pw.SizedBox(height: 5),
        pw.Text(
          'Page ${context.pageNumber} of ${context.pagesCount}',
          style: const pw.TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  /// Get the PDF color for the invoice status
  PdfColor _getStatusPdfColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return PdfColors.blue;
      case 'paid':
        return PdfColors.green;
      case 'partially paid':
        return PdfColors.orange;
      case 'overdue':
        return PdfColors.red;
      default:
        return PdfColors.grey;
    }
  }

  /// Save the PDF to a temporary file and return the file path
  /// This method handles platform-specific file paths and provides error handling
  Future<File> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    try {
      // Get the temporary directory for the current platform
      final directory = await getTemporaryDirectory();

      // Ensure the directory exists
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Create a platform-appropriate file path
      final filePath = '${directory.path}${Platform.pathSeparator}$fileName';

      // Create the file and write the PDF bytes
      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      return file;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'savePdfToFile',
          'fileName': fileName,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
