import 'dart:async';
import 'package:quarterlies/models/sync_status.dart'; // Import the enum directly
import 'package:supabase_flutter/supabase_flutter.dart';

class SyncService {
  // Singleton pattern
  static final SyncService _instance = SyncService._internal();
  factory SyncService() {
    // Check if Supabase is initialized by trying to access the client
    try {
      final client = Supabase.instance.client;
      if (client.auth.currentUser == null) {
        // This is acceptable - user might not be logged in yet
      }
    } catch (e) {
      throw AssertionError(
        'SyncService requires Supabase to be initialized: ${e.toString()}',
      );
    }
    return _instance;
  }
  SyncService._internal();

  // Stream controller for sync status updates
  final _syncStatusController = StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  // Method to update sync status
  void updateSyncStatus(SyncStatus status) {
    _syncStatusController.add(status);
  }

  // Dispose resources
  void dispose() {
    _syncStatusController.close();
  }
}
