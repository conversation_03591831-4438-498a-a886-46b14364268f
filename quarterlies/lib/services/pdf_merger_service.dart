import 'dart:typed_data';
import 'package:pdf/widgets.dart' as pw;
import 'package:http/http.dart' as http;
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

/// Service for merging multiple PDFs into a single document
class PdfMergerService {
  // Singleton pattern
  static final PdfMergerService _instance = PdfMergerService._internal();
  factory PdfMergerService() => _instance;
  PdfMergerService._internal();

  /// Merge a signed document with its certification
  ///
  /// This method:
  /// 1. Creates a new PDF document
  /// 2. Adds all pages from the signed document
  /// 3. Adds all pages from the certification document
  /// 4. Returns the merged PDF
  Future<Uint8List> mergeSignedDocumentWithCertification({
    required Uint8List signedDocumentBytes,
    required Uint8List certificationBytes,
  }) async {
    try {
      // Create a new PDF document
      final mergedPdf = pw.Document();

      // Load the signed document
      final signedDocument = await _loadPdfDocument(signedDocumentBytes);

      // Load the certification document
      final certificationDocument = await _loadPdfDocument(certificationBytes);

      // Add all pages from the signed document
      for (var i = 0; i < signedDocument.length; i++) {
        mergedPdf.addPage(signedDocument[i]);
      }

      // Add a separator page
      mergedPdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'ELECTRONIC SIGNATURE CERTIFICATION',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'The following pages contain the certification of the electronic signature',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'This certification is an integral part of the signed document',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                ],
              ),
            );
          },
        ),
      );

      // Add all pages from the certification document
      for (var i = 0; i < certificationDocument.length; i++) {
        mergedPdf.addPage(certificationDocument[i]);
      }

      // Save and return the merged PDF
      return mergedPdf.save();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'mergeSignedDocumentWithCertification',
          'signedDocumentSize': signedDocumentBytes.length,
          'certificationSize': certificationBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Download a PDF from a URL
  Future<Uint8List> downloadPdf(String url) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await http.get(Uri.parse(url));

        if (response.statusCode != 200) {
          throw Exception(
            'HTTP ${response.statusCode}: Failed to download PDF',
          );
        }

        return response.bodyBytes;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'downloadPdf',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Load a PDF document from bytes
  Future<List<pw.Page>> _loadPdfDocument(Uint8List pdfBytes) async {
    try {
      // Create a new page with the content from the PDF bytes
      final pages = <pw.Page>[];

      // Create a simple page with the PDF data embedded as an image
      pages.add(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(child: pw.Image(pw.MemoryImage(pdfBytes)));
          },
        ),
      );

      return pages;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': '_loadPdfDocument', 'pdfSize': pdfBytes.length},
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
