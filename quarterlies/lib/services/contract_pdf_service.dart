import 'dart:io';
import 'dart:typed_data';
import 'dart:async';

import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
// Import specific models to avoid conflicts
import 'package:quarterlies/models/contract.dart';
import 'package:quarterlies/models/job.dart' show Job;
import 'package:quarterlies/models/customer.dart' show Customer;
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

class ContractPdfService {
  // Reference to LoadingStateProvider for UI updates
  LoadingStateProvider? _loadingStateProvider;

  // Set the LoadingStateProvider for UI updates
  void setLoadingStateProvider(LoadingStateProvider provider) {
    _loadingStateProvider = provider;
  }

  /// Generate a PDF document from a contract
  Future<Uint8List> generateContractPdf({
    required Contract contract,
    required Customer customer,
    required Job job,
    bool includeSignaturePage = false,
    String? signatureImageUrl,
    DateTime? signedDate,
  }) async {
    // Use LoadingStateProvider if available
    if (_loadingStateProvider != null) {
      return await _loadingStateProvider!.executeWithPdfLoading(() async {
        return await _performGenerateContractPdf(
          contract: contract,
          customer: customer,
          job: job,
          includeSignaturePage: includeSignaturePage,
          signatureImageUrl: signatureImageUrl,
          signedDate: signedDate,
        );
      }, documentType: 'Contract');
    } else {
      return await _performGenerateContractPdf(
        contract: contract,
        customer: customer,
        job: job,
        includeSignaturePage: includeSignaturePage,
        signatureImageUrl: signatureImageUrl,
        signedDate: signedDate,
      );
    }
  }

  /// Internal PDF generation implementation
  Future<Uint8List> _performGenerateContractPdf({
    required Contract contract,
    required Customer customer,
    required Job job,
    bool includeSignaturePage = false,
    String? signatureImageUrl,
    DateTime? signedDate,
  }) async {
    try {
      final pdf = pw.Document();

      // Load signature image if provided
      pw.MemoryImage? signatureImage;
      if (includeSignaturePage && signatureImageUrl != null) {
        try {
          final response = await http.get(Uri.parse(signatureImageUrl));
          if (response.statusCode == 200) {
            signatureImage = pw.MemoryImage(response.bodyBytes);
          }
        } catch (e) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'loadSignatureImage',
              'signatureImageUrl': signatureImageUrl,
            },
          );
          ErrorHandler.logError(appError);
          debugPrint('Error loading signature image: $e');
        }
      }

      // Create a PDF theme with custom colors
      final theme = pw.ThemeData.withFont(
        base: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
      );

      // Add a page to the PDF document
      pdf.addPage(
        pw.MultiPage(
          theme: theme,
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build:
              (pw.Context context) => [
                _buildHeader(contract, customer),
                pw.SizedBox(height: 20),
                _buildContractInfo(contract, job),
                pw.SizedBox(height: 20),
                _buildLineItems(contract),
                pw.SizedBox(height: 20),
                _buildTotal(contract),
                pw.SizedBox(height: 20),
                if (contract.notes != null && contract.notes!.isNotEmpty)
                  _buildNotes(contract),
                pw.SizedBox(height: 20),
                _buildSignatureSection(customer),
              ],
          footer: (context) => _buildFooter(context),
        ),
      );

      // Add signature page if requested
      if (includeSignaturePage && signedDate != null) {
        pdf.addPage(
          pw.Page(
            theme: theme,
            pageFormat: PdfPageFormat.letter,
            margin: const pw.EdgeInsets.all(40),
            build: (context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Center(
                    child: pw.Text(
                      'SIGNATURE CONFIRMATION',
                      style: pw.TextStyle(
                        fontSize: 20,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'This document was electronically signed by:',
                    style: pw.TextStyle(fontSize: 12),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'Name: ${customer.name}',
                    style: pw.TextStyle(
                      fontSize: 12,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.Text(
                    'Email: ${customer.email ?? "Not provided"}',
                    style: pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'Date: ${DateFormat('MMMM d, yyyy').format(signedDate)}',
                    style: pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'Time: ${DateFormat('h:mm a').format(signedDate)}',
                    style: pw.TextStyle(fontSize: 12),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text('Signature:', style: pw.TextStyle(fontSize: 12)),
                  pw.SizedBox(height: 10),
                  if (signatureImage != null)
                    pw.Container(
                      height: 100,
                      width: 300,
                      child: pw.Image(signatureImage),
                    )
                  else
                    pw.Container(
                      height: 100,
                      width: 300,
                      decoration: pw.BoxDecoration(border: pw.Border.all()),
                      child: pw.Center(
                        child: pw.Text(
                          'Signature image not available',
                          style: pw.TextStyle(
                            fontStyle: pw.FontStyle.italic,
                            color: PdfColors.grey,
                          ),
                        ),
                      ),
                    ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'This electronic signature is legally binding and equivalent to a handwritten signature under the Electronic Signatures in Global and National Commerce Act (E-SIGN Act) and the Uniform Electronic Transactions Act (UETA).',
                    style: pw.TextStyle(
                      fontSize: 10,
                      fontStyle: pw.FontStyle.italic,
                    ),
                  ),
                ],
              );
            },
          ),
        );
      }

      return pdf.save();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'generateContractPdf',
          'contractId': contract.id,
          'customerId': customer.id,
          'jobId': job.id,
          'includeSignaturePage': includeSignaturePage,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }

  /// Download a PDF from a URL
  Future<Uint8List> downloadPdf(String url) async {
    final result = await RetryMechanism.execute(
      () async {
        final response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          return response.bodyBytes;
        } else {
          throw Exception(
            'HTTP ${response.statusCode}: Failed to download PDF',
          );
        }
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'downloadPdf',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }

    return result.data!;
  }

  /// Build the contract header with company and customer information
  pw.Widget _buildHeader(Contract contract, Customer customer) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'CONTRACT',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text('Contract #${contract.id.substring(0, 8)}'),
                pw.Text(
                  'Date: ${DateFormat('MM/dd/yyyy').format(contract.createdAt)}',
                ),
              ],
            ),
            pw.Expanded(
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Bill To:',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Container(
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.green50,
                      border: pw.Border.all(
                        color: PdfColors.green200,
                        width: 0.5,
                      ),
                      borderRadius: const pw.BorderRadius.all(
                        pw.Radius.circular(4),
                      ),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          customer.name,
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        if (customer.address != null)
                          pw.Text(customer.address!),
                        pw.Text(
                          [
                                if (customer.city != null) customer.city,
                                if (customer.state != null) customer.state,
                                if (customer.zipCode != null) customer.zipCode,
                              ]
                              .where((part) => part != null && part.isNotEmpty)
                              .join(', '),
                        ),
                        if (customer.phone != null)
                          pw.Text('Phone: ${customer.phone}'),
                        if (customer.email != null)
                          pw.Text('Email: ${customer.email}'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the contract information section
  pw.Widget _buildContractInfo(Contract contract, Job job) {
    // Helper function to build service address
    String buildServiceAddress() {
      final List<String> addressParts = [];

      if (job.address != null && job.address!.isNotEmpty) {
        addressParts.add(job.address!);
      }

      final List<String> cityStateParts = [];
      if (job.city != null && job.city!.isNotEmpty) {
        cityStateParts.add(job.city!);
      }
      if (job.state != null && job.state!.isNotEmpty) {
        cityStateParts.add(job.state!);
      }

      if (cityStateParts.isNotEmpty) {
        if (job.zipCode != null && job.zipCode!.isNotEmpty) {
          addressParts.add('${cityStateParts.join(', ')} ${job.zipCode}');
        } else {
          addressParts.add(cityStateParts.join(', '));
        }
      } else if (job.zipCode != null && job.zipCode!.isNotEmpty) {
        addressParts.add(job.zipCode!);
      }

      return addressParts.join('\n');
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Job Information',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Text('Job: ${job.title}'),
        if (job.description != null) pw.Text('Description: ${job.description}'),

        // Add Service Address section with improved formatting
        if (job.address != null ||
            job.city != null ||
            job.state != null ||
            job.zipCode != null) ...[
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.blue50,
              border: pw.Border.all(color: PdfColors.blue200, width: 0.5),
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Service Address:',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 12,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  buildServiceAddress(),
                  style: const pw.TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
        ],

        pw.Divider(),
      ],
    );
  }

  /// Build the line items section
  pw.Widget _buildLineItems(Contract contract) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Contract Details',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(4),
            1: const pw.FlexColumnWidth(1),
            2: const pw.FlexColumnWidth(1),
            3: const pw.FlexColumnWidth(1.5),
          },
          children: [
            // Table header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Description',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Qty',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Price',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(
                    'Total',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    textAlign: pw.TextAlign.right,
                  ),
                ),
              ],
            ),
            // Table rows for each line item
            ...contract.lineItems.map(
              (item) => pw.TableRow(
                children: [
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(item.description),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '${item.quantity}${item.unit != null ? ' ${item.unit}' : ''}',
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '\$${item.unitPrice.toStringAsFixed(2)}',
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                  pw.Padding(
                    padding: const pw.EdgeInsets.all(8),
                    child: pw.Text(
                      '\$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                      textAlign: pw.TextAlign.right,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the total section
  pw.Widget _buildTotal(Contract contract) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Row(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Text(
                'Total:',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(width: 20),
              pw.Text(
                '\$${contract.totalAmount.toStringAsFixed(2)}',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the notes section
  pw.Widget _buildNotes(Contract contract) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Notes',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 5),
        pw.Text(contract.notes!),
      ],
    );
  }

  /// Build the signature section
  pw.Widget _buildSignatureSection(Customer customer) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Agreement',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'By signing below, I agree to the terms and conditions of this contract.',
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black, width: 1),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
          ),
          padding: const pw.EdgeInsets.all(10),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'ELECTRONIC SIGNATURE CONSENT',
                style: pw.TextStyle(
                  fontSize: 12,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                'By signing electronically, you confirm that you have read and agree to the terms of this contract. '
                'Your electronic signature is legally binding as if you signed with pen and paper.',
                style: const pw.TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  width: 200,
                  height: 50,
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.black, width: 1),
                    borderRadius: const pw.BorderRadius.all(
                      pw.Radius.circular(5),
                    ),
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text('Customer Signature'),
                pw.SizedBox(height: 15),
                pw.Container(
                  width: 200,
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  height: 1,
                ),
                pw.SizedBox(height: 5),
                pw.Text('Date'),
              ],
            ),
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Container(
                  width: 200,
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  height: 1,
                ),
                pw.SizedBox(height: 5),
                pw.Text('Company Representative'),
                pw.SizedBox(height: 15),
                pw.Container(
                  width: 200,
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(color: PdfColors.black),
                    ),
                  ),
                  height: 1,
                ),
                pw.SizedBox(height: 5),
                pw.Text('Date'),
              ],
            ),
          ],
        ),
      ],
    );
  }

  /// Build the footer
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Text(
        'Page ${context.pageNumber} of ${context.pagesCount}',
        style: const pw.TextStyle(color: PdfColors.grey700, fontSize: 10),
      ),
    );
  }

  /// Save the PDF to a temporary file and return the file path
  Future<File> savePdfToFile(Uint8List pdfBytes, String fileName) async {
    try {
      // Get the temporary directory for the current platform
      final directory = await getTemporaryDirectory();

      // Ensure the directory exists
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Create a platform-appropriate file path
      final filePath = '${directory.path}${Platform.pathSeparator}$fileName';

      // Create the file and write the PDF bytes
      final file = File(filePath);
      await file.writeAsBytes(pdfBytes);

      return file;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'savePdfToFile',
          'fileName': fileName,
          'pdfSize': pdfBytes.length,
        },
      );
      ErrorHandler.logError(appError);
      throw Exception(ErrorHandler.getUserFriendlyMessage(appError));
    }
  }
}
