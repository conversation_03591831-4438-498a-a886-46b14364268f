import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing glare resistance and outdoor visibility features
class GlareResistanceService extends ChangeNotifier {
  static const String _automaticBrightnessKey =
      'automatic_brightness_detection';
  static const String _dynamicColorAdjustmentKey = 'dynamic_color_adjustment';
  static const String _enhancedContrastModeKey = 'enhanced_contrast_mode';
  static const String _currentBrightnessKey = 'current_brightness_level';

  // Default settings
  bool _automaticBrightnessDetection = true; // Default to device setting
  bool _dynamicColorAdjustment = true; // Default on
  bool _enhancedContrastMode = true; // Default on
  double _currentBrightnessLevel = 0.5; // 0.0 to 1.0

  // Brightness thresholds
  static const double _lowLightThreshold = 0.3;
  static const double _brightLightThreshold = 0.7;
  static const double _veryBrightThreshold = 0.9;

  // Color adjustment factors
  static const double _contrastBoostFactor = 1.2;
  static const double _saturationBoostFactor = 1.1;
  static const double _brightnessBoostFactor = 1.15;

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Getters
  bool get automaticBrightnessDetection => _automaticBrightnessDetection;
  bool get dynamicColorAdjustment => _dynamicColorAdjustment;
  bool get enhancedContrastMode => _enhancedContrastMode;
  double get currentBrightnessLevel => _currentBrightnessLevel;
  bool get isInitialized => _isInitialized;

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();

      // Start brightness monitoring if automatic detection is enabled
      if (_automaticBrightnessDetection) {
        await _startBrightnessMonitoring();
      }

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing GlareResistanceService: $e');
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _automaticBrightnessDetection =
        _prefs!.getBool(_automaticBrightnessKey) ?? true;
    _dynamicColorAdjustment =
        _prefs!.getBool(_dynamicColorAdjustmentKey) ?? true;
    _enhancedContrastMode = _prefs!.getBool(_enhancedContrastModeKey) ?? true;
    _currentBrightnessLevel = _prefs!.getDouble(_currentBrightnessKey) ?? 0.5;
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    if (_prefs == null) return;

    await _prefs!.setBool(
      _automaticBrightnessKey,
      _automaticBrightnessDetection,
    );
    await _prefs!.setBool(_dynamicColorAdjustmentKey, _dynamicColorAdjustment);
    await _prefs!.setBool(_enhancedContrastModeKey, _enhancedContrastMode);
    await _prefs!.setDouble(_currentBrightnessKey, _currentBrightnessLevel);
  }

  /// Start monitoring device brightness
  Future<void> _startBrightnessMonitoring() async {
    try {
      // Get initial brightness from device
      final brightness = await _getDeviceBrightness();
      if (brightness != null) {
        _currentBrightnessLevel = brightness;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error starting brightness monitoring: $e');
    }
  }

  /// Get device brightness level (platform-specific implementation would be needed)
  Future<double?> _getDeviceBrightness() async {
    try {
      // This would require platform-specific implementation
      // For now, return a simulated value
      return 0.5; // Placeholder
    } catch (e) {
      debugPrint('Error getting device brightness: $e');
      return null;
    }
  }

  /// Update automatic brightness detection setting
  Future<void> setAutomaticBrightnessDetection(bool enabled) async {
    _automaticBrightnessDetection = enabled;

    if (enabled) {
      await _startBrightnessMonitoring();
    }

    await _saveSettings();
    notifyListeners();
  }

  /// Update dynamic color adjustment setting
  Future<void> setDynamicColorAdjustment(bool enabled) async {
    _dynamicColorAdjustment = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Update enhanced contrast mode setting
  Future<void> setEnhancedContrastMode(bool enabled) async {
    _enhancedContrastMode = enabled;
    await _saveSettings();
    notifyListeners();
  }

  /// Manually update brightness level (for testing or manual override)
  Future<void> updateBrightnessLevel(double level) async {
    _currentBrightnessLevel = level.clamp(0.0, 1.0);
    await _saveSettings();
    notifyListeners();
  }

  /// Get brightness category based on current level
  BrightnessCategory getBrightnessCategory() {
    if (_currentBrightnessLevel < _lowLightThreshold) {
      return BrightnessCategory.lowLight;
    } else if (_currentBrightnessLevel < _brightLightThreshold) {
      return BrightnessCategory.normal;
    } else if (_currentBrightnessLevel < _veryBrightThreshold) {
      return BrightnessCategory.bright;
    } else {
      return BrightnessCategory.veryBright;
    }
  }

  /// Get adjusted color based on current settings and brightness
  Color getAdjustedColor(Color originalColor) {
    if (!_dynamicColorAdjustment) {
      return originalColor;
    }

    final category = getBrightnessCategory();

    switch (category) {
      case BrightnessCategory.lowLight:
        // Increase brightness and reduce contrast for low light
        return _adjustColorForLowLight(originalColor);

      case BrightnessCategory.normal:
        // Minimal adjustments for normal lighting
        return _enhancedContrastMode
            ? _applyContrastBoost(originalColor)
            : originalColor;

      case BrightnessCategory.bright:
        // Increase contrast and saturation for bright conditions
        return _adjustColorForBrightLight(originalColor);

      case BrightnessCategory.veryBright:
        // Maximum contrast and saturation for very bright conditions
        return _adjustColorForVeryBrightLight(originalColor);
    }
  }

  /// Adjust color for low light conditions
  Color _adjustColorForLowLight(Color color) {
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness((hsl.lightness * _brightnessBoostFactor).clamp(0.0, 1.0))
        .toColor();
  }

  /// Apply contrast boost
  Color _applyContrastBoost(Color color) {
    final hsl = HSLColor.fromColor(color);

    // Increase saturation and adjust lightness for better contrast
    return hsl
        .withSaturation(
          (hsl.saturation * _saturationBoostFactor).clamp(0.0, 1.0),
        )
        .withLightness(
          hsl.lightness < 0.5
              ? (hsl.lightness * 0.9).clamp(0.0, 1.0) // Darken dark colors
              : (hsl.lightness * 1.1).clamp(0.0, 1.0), // Lighten light colors
        )
        .toColor();
  }

  /// Adjust color for bright light conditions
  Color _adjustColorForBrightLight(Color color) {
    final hsl = HSLColor.fromColor(color);

    return hsl
        .withSaturation(
          (hsl.saturation * _saturationBoostFactor).clamp(0.0, 1.0),
        )
        .withLightness(
          hsl.lightness < 0.5
              ? (hsl.lightness * 0.8).clamp(0.0, 1.0) // Darken dark colors more
              : (hsl.lightness * 1.2).clamp(
                0.0,
                1.0,
              ), // Lighten light colors more
        )
        .toColor();
  }

  /// Adjust color for very bright light conditions
  Color _adjustColorForVeryBrightLight(Color color) {
    final hsl = HSLColor.fromColor(color);

    return hsl
        .withSaturation((hsl.saturation * _contrastBoostFactor).clamp(0.0, 1.0))
        .withLightness(
          hsl.lightness < 0.5
              ? (hsl.lightness * 0.7).clamp(
                0.0,
                1.0,
              ) // Darken dark colors significantly
              : (hsl.lightness * 1.3).clamp(
                0.0,
                1.0,
              ), // Lighten light colors significantly
        )
        .toColor();
  }

  /// Get adjusted theme data based on current conditions
  ThemeData getAdjustedTheme(ThemeData baseTheme) {
    if (!_dynamicColorAdjustment && !_enhancedContrastMode) {
      return baseTheme;
    }

    final category = getBrightnessCategory();

    // Apply adjustments based on brightness category
    switch (category) {
      case BrightnessCategory.lowLight:
        return _adjustThemeForLowLight(baseTheme);

      case BrightnessCategory.normal:
        return _enhancedContrastMode
            ? _applyContrastBoostToTheme(baseTheme)
            : baseTheme;

      case BrightnessCategory.bright:
        return _adjustThemeForBrightLight(baseTheme);

      case BrightnessCategory.veryBright:
        return _adjustThemeForVeryBrightLight(baseTheme);
    }
  }

  /// Adjust theme for low light conditions
  ThemeData _adjustThemeForLowLight(ThemeData theme) {
    return theme.copyWith(
      colorScheme: theme.colorScheme.copyWith(
        primary: getAdjustedColor(theme.colorScheme.primary),
        secondary: getAdjustedColor(theme.colorScheme.secondary),
        surface: getAdjustedColor(theme.colorScheme.surface),
      ),
    );
  }

  /// Apply contrast boost to theme
  ThemeData _applyContrastBoostToTheme(ThemeData theme) {
    return theme.copyWith(
      colorScheme: theme.colorScheme.copyWith(
        primary: getAdjustedColor(theme.colorScheme.primary),
        secondary: getAdjustedColor(theme.colorScheme.secondary),
        surface: getAdjustedColor(theme.colorScheme.surface),
      ),
    );
  }

  /// Adjust theme for bright light conditions
  ThemeData _adjustThemeForBrightLight(ThemeData theme) {
    return theme.copyWith(
      colorScheme: theme.colorScheme.copyWith(
        primary: getAdjustedColor(theme.colorScheme.primary),
        secondary: getAdjustedColor(theme.colorScheme.secondary),
        surface: getAdjustedColor(theme.colorScheme.surface),
      ),
      cardTheme: theme.cardTheme.copyWith(
        elevation:
            (theme.cardTheme.elevation ?? 4.0) +
            2.0, // Higher elevation for better visibility
      ),
    );
  }

  /// Adjust theme for very bright light conditions
  ThemeData _adjustThemeForVeryBrightLight(ThemeData theme) {
    return theme.copyWith(
      colorScheme: theme.colorScheme.copyWith(
        primary: getAdjustedColor(theme.colorScheme.primary),
        secondary: getAdjustedColor(theme.colorScheme.secondary),
        surface: getAdjustedColor(theme.colorScheme.surface),
      ),
      cardTheme: theme.cardTheme.copyWith(
        elevation:
            (theme.cardTheme.elevation ?? 4.0) + 4.0, // Maximum elevation
      ),
      appBarTheme: theme.appBarTheme.copyWith(
        elevation: (theme.appBarTheme.elevation ?? 4.0) + 2.0,
      ),
    );
  }

  /// Get recommended text style adjustments
  TextStyle getAdjustedTextStyle(TextStyle baseStyle) {
    if (!_enhancedContrastMode) {
      return baseStyle;
    }

    final category = getBrightnessCategory();

    switch (category) {
      case BrightnessCategory.lowLight:
        return baseStyle; // No adjustments for low light

      case BrightnessCategory.normal:
        return baseStyle.copyWith(
          fontWeight: _enhanceWeight(baseStyle.fontWeight),
        );

      case BrightnessCategory.bright:
      case BrightnessCategory.veryBright:
        return baseStyle.copyWith(
          fontWeight: _enhanceWeight(baseStyle.fontWeight),
          shadows: [
            Shadow(
              offset: const Offset(0.5, 0.5),
              blurRadius: 1.0,
              color: Colors.black.withValues(alpha: 0.3),
            ),
          ],
        );
    }
  }

  /// Enhance font weight for better visibility
  FontWeight _enhanceWeight(FontWeight? weight) {
    if (weight == null) return FontWeight.w500;

    switch (weight) {
      case FontWeight.w100:
      case FontWeight.w200:
      case FontWeight.w300:
        return FontWeight.w400;
      case FontWeight.w400:
        return FontWeight.w500;
      case FontWeight.w500:
        return FontWeight.w600;
      case FontWeight.w600:
        return FontWeight.w700;
      case FontWeight.w700:
      case FontWeight.w800:
      case FontWeight.w900:
        return weight; // Already bold enough
      default:
        return FontWeight.w500;
    }
  }
}

/// Brightness categories for different lighting conditions
enum BrightnessCategory { lowLight, normal, bright, veryBright }
