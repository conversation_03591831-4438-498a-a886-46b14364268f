import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/retry_mechanism.dart';

/// Service for sending emails using Supabase Edge Functions
class EmailService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  // Singleton pattern
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  /// Send a document signing request email to a customer
  ///
  /// This method sends an email to the customer with a link to sign the document
  Future<bool> sendSigningRequestEmail({
    required String documentType,
    required String documentId,
    required String signingLink,
    required Customer customer,
    required Job job,
    DateTime? expirationDate,
  }) async {
    final result = await RetryMechanism.execute(
      () async {
        // Format the expiration date if provided
        String formattedExpirationDate = '';
        if (expirationDate != null) {
          formattedExpirationDate = DateFormat(
            'MMMM d, yyyy',
          ).format(expirationDate);
        }

        // Build the service address string if available
        String serviceAddress = '';
        if (job.address != null ||
            job.city != null ||
            job.state != null ||
            job.zipCode != null) {
          List<String> addressParts = [];
          if (job.address != null && job.address!.isNotEmpty) {
            addressParts.add(job.address!);
          }

          List<String> cityStateParts = [];
          if (job.city != null && job.city!.isNotEmpty) {
            cityStateParts.add(job.city!);
          }
          if (job.state != null && job.state!.isNotEmpty) {
            cityStateParts.add(job.state!);
          }

          if (cityStateParts.isNotEmpty) {
            addressParts.add(cityStateParts.join(', '));
          }
          if (job.zipCode != null && job.zipCode!.isNotEmpty) {
            addressParts.add(job.zipCode!);
          }

          serviceAddress = addressParts.join(', ');
        }

        // Call the Supabase Edge Function to send the email
        final response = await _supabaseClient.functions.invoke(
          'send-signing-request-email',
          body: {
            'to': customer.email,
            'subject':
                '${documentType.toUpperCase()} for ${job.title} - Signature Required',
            'documentType': documentType,
            'customerName': customer.name,
            'jobName': job.title,
            'jobDescription': job.description,
            'serviceAddress': serviceAddress,
            'signingLink': signingLink,
            'documentId': documentId,
            'expirationDate': formattedExpirationDate,
          },
        );

        if (response.status != 200) {
          throw Exception(
            'Email service returned error status: ${response.status} - ${response.data}',
          );
        }

        return true;
      },
      config: RetryMechanism.networkRetryConfig(),
      operationName: 'sendSigningRequestEmail',
    );

    if (!result.isSuccess) {
      final appError = result.error!;
      ErrorHandler.logError(appError);
      return false;
    }

    return result.data!;
  }

  /// Send a notification email to the contractor when a document is signed
  ///
  /// The contractor receives a version of the document that includes the certification
  Future<bool> sendSignedDocumentNotification({
    required String documentType,
    required String documentId,
    required String customerName,
    required String jobName,
    required String signedDocumentUrl,
    required String contractorEmail,
    String? serviceAddress,
    String? jobDescription,
    DateTime? signedAt,
  }) async {
    try {
      // Format the signing date if provided
      String formattedSigningDate = '';
      if (signedAt != null) {
        formattedSigningDate = DateFormat('MMMM d, yyyy').format(signedAt);
      }

      // Call the Supabase Edge Function to send the email
      final response = await _supabaseClient.functions.invoke(
        'send-signed-document-notification',
        body: {
          'to': contractorEmail,
          'subject': '${documentType.toUpperCase()} Signed by $customerName',
          'documentType': documentType,
          'customerName': customerName,
          'jobName': jobName,
          'jobDescription': jobDescription ?? '',
          'serviceAddress': serviceAddress ?? '',
          'signedDocumentUrl': signedDocumentUrl,
          'documentId': documentId,
          'signedAt': formattedSigningDate,
        },
      );

      if (response.status != 200) {
        final appError = AppError(
          type: ErrorType.network,
          severity: ErrorSeverity.high,
          message: 'Email service returned error status: ${response.status}',
          userFriendlyMessage:
              'Failed to send signed document notification. Please try again.',
          technicalDetails: response.data?.toString(),
          context: {
            'operation': 'sendSignedDocumentNotification',
            'status': response.status,
            'contractorEmail': contractorEmail,
            'documentType': documentType,
          },
        );
        ErrorHandler.logError(appError);
        return false;
      }

      return true;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'sendSignedDocumentNotification',
          'contractorEmail': contractorEmail,
          'documentType': documentType,
          'documentId': documentId,
        },
      );
      ErrorHandler.logError(appError);
      return false;
    }
  }

  /// Send a signed document copy to the customer
  Future<bool> sendSignedDocumentToCustomer({
    required String documentType,
    required String documentId,
    required String customerName,
    required String customerEmail,
    required String jobName,
    required String signedDocumentUrl,
    String? serviceAddress,
    String? jobDescription,
    DateTime? signedAt,
  }) async {
    try {
      // Format the signing date if provided
      String formattedSigningDate = '';
      if (signedAt != null) {
        formattedSigningDate = DateFormat('MMMM d, yyyy').format(signedAt);
      }

      // Call the Supabase Edge Function to send the email
      final response = await _supabaseClient.functions.invoke(
        'send-signed-document-to-customer',
        body: {
          'to': customerEmail,
          'subject': 'Your Signed ${documentType.toUpperCase()} for $jobName',
          'documentType': documentType,
          'customerName': customerName,
          'jobName': jobName,
          'jobDescription': jobDescription ?? '',
          'serviceAddress': serviceAddress ?? '',
          'signedDocumentUrl': signedDocumentUrl,
          'documentId': documentId,
          'signedAt': formattedSigningDate,
        },
      );

      if (response.status != 200) {
        final appError = AppError(
          type: ErrorType.network,
          severity: ErrorSeverity.high,
          message: 'Email service returned error status: ${response.status}',
          userFriendlyMessage:
              'Failed to send signed document to customer. Please try again.',
          technicalDetails: response.data?.toString(),
          context: {
            'operation': 'sendSignedDocumentToCustomer',
            'status': response.status,
            'customerEmail': customerEmail,
            'documentType': documentType,
          },
        );
        ErrorHandler.logError(appError);
        return false;
      }

      return true;
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'sendSignedDocumentToCustomer',
          'customerEmail': customerEmail,
          'documentType': documentType,
          'documentId': documentId,
        },
      );
      ErrorHandler.logError(appError);
      return false;
    }
  }
}
