import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/models.dart';

// Service class for tax payment operations
class TaxPaymentService {
  // Singleton pattern
  static final TaxPaymentService _instance = TaxPaymentService._internal();
  factory TaxPaymentService() => _instance;
  TaxPaymentService._internal();

  // Supabase client
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  // Add a new tax payment
  Future<void> addTaxPayment(TaxPayment taxPayment) async {
    try {
      await _supabaseClient.from('tax_payments').insert(taxPayment.toJson());
    } catch (e) {
      throw Exception('Failed to add tax payment: $e');
    }
  }

  // Get all tax payments for the current user
  Future<List<TaxPayment>> getTaxPayments() async {
    try {
      final response = await _supabaseClient
          .from('tax_payments')
          .select()
          .eq('user_id', _supabaseClient.auth.currentUser!.id)
          .order('date', ascending: false);

      return response.map((json) => TaxPayment.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get tax payments: $e');
    }
  }

  // Get tax payments filtered by period
  Future<List<TaxPayment>> getTaxPaymentsByPeriod(String taxPeriod) async {
    try {
      final response = await _supabaseClient
          .from('tax_payments')
          .select()
          .eq('user_id', _supabaseClient.auth.currentUser!.id)
          .eq('tax_period', taxPeriod)
          .order('date', ascending: false);

      return response.map((json) => TaxPayment.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to get tax payments for period: $e');
    }
  }

  // Get a specific tax payment by ID
  Future<TaxPayment> getTaxPaymentById(String taxPaymentId) async {
    try {
      final response =
          await _supabaseClient
              .from('tax_payments')
              .select()
              .eq('id', taxPaymentId)
              .eq('user_id', _supabaseClient.auth.currentUser!.id)
              .single();

      return TaxPayment.fromJson(response);
    } catch (e) {
      throw Exception('Failed to get tax payment: $e');
    }
  }

  // Update an existing tax payment
  Future<void> updateTaxPayment(TaxPayment taxPayment) async {
    try {
      await _supabaseClient
          .from('tax_payments')
          .update(taxPayment.toJson())
          .eq('id', taxPayment.id)
          .eq('user_id', _supabaseClient.auth.currentUser!.id);
    } catch (e) {
      throw Exception('Failed to update tax payment: $e');
    }
  }

  // Delete a tax payment
  Future<void> deleteTaxPayment(String taxPaymentId) async {
    try {
      await _supabaseClient
          .from('tax_payments')
          .delete()
          .eq('id', taxPaymentId)
          .eq('user_id', _supabaseClient.auth.currentUser!.id);
    } catch (e) {
      throw Exception('Failed to delete tax payment: $e');
    }
  }
}
