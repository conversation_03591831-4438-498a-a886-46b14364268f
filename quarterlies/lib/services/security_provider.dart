import 'package:quarterlies/utils/app_config.dart';
import 'package:flutter/material.dart';

import 'auth_service.dart';
import 'encryption_service.dart';
import 'security_services.dart';
import 'package:quarterlies/utils/error_handler.dart';

/// A provider class that initializes and provides access to all security services
class SecurityProvider extends ChangeNotifier {
  late AuthService authService;
  late EncryptionService encryptionService;
  late ValidationService validationService;
  late SanitizationService sanitizationService;
  late NetworkSecurityService networkSecurityService;
  late AuditService auditService;

  bool _initialized = false;
  bool get isInitialized => _initialized;

  SecurityProvider() {
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize auth service
      authService = AuthService();

      try {
        // Initialize encryption service (may not work on web)
        encryptionService = EncryptionService();
        await encryptionService.initialize();
      } catch (e) {
        debugPrint(
          'Encryption service initialization failed (expected on web): $e',
        );
        // Create a mock encryption service for web
        encryptionService = EncryptionService();
      }

      // Initialize validation and sanitization services (these should work)
      validationService = ValidationService();
      sanitizationService = SanitizationService();

      try {
        // Initialize network security service (may not work on web)
        networkSecurityService = NetworkSecurityService();
      } catch (e) {
        debugPrint(
          'Network security service initialization failed (expected on web): $e',
        );
        // Create a mock network security service for web
        networkSecurityService = NetworkSecurityService();
      }

      try {
        // Initialize audit service with secure network client (may not work on web)
        final supabaseUrl = AppConfig.supabaseUrl;
        auditService = AuditService(supabaseUrl, networkSecurityService.client);
      } catch (e) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'initializeAuditService',
            'service': 'SecurityProvider',
            'platform': 'web',
          },
        );
        ErrorHandler.logError(appError);

        // Create a mock audit service for web
        final supabaseUrl = AppConfig.supabaseUrl;
        auditService = AuditService(supabaseUrl, networkSecurityService.client);
      }

      _initialized = true;
      notifyListeners();
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'initializeSecurityProvider',
          'service': 'SecurityProvider',
        },
      );
      ErrorHandler.logError(appError);

      // Initialize with minimal services for web compatibility
      authService = AuthService();
      validationService = ValidationService();
      sanitizationService = SanitizationService();
      networkSecurityService = NetworkSecurityService();
      encryptionService = EncryptionService();
      final supabaseUrl = AppConfig.supabaseUrl;
      auditService = AuditService(supabaseUrl, networkSecurityService.client);

      _initialized = true;
      notifyListeners();
    }
  }

  /// Ensures all security services are initialized before use
  Future<void> ensureInitialized() async {
    if (!_initialized) {
      await _initializeServices();
    }
  }

  /// Securely process financial data - encrypts sensitive data and logs access
  Future<String> processFinancialData({
    required String data,
    required String dataType,
    required String recordId,
    required String action,
  }) async {
    await ensureInitialized();

    // Sanitize input
    final sanitizedData = sanitizationService.sanitizeInput(data);

    // Encrypt data
    final dataKey = '${dataType}_${authService.currentUser?.id}_$recordId';
    final encryptedData = await encryptionService.encryptFinancialData(
      sanitizedData,
      dataKey,
    );

    // Log access
    if (authService.currentUser != null) {
      await auditService.logDataAccess(
        userId: authService.currentUser!.id,
        tableName: dataType,
        recordId: recordId,
        action: action,
      );
    }

    return encryptedData;
  }

  /// Securely retrieve financial data - decrypts data and logs access
  Future<String> retrieveFinancialData({
    required String encryptedData,
    required String dataType,
    required String recordId,
  }) async {
    await ensureInitialized();

    // Log access
    if (authService.currentUser != null) {
      await auditService.logDataAccess(
        userId: authService.currentUser!.id,
        tableName: dataType,
        recordId: recordId,
        action: 'VIEW',
      );
    }

    // Decrypt data
    final dataKey = '${dataType}_${authService.currentUser?.id}_$recordId';
    return encryptionService.decryptFinancialData(encryptedData, dataKey);
  }
}
