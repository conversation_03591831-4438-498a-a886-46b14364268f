import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/collapsible_section.dart';

/// A detail section that adapts its behavior based on display mode
///
/// In Field Mode: Uses collapsible sections (existing behavior)
/// In Office Mode: Shows content expanded by default or always visible
class AdaptiveDetailSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final bool initiallyExpanded;
  final String? sectionKey;
  final Color? backgroundColor;
  final Color? iconColor;
  final VoidCallback? onExpansionChanged;
  final bool
  alwaysExpandedInOffice; // Whether to always show expanded in Office Mode
  final bool showHeaderInOffice; // Whether to show the header in Office Mode

  const AdaptiveDetailSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.initiallyExpanded = false,
    this.sectionKey,
    this.backgroundColor,
    this.iconColor,
    this.onExpansionChanged,
    this.alwaysExpandedInOffice = true,
    this.showHeaderInOffice = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        if (displayProvider.isOfficeMode && alwaysExpandedInOffice) {
          // Office Mode: Show content directly with optional header
          return _buildOfficeLayout(context);
        } else {
          // Field Mode or Office Mode with collapsible preference
          return CollapsibleSection(
            title: title,
            icon: icon,
            initiallyExpanded:
                displayProvider.isOfficeMode ? true : initiallyExpanded,
            sectionKey: sectionKey,
            backgroundColor: backgroundColor,
            iconColor: iconColor,
            onExpansionChanged: onExpansionChanged,
            children: children,
          );
        }
      },
    );
  }

  Widget _buildOfficeLayout(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: 4,
      ), // Reduced margin for Office Mode
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Optional header in Office Mode
          if (showHeaderInOffice) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color:
                    backgroundColor ??
                    theme.colorScheme.primaryContainer.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    size: 20, // Smaller icon for Office Mode
                    color: iconColor ?? theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Content area
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(
              12,
            ), // Reduced padding for Office Mode
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius:
                  showHeaderInOffice
                      ? const BorderRadius.vertical(bottom: Radius.circular(8))
                      : BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }
}

/// A helper widget for creating adaptive info rows in detail screens
class AdaptiveInfoRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final Widget? trailing;
  final VoidCallback? onTap;

  const AdaptiveInfoRow({
    super.key,
    required this.label,
    required this.value,
    this.icon,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final theme = Theme.of(context);
        final isOfficeMode = displayProvider.isOfficeMode;

        return InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: isOfficeMode ? 6 : 8,
              horizontal: isOfficeMode ? 8 : 12,
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: isOfficeMode ? 16 : 20,
                    color: theme.colorScheme.primary,
                  ),
                  SizedBox(width: isOfficeMode ? 8 : 12),
                ],
                Expanded(
                  flex: isOfficeMode ? 2 : 3,
                  child: Text(
                    label,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: isOfficeMode ? 13 : 14,
                    ),
                  ),
                ),
                Expanded(
                  flex: isOfficeMode ? 3 : 4,
                  child: Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontSize: isOfficeMode ? 13 : 14,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
                if (trailing != null) ...[
                  SizedBox(width: isOfficeMode ? 8 : 12),
                  trailing!,
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

/// A helper widget for creating adaptive cards in detail screens
class AdaptiveDetailCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const AdaptiveDetailCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final isOfficeMode = displayProvider.isOfficeMode;

        return Card(
          elevation: isOfficeMode ? 1 : 3,
          margin:
              margin ??
              EdgeInsets.symmetric(
                vertical: isOfficeMode ? 4 : 8,
                horizontal: isOfficeMode ? 8 : 16,
              ),
          child: Padding(
            padding: padding ?? EdgeInsets.all(isOfficeMode ? 12 : 16),
            child: this.child,
          ),
        );
      },
    );
  }
}
