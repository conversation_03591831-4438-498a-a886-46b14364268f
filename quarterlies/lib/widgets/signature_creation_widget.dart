import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signature/signature.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';

/// A widget for creating and managing user signatures
/// Used in onboarding and settings for signature creation/editing
class SignatureCreationWidget extends StatefulWidget {
  final Function(Uint8List?) onSignatureChanged;
  final Uint8List? initialSignature;
  final String? title;
  final String? subtitle;
  final bool showClearButton;
  final bool showInstructions;

  const SignatureCreationWidget({
    super.key,
    required this.onSignatureChanged,
    this.initialSignature,
    this.title,
    this.subtitle,
    this.showClearButton = true,
    this.showInstructions = true,
  });

  @override
  State<SignatureCreationWidget> createState() =>
      _SignatureCreationWidgetState();
}

class _SignatureCreationWidgetState extends State<SignatureCreationWidget> {
  late SignatureController _signatureController;
  bool _hasSignature = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  void _initializeController() {
    _signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: Colors.black,
      exportBackgroundColor: Colors.transparent,
    );

    // Load initial signature if provided
    if (widget.initialSignature != null) {
      // Note: The signature package doesn't support loading from bytes directly
      // This would need to be implemented differently in a production app
      _hasSignature = true;
    }

    // Listen for signature changes
    _signatureController.addListener(_onSignatureChanged);
  }

  @override
  void dispose() {
    _signatureController.removeListener(_onSignatureChanged);
    _signatureController.dispose();
    super.dispose();
  }

  void _onSignatureChanged() {
    final isEmpty = _signatureController.isEmpty;
    if (_hasSignature != !isEmpty) {
      setState(() {
        _hasSignature = !isEmpty;
      });
    }
    _exportSignature();
  }

  Future<void> _exportSignature() async {
    if (_signatureController.isEmpty) {
      widget.onSignatureChanged(null);
      return;
    }

    try {
      final signatureBytes = await _signatureController.toPngBytes();
      widget.onSignatureChanged(signatureBytes);
    } catch (e) {
      debugPrint('Error exporting signature: $e');
      widget.onSignatureChanged(null);
    }
  }

  void _clearSignature() {
    _signatureController.clear();
    setState(() {
      _hasSignature = false;
    });
    widget.onSignatureChanged(null);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and subtitle
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 16 : 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              if (widget.subtitle != null) ...[
                SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 6.0),
                Text(
                  widget.subtitle!,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 14 : 16,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
              SizedBox(height: displayProvider.isOfficeMode ? 12.0 : 16.0),
            ],

            // Instructions
            if (widget.showInstructions) ...[
              Container(
                padding: EdgeInsets.all(
                  displayProvider.isOfficeMode ? 12.0 : 16.0,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: displayProvider.isOfficeMode ? 18 : 20,
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                    Expanded(
                      child: Text(
                        'Draw your signature in the box below. This will be automatically applied to documents you create.',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 13 : 14,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: displayProvider.isOfficeMode ? 12.0 : 16.0),
            ],

            // Signature canvas
            Container(
              height: displayProvider.isOfficeMode ? 120 : 160,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color:
                      _hasSignature
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey[400]!,
                  width: _hasSignature ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Signature(
                  controller: _signatureController,
                  backgroundColor: Colors.white,
                ),
              ),
            ),

            SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),

            // Action buttons
            Row(
              children: [
                if (widget.showClearButton) ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _hasSignature ? _clearSignature : null,
                      icon: Icon(
                        Icons.clear,
                        size: displayProvider.isOfficeMode ? 16 : 18,
                      ),
                      label: Text(
                        'Clear',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 14 : 16,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 8 : 12,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                ],

                Expanded(
                  flex: 2,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: displayProvider.isOfficeMode ? 12.0 : 16.0,
                      vertical: displayProvider.isOfficeMode ? 8.0 : 12.0,
                    ),
                    decoration: BoxDecoration(
                      color:
                          _hasSignature
                              ? Theme.of(context).colorScheme.primaryContainer
                                  .withValues(alpha: 0.3)
                              : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            _hasSignature
                                ? Theme.of(
                                  context,
                                ).colorScheme.primary.withValues(alpha: 0.5)
                                : Colors.grey[300]!,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _hasSignature ? Icons.check_circle : Icons.edit,
                          color:
                              _hasSignature
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey[600],
                          size: displayProvider.isOfficeMode ? 16 : 18,
                        ),
                        SizedBox(
                          width: displayProvider.isOfficeMode ? 6.0 : 8.0,
                        ),
                        Text(
                          _hasSignature
                              ? 'Signature Created'
                              : 'Draw Your Signature',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            fontWeight: FontWeight.w500,
                            color:
                                _hasSignature
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Optional signature preview
            if (_hasSignature) ...[
              SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),
              Text(
                'Preview:',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 6.0),
              Container(
                height: displayProvider.isOfficeMode ? 40 : 50,
                width: double.infinity,
                padding: EdgeInsets.all(
                  displayProvider.isOfficeMode ? 4.0 : 8.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: FutureBuilder<Uint8List?>(
                  future: _signatureController.toPngBytes(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData && snapshot.data != null) {
                      return Image.memory(snapshot.data!, fit: BoxFit.contain);
                    }
                    return const SizedBox();
                  },
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
