import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:quarterlies/models/address.dart';
import 'package:quarterlies/services/address_service.dart';

/// A text field with address autocomplete functionality.
/// This widget extends the standard text field with address suggestions.
class AddressAutocompleteField extends StatefulWidget {
  /// The controller for the text field.
  final TextEditingController controller;

  /// The label text to display.
  final String labelText;

  /// The hint text to display when the text field is empty.
  final String hintText;

  /// The callback to execute when the address changes.
  final Function(String)? onChanged;

  /// The callback to execute when an address is selected.
  final Function(Address)? onAddressSelected;

  /// The validator function for the text field.
  final String? Function(String?)? validator;

  /// Whether to show the current location button.
  final bool showLocationButton;

  /// The focus node for the text field.
  final FocusNode? focusNode;

  const AddressAutocompleteField({
    super.key,
    required this.controller,
    this.labelText = 'Address',
    this.hintText = 'Enter address',
    this.onChanged,
    this.onAddressSelected,
    this.validator,
    this.showLocationButton = true,
    this.focusNode,
  });

  @override
  State<AddressAutocompleteField> createState() =>
      _AddressAutocompleteFieldState();
}

class _AddressAutocompleteFieldState extends State<AddressAutocompleteField> {
  List<Address> _suggestions = [];
  bool _isLoading = false;
  bool _isCapturingLocation = false;
  String? _errorMessage;
  final AddressService _addressService = AddressService();

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  Future<void> _onTextChanged() async {
    final query = widget.controller.text;
    if (query.length < 3) {
      setState(() {
        _suggestions = [];
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Use Mapbox AddressService to get address suggestions
      final predictions = await _addressService.getAddressPredictions(query);

      setState(() {
        _suggestions =
            predictions.map((prediction) {
              // Convert AddressPrediction to Address objects
              // Extract street address from the full description
              final parts = prediction.description.split(',');
              final streetAddress =
                  parts.isNotEmpty ? parts[0].trim() : prediction.description;

              return Address(
                streetAddress: streetAddress,
                city: prediction.city ?? '',
                state: prediction.state ?? '',
                zipCode: prediction.zipCode ?? '',
                country: 'USA',
                latitude: prediction.latitude,
                longitude: prediction.longitude,
              );
            }).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _suggestions = [];
        _isLoading = false;
        _errorMessage = 'Failed to load address suggestions';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isCapturingLocation = true;
      _errorMessage = null;
    });

    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permission denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      // Convert coordinates to address
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;

        // Create an Address object
        final address = Address(
          streetAddress: placemark.street ?? '',
          city: placemark.locality ?? '',
          state: placemark.administrativeArea ?? '',
          zipCode: placemark.postalCode ?? '',
          country: placemark.country ?? 'USA',
          latitude: position.latitude,
          longitude: position.longitude,
        );

        // Update the text field with the full address
        widget.controller.text = address.shortFormattedAddress;

        // Notify parent about the selected address
        if (widget.onAddressSelected != null) {
          widget.onAddressSelected!(address);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to get location: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isCapturingLocation = false;
      });
    }
  }

  void _selectSuggestion(Address address) {
    // Update the text field with the formatted address
    widget.controller.text = address.shortFormattedAddress;

    setState(() {
      _suggestions = [];
    });

    // Notify parent about the selected address
    if (widget.onAddressSelected != null) {
      widget.onAddressSelected!(address);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TextFormField(
                controller: widget.controller,
                focusNode: widget.focusNode,
                decoration: InputDecoration(
                  labelText: widget.labelText,
                  hintText: widget.hintText,
                  border: const OutlineInputBorder(),
                  suffixIcon:
                      _isLoading
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                          : null,
                ),
                validator: widget.validator,
                onChanged: widget.onChanged,
              ),
            ),
            if (widget.showLocationButton)
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: IconButton(
                  icon:
                      _isCapturingLocation
                          ? const CircularProgressIndicator()
                          : const Icon(Icons.my_location),
                  onPressed: _isCapturingLocation ? null : _getCurrentLocation,
                  tooltip: 'Use current location',
                ),
              ),
          ],
        ),
        if (_suggestions.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(_suggestions[index].shortFormattedAddress),
                  subtitle: Text(_suggestions[index].city),
                  onTap: () => _selectSuggestion(_suggestions[index]),
                  dense: true,
                );
              },
            ),
          ),
      ],
    );
  }
}
