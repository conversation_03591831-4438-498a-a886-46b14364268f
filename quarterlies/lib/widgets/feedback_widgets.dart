import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/display_settings_provider.dart';
import '../services/feedback_service.dart';
import '../utils/feedback_messages.dart';

/// A widget that shows feedback for heavy operations with progress indication
class OperationFeedbackWidget extends StatelessWidget {
  final String operation;
  final bool isComplete;
  final bool isOffline;
  final double? progress; // 0.0 to 1.0, null for indeterminate
  final String? customMessage;
  final VoidCallback? onCancel;

  const OperationFeedbackWidget({
    super.key,
    required this.operation,
    this.isComplete = false,
    this.isOffline = false,
    this.progress,
    this.customMessage,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final message =
            customMessage ??
            FeedbackMessages.getOperationMessage(operation, isComplete);

        return Container(
          margin: EdgeInsets.all(displayProvider.isOfficeMode ? 8.0 : 16.0),
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
          decoration: BoxDecoration(
            color: _getBackgroundColor().withValues(alpha: 0.1),
            border: Border.all(color: _getBackgroundColor()),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  if (!isComplete) ...[
                    SizedBox(
                      width: displayProvider.isOfficeMode ? 16 : 20,
                      height: displayProvider.isOfficeMode ? 16 : 20,
                      child: CircularProgressIndicator(
                        strokeWidth: displayProvider.isOfficeMode ? 1.5 : 2.0,
                        color: _getBackgroundColor(),
                        value: progress,
                      ),
                    ),
                  ] else ...[
                    Icon(
                      Icons.check_circle,
                      color: _getBackgroundColor(),
                      size: displayProvider.isOfficeMode ? 16 : 20,
                    ),
                  ],
                  SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                  Expanded(
                    child: Text(
                      message,
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        fontWeight: FontWeight.w500,
                        color: _getBackgroundColor(),
                      ),
                    ),
                  ),
                  if (isOffline) ...[
                    SizedBox(width: displayProvider.isOfficeMode ? 4.0 : 8.0),
                    Icon(
                      Icons.cloud_off,
                      color: Colors.orange,
                      size: displayProvider.isOfficeMode ? 14 : 16,
                    ),
                  ],
                  if (onCancel != null && !isComplete) ...[
                    SizedBox(width: displayProvider.isOfficeMode ? 4.0 : 8.0),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: onCancel,
                      iconSize: displayProvider.isOfficeMode ? 16 : 20,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ],
              ),
              if (progress != null && !isComplete) ...[
                SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: _getBackgroundColor().withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getBackgroundColor(),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Color _getBackgroundColor() {
    if (isComplete) {
      return Colors.green;
    } else {
      return Colors.blue;
    }
  }
}

/// A widget that shows sync status with appropriate styling
class SyncStatusFeedbackWidget extends StatelessWidget {
  final String status;
  final bool isOffline;
  final String? details;
  final VoidCallback? onRetry;

  const SyncStatusFeedbackWidget({
    super.key,
    required this.status,
    this.isOffline = false,
    this.details,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final message = FeedbackMessages.getSyncMessage(status);

        return Container(
          margin: EdgeInsets.all(displayProvider.isOfficeMode ? 8.0 : 16.0),
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
          decoration: BoxDecoration(
            color: _getSyncColor().withValues(alpha: 0.1),
            border: Border.all(color: _getSyncColor()),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getSyncIcon(),
                    color: _getSyncColor(),
                    size: displayProvider.isOfficeMode ? 16 : 20,
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                  Expanded(
                    child: Text(
                      message,
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        fontWeight: FontWeight.w500,
                        color: _getSyncColor(),
                      ),
                    ),
                  ),
                  if (onRetry != null && _shouldShowRetry()) ...[
                    SizedBox(width: displayProvider.isOfficeMode ? 4.0 : 8.0),
                    TextButton(
                      onPressed: onRetry,
                      style: TextButton.styleFrom(
                        foregroundColor: _getSyncColor(),
                        padding: EdgeInsets.symmetric(
                          horizontal: displayProvider.isOfficeMode ? 8.0 : 12.0,
                          vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
                        ),
                      ),
                      child: Text(
                        'Retry',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 10 : 12,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              if (details != null) ...[
                SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 8.0),
                Text(
                  details!,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 10 : 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Color _getSyncColor() {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return Colors.green;
      case 'failed':
      case 'error':
        return Colors.red;
      case 'conflict':
        return Colors.deepOrange;
      case 'started':
      case 'syncing':
      default:
        return Colors.orange;
    }
  }

  IconData _getSyncIcon() {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return Icons.cloud_done;
      case 'failed':
      case 'error':
        return Icons.cloud_off;
      case 'conflict':
        return Icons.sync_problem;
      case 'started':
      case 'syncing':
      default:
        return Icons.sync;
    }
  }

  bool _shouldShowRetry() {
    return status.toLowerCase() == 'failed' ||
        status.toLowerCase() == 'error' ||
        status.toLowerCase() == 'conflict';
  }
}

/// A simple feedback banner for persistent messages
class FeedbackBanner extends StatelessWidget {
  final String message;
  final FeedbackType type;
  final bool isOffline;
  final VoidCallback? onDismiss;
  final VoidCallback? onAction;
  final String? actionLabel;

  const FeedbackBanner({
    super.key,
    required this.message,
    this.type = FeedbackType.info,
    this.isOffline = false,
    this.onDismiss,
    this.onAction,
    this.actionLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 8.0 : 12.0),
          decoration: BoxDecoration(
            color: _getBannerColor().withValues(alpha: 0.1),
            border: Border(
              bottom: BorderSide(color: _getBannerColor(), width: 2),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getBannerIcon(),
                color: _getBannerColor(),
                size: displayProvider.isOfficeMode ? 16 : 20,
              ),
              SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
              Expanded(
                child: Text(
                  isOffline ? '$message (Offline)' : message,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    color: _getBannerColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (onAction != null && actionLabel != null) ...[
                SizedBox(width: displayProvider.isOfficeMode ? 4.0 : 8.0),
                TextButton(
                  onPressed: onAction,
                  style: TextButton.styleFrom(
                    foregroundColor: _getBannerColor(),
                    padding: EdgeInsets.symmetric(
                      horizontal: displayProvider.isOfficeMode ? 8.0 : 12.0,
                      vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
                    ),
                  ),
                  child: Text(
                    actionLabel!,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 10 : 12,
                    ),
                  ),
                ),
              ],
              if (onDismiss != null) ...[
                SizedBox(width: displayProvider.isOfficeMode ? 4.0 : 8.0),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onDismiss,
                  iconSize: displayProvider.isOfficeMode ? 16 : 20,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Color _getBannerColor() {
    switch (type) {
      case FeedbackType.success:
        return Colors.green;
      case FeedbackType.info:
        return Colors.blue;
      case FeedbackType.sync:
        return Colors.orange;
      case FeedbackType.operation:
        return Colors.purple;
      case FeedbackType.warning:
        return Colors.amber;
    }
  }

  IconData _getBannerIcon() {
    switch (type) {
      case FeedbackType.success:
        return Icons.check_circle;
      case FeedbackType.info:
        return Icons.info;
      case FeedbackType.sync:
        return Icons.sync;
      case FeedbackType.operation:
        return Icons.settings;
      case FeedbackType.warning:
        return Icons.warning;
    }
  }
}
