import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../services/budget_service.dart';
import '../services/data_repository.dart';

/// Widget that displays a budget vs actuals chart
class Budget<PERSON>hart extends StatelessWidget {
  final BudgetData budgetData;

  const BudgetChart({super.key, required this.budgetData});

  @override
  Widget build(BuildContext context) {
    if (!budgetData.hasBudget) {
      return _buildNoBudgetCard(context);
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildChart(context),
            const SizedBox(height: 16),
            _buildSummary(context),
            if (budgetData.budgetStatus != BudgetStatus.underBudget)
              _buildAlert(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(Icons.analytics, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 8),
        Text(
          'Budget vs. Actuals',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildChart(BuildContext context) {
    return SizedBox(
      height: 200,
      child: Row(
        children: [
          Expanded(flex: 3, child: _buildBarChart(context)),
          const SizedBox(width: 16),
          Expanded(child: _buildLegend(context)),
        ],
      ),
    );
  }

  Widget _buildBarChart(BuildContext context) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxYValue(),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => Colors.blueGrey,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              String label;
              String value;
              if (groupIndex == 0) {
                label = 'Budget';
                value = '\$${budgetData.budget.toStringAsFixed(2)}';
              } else {
                label = 'Actual Costs';
                value = '\$${budgetData.totalActualCosts.toStringAsFixed(2)}';
              }
              return BarTooltipItem(
                '$label\n$value',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                switch (value.toInt()) {
                  case 0:
                    return const Text(
                      'Budget',
                      style: TextStyle(
                        color: Colors.black54,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    );
                  case 1:
                    return const Text(
                      'Actual',
                      style: TextStyle(
                        color: Colors.black54,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    );
                  default:
                    return const Text('');
                }
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 60,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${_formatCurrency(value)}',
                  style: const TextStyle(color: Colors.black54, fontSize: 10),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        barGroups: _buildBarGroups(),
        gridData: FlGridData(
          show: true,
          drawHorizontalLine: true,
          drawVerticalLine: false,
          horizontalInterval: _getGridInterval(),
          getDrawingHorizontalLine: (value) {
            return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
          },
        ),
      ),
    );
  }

  List<BarChartGroupData> _buildBarGroups() {
    return [
      // Budget bar
      BarChartGroupData(
        x: 0,
        barRods: [
          BarChartRodData(
            toY: budgetData.budget,
            color: Colors.grey[400]!,
            width: 40,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      ),
      // Actual costs bar
      BarChartGroupData(
        x: 1,
        barRods: [
          BarChartRodData(
            toY: budgetData.totalActualCosts,
            color: _getBudgetColor(budgetData.budgetStatus),
            width: 40,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
      ),
    ];
  }

  double _getMaxYValue() {
    final maxValue = [
      budgetData.budget,
      budgetData.totalActualCosts,
    ].reduce((a, b) => a > b ? a : b);
    // Add 20% padding to the top
    return maxValue * 1.2;
  }

  double _getGridInterval() {
    final maxValue = _getMaxYValue();
    // Create approximately 5 grid lines
    return maxValue / 5;
  }

  String _formatCurrency(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toStringAsFixed(0);
    }
  }

  Widget _buildLegend(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          'Budget',
          '\$${budgetData.budget.toStringAsFixed(2)}',
          Colors.grey[400]!,
        ),
        const SizedBox(height: 8),
        _buildLegendItem(
          'Actual Costs',
          '\$${budgetData.totalActualCosts.toStringAsFixed(2)}',
          _getBudgetColor(budgetData.budgetStatus),
        ),
        const SizedBox(height: 8),
        _buildLegendItem(
          'Remaining',
          '\$${budgetData.remainingBudget.toStringAsFixed(2)}',
          budgetData.remainingBudget >= 0 ? Colors.green : Colors.red,
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, String value, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(fontSize: 12, color: Colors.black54),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummary(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            'Expenses',
            '\$${budgetData.totalExpenses.toStringAsFixed(2)}',
            Icons.receipt,
          ),
          _buildSummaryItem(
            'Labor',
            '\$${budgetData.totalLaborCost.toStringAsFixed(2)}',
            Icons.access_time,
          ),
          _buildSummaryItem(
            'Total',
            '\$${budgetData.totalActualCosts.toStringAsFixed(2)}',
            Icons.calculate,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.black54),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black54),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildAlert(BuildContext context) {
    final budgetService = BudgetService(DataRepository());
    final message = budgetService.getBudgetAlertMessage(
      budgetData.budgetStatus,
      budgetData.budgetUsedPercentage,
    );

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getAlertBackgroundColor(budgetData.budgetStatus),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBudgetColor(budgetData.budgetStatus),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getAlertIcon(budgetData.budgetStatus),
            color: _getBudgetColor(budgetData.budgetStatus),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: _getBudgetColor(budgetData.budgetStatus),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoBudgetCard(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Budget Set',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Set an estimated expenses budget to track costs vs budget.',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getBudgetColor(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.underBudget:
        return Colors.green;
      case BudgetStatus.nearBudget:
        return Colors.orange;
      case BudgetStatus.overBudget:
        return Colors.red;
      case BudgetStatus.noBudget:
        return Colors.grey;
    }
  }

  Color _getAlertBackgroundColor(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.nearBudget:
        return Colors.orange[50]!;
      case BudgetStatus.overBudget:
        return Colors.red[50]!;
      default:
        return Colors.grey[50]!;
    }
  }

  IconData _getAlertIcon(BudgetStatus status) {
    switch (status) {
      case BudgetStatus.nearBudget:
        return Icons.warning;
      case BudgetStatus.overBudget:
        return Icons.error;
      default:
        return Icons.info;
    }
  }
}
