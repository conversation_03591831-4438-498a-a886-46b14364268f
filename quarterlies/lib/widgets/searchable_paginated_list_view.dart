import 'package:flutter/material.dart';
import 'package:quarterlies/widgets/paginated_list_view.dart';
import 'package:quarterlies/widgets/voice_search_field.dart';

/// A paginated list view with search functionality and voice input.
/// This widget extends the PaginatedListView with search capabilities.
class SearchablePaginatedListView<T> extends StatefulWidget {
  /// The total number of items in the dataset
  final int totalItems;

  /// The number of items to load per page
  final int pageSize;

  /// Function to load a page of items, with optional search query
  final Future<List<T>> Function(int page, int pageSize, String? searchQuery)
  loadPage;

  /// Builder function to create a widget for each item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// Widget to display when loading more items
  final Widget? loadingWidget;

  /// Widget to display when the list is empty
  final Widget? emptyWidget;

  /// Widget to display when an error occurs
  final Widget Function(BuildContext context, String error)? errorBuilder;

  /// Whether to show a pull-to-refresh indicator
  final bool enablePullToRefresh;

  /// Callback when pull-to-refresh is triggered
  final Future<void> Function()? onRefresh;

  /// Optional header widget to display at the top of the list
  final Widget? headerWidget;

  /// Optional footer widget to display at the bottom of the list
  final Widget? footerWidget;

  /// Hint text for the search field
  final String searchHint;

  const SearchablePaginatedListView({
    required this.totalItems,
    required this.pageSize,
    required this.loadPage,
    required this.itemBuilder,
    this.loadingWidget,
    this.emptyWidget,
    this.errorBuilder,
    this.enablePullToRefresh = false,
    this.onRefresh,
    this.headerWidget,
    this.footerWidget,
    this.searchHint = 'Search...',
    super.key,
  });

  @override
  State<SearchablePaginatedListView<T>> createState() =>
      _SearchablePaginatedListViewState<T>();
}

class _SearchablePaginatedListViewState<T>
    extends State<SearchablePaginatedListView<T>> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  Future<List<T>> _loadPageWithSearch(int page, int pageSize) async {
    setState(() {
      _isSearching = _searchQuery.isNotEmpty;
    });
    return widget.loadPage(
      page,
      pageSize,
      _searchQuery.isNotEmpty ? _searchQuery : null,
    );
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
      _isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search bar with voice input
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: VoiceSearchField(
            controller: _searchController,
            hintText: widget.searchHint,
            focusNode: _searchFocusNode,
            onClear: _clearSearch,
          ),
        ),

        // Results count or searching indicator
        if (_isSearching)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  'Searching for "$_searchQuery"',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
                const Spacer(),
                TextButton(onPressed: _clearSearch, child: const Text('Clear')),
              ],
            ),
          ),

        // Paginated list view
        Expanded(
          child: PaginatedListView<T>(
            totalItems: widget.totalItems,
            pageSize: widget.pageSize,
            loadPage: _loadPageWithSearch,
            itemBuilder: widget.itemBuilder,
            loadingWidget: widget.loadingWidget,
            emptyWidget:
                _isSearching
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: Theme.of(
                              context,
                            ).colorScheme.secondary.withAlpha(179),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No results found for "$_searchQuery"',
                            style: TextStyle(
                              fontSize: 18,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          TextButton(
                            onPressed: _clearSearch,
                            child: const Text('Clear Search'),
                          ),
                        ],
                      ),
                    )
                    : widget.emptyWidget,
            errorBuilder: widget.errorBuilder,
            enablePullToRefresh: widget.enablePullToRefresh,
            onRefresh: widget.onRefresh,
            headerWidget: widget.headerWidget,
            footerWidget: widget.footerWidget,
          ),
        ),
      ],
    );
  }
}
