import 'package:flutter/material.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/app_constants.dart';
import 'package:quarterlies/services/feedback_service.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

/// A widget that displays error messages in a user-friendly way
class ErrorDisplayWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showTechnicalDetails;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showTechnicalDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getErrorColor(error.severity).withValues(alpha: 0.1),
        border: Border.all(color: _getErrorColor(error.severity)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                _getErrorIcon(error.type),
                color: _getErrorColor(error.severity),
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getSeverityLabel(error.severity),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getErrorColor(error.severity),
                  ),
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onDismiss,
                  iconSize: 20,
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            ErrorHandler.getUserFriendlyMessage(error),
            style: const TextStyle(fontSize: 14),
          ),
          if (showTechnicalDetails && error.technicalDetails != null) ...[
            const SizedBox(height: 8),
            ExpansionTile(
              title: const Text('Technical Details'),
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    error.technicalDetails!,
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
          ],
          if (onRetry != null && ErrorHandler.shouldRetry(error)) ...[
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _getErrorColor(error.severity),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.orange;
      case ErrorSeverity.medium:
        return Colors.amber;
      case ErrorSeverity.high:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock;
      case ErrorType.authorization:
      case ErrorType.rls:
        return Icons.security;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.database:
        return Icons.storage;
      case ErrorType.storage:
        return Icons.cloud_off;
      case ErrorType.timeout:
        return Icons.timer_off;
      case ErrorType.conflict:
        return Icons.merge_type;
      case ErrorType.unknown:
        return Icons.error;
    }
  }

  String _getSeverityLabel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return 'Notice';
      case ErrorSeverity.medium:
        return 'Warning';
      case ErrorSeverity.high:
        return 'Error';
      case ErrorSeverity.critical:
        return 'Critical Error';
    }
  }
}

/// Utility class for showing error messages using various UI components
class ErrorDisplay {
  ErrorDisplay._();

  /// Show error using SnackBar
  static void showSnackBar(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
  }) {
    final messenger = ScaffoldMessenger.of(context);
    messenger.hideCurrentSnackBar();

    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(_getErrorIcon(error.type), color: Colors.white, size: 20),
          const SizedBox(width: 8),
          Expanded(child: Text(ErrorHandler.getUserFriendlyMessage(error))),
        ],
      ),
      backgroundColor: _getErrorColor(error.severity),
      duration: Duration(seconds: AppConstants.errorDisplayDurationSeconds),
      action:
          onRetry != null && ErrorHandler.shouldRetry(error)
              ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
              : null,
    );

    messenger.showSnackBar(snackBar);
  }

  /// Show error using AlertDialog
  static Future<void> showErrorDialog(
    BuildContext context,
    AppError error, {
    VoidCallback? onRetry,
    bool showTechnicalDetails = false,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                _getErrorIcon(error.type),
                color: _getErrorColor(error.severity),
              ),
              const SizedBox(width: 8),
              Text(_getSeverityLabel(error.severity)),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(ErrorHandler.getUserFriendlyMessage(error)),
                if (showTechnicalDetails && error.technicalDetails != null) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Technical Details:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      error.technicalDetails!,
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('OK'),
            ),
            if (onRetry != null && ErrorHandler.shouldRetry(error))
              ElevatedButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  onRetry();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getErrorColor(error.severity),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Try Again'),
              ),
          ],
        );
      },
    );
  }

  /// Show error in a dedicated error area of the screen
  static Widget buildErrorArea(
    AppError error, {
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool showTechnicalDetails = false,
  }) {
    return ErrorDisplayWidget(
      error: error,
      onRetry: onRetry,
      onDismiss: onDismiss,
      showTechnicalDetails: showTechnicalDetails,
    );
  }

  static Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return Colors.orange;
      case ErrorSeverity.medium:
        return Colors.amber.shade700;
      case ErrorSeverity.high:
        return Colors.red;
      case ErrorSeverity.critical:
        return Colors.red.shade900;
    }
  }

  static IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock;
      case ErrorType.authorization:
      case ErrorType.rls:
        return Icons.security;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.database:
        return Icons.storage;
      case ErrorType.storage:
        return Icons.cloud_off;
      case ErrorType.timeout:
        return Icons.timer_off;
      case ErrorType.conflict:
        return Icons.merge_type;
      case ErrorType.unknown:
        return Icons.error;
    }
  }

  static String _getSeverityLabel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return 'Notice';
      case ErrorSeverity.medium:
        return 'Warning';
      case ErrorSeverity.high:
        return 'Error';
      case ErrorSeverity.critical:
        return 'Critical Error';
    }
  }

  // ==================== FEEDBACK METHODS ====================

  /// Show success feedback using SnackBar
  static void showSuccess(
    BuildContext context,
    String message, {
    bool isOffline = false,
  }) {
    FeedbackService.showSuccess(context, message, isOffline: isOffline);
  }

  /// Show info feedback using SnackBar
  static void showInfo(
    BuildContext context,
    String message, {
    bool isOffline = false,
  }) {
    FeedbackService.showInfo(context, message, isOffline: isOffline);
  }

  /// Show sync feedback using SnackBar
  static void showSync(
    BuildContext context,
    String message, {
    bool isOffline = false,
  }) {
    FeedbackService.showSync(context, message, isOffline: isOffline);
  }

  /// Show operation feedback using SnackBar
  static void showOperation(
    BuildContext context,
    String message, {
    bool isOffline = false,
  }) {
    FeedbackService.showOperation(context, message, isOffline: isOffline);
  }

  /// Show warning feedback using SnackBar
  static void showWarning(
    BuildContext context,
    String message, {
    bool isOffline = false,
  }) {
    FeedbackService.showWarning(context, message, isOffline: isOffline);
  }

  /// Show data operation feedback with appropriate message
  static void showDataOperation(
    BuildContext context,
    String entityType,
    String operation, {
    bool isOffline = false,
  }) {
    final message = FeedbackMessages.getDataOperationMessage(
      entityType,
      operation,
      isOffline: isOffline,
    );
    showSuccess(context, message, isOffline: isOffline);
  }

  /// Show sync status feedback
  static void showSyncStatus(
    BuildContext context,
    String status, {
    bool isOffline = false,
  }) {
    final message = FeedbackMessages.getSyncMessage(status);
    showSync(context, message, isOffline: isOffline);
  }

  /// Show operation progress feedback
  static void showOperationProgress(
    BuildContext context,
    String operation,
    bool isComplete, {
    bool isOffline = false,
  }) {
    final message = FeedbackMessages.getOperationMessage(operation, isComplete);
    if (isComplete) {
      showOperation(context, message, isOffline: isOffline);
    } else {
      showInfo(context, message, isOffline: isOffline);
    }
  }
}
