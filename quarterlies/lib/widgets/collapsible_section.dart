import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A field-friendly collapsible section widget optimized for outdoor use
/// with large touch targets and high contrast design
class CollapsibleSection extends StatefulWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final bool initiallyExpanded;
  final String? sectionKey; // For remembering state
  final Color? backgroundColor;
  final Color? iconColor;
  final VoidCallback? onExpansionChanged;

  const CollapsibleSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.initiallyExpanded = false,
    this.sectionKey,
    this.backgroundColor,
    this.iconColor,
    this.onExpansionChanged,
  });

  @override
  State<CollapsibleSection> createState() => _CollapsibleSectionState();
}

class _CollapsibleSectionState extends State<CollapsibleSection>
    with SingleTickerProviderStateMixin {
  late bool _isExpanded;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Load saved state if sectionKey is provided
    if (widget.sectionKey != null) {
      _loadExpandedState();
    }

    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadExpandedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedState = prefs.getBool('section_${widget.sectionKey}');
      if (savedState != null && mounted) {
        setState(() {
          _isExpanded = savedState;
        });
        if (_isExpanded) {
          _animationController.value = 1.0;
        }
      }
    } catch (e) {
      // Ignore errors loading state
    }
  }

  Future<void> _saveExpandedState() async {
    if (widget.sectionKey != null) {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('section_${widget.sectionKey}', _isExpanded);
      } catch (e) {
        // Ignore errors saving state
      }
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    _saveExpandedState();
    widget.onExpansionChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          // Header with large touch target
          Material(
            color: widget.backgroundColor ?? colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: InkWell(
              onTap: _toggleExpanded,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20), // Large touch area
                child: Row(
                  children: [
                    // Section icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: (widget.iconColor ?? colorScheme.primary)
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        widget.icon,
                        size: 24,
                        color: widget.iconColor ?? colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Section title
                    Expanded(
                      child: Text(
                        widget.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 18, // Larger text for outdoor visibility
                        ),
                      ),
                    ),

                    // Expand/collapse indicator with large touch target
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: AnimatedRotation(
                        turns: _isExpanded ? 0.5 : 0,
                        duration: const Duration(milliseconds: 300),
                        child: Icon(
                          Icons.expand_more,
                          size: 32, // Large icon for easy visibility
                          color: colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Collapsible content
          SizeTransition(
            sizeFactor: _animation,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: widget.children,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A specialized collapsible section for settings
class SettingsSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final bool initiallyExpanded;
  final String? sectionKey;

  const SettingsSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.initiallyExpanded = false,
    this.sectionKey,
  });

  @override
  Widget build(BuildContext context) {
    return CollapsibleSection(
      title: title,
      icon: icon,
      initiallyExpanded: initiallyExpanded,
      sectionKey: sectionKey,
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
      children: children,
    );
  }
}

/// A specialized collapsible section for detail screens
class DetailSection extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;
  final bool initiallyExpanded;
  final String? sectionKey;
  final Color? accentColor;

  const DetailSection({
    super.key,
    required this.title,
    required this.icon,
    required this.children,
    this.initiallyExpanded = true, // Details usually expanded by default
    this.sectionKey,
    this.accentColor,
  });

  @override
  Widget build(BuildContext context) {
    return CollapsibleSection(
      title: title,
      icon: icon,
      initiallyExpanded: initiallyExpanded,
      sectionKey: sectionKey,
      iconColor: accentColor,
      children: children,
    );
  }
}
