import 'package:flutter/material.dart';
import 'package:quarterlies/services/voice_recording_service.dart';

/// A search field with voice input capability.
/// This widget extends the standard search field with voice recording functionality.
class VoiceSearchField extends StatefulWidget {
  /// The controller for the search field.
  final TextEditingController controller;

  /// The hint text to display when the search field is empty.
  final String hintText;

  /// The callback to execute when the search query changes.
  final Function(String)? onChanged;

  /// The callback to execute when the search is submitted.
  final Function(String)? onSubmitted;

  /// The callback to execute when the clear button is pressed.
  final VoidCallback? onClear;

  /// Whether to show the clear button.
  final bool showClearButton;

  /// The focus node for the search field.
  final FocusNode? focusNode;

  const VoiceSearchField({
    super.key,
    required this.controller,
    this.hintText = 'Search...',
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.showClearButton = true,
    this.focusNode,
  });

  @override
  State<VoiceSearchField> createState() => _VoiceSearchFieldState();
}

class _VoiceSearchFieldState extends State<VoiceSearchField> {
  final _voiceRecordingService = VoiceRecordingService();
  bool _isRecording = false;
  String? _errorMessage;

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
        _errorMessage = null;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text for search
        final processedQuery = _voiceRecordingService.processSearchQuery(
          transcribedText,
        );

        // Update the search field with the processed query
        widget.controller.text = processedQuery;

        // Trigger the onChanged callback
        if (widget.onChanged != null) {
          widget.onChanged!(processedQuery);
        }

        // Trigger the onSubmitted callback
        if (widget.onSubmitted != null) {
          widget.onSubmitted!(processedQuery);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  void _clearSearch() {
    widget.controller.clear();
    if (widget.onClear != null) {
      widget.onClear!();
    }
    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
        TextField(
          controller: widget.controller,
          focusNode: widget.focusNode,
          decoration: InputDecoration(
            hintText: widget.hintText,
            prefixIcon: const Icon(Icons.search),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.showClearButton && widget.controller.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearSearch,
                    tooltip: 'Clear search',
                  ),
                IconButton(
                  icon: Icon(
                    _isRecording ? Icons.stop : Icons.mic,
                    color: _isRecording ? Colors.red : null,
                  ),
                  onPressed: _isRecording ? _stopRecording : _startRecording,
                  tooltip: _isRecording ? 'Stop recording' : 'Voice search',
                ),
              ],
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          onChanged: widget.onChanged,
          onSubmitted: widget.onSubmitted,
        ),
      ],
    );
  }
}
