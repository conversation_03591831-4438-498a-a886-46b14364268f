import 'package:flutter/material.dart';

/// A generic dialog for resolving data conflicts between local and server versions.
///
/// This dialog shows both versions of the data and allows the user to choose
/// which version to keep. It supports different entity types (Invoice, Expense,
/// TaxPayment) and provides a consistent UI for conflict resolution.
class ConflictResolutionDialog<T> extends StatefulWidget {
  final T localVersion;
  final T serverVersion;
  final String title;
  final String entityType;
  final Function(bool keepLocal) onResolved;
  final Widget Function(T entity) entityDetailsBuilder;

  const ConflictResolutionDialog({
    required this.localVersion,
    required this.serverVersion,
    required this.title,
    required this.entityType,
    required this.onResolved,
    required this.entityDetailsBuilder,
    super.key,
  });

  @override
  State<ConflictResolutionDialog<T>> createState() =>
      _ConflictResolutionDialogState<T>();
}

class _ConflictResolutionDialogState<T>
    extends State<ConflictResolutionDialog<T>> {
  bool _keepLocal = true; // Default to keeping local version

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'There is a conflict between your local version and the server version of this ${widget.entityType}.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            _buildVersionSelector(),
            const SizedBox(height: 16),
            _buildVersionDetails(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onResolved(_keepLocal);
            Navigator.of(context).pop();
          },
          child: const Text('Resolve Conflict'),
        ),
      ],
    );
  }

  Widget _buildVersionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Select which version to keep:'),
        RadioListTile<bool>(
          title: const Text('Local Version (Your Device)'),
          subtitle: const Text('Changes you made on this device'),
          value: true,
          groupValue: _keepLocal,
          onChanged: (value) {
            setState(() {
              _keepLocal = value!;
            });
          },
        ),
        RadioListTile<bool>(
          title: const Text('Server Version'),
          subtitle: const Text('Changes made on the server or another device'),
          value: false,
          groupValue: _keepLocal,
          onChanged: (value) {
            setState(() {
              _keepLocal = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildVersionDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const SizedBox(height: 8),
        Text('Version Details', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Local Version',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: _keepLocal ? Theme.of(context).primaryColor : null,
                      fontWeight: _keepLocal ? FontWeight.bold : null,
                    ),
                  ),
                  const SizedBox(height: 8),
                  widget.entityDetailsBuilder(widget.localVersion),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Server Version',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color:
                          !_keepLocal ? Theme.of(context).primaryColor : null,
                      fontWeight: !_keepLocal ? FontWeight.bold : null,
                    ),
                  ),
                  const SizedBox(height: 8),
                  widget.entityDetailsBuilder(widget.serverVersion),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
