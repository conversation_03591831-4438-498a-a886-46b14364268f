import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/services/connectivity_service.dart';

/// A comprehensive set of loading widgets for the Quarterlies app
/// that are offline-first aware and adapt to different display modes

/// Primary loading indicator with offline awareness
class QuarterliesLoadingIndicator extends StatelessWidget {
  final String? message;
  final bool showOfflineStatus;
  final double size;
  final Color? color;

  const QuarterliesLoadingIndicator({
    super.key,
    this.message,
    this.showOfflineStatus = true,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return FutureBuilder<bool>(
          future: ConnectivityService().checkConnection(),
          builder: (context, snapshot) {
            final isOnline = snapshot.data ?? true;

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: size,
                  height: size,
                  child: CircularProgressIndicator(
                    strokeWidth: displayProvider.isOfficeMode ? 2.0 : 3.0,
                    color: color ?? Theme.of(context).colorScheme.primary,
                  ),
                ),
                if (message != null) ...[
                  SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),
                  Text(
                    message!,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                if (showOfflineStatus && !isOnline) ...[
                  SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 8.0),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: displayProvider.isOfficeMode ? 6.0 : 8.0,
                      vertical: displayProvider.isOfficeMode ? 2.0 : 4.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.cloud_off,
                          size: displayProvider.isOfficeMode ? 12 : 14,
                          color: Colors.orange,
                        ),
                        SizedBox(
                          width: displayProvider.isOfficeMode ? 4.0 : 6.0,
                        ),
                        Text(
                          'Working offline',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 10 : 12,
                            color: Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            );
          },
        );
      },
    );
  }
}

/// Inline loading indicator for list items and small spaces
class InlineLoadingIndicator extends StatelessWidget {
  final String? text;
  final double size;
  final Color? color;

  const InlineLoadingIndicator({
    super.key,
    this.text,
    this.size = 16.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: displayProvider.isOfficeMode ? 1.5 : 2.0,
                color: color ?? Theme.of(context).colorScheme.primary,
              ),
            ),
            if (text != null) ...[
              SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
              Text(
                text!,
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// Linear progress indicator for operations with known progress
class QuarterliesProgressIndicator extends StatelessWidget {
  final double? value;
  final String? label;
  final String? subtitle;
  final bool showPercentage;

  const QuarterliesProgressIndicator({
    super.key,
    this.value,
    this.label,
    this.subtitle,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (label != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label!,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (showPercentage && value != null)
                    Text(
                      '${(value! * 100).round()}%',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 10 : 12,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                ],
              ),
              SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 6.0),
            ],
            LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            if (subtitle != null) ...[
              SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 6.0),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 10 : 12,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// Loading overlay for full-screen operations
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;
  final bool canDismiss;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingMessage,
    this.canDismiss = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: QuarterliesLoadingIndicator(
                    message: loadingMessage,
                    size: 32.0,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Specialized loading indicator for sync operations
class SyncLoadingIndicator extends StatelessWidget {
  final String operation;
  final bool showSyncIcon;
  final double size;

  const SyncLoadingIndicator({
    super.key,
    required this.operation,
    this.showSyncIcon = true,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showSyncIcon) ...[
              Icon(
                Icons.sync,
                size: size,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
            ],
            SizedBox(
              width: size * 0.8,
              height: size * 0.8,
              child: CircularProgressIndicator(
                strokeWidth: displayProvider.isOfficeMode ? 1.5 : 2.0,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
            Text(
              operation,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 12 : 14,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Loading indicator for OCR processing
class OcrLoadingIndicator extends StatelessWidget {
  final String? stage;
  final double? progress;

  const OcrLoadingIndicator({super.key, this.stage, this.progress});

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.document_scanner,
                  size: displayProvider.isOfficeMode ? 16 : 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
                Text(
                  stage ?? 'Processing image...',
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
            if (progress != null)
              QuarterliesProgressIndicator(
                value: progress,
                showPercentage: true,
              )
            else
              const InlineLoadingIndicator(size: 16.0),
          ],
        );
      },
    );
  }
}

/// Loading indicator for PDF generation
class PdfLoadingIndicator extends StatelessWidget {
  final String documentType;
  final double? progress;

  const PdfLoadingIndicator({
    super.key,
    required this.documentType,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  size: displayProvider.isOfficeMode ? 16 : 20,
                  color: Colors.red,
                ),
                SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
                Text(
                  'Generating $documentType PDF...',
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
            if (progress != null)
              QuarterliesProgressIndicator(
                value: progress,
                showPercentage: true,
              )
            else
              const InlineLoadingIndicator(size: 16.0),
          ],
        );
      },
    );
  }
}

/// Loading indicator for voice operations
class VoiceLoadingIndicator extends StatelessWidget {
  final String operation;
  final bool isRecording;

  const VoiceLoadingIndicator({
    super.key,
    required this.operation,
    this.isRecording = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isRecording ? Icons.mic : Icons.voice_over_off,
              size: displayProvider.isOfficeMode ? 16 : 20,
              color:
                  isRecording
                      ? Colors.red
                      : Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
            if (isRecording)
              TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.5, end: 1.0),
                duration: const Duration(milliseconds: 800),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: const InlineLoadingIndicator(size: 16.0),
                  );
                },
                onEnd: () {
                  // Animation will restart automatically due to rebuild
                },
              )
            else
              const InlineLoadingIndicator(size: 16.0),
            SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
            Text(
              operation,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 12 : 14,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ],
        );
      },
    );
  }
}
