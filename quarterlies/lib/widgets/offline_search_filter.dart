import 'package:flutter/material.dart';
import 'package:quarterlies/services/connectivity_service.dart';

/// A reusable widget for search and filtering that works with cached data when offline.
///
/// This widget provides a consistent UI for searching and filtering data,
/// regardless of network connectivity. It adapts to show appropriate UI elements
/// based on the current connectivity status.
class OfflineSearchFilter extends StatefulWidget {
  final String title;
  final String searchHint;
  final List<String>? filterCategories;
  final Function(String) onSearch;
  final Function(String?)? onFilterChanged;
  final Widget? filterIcon;
  final bool showOfflineIndicator;

  const OfflineSearchFilter({
    required this.title,
    required this.searchHint,
    required this.onSearch,
    this.filterCategories,
    this.onFilterChanged,
    this.filterIcon,
    this.showOfflineIndicator = true,
    super.key,
  });

  @override
  State<OfflineSearchFilter> createState() => _OfflineSearchFilterState();
}

class _OfflineSearchFilterState extends State<OfflineSearchFilter> {
  final TextEditingController _searchController = TextEditingController();
  final ConnectivityService _connectivityService = ConnectivityService();
  String? _selectedFilter;
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
    _connectivityService.connectionStatus.listen((isConnected) {
      setState(() {
        _isOffline = !isConnected;
      });
    });
  }

  Future<void> _checkConnectivity() async {
    final isConnected = await _connectivityService.checkConnection();
    setState(() {
      _isOffline = !isConnected;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and offline indicator
        if (widget.title.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                if (_isOffline && widget.showOfflineIndicator)
                  _buildOfflineIndicator(),
              ],
            ),
          ),

        // Search bar
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: widget.searchHint,
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
            suffixIcon:
                _searchController.text.isNotEmpty
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearch('');
                      },
                    )
                    : null,
          ),
          onChanged: widget.onSearch,
        ),

        // Filter dropdown if categories are provided
        if (widget.filterCategories != null &&
            widget.filterCategories!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                widget.filterIcon ?? const Icon(Icons.filter_list, size: 20),
                const SizedBox(width: 8),
                const Text('Filter:'),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      isExpanded: true,
                      hint: const Text('All'),
                      value: _selectedFilter,
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('All'),
                        ),
                        ...widget.filterCategories!.map((category) {
                          return DropdownMenuItem<String>(
                            value: category,
                            child: Text(category),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedFilter = value;
                        });
                        if (widget.onFilterChanged != null) {
                          widget.onFilterChanged!(value);
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

        // Offline mode explanation if needed
        if (_isOffline && widget.showOfflineIndicator)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              'Searching in offline mode. Results are from cached data.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.orange[100],
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(color: Colors.orange[300]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.offline_bolt, size: 16, color: Colors.orange[800]),
          const SizedBox(width: 4),
          Text(
            'Offline',
            style: TextStyle(fontSize: 12, color: Colors.orange[800]),
          ),
        ],
      ),
    );
  }
}
