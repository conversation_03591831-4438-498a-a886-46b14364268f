import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';

/// A comprehensive responsive text widget that automatically adjusts font size based on:
/// - Available space (prevents overflow)
/// - Display mode (Field vs Office)
/// - Screen size and orientation
/// - Device type (mobile, tablet, desktop)
/// - Device aspect ratio (ultra-wide, tall/narrow, foldable)
///
/// This ensures all text is visible without overflow while maintaining readability
/// across all device types and orientations
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? minFontSize;
  final double? maxFontSize;
  final bool? softWrap;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final Locale? locale;
  final TextWidthBasis? textWidthBasis;
  final TextHeightBehavior? textHeightBehavior;
  final bool adaptToDisplayMode;
  final bool adaptToScreenSize;
  final bool adaptToOrientation;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.minFontSize,
    this.maxFontSize,
    this.softWrap,
    this.strutStyle,
    this.textDirection,
    this.locale,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.adaptToDisplayMode = true,
    this.adaptToScreenSize = true,
    this.adaptToOrientation = true,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final adaptedStyle = _getAdaptedTextStyle(context, displayProvider);

        return LayoutBuilder(
          builder: (context, constraints) {
            final finalStyle = _getFinalTextStyle(
              context,
              displayProvider,
              constraints,
              adaptedStyle,
            );

            return Text(
              text,
              style: finalStyle,
              textAlign: textAlign,
              maxLines: maxLines,
              overflow: overflow ?? TextOverflow.visible,
              softWrap: softWrap ?? true,
              strutStyle: strutStyle,
              textDirection: textDirection,
              locale: locale,
              textWidthBasis: textWidthBasis,
              textHeightBehavior: textHeightBehavior,
            );
          },
        );
      },
    );
  }

  TextStyle _getAdaptedTextStyle(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
  ) {
    final baseStyle =
        style ?? Theme.of(context).textTheme.bodyMedium ?? const TextStyle();
    double baseFontSize = baseStyle.fontSize ?? 14.0;

    // Get screen metrics for comprehensive adaptation
    final screenSize = MediaQuery.of(context).size;
    final aspectRatio = screenSize.width / screenSize.height;
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      if (displayProvider.isOfficeMode) {
        // Office mode: smaller text for density, but ensure readability
        baseFontSize *= 0.85;
      } else {
        // Field mode: larger text for outdoor visibility and touch-friendly UI
        baseFontSize *= 1.15;
      }
    }

    // Adapt to device type and screen size
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseFontSize *= 1.3; // Larger for desktop viewing distance
      } else if (ResponsiveHelper.isTablet(context)) {
        baseFontSize *= 1.15; // Moderate increase for tablets
      } else {
        // Mobile: adjust based on screen density
        if (devicePixelRatio > 3.0) {
          baseFontSize *= 1.1; // High-density screens
        } else if (devicePixelRatio < 2.0) {
          baseFontSize *= 0.95; // Lower-density screens
        }
      }
    }

    // Adapt to aspect ratio and orientation
    if (adaptToOrientation) {
      if (isLandscape) {
        if (aspectRatio > 2.0) {
          // Ultra-wide screens (e.g., foldables, ultra-wide monitors)
          baseFontSize *= 0.9;
        } else if (aspectRatio > 1.8) {
          // Wide screens
          baseFontSize *= 0.95;
        } else {
          // Standard landscape
          baseFontSize *= 0.92;
        }
      } else {
        // Portrait mode
        if (aspectRatio < 0.5) {
          // Very tall/narrow screens (e.g., foldables in portrait)
          baseFontSize *= 0.9;
        } else if (aspectRatio < 0.6) {
          // Tall screens (modern phones)
          baseFontSize *= 0.95;
        }
      }
    }

    // Ensure minimum readability
    baseFontSize = baseFontSize.clamp(8.0, 48.0);

    return baseStyle.copyWith(fontSize: baseFontSize);
  }

  TextStyle _getFinalTextStyle(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    BoxConstraints constraints,
    TextStyle adaptedStyle,
  ) {
    final fontSize = adaptedStyle.fontSize ?? 14.0;
    final minSize = minFontSize ?? (fontSize * 0.7);
    final maxSize = maxFontSize ?? (fontSize * 1.3);

    // Calculate optimal font size based on available space
    final optimalSize = _calculateOptimalFontSize(
      context,
      constraints,
      adaptedStyle,
      minSize,
      maxSize,
    );

    return adaptedStyle.copyWith(fontSize: optimalSize);
  }

  double _calculateOptimalFontSize(
    BuildContext context,
    BoxConstraints constraints,
    TextStyle style,
    double minSize,
    double maxSize,
  ) {
    if (constraints.maxWidth == double.infinity) {
      return style.fontSize ?? 14.0;
    }

    // Binary search for optimal font size
    double low = minSize;
    double high = maxSize;
    double optimalSize = style.fontSize ?? 14.0;

    while (high - low > 0.5) {
      final mid = (low + high) / 2;
      final testStyle = style.copyWith(fontSize: mid);

      final textPainter = TextPainter(
        text: TextSpan(text: text, style: testStyle),
        textDirection: textDirection ?? TextDirection.ltr,
        maxLines: maxLines,
      );

      textPainter.layout(maxWidth: constraints.maxWidth);

      if (textPainter.didExceedMaxLines ||
          (maxLines != null && textPainter.height > constraints.maxHeight)) {
        high = mid;
      } else {
        low = mid;
        optimalSize = mid;
      }
    }

    return optimalSize.clamp(minSize, maxSize);
  }
}

/// A responsive text widget specifically for titles
class ResponsiveTitle extends ResponsiveText {
  const ResponsiveTitle(
    super.text, {
    super.key,
    super.style,
    super.textAlign,
    super.maxLines = 2,
    super.minFontSize = 16.0,
    super.maxFontSize = 28.0,
    super.adaptToDisplayMode = true,
    super.adaptToScreenSize = true,
    super.adaptToOrientation = true,
  });
}

/// A responsive text widget specifically for subtitles
class ResponsiveSubtitle extends ResponsiveText {
  const ResponsiveSubtitle(
    super.text, {
    super.key,
    super.style,
    super.textAlign,
    super.maxLines = 3,
    super.minFontSize = 12.0,
    super.maxFontSize = 18.0,
    super.adaptToDisplayMode = true,
    super.adaptToScreenSize = true,
    super.adaptToOrientation = true,
  });
}

/// A responsive text widget specifically for body text
class ResponsiveBody extends ResponsiveText {
  const ResponsiveBody(
    super.text, {
    super.key,
    super.style,
    super.textAlign,
    super.maxLines,
    super.minFontSize = 10.0,
    super.maxFontSize = 20.0,
    super.adaptToDisplayMode = true,
    super.adaptToScreenSize = true,
    super.adaptToOrientation = true,
  });
}

/// A responsive text widget specifically for labels
class ResponsiveLabel extends ResponsiveText {
  const ResponsiveLabel(
    super.text, {
    super.key,
    super.style,
    super.textAlign,
    super.maxLines = 1,
    super.minFontSize = 8.0,
    super.maxFontSize = 16.0,
    super.adaptToDisplayMode = true,
    super.adaptToScreenSize = true,
    super.adaptToOrientation = true,
  });
}
