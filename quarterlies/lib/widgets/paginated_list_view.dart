import 'package:flutter/material.dart';

/// A reusable widget for displaying paginated lists with virtual scrolling.
///
/// This widget efficiently handles large datasets by loading items on demand
/// as the user scrolls, reducing memory usage and improving performance.
class PaginatedListView<T> extends StatefulWidget {
  /// The total number of items in the dataset
  final int totalItems;

  /// The number of items to load per page
  final int pageSize;

  /// Function to load a page of items
  final Future<List<T>> Function(int page, int pageSize) loadPage;

  /// Builder function to create a widget for each item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// Widget to display when loading more items
  final Widget? loadingWidget;

  /// Widget to display when the list is empty
  final Widget? emptyWidget;

  /// Widget to display when an error occurs
  final Widget Function(BuildContext context, String error)? errorBuilder;

  /// Whether to show a pull-to-refresh indicator
  final bool enablePullToRefresh;

  /// Callback when pull-to-refresh is triggered
  final Future<void> Function()? onRefresh;

  /// Optional header widget to display at the top of the list
  final Widget? headerWidget;

  /// Optional footer widget to display at the bottom of the list
  final Widget? footerWidget;

  const PaginatedListView({
    required this.totalItems,
    required this.pageSize,
    required this.loadPage,
    required this.itemBuilder,
    this.loadingWidget,
    this.emptyWidget,
    this.errorBuilder,
    this.enablePullToRefresh = false,
    this.onRefresh,
    this.headerWidget,
    this.footerWidget,
    super.key,
  });

  @override
  State<PaginatedListView<T>> createState() => _PaginatedListViewState<T>();
}

class _PaginatedListViewState<T> extends State<PaginatedListView<T>> {
  final List<T> _items = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 0;
  bool _hasMoreItems = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(PaginatedListView<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.totalItems != widget.totalItems) {
      _loadInitialData();
    }
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
      _currentPage = 0;
      _items.clear();
    });

    try {
      final items = await widget.loadPage(_currentPage, widget.pageSize);
      setState(() {
        _items.addAll(items);
        _isLoading = false;
        _hasMoreItems = items.length >= widget.pageSize;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMoreItems) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final nextPage = _currentPage + 1;
      final items = await widget.loadPage(nextPage, widget.pageSize);
      
      setState(() {
        _items.addAll(items);
        _currentPage = nextPage;
        _isLoading = false;
        _hasMoreItems = items.length >= widget.pageSize;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _handleRefresh() async {
    if (widget.onRefresh != null) {
      await widget.onRefresh!();
    }
    await _loadInitialData();
    return;
  }

  @override
  Widget build(BuildContext context) {
    if (_items.isEmpty && _isLoading) {
      return Center(
        child: widget.loadingWidget ?? const CircularProgressIndicator(),
      );
    }

    if (_items.isEmpty && _hasError) {
      return widget.errorBuilder != null
          ? widget.errorBuilder!(context, _errorMessage)
          : Center(child: Text('Error: $_errorMessage'));
    }

    if (_items.isEmpty) {
      return Center(
        child: widget.emptyWidget ?? const Text('No items found'),
      );
    }

    final listView = ListView.builder(
      controller: _scrollController,
      itemCount: _items.length + (_hasMoreItems ? 1 : 0) + 
                (widget.headerWidget != null ? 1 : 0) + 
                (widget.footerWidget != null ? 1 : 0),
      itemBuilder: (context, index) {
        // Handle header widget
        if (widget.headerWidget != null && index == 0) {
          return widget.headerWidget!;
        }
        
        // Adjust index for header
        int adjustedIndex = index;
        if (widget.headerWidget != null) {
          adjustedIndex--;
        }
        
        // Handle footer widget
        if (widget.footerWidget != null && 
            adjustedIndex == _items.length + (_hasMoreItems ? 1 : 0)) {
          return widget.footerWidget!;
        }
        
        // Handle loading indicator at the end
        if (_hasMoreItems && adjustedIndex == _items.length) {
          return Center(
            child: widget.loadingWidget ?? const CircularProgressIndicator(),
          );
        }
        
        // Handle regular items
        if (adjustedIndex < _items.length) {
          return widget.itemBuilder(context, _items[adjustedIndex], adjustedIndex);
        }
        
        return const SizedBox.shrink();
      },
    );

    if (widget.enablePullToRefresh) {
      return RefreshIndicator(
        onRefresh: _handleRefresh,
        child: listView,
      );
    }

    return listView;
  }
}
