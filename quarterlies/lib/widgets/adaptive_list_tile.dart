import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/field_friendly_theme.dart';

/// A list tile that adapts its layout based on the current display mode
///
/// In Field Mode: Uses larger touch targets and more spacing
/// In Office Mode: Uses more compact layout and can show additional details
class AdaptiveListTile extends StatelessWidget {
  final Widget title;
  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsets? contentPadding;
  final bool dense;
  final Widget? additionalInfo; // Extra info shown only in Office Mode
  final List<Widget>? officeActions; // Additional actions shown in Office Mode

  const AdaptiveListTile({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.contentPadding,
    this.dense = false,
    this.additionalInfo,
    this.officeActions,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final displayMode = displayProvider.displayMode;
        final isOfficeMode = displayProvider.isOfficeMode;

        return Card(
          elevation: isOfficeMode ? 2 : 3,
          margin: FieldFriendlyTheme.getCardMargin(displayMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(12.0),
            child: Padding(
              padding:
                  contentPadding ??
                  FieldFriendlyTheme.getListTilePadding(displayMode),
              child: isOfficeMode ? _buildOfficeLayout() : _buildFieldLayout(),
            ),
          ),
        );
      },
    );
  }

  /// Build layout for Field Mode (existing design)
  Widget _buildFieldLayout() {
    return Row(
      children: [
        if (leading != null) ...[leading!, const SizedBox(width: 16)],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              title,
              if (subtitle != null) ...[const SizedBox(height: 4), subtitle!],
            ],
          ),
        ),
        if (trailing != null) ...[const SizedBox(width: 16), trailing!],
      ],
    );
  }

  /// Build layout for Office Mode (more compact with additional info)
  Widget _buildOfficeLayout() {
    return Column(
      children: [
        // Main row (similar to field mode but more compact)
        Row(
          children: [
            if (leading != null) ...[leading!, const SizedBox(width: 12)],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title,
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    subtitle!,
                  ],
                ],
              ),
            ),
            if (trailing != null) ...[const SizedBox(width: 12), trailing!],
          ],
        ),

        // Additional info row (only in Office Mode)
        if (additionalInfo != null) ...[
          const SizedBox(height: 8),
          additionalInfo!,
        ],

        // Office actions (only in Office Mode)
        if (officeActions != null && officeActions!.isNotEmpty) ...[
          const SizedBox(height: 8),
          const Divider(height: 1),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: officeActions!,
          ),
        ],
      ],
    );
  }
}

/// A helper widget for showing additional information in Office Mode
class OfficeAdditionalInfo extends StatelessWidget {
  final List<InfoItem> items;

  const OfficeAdditionalInfo({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 16,
      runSpacing: 4,
      children: items.map((item) => _buildInfoChip(context, item)).toList(),
    );
  }

  Widget _buildInfoChip(BuildContext context, InfoItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (item.icon != null) ...[
            Icon(
              item.icon,
              size: 14,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            '${item.label}: ${item.value}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for additional information items
class InfoItem {
  final String label;
  final String value;
  final IconData? icon;

  const InfoItem({required this.label, required this.value, this.icon});
}

/// A helper widget for Office Mode action buttons
class OfficeActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? color;

  const OfficeActionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 18,
              color: color ?? Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color ?? Theme.of(context).colorScheme.primary,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
