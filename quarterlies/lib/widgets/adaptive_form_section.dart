import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';

/// A form section that adapts its layout based on display mode
/// Office Mode: Compact spacing, smaller fonts, grid layouts where appropriate
/// Field Mode: Generous spacing, larger fonts, single column layout
class AdaptiveFormSection extends StatelessWidget {
  final String title;
  final IconData? icon;
  final List<Widget> children;
  final bool showDivider;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final bool useGridLayout; // For Office Mode: arrange children in 2-column grid
  final int gridColumns; // Number of columns for grid layout in Office Mode

  const AdaptiveFormSection({
    super.key,
    required this.title,
    required this.children,
    this.icon,
    this.showDivider = true,
    this.backgroundColor,
    this.padding,
    this.useGridLayout = false,
    this.gridColumns = 2,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final isOfficeMode = displayProvider.isOfficeMode;
        
        return Container(
          margin: EdgeInsets.only(bottom: isOfficeMode ? 12 : 16),
          padding: padding ?? EdgeInsets.all(isOfficeMode ? 12 : 16),
          decoration: BoxDecoration(
            color: backgroundColor ?? (isOfficeMode ? Colors.grey[50] : Colors.white),
            borderRadius: BorderRadius.circular(isOfficeMode ? 6 : 8),
            border: Border.all(
              color: isOfficeMode ? Colors.grey[300]! : Colors.grey[200]!,
              width: isOfficeMode ? 0.5 : 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section header
              Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      size: isOfficeMode ? 18 : 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(width: isOfficeMode ? 6 : 8),
                  ],
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: isOfficeMode ? 14 : 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              
              if (showDivider) ...[
                SizedBox(height: isOfficeMode ? 6 : 8),
                Divider(
                  height: 1,
                  thickness: isOfficeMode ? 0.5 : 1,
                  color: Colors.grey[300],
                ),
              ],
              
              SizedBox(height: isOfficeMode ? 8 : 12),
              
              // Section content
              if (isOfficeMode && useGridLayout && children.length > 1)
                _buildGridLayout(children, gridColumns, isOfficeMode)
              else
                _buildColumnLayout(children, isOfficeMode),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGridLayout(List<Widget> children, int columns, bool isOfficeMode) {
    final List<Widget> rows = [];
    
    for (int i = 0; i < children.length; i += columns) {
      final rowChildren = <Widget>[];
      
      for (int j = 0; j < columns && i + j < children.length; j++) {
        rowChildren.add(
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                right: j < columns - 1 ? (isOfficeMode ? 8 : 12) : 0,
              ),
              child: children[i + j],
            ),
          ),
        );
      }
      
      // Fill remaining columns if needed
      while (rowChildren.length < columns) {
        rowChildren.add(const Expanded(child: SizedBox()));
      }
      
      rows.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowChildren,
        ),
      );
      
      if (i + columns < children.length) {
        rows.add(SizedBox(height: isOfficeMode ? 8 : 12));
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: rows,
    );
  }

  Widget _buildColumnLayout(List<Widget> children, bool isOfficeMode) {
    final List<Widget> spacedChildren = [];
    
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(height: isOfficeMode ? 8 : 12));
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: spacedChildren,
    );
  }
}

/// A specialized form field wrapper that adapts to display mode
class AdaptiveFormField extends StatelessWidget {
  final Widget child;
  final String? label;
  final bool required;
  final String? helpText;

  const AdaptiveFormField({
    super.key,
    required this.child,
    this.label,
    this.required = false,
    this.helpText,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        final isOfficeMode = displayProvider.isOfficeMode;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (label != null) ...[
              Row(
                children: [
                  Text(
                    label!,
                    style: TextStyle(
                      fontSize: isOfficeMode ? 12 : 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  if (required) ...[
                    const SizedBox(width: 4),
                    Text(
                      '*',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: isOfficeMode ? 12 : 14,
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: isOfficeMode ? 4 : 6),
            ],
            this.child,
            if (helpText != null) ...[
              SizedBox(height: isOfficeMode ? 2 : 4),
              Text(
                helpText!,
                style: TextStyle(
                  fontSize: isOfficeMode ? 10 : 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// A responsive row that stacks children vertically in Field Mode
/// and horizontally in Office Mode
class AdaptiveFormRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  const AdaptiveFormRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        if (displayProvider.isOfficeMode && children.length <= 3) {
          // Office Mode: Horizontal layout for up to 3 children
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children.map((child) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: child,
                ),
              );
            }).toList(),
          );
        } else {
          // Field Mode or too many children: Vertical layout
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children.map((child) {
              return Padding(
                padding: EdgeInsets.only(bottom: displayProvider.isOfficeMode ? 8 : 12),
                child: child,
              );
            }).toList(),
          );
        }
      },
    );
  }
}
