import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/services/connectivity_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

/// A subtle banner that displays the current connectivity status.
///
/// This widget shows a non-intrusive banner at the top of the screen when
/// the app is offline. It automatically updates when connectivity changes.
class OfflineStatusBanner extends StatefulWidget {
  final Widget child;
  final bool showToastOnChange;

  const OfflineStatusBanner({
    super.key,
    required this.child,
    this.showToastOnChange = true,
  });

  @override
  State<OfflineStatusBanner> createState() => _OfflineStatusBannerState();
}

class _OfflineStatusBannerState extends State<OfflineStatusBanner> {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isOffline = false;
  StreamSubscription? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
    _connectivitySubscription = _connectivityService.connectionStatus.listen((
      isConnected,
    ) {
      final wasOffline = _isOffline;
      setState(() {
        _isOffline = !isConnected;
      });

      // Show toast notification when connectivity status changes
      if (widget.showToastOnChange && mounted && wasOffline != _isOffline) {
        if (_isOffline) {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        } else {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        }
      }
    });
  }

  Future<void> _checkConnectivity() async {
    final isConnected = await _connectivityService.checkConnection();
    if (mounted) {
      setState(() {
        _isOffline = !isConnected;
      });
    }
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Only show the banner when offline
        if (_isOffline)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            color: Colors.orange.withAlpha((255 * 0.8).round()),
            child: const Center(
              child: Text(
                'Offline Mode - Changes will sync when connection is restored',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        // Main content
        Expanded(child: widget.child),
      ],
    );
  }
}
