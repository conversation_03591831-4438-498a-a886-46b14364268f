import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing financial data and dashboard figures across the application
///
/// This provider manages financial summaries, dashboard calculations, YTD figures,
/// and provides methods for financial analytics while maintaining state consistency
/// across the app with offline support.
class FinancialProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();

  // State variables
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Financial summary data
  double _ytdIncome = 0.0;
  double _ytdJobExpenses = 0.0;
  double _ytdOverheadExpenses = 0.0;
  double _ytdTotalExpenses = 0.0;
  double _ytdNetProfit = 0.0;

  // Job data
  List<Map<String, dynamic>> _activeJobs = [];

  // Invoice and estimate data
  int _openEstimates = 0;
  int _openInvoices = 0;
  int _overdueInvoices = 0;

  // Tax data
  double _currentTaxEstimate = 0.0;
  double _taxPaymentsMade = 0.0;
  DateTime _nextTaxDueDate = DateTime.now();
  String _currentTaxPeriod = '';

  // Quarterly financial data
  Map<String, dynamic> _quarterlyFinancials = {};

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;

  // Financial summary getters
  double get ytdIncome => _ytdIncome;
  double get ytdJobExpenses => _ytdJobExpenses;
  double get ytdOverheadExpenses => _ytdOverheadExpenses;
  double get ytdTotalExpenses => _ytdTotalExpenses;
  double get ytdNetProfit => _ytdNetProfit;

  // Job data getters
  List<Map<String, dynamic>> get activeJobs => _activeJobs;

  // Invoice and estimate getters
  int get openEstimates => _openEstimates;
  int get openInvoices => _openInvoices;
  int get overdueInvoices => _overdueInvoices;

  // Tax data getters
  double get currentTaxEstimate => _currentTaxEstimate;
  double get taxPaymentsMade => _taxPaymentsMade;
  DateTime get nextTaxDueDate => _nextTaxDueDate;
  String get currentTaxPeriod => _currentTaxPeriod;

  // Quarterly financial getters
  Map<String, dynamic> get quarterlyFinancials => _quarterlyFinancials;

  FinancialProvider() {
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadDashboardData();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadDashboardData();
      }
    });
  }

  /// Load all dashboard data
  Future<void> loadDashboardData() async {
    try {
      _setLoading(true);
      _clearError();

      // Load financial summary data
      await _loadFinancialSummary();

      // Load job data
      await _loadActiveJobsData();

      // Load invoice and estimate data
      await _loadInvoiceEstimateData();

      // Load tax data
      await _loadTaxData();

      // Load quarterly financials
      await _loadQuarterlyFinancials();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load dashboard data: ${e.toString()}');
    }
  }

  /// Load financial summary data
  Future<void> _loadFinancialSummary() async {
    try {
      final currentYear = DateTime.now().year;
      final startOfYear = DateTime(currentYear, 1, 1);

      // Get YTD income from invoices
      final invoices = await _dataRepository.getInvoicesSince(startOfYear);
      _ytdIncome = invoices
          .where((invoice) => invoice.status.toLowerCase() == 'paid')
          .fold(0.0, (sum, invoice) => sum + invoice.totalAmount);

      // Get YTD expenses
      final expenses = await _dataRepository.getExpensesSince(startOfYear);

      // Separate job expenses and overhead expenses
      _ytdJobExpenses = expenses
          .where((expense) => expense.jobId != null)
          .fold(0.0, (sum, expense) => sum + expense.amount);

      _ytdOverheadExpenses = expenses
          .where((expense) => expense.jobId == null)
          .fold(0.0, (sum, expense) => sum + expense.amount);

      _ytdTotalExpenses = _ytdJobExpenses + _ytdOverheadExpenses;
      _ytdNetProfit = _ytdIncome - _ytdTotalExpenses;
    } catch (e) {
      debugPrint('Failed to load financial summary: ${e.toString()}');
    }
  }

  /// Load active jobs data
  Future<void> _loadActiveJobsData() async {
    try {
      final activeJobs = await _dataRepository.getActiveJobs();

      _activeJobs =
          activeJobs.map((job) {
            return {
              'id': job.id,
              'title': job.title,
              'status': job.status,
              'estimatedPrice': job.estimatedPrice ?? 0.0,
              'actualIncome': job.actualIncome ?? 0.0,
              'actualExpenses': job.actualExpenses ?? 0.0,
              'profit': (job.actualIncome ?? 0.0) - (job.actualExpenses ?? 0.0),
            };
          }).toList();
    } catch (e) {
      debugPrint('Failed to load active jobs: ${e.toString()}');
    }
  }

  /// Load invoice and estimate data
  Future<void> _loadInvoiceEstimateData() async {
    try {
      // Get open estimates
      final estimates = await _dataRepository.getEstimates();
      _openEstimates =
          estimates
              .where(
                (estimate) =>
                    estimate.status.toLowerCase() != 'approved' &&
                    estimate.status.toLowerCase() != 'rejected',
              )
              .length;

      // Get invoice data
      final invoices = await _dataRepository.getInvoices();
      _openInvoices =
          invoices
              .where((invoice) => invoice.status.toLowerCase() != 'paid')
              .length;

      // Get overdue invoices
      final now = DateTime.now();
      _overdueInvoices =
          invoices
              .where(
                (invoice) =>
                    invoice.status.toLowerCase() != 'paid' &&
                    invoice.dueDate.isBefore(now),
              )
              .length;
    } catch (e) {
      debugPrint('Failed to load invoice/estimate data: ${e.toString()}');
    }
  }

  /// Load tax data
  Future<void> _loadTaxData() async {
    try {
      // Calculate current tax estimate (simplified - 25% of net profit)
      _currentTaxEstimate = _ytdNetProfit * 0.25;

      // Get tax payments made this year
      final currentYear = DateTime.now().year;
      final startOfYear = DateTime(currentYear, 1, 1);
      final taxPayments = await _dataRepository.getTaxPaymentsSince(
        startOfYear,
      );

      _taxPaymentsMade = taxPayments.fold(
        0.0,
        (sum, payment) => sum + payment.amount,
      );

      // Calculate next tax due date (simplified - quarterly)
      final now = DateTime.now();
      final currentQuarter = ((now.month - 1) ~/ 3) + 1;
      final nextQuarter = currentQuarter == 4 ? 1 : currentQuarter + 1;
      final nextYear = currentQuarter == 4 ? now.year + 1 : now.year;

      // Due dates: Q1 (Apr 15), Q2 (Jun 15), Q3 (Sep 15), Q4 (Jan 15)
      final dueDates = [
        DateTime(nextYear, 1, 15), // Q4 previous year
        DateTime(now.year, 4, 15), // Q1
        DateTime(now.year, 6, 15), // Q2
        DateTime(now.year, 9, 15), // Q3
      ];

      _nextTaxDueDate = dueDates[nextQuarter - 1];
      _currentTaxPeriod = 'Q$currentQuarter $currentYear';
    } catch (e) {
      debugPrint('Failed to load tax data: ${e.toString()}');
    }
  }

  /// Load quarterly financial data
  Future<void> _loadQuarterlyFinancials() async {
    try {
      final now = DateTime.now();
      final currentQuarter = ((now.month - 1) ~/ 3) + 1;
      _quarterlyFinancials = await _dataRepository.getQuarterlyFinancials(
        now.year,
        currentQuarter,
      );
    } catch (e) {
      debugPrint('Failed to load quarterly financials: ${e.toString()}');
      _quarterlyFinancials = {
        'total_income': 0.0,
        'total_expenses': 0.0,
        'net_profit': 0.0,
        'estimated_tax': 0.0,
        'tax_paid': 0.0,
      };
    }
  }

  /// Refresh all dashboard data
  Future<void> refreshDashboardData() async {
    await loadDashboardData();
  }

  /// Get profit margin percentage
  double get profitMarginPercentage {
    if (_ytdIncome == 0) return 0.0;
    return (_ytdNetProfit / _ytdIncome) * 100;
  }

  /// Get tax payment progress percentage
  double get taxPaymentProgress {
    if (_currentTaxEstimate == 0) return 0.0;
    return (_taxPaymentsMade / _currentTaxEstimate).clamp(0.0, 1.0);
  }

  /// Get remaining tax owed
  double get remainingTaxOwed {
    return (_currentTaxEstimate - _taxPaymentsMade).clamp(0.0, double.infinity);
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
