import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing invoice data across the application
///
/// This provider manages invoice lists, individual invoice details,
/// and provides methods for CRUD operations while maintaining state consistency
/// across the app with offline support.
class InvoiceProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();

  // State variables
  List<Invoice> _invoices = [];
  Invoice? _selectedInvoice;
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Filter state
  String? _statusFilter;
  List<Invoice> _filteredInvoices = [];

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  List<Invoice> get invoices => _invoices;
  List<Invoice> get filteredInvoices =>
      _statusFilter == null ? _invoices : _filteredInvoices;
  Invoice? get selectedInvoice => _selectedInvoice;
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;
  String? get statusFilter => _statusFilter;

  InvoiceProvider() {
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadInvoices();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadInvoices();
      }
    });
  }

  /// Load invoices
  Future<void> loadInvoices() async {
    try {
      _setLoading(true);
      _clearError();

      _invoices = await _dataRepository.getInvoices();

      // Apply current filter if active
      if (_statusFilter != null) {
        _applyStatusFilter(_statusFilter!);
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load invoices: ${e.toString()}');
    }
  }

  /// Refresh invoice list
  Future<void> refreshInvoices() async {
    await loadInvoices();
  }

  /// Filter invoices by status
  Future<void> filterByStatus(String? status) async {
    _statusFilter = status;

    if (status == null) {
      _filteredInvoices.clear();
    } else {
      _applyStatusFilter(status);
    }

    notifyListeners();
  }

  /// Apply status filter to invoices
  void _applyStatusFilter(String status) {
    _filteredInvoices =
        _invoices.where((invoice) {
          return invoice.status.toLowerCase() == status.toLowerCase();
        }).toList();
  }

  /// Get invoice by ID
  Future<Invoice?> getInvoiceById(String id) async {
    try {
      _clearError();

      // Check if invoice is already in memory
      final existingInvoice = _invoices.firstWhere(
        (invoice) => invoice.id == id,
        orElse:
            () => Invoice(
              id: '',
              userId: '',
              customerId: '',
              jobId: '',
              issueDate: DateTime.now(),
              dueDate: DateTime.now(),
              totalAmount: 0.0,
              status: '',
              lineItems: [],
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      if (existingInvoice.id!.isNotEmpty) {
        _selectedInvoice = existingInvoice;
        notifyListeners();
        return existingInvoice;
      }

      // Load from repository
      final invoice = await _dataRepository.getInvoiceById(id);
      if (invoice != null) {
        _selectedInvoice = invoice;
        notifyListeners();
      }

      return invoice;
    } catch (e) {
      _setError('Failed to load invoice: ${e.toString()}');
      return null;
    }
  }

  /// Add new invoice
  Future<bool> addInvoice(Invoice invoice) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.addInvoice(invoice);

      // Add to local list
      _invoices.insert(0, invoice);

      // Update filtered list if filter is active
      if (_statusFilter != null) {
        _applyStatusFilter(_statusFilter!);
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add invoice: ${e.toString()}');
      return false;
    }
  }

  /// Update existing invoice
  Future<bool> updateInvoice(Invoice invoice) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.updateInvoice(invoice);

      // Update in local list
      final index = _invoices.indexWhere((i) => i.id == invoice.id);
      if (index != -1) {
        _invoices[index] = invoice;
      }

      // Update in filtered list if filter is active
      if (_statusFilter != null) {
        _applyStatusFilter(_statusFilter!);
      }

      // Update selected invoice if it's the same
      if (_selectedInvoice?.id == invoice.id) {
        _selectedInvoice = invoice;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update invoice: ${e.toString()}');
      return false;
    }
  }

  /// Delete invoice
  Future<bool> deleteInvoice(String invoiceId) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.deleteInvoice(invoiceId);

      // Remove from local list
      _invoices.removeWhere((i) => i.id == invoiceId);

      // Update filtered list if filter is active
      if (_statusFilter != null) {
        _applyStatusFilter(_statusFilter!);
      }

      // Clear selected invoice if it was deleted
      if (_selectedInvoice?.id == invoiceId) {
        _selectedInvoice = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete invoice: ${e.toString()}');
      return false;
    }
  }

  /// Get invoices by job ID
  Future<List<Invoice>> getInvoicesByJob(String jobId) async {
    try {
      return await _dataRepository.getInvoicesByJob(jobId);
    } catch (e) {
      debugPrint('Failed to get invoices by job: ${e.toString()}');
      return [];
    }
  }

  /// Get invoices since a specific date
  Future<List<Invoice>> getInvoicesSince(DateTime date) async {
    try {
      return await _dataRepository.getInvoicesSince(date);
    } catch (e) {
      debugPrint('Failed to get invoices since date: ${e.toString()}');
      return [];
    }
  }

  /// Get overdue invoices
  List<Invoice> get overdueInvoices {
    final now = DateTime.now();
    return _invoices
        .where(
          (invoice) =>
              invoice.status.toLowerCase() != 'paid' &&
              invoice.dueDate.isBefore(now),
        )
        .toList();
  }

  /// Get open invoices (not paid)
  List<Invoice> get openInvoices {
    return _invoices
        .where((invoice) => invoice.status.toLowerCase() != 'paid')
        .toList();
  }

  /// Get paid invoices
  List<Invoice> get paidInvoices {
    return _invoices
        .where((invoice) => invoice.status.toLowerCase() == 'paid')
        .toList();
  }

  /// Get total amount for invoices with specific status
  double getTotalAmountByStatus(String status) {
    return _invoices
        .where(
          (invoice) => invoice.status.toLowerCase() == status.toLowerCase(),
        )
        .fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
  }

  /// Get total outstanding amount (unpaid invoices)
  double get totalOutstandingAmount {
    return openInvoices.fold(0.0, (sum, invoice) => sum + invoice.totalAmount);
  }

  /// Get total overdue amount
  double get totalOverdueAmount {
    return overdueInvoices.fold(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
  }

  /// Clear selected invoice
  void clearSelectedInvoice() {
    _selectedInvoice = null;
    notifyListeners();
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
