import 'package:flutter/foundation.dart';

/// Centralized provider for managing loading states across the application
/// Provides offline-first aware loading state management
class LoadingStateProvider extends ChangeNotifier {
  // Global loading states
  final Map<String, bool> _loadingStates = {};
  final Map<String, String?> _loadingMessages = {};
  final Map<String, double?> _loadingProgress = {};
  final Map<String, String?> _loadingErrors = {};

  // Sync-specific loading states
  bool _isSyncing = false;
  String? _syncOperation;
  double? _syncProgress;
  String? _syncError;

  // OCR-specific loading states
  bool _isProcessingOcr = false;
  String? _ocrStage;
  double? _ocrProgress;

  // PDF generation loading states
  bool _isGeneratingPdf = false;
  String? _pdfDocumentType;
  double? _pdfProgress;

  // Voice operation loading states
  bool _isProcessingVoice = false;
  bool _isRecording = false;
  String? _voiceOperation;

  // Getters for global loading states
  bool isLoading(String key) => _loadingStates[key] ?? false;
  String? getLoadingMessage(String key) => _loadingMessages[key];
  double? getLoadingProgress(String key) => _loadingProgress[key];
  String? getLoadingError(String key) => _loadingErrors[key];
  bool get hasAnyLoading => _loadingStates.values.any((loading) => loading);

  // Getters for sync states
  bool get isSyncing => _isSyncing;
  String? get syncOperation => _syncOperation;
  double? get syncProgress => _syncProgress;
  String? get syncError => _syncError;

  // Getters for OCR states
  bool get isProcessingOcr => _isProcessingOcr;
  String? get ocrStage => _ocrStage;
  double? get ocrProgress => _ocrProgress;

  // Getters for PDF states
  bool get isGeneratingPdf => _isGeneratingPdf;
  String? get pdfDocumentType => _pdfDocumentType;
  double? get pdfProgress => _pdfProgress;

  // Getters for voice states
  bool get isProcessingVoice => _isProcessingVoice;
  bool get isRecording => _isRecording;
  String? get voiceOperation => _voiceOperation;

  /// Set loading state for a specific operation
  void setLoading(String key, bool loading, {String? message, double? progress}) {
    _loadingStates[key] = loading;
    _loadingMessages[key] = message;
    _loadingProgress[key] = progress;
    
    if (!loading) {
      _loadingErrors.remove(key);
    }
    
    notifyListeners();
  }

  /// Set loading error for a specific operation
  void setLoadingError(String key, String error) {
    _loadingStates[key] = false;
    _loadingErrors[key] = error;
    notifyListeners();
  }

  /// Clear loading state for a specific operation
  void clearLoading(String key) {
    _loadingStates.remove(key);
    _loadingMessages.remove(key);
    _loadingProgress.remove(key);
    _loadingErrors.remove(key);
    notifyListeners();
  }

  /// Set sync loading state
  void setSyncLoading(bool loading, {String? operation, double? progress}) {
    _isSyncing = loading;
    _syncOperation = operation;
    _syncProgress = progress;
    
    if (!loading) {
      _syncError = null;
    }
    
    notifyListeners();
  }

  /// Set sync error
  void setSyncError(String error) {
    _isSyncing = false;
    _syncError = error;
    notifyListeners();
  }

  /// Set OCR loading state
  void setOcrLoading(bool loading, {String? stage, double? progress}) {
    _isProcessingOcr = loading;
    _ocrStage = stage;
    _ocrProgress = progress;
    notifyListeners();
  }

  /// Set PDF generation loading state
  void setPdfLoading(bool loading, {String? documentType, double? progress}) {
    _isGeneratingPdf = loading;
    _pdfDocumentType = documentType;
    _pdfProgress = progress;
    notifyListeners();
  }

  /// Set voice operation loading state
  void setVoiceLoading(bool loading, {String? operation, bool isRecording = false}) {
    _isProcessingVoice = loading;
    _isRecording = isRecording;
    _voiceOperation = operation;
    notifyListeners();
  }

  /// Clear all loading states
  void clearAllLoading() {
    _loadingStates.clear();
    _loadingMessages.clear();
    _loadingProgress.clear();
    _loadingErrors.clear();
    
    _isSyncing = false;
    _syncOperation = null;
    _syncProgress = null;
    _syncError = null;
    
    _isProcessingOcr = false;
    _ocrStage = null;
    _ocrProgress = null;
    
    _isGeneratingPdf = false;
    _pdfDocumentType = null;
    _pdfProgress = null;
    
    _isProcessingVoice = false;
    _isRecording = false;
    _voiceOperation = null;
    
    notifyListeners();
  }

  /// Helper method to execute an operation with loading state
  Future<T> executeWithLoading<T>(
    String key,
    Future<T> Function() operation, {
    String? message,
    String? errorMessage,
  }) async {
    try {
      setLoading(key, true, message: message);
      final result = await operation();
      setLoading(key, false);
      return result;
    } catch (e) {
      setLoadingError(key, errorMessage ?? e.toString());
      rethrow;
    }
  }

  /// Helper method to execute sync operation with loading state
  Future<T> executeWithSyncLoading<T>(
    Future<T> Function() operation, {
    String? operationName,
    String? errorMessage,
  }) async {
    try {
      setSyncLoading(true, operation: operationName);
      final result = await operation();
      setSyncLoading(false);
      return result;
    } catch (e) {
      setSyncError(errorMessage ?? e.toString());
      rethrow;
    }
  }

  /// Helper method to execute OCR operation with loading state
  Future<T> executeWithOcrLoading<T>(
    Future<T> Function() operation, {
    String? stage,
  }) async {
    try {
      setOcrLoading(true, stage: stage);
      final result = await operation();
      setOcrLoading(false);
      return result;
    } catch (e) {
      setOcrLoading(false);
      rethrow;
    }
  }

  /// Helper method to execute PDF generation with loading state
  Future<T> executeWithPdfLoading<T>(
    Future<T> Function() operation, {
    required String documentType,
  }) async {
    try {
      setPdfLoading(true, documentType: documentType);
      final result = await operation();
      setPdfLoading(false);
      return result;
    } catch (e) {
      setPdfLoading(false);
      rethrow;
    }
  }

  /// Helper method to execute voice operation with loading state
  Future<T> executeWithVoiceLoading<T>(
    Future<T> Function() operation, {
    required String operationName,
    bool isRecording = false,
  }) async {
    try {
      setVoiceLoading(true, operation: operationName, isRecording: isRecording);
      final result = await operation();
      setVoiceLoading(false);
      return result;
    } catch (e) {
      setVoiceLoading(false);
      rethrow;
    }
  }
}
