import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing customer data across the application
///
/// This provider manages customer lists, individual customer details,
/// and provides methods for CRUD operations while maintaining state consistency
/// across the app with offline support.
class CustomerProvider extends ChangeNotifier {
  final DataRepository _dataRepository;

  // State variables
  List<Customer> _customers = [];
  Customer? _selectedCustomer;
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Pagination state
  int _currentPage = 0;
  final int _pageSize = 20;
  int _totalCustomers = 0;
  bool _hasMoreData = true;

  // Search state
  String _searchQuery = '';
  List<Customer> _filteredCustomers = [];

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  List<Customer> get customers => _customers;
  List<Customer> get filteredCustomers =>
      _searchQuery.isEmpty ? _customers : _filteredCustomers;
  Customer? get selectedCustomer => _selectedCustomer;
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;
  int get totalCustomers => _totalCustomers;
  bool get hasMoreData => _hasMoreData;
  String get searchQuery => _searchQuery;
  int get currentPage => _currentPage;
  int get pageSize => _pageSize;

  CustomerProvider({LoadingStateProvider? loadingStateProvider})
    : _dataRepository = DataRepository(
        loadingStateProvider: loadingStateProvider,
      ) {
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadCustomers();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadCustomers();
      }
    });
  }

  /// Load customers with pagination support
  Future<void> loadCustomers({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _customers.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    try {
      _setLoading(true);
      _clearError();

      final offset = _currentPage * _pageSize;
      final newCustomers = await _dataRepository.getCustomersPaginated(
        offset,
        _pageSize,
      );

      if (refresh) {
        _customers = newCustomers;
      } else {
        _customers.addAll(newCustomers);
      }

      _hasMoreData = newCustomers.length == _pageSize;
      _currentPage++;

      // Update total count
      _totalCustomers = await _dataRepository.getCustomersCount();

      // Apply current search if active
      if (_searchQuery.isNotEmpty) {
        await _performSearch(_searchQuery);
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load customers: ${e.toString()}');
    }
  }

  /// Load more customers (for pagination)
  Future<void> loadMoreCustomers() async {
    await loadCustomers();
  }

  /// Refresh customer list
  Future<void> refreshCustomers() async {
    await loadCustomers(refresh: true);
  }

  /// Search customers
  Future<void> searchCustomers(String query) async {
    _searchQuery = query;

    if (query.isEmpty) {
      _filteredCustomers.clear();
      notifyListeners();
      return;
    }

    await _performSearch(query);
  }

  /// Perform search operation
  Future<void> _performSearch(String query) async {
    try {
      _setLoading(true);
      _clearError();

      final searchResults = await _dataRepository.search<Customer>(
        keyword: query,
      );
      _filteredCustomers = searchResults;

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search customers: ${e.toString()}');
    }
  }

  /// Get customer by ID
  Future<Customer?> getCustomerById(String id) async {
    try {
      _clearError();

      // Check if customer is already in memory
      final existingCustomer = _customers.firstWhere(
        (customer) => customer.id == id,
        orElse:
            () => Customer(
              id: '',
              userId: '',
              name: '',
              email: '',
              phone: '',
              address: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      if (existingCustomer.id.isNotEmpty) {
        _selectedCustomer = existingCustomer;
        notifyListeners();
        return existingCustomer;
      }

      // Load from repository
      final customer = await _dataRepository.getCustomerById(id);
      if (customer != null) {
        _selectedCustomer = customer;
        notifyListeners();
      }

      return customer;
    } catch (e) {
      _setError('Failed to load customer: ${e.toString()}');
      return null;
    }
  }

  /// Add new customer
  Future<bool> addCustomer(Customer customer) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.addCustomer(customer);

      // Add to local list if not searching
      if (_searchQuery.isEmpty) {
        _customers.insert(0, customer);
        _totalCustomers++;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add customer: ${e.toString()}');
      return false;
    }
  }

  /// Update existing customer
  Future<bool> updateCustomer(Customer customer) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.updateCustomer(customer);

      // Update in local list
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = customer;
      }

      // Update in filtered list if searching
      if (_searchQuery.isNotEmpty) {
        final filteredIndex = _filteredCustomers.indexWhere(
          (c) => c.id == customer.id,
        );
        if (filteredIndex != -1) {
          _filteredCustomers[filteredIndex] = customer;
        }
      }

      // Update selected customer if it's the same
      if (_selectedCustomer?.id == customer.id) {
        _selectedCustomer = customer;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update customer: ${e.toString()}');
      return false;
    }
  }

  /// Delete customer
  Future<bool> deleteCustomer(String customerId) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.deleteCustomer(customerId);

      // Remove from local list
      _customers.removeWhere((c) => c.id == customerId);
      _filteredCustomers.removeWhere((c) => c.id == customerId);
      _totalCustomers--;

      // Clear selected customer if it was deleted
      if (_selectedCustomer?.id == customerId) {
        _selectedCustomer = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete customer: ${e.toString()}');
      return false;
    }
  }

  /// Clear selected customer
  void clearSelectedCustomer() {
    _selectedCustomer = null;
    notifyListeners();
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
