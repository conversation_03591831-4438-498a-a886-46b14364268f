import 'package:flutter/material.dart';
import 'package:quarterlies/models/user_settings.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing display settings across the application
/// 
/// This provider manages the display mode (Field Mode vs Office Mode) and
/// provides methods to update the setting while maintaining state consistency
/// across the app.
class DisplaySettingsProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();
  
  DisplayMode _displayMode = DisplayMode.field;
  bool _isLoading = false;
  String? _errorMessage;

  /// Current display mode
  DisplayMode get displayMode => _displayMode;
  
  /// Whether the provider is currently loading
  bool get isLoading => _isLoading;
  
  /// Current error message, if any
  String? get errorMessage => _errorMessage;
  
  /// Whether the current mode is Field Mode (default)
  bool get isFieldMode => _displayMode == DisplayMode.field;
  
  /// Whether the current mode is Office Mode
  bool get isOfficeMode => _displayMode == DisplayMode.office;

  /// Initialize the provider by loading current user settings
  Future<void> initialize() async {
    await _loadDisplayMode();
  }

  /// Load the current display mode from user settings
  Future<void> _loadDisplayMode() async {
    try {
      _setLoading(true);
      _clearError();
      
      final userSettings = await _dataRepository.getUserSettings();
      _displayMode = userSettings.displayMode;
      
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load display settings: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Update the display mode and persist to storage
  Future<void> updateDisplayMode(DisplayMode newMode) async {
    if (_displayMode == newMode) return;

    try {
      _setLoading(true);
      _clearError();

      // Get current user settings
      final currentSettings = await _dataRepository.getUserSettings();
      
      // Update with new display mode
      final updatedSettings = currentSettings.copyWith(
        displayMode: newMode,
        updatedAt: DateTime.now(),
      );

      // Save to repository
      await _dataRepository.updateUserSettings(updatedSettings);

      // Update local state
      _displayMode = newMode;
      
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to update display mode: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Toggle between Field Mode and Office Mode
  Future<void> toggleDisplayMode() async {
    final newMode = _displayMode == DisplayMode.field 
        ? DisplayMode.office 
        : DisplayMode.field;
    await updateDisplayMode(newMode);
  }

  /// Set Field Mode
  Future<void> setFieldMode() async {
    await updateDisplayMode(DisplayMode.field);
  }

  /// Set Office Mode
  Future<void> setOfficeMode() async {
    await updateDisplayMode(DisplayMode.office);
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }

  /// Get display mode description for UI
  String getDisplayModeDescription(DisplayMode mode) {
    switch (mode) {
      case DisplayMode.field:
        return 'Large touch targets, increased spacing, optimized for outdoor use';
      case DisplayMode.office:
        return 'Compact layout, more details visible, optimized for office use';
    }
  }

  /// Get current display mode description
  String get currentModeDescription => getDisplayModeDescription(_displayMode);
}
