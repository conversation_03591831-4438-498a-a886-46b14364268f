import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing job data across the application
///
/// This provider manages job lists, individual job details, related data
/// (estimates, expenses, time logs, invoices, payments), and provides methods
/// for CRUD operations while maintaining state consistency across the app.
class JobProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();

  // State variables
  List<Job> _jobs = [];
  Job? _selectedJob;
  Customer? _selectedJobCustomer;
  Map<String, dynamic> _selectedJobData = {};
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Pagination state
  int _currentPage = 0;
  final int _pageSize = 20;
  int _totalJobs = 0;
  bool _hasMoreData = true;

  // Search and filter state
  String _searchQuery = '';
  String? _statusFilter;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  List<Job> get jobs => _jobs;
  List<Job> get filteredJobs => _getFilteredJobs();
  Job? get selectedJob => _selectedJob;
  Customer? get selectedJobCustomer => _selectedJobCustomer;
  Map<String, dynamic> get selectedJobData => _selectedJobData;
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;
  int get totalJobs => _totalJobs;
  bool get hasMoreData => _hasMoreData;
  String get searchQuery => _searchQuery;
  String? get statusFilter => _statusFilter;
  int get currentPage => _currentPage;
  int get pageSize => _pageSize;

  JobProvider() {
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadJobs();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadJobs();
      }
    });
  }

  /// Get filtered jobs based on search and status filter
  List<Job> _getFilteredJobs() {
    List<Job> result = _jobs;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      result =
          result
              .where(
                (job) =>
                    job.title.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    (job.description ?? '').toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
              )
              .toList();
    }

    // Apply status filter
    if (_statusFilter != null && _statusFilter!.isNotEmpty) {
      result =
          result
              .where(
                (job) =>
                    job.status.toLowerCase() == _statusFilter!.toLowerCase(),
              )
              .toList();
    }

    return result;
  }

  /// Load jobs with pagination support
  Future<void> loadJobs({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _jobs.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    try {
      _setLoading(true);
      _clearError();

      final offset = _currentPage * _pageSize;
      final newJobs = await _dataRepository.getJobsPaginated(offset, _pageSize);

      if (refresh) {
        _jobs = newJobs;
      } else {
        _jobs.addAll(newJobs);
      }

      _hasMoreData = newJobs.length == _pageSize;
      _currentPage++;

      // Update total count
      _totalJobs = await _dataRepository.getJobsCount();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load jobs: ${e.toString()}');
    }
  }

  /// Load more jobs (for pagination)
  Future<void> loadMoreJobs() async {
    await loadJobs();
  }

  /// Refresh job list
  Future<void> refreshJobs() async {
    await loadJobs(refresh: true);
  }

  /// Search jobs
  Future<void> searchJobs(String query) async {
    _searchQuery = query;
    notifyListeners();
  }

  /// Filter jobs by status
  Future<void> filterJobsByStatus(String? status) async {
    _statusFilter = status;
    notifyListeners();
  }

  /// Get job by ID with all related data
  Future<Job?> getJobById(String id) async {
    try {
      _setLoading(true);
      _clearError();

      // Load job details
      final job = await _dataRepository.getJobById(id);
      if (job == null) {
        _setError('Job not found');
        return null;
      }

      // Load customer details
      final customer = await _dataRepository.getCustomerById(job.customerId);

      // Load related data
      final estimates = await _dataRepository.getEstimatesByJobId(id);
      final expenses = await _dataRepository.getExpensesByJob(id);
      final timeLogs = await _dataRepository.getTimeLogsByJob(id);
      final invoices = await _dataRepository.getInvoicesByJob(id);
      final payments = await _dataRepository.getPaymentsByJob(id);

      // Calculate financial metrics
      final totalExpenses = expenses.fold(0.0, (sum, e) => sum + e.amount);
      final totalLaborCost = timeLogs.fold(0.0, (sum, t) => sum + t.laborCost);
      final totalActualIncome = payments.fold(
        0.0,
        (sum, p) => sum + p.amountReceived,
      );

      // Update job with calculated values
      final updatedJob = job.copyWith(
        actualExpenses: totalExpenses,
        actualLaborCost: totalLaborCost,
        actualIncome: totalActualIncome,
      );

      _selectedJob = updatedJob;
      _selectedJobCustomer = customer;
      _selectedJobData = {
        'estimates': estimates,
        'expenses': expenses,
        'timeLogs': timeLogs,
        'invoices': invoices,
        'payments': payments,
      };

      _setLoading(false);
      notifyListeners();
      return updatedJob;
    } catch (e) {
      _setError('Failed to load job: ${e.toString()}');
      return null;
    }
  }

  /// Add new job
  Future<bool> addJob(Job job) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.addJob(job);

      // Add to local list
      _jobs.insert(0, job);
      _totalJobs++;

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add job: ${e.toString()}');
      return false;
    }
  }

  /// Update existing job
  Future<bool> updateJob(Job job) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.updateJob(job);

      // Update in local list
      final index = _jobs.indexWhere((j) => j.id == job.id);
      if (index != -1) {
        _jobs[index] = job;
      }

      // Update selected job if it's the same
      if (_selectedJob?.id == job.id) {
        _selectedJob = job;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update job: ${e.toString()}');
      return false;
    }
  }

  /// Delete job
  Future<bool> deleteJob(String jobId) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.deleteJob(jobId);

      // Remove from local list
      _jobs.removeWhere((j) => j.id == jobId);
      _totalJobs--;

      // Clear selected job if it was deleted
      if (_selectedJob?.id == jobId) {
        _selectedJob = null;
        _selectedJobCustomer = null;
        _selectedJobData.clear();
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete job: ${e.toString()}');
      return false;
    }
  }

  /// Get active jobs
  Future<List<Job>> getActiveJobs() async {
    try {
      final activeJobs = await _dataRepository.getActiveJobs();
      return activeJobs;
    } catch (e) {
      debugPrint('Failed to get active jobs: ${e.toString()}');
      return [];
    }
  }

  /// Clear selected job
  void clearSelectedJob() {
    _selectedJob = null;
    _selectedJobCustomer = null;
    _selectedJobData.clear();
    notifyListeners();
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
