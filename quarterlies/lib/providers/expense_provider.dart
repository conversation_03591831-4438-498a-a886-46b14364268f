import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing expense data across the application
///
/// This provider manages expense lists, individual expense details,
/// and provides methods for CRUD operations while maintaining state consistency
/// across the app with offline support.
class ExpenseProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();

  // State variables
  List<Expense> _expenses = [];
  Expense? _selectedExpense;
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Filter state
  String? _categoryFilter;
  String? _jobFilter;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  List<Expense> get expenses => _expenses;
  List<Expense> get filteredExpenses => _getFilteredExpenses();
  Expense? get selectedExpense => _selectedExpense;
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;
  String? get categoryFilter => _categoryFilter;
  String? get jobFilter => _jobFilter;

  ExpenseProvider() {
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadExpenses();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadExpenses();
      }
    });
  }

  /// Get filtered expenses based on category and job filters
  List<Expense> _getFilteredExpenses() {
    List<Expense> result = _expenses;

    // Apply category filter
    if (_categoryFilter != null && _categoryFilter!.isNotEmpty) {
      result =
          result
              .where(
                (expense) =>
                    (expense.category ?? '').toLowerCase() ==
                    _categoryFilter!.toLowerCase(),
              )
              .toList();
    }

    // Apply job filter
    if (_jobFilter != null && _jobFilter!.isNotEmpty) {
      if (_jobFilter == 'overhead') {
        result = result.where((expense) => expense.jobId == null).toList();
      } else {
        result =
            result.where((expense) => expense.jobId == _jobFilter).toList();
      }
    }

    return result;
  }

  /// Load expenses
  Future<void> loadExpenses() async {
    try {
      _setLoading(true);
      _clearError();

      _expenses = await _dataRepository.getExpenses();

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load expenses: ${e.toString()}');
    }
  }

  /// Refresh expense list
  Future<void> refreshExpenses() async {
    await loadExpenses();
  }

  /// Filter expenses by category
  Future<void> filterByCategory(String? category) async {
    _categoryFilter = category;
    notifyListeners();
  }

  /// Filter expenses by job
  Future<void> filterByJob(String? jobId) async {
    _jobFilter = jobId;
    notifyListeners();
  }

  /// Clear all filters
  void clearFilters() {
    _categoryFilter = null;
    _jobFilter = null;
    notifyListeners();
  }

  /// Get expense by ID
  Future<Expense?> getExpenseById(String id) async {
    try {
      _clearError();

      // Check if expense is already in memory
      final existingExpense = _expenses.firstWhere(
        (expense) => expense.id == id,
        orElse:
            () => Expense(
              id: '',
              userId: '',
              amount: 0.0,
              category: '',
              description: '',
              date: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      if (existingExpense.id.isNotEmpty) {
        _selectedExpense = existingExpense;
        notifyListeners();
        return existingExpense;
      }

      // Load from repository
      final expense = await _dataRepository.getExpenseById(id);
      if (expense != null) {
        _selectedExpense = expense;
        notifyListeners();
      }

      return expense;
    } catch (e) {
      _setError('Failed to load expense: ${e.toString()}');
      return null;
    }
  }

  /// Add new expense
  Future<bool> addExpense(Expense expense) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.addExpense(expense);

      // Add to local list
      _expenses.insert(0, expense);

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add expense: ${e.toString()}');
      return false;
    }
  }

  /// Update existing expense
  Future<bool> updateExpense(Expense expense) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.updateExpense(expense);

      // Update in local list
      final index = _expenses.indexWhere((e) => e.id == expense.id);
      if (index != -1) {
        _expenses[index] = expense;
      }

      // Update selected expense if it's the same
      if (_selectedExpense?.id == expense.id) {
        _selectedExpense = expense;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update expense: ${e.toString()}');
      return false;
    }
  }

  /// Delete expense
  Future<bool> deleteExpense(String expenseId) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.deleteExpense(expenseId);

      // Remove from local list
      _expenses.removeWhere((e) => e.id == expenseId);

      // Clear selected expense if it was deleted
      if (_selectedExpense?.id == expenseId) {
        _selectedExpense = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete expense: ${e.toString()}');
      return false;
    }
  }

  /// Get expenses by job ID
  Future<List<Expense>> getExpensesByJob(String jobId) async {
    try {
      return await _dataRepository.getExpensesByJob(jobId);
    } catch (e) {
      debugPrint('Failed to get expenses by job: ${e.toString()}');
      return [];
    }
  }

  /// Get expenses since a specific date
  Future<List<Expense>> getExpensesSince(DateTime date) async {
    try {
      return await _dataRepository.getExpensesSince(date);
    } catch (e) {
      debugPrint('Failed to get expenses since date: ${e.toString()}');
      return [];
    }
  }

  /// Get overhead expenses (no job assigned)
  List<Expense> get overheadExpenses {
    return _expenses.where((expense) => expense.jobId == null).toList();
  }

  /// Get job-specific expenses
  List<Expense> get jobExpenses {
    return _expenses.where((expense) => expense.jobId != null).toList();
  }

  /// Get expenses by category
  Map<String, List<Expense>> get expensesByCategory {
    final Map<String, List<Expense>> categorizedExpenses = {};

    for (final expense in _expenses) {
      final category = expense.category ?? 'Uncategorized';
      if (!categorizedExpenses.containsKey(category)) {
        categorizedExpenses[category] = [];
      }
      categorizedExpenses[category]!.add(expense);
    }

    return categorizedExpenses;
  }

  /// Get total expenses by category
  Map<String, double> get totalExpensesByCategory {
    final Map<String, double> totals = {};

    for (final expense in _expenses) {
      final category = expense.category ?? 'Uncategorized';
      totals[category] = (totals[category] ?? 0.0) + expense.amount;
    }

    return totals;
  }

  /// Get total overhead expenses
  double get totalOverheadExpenses {
    return overheadExpenses.fold(0.0, (sum, expense) => sum + expense.amount);
  }

  /// Get total job expenses
  double get totalJobExpenses {
    return jobExpenses.fold(0.0, (sum, expense) => sum + expense.amount);
  }

  /// Get total expenses
  double get totalExpenses {
    return _expenses.fold(0.0, (sum, expense) => sum + expense.amount);
  }

  /// Get expenses for current year
  List<Expense> get currentYearExpenses {
    final currentYear = DateTime.now().year;
    return _expenses
        .where((expense) => expense.date.year == currentYear)
        .toList();
  }

  /// Get total expenses for current year
  double get currentYearTotalExpenses {
    return currentYearExpenses.fold(
      0.0,
      (sum, expense) => sum + expense.amount,
    );
  }

  /// Clear selected expense
  void clearSelectedExpense() {
    _selectedExpense = null;
    notifyListeners();
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
