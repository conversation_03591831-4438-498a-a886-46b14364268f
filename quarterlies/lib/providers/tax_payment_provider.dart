import 'dart:async';
import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';

/// Provider for managing tax payment data across the application
///
/// This provider manages tax payment lists, individual tax payment details,
/// and provides methods for CRUD operations while maintaining state consistency
/// across the app with offline support.
class TaxPaymentProvider extends ChangeNotifier {
  final DataRepository _dataRepository = DataRepository();

  // State variables
  List<TaxPayment> _taxPayments = [];
  TaxPayment? _selectedTaxPayment;
  bool _isLoading = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Filter state
  String? _selectedPeriodFilter;
  List<String> _periodOptions = [];
  List<TaxPayment> _filteredTaxPayments = [];

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  // Getters
  List<TaxPayment> get taxPayments => _taxPayments;
  List<TaxPayment> get filteredTaxPayments =>
      _selectedPeriodFilter == null ? _taxPayments : _filteredTaxPayments;
  TaxPayment? get selectedTaxPayment => _selectedTaxPayment;
  bool get isLoading => _isLoading;
  bool get isOffline => _isOffline;
  String? get errorMessage => _errorMessage;
  String? get selectedPeriodFilter => _selectedPeriodFilter;
  List<String> get periodOptions => _periodOptions;

  TaxPaymentProvider() {
    _setupConnectivityListener();
    _initializePeriodOptions();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadTaxPayments();
  }

  /// Initialize period options
  void _initializePeriodOptions() {
    _periodOptions = TaxPeriod.getTaxPeriodOptions();
  }

  /// Set up connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      _isOffline = !isConnected;
      notifyListeners();

      if (isConnected) {
        // When back online, refresh data
        loadTaxPayments();
      }
    });
  }

  /// Load tax payments
  Future<void> loadTaxPayments() async {
    try {
      _setLoading(true);
      _clearError();

      _taxPayments = await _dataRepository.getTaxPayments();

      // Apply current filter if active
      if (_selectedPeriodFilter != null) {
        _applyPeriodFilter(_selectedPeriodFilter!);
      }

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load tax payments: ${e.toString()}');
    }
  }

  /// Refresh tax payment list
  Future<void> refreshTaxPayments() async {
    await loadTaxPayments();
  }

  /// Filter tax payments by period
  Future<void> filterByPeriod(String? period) async {
    _selectedPeriodFilter = period;

    if (period == null) {
      _filteredTaxPayments.clear();
    } else {
      _applyPeriodFilter(period);
    }

    notifyListeners();
  }

  /// Apply period filter to tax payments
  void _applyPeriodFilter(String period) {
    _filteredTaxPayments =
        _taxPayments.where((payment) {
          return payment.taxPeriod == period;
        }).toList();
  }

  /// Get tax payment by ID
  Future<TaxPayment?> getTaxPaymentById(String id) async {
    try {
      _clearError();

      // Check if tax payment is already in memory
      final existingPayment = _taxPayments.firstWhere(
        (payment) => payment.id == id,
        orElse:
            () => TaxPayment(
              id: '',
              userId: '',
              date: DateTime.now(),
              amount: 0.0,
              taxPeriod: '',
              paymentMethod: '',
              confirmationNumber: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      if (existingPayment.id.isNotEmpty) {
        _selectedTaxPayment = existingPayment;
        notifyListeners();
        return existingPayment;
      }

      // Load from repository
      final payment = await _dataRepository.getTaxPaymentById(id);
      if (payment != null) {
        _selectedTaxPayment = payment;
        notifyListeners();
      }

      return payment;
    } catch (e) {
      _setError('Failed to load tax payment: ${e.toString()}');
      return null;
    }
  }

  /// Add new tax payment
  Future<bool> addTaxPayment(TaxPayment payment) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.addTaxPayment(payment);

      // Add to local list
      _taxPayments.insert(0, payment);

      // Update filtered list if filter is active
      if (_selectedPeriodFilter != null) {
        _applyPeriodFilter(_selectedPeriodFilter!);
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add tax payment: ${e.toString()}');
      return false;
    }
  }

  /// Update existing tax payment
  Future<bool> updateTaxPayment(TaxPayment payment) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.updateTaxPayment(payment);

      // Update in local list
      final index = _taxPayments.indexWhere((p) => p.id == payment.id);
      if (index != -1) {
        _taxPayments[index] = payment;
      }

      // Update in filtered list if filter is active
      if (_selectedPeriodFilter != null) {
        _applyPeriodFilter(_selectedPeriodFilter!);
      }

      // Update selected payment if it's the same
      if (_selectedTaxPayment?.id == payment.id) {
        _selectedTaxPayment = payment;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update tax payment: ${e.toString()}');
      return false;
    }
  }

  /// Delete tax payment
  Future<bool> deleteTaxPayment(String paymentId) async {
    try {
      _setLoading(true);
      _clearError();

      await _dataRepository.deleteTaxPayment(paymentId);

      // Remove from local list
      _taxPayments.removeWhere((p) => p.id == paymentId);

      // Update filtered list if filter is active
      if (_selectedPeriodFilter != null) {
        _applyPeriodFilter(_selectedPeriodFilter!);
      }

      // Clear selected payment if it was deleted
      if (_selectedTaxPayment?.id == paymentId) {
        _selectedTaxPayment = null;
      }

      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to delete tax payment: ${e.toString()}');
      return false;
    }
  }

  /// Get tax payments for a specific period
  List<TaxPayment> getTaxPaymentsForPeriod(String period) {
    return _taxPayments
        .where((payment) => payment.taxPeriod == period)
        .toList();
  }

  /// Get total tax payments for a specific period
  double getTotalTaxPaymentsForPeriod(String period) {
    return getTaxPaymentsForPeriod(
      period,
    ).fold(0.0, (sum, payment) => sum + payment.amount);
  }

  /// Get tax payments since a specific date
  Future<List<TaxPayment>> getTaxPaymentsSince(DateTime date) async {
    try {
      return await _dataRepository.getTaxPaymentsSince(date);
    } catch (e) {
      debugPrint('Failed to get tax payments since date: ${e.toString()}');
      return [];
    }
  }

  /// Get total tax payments for current year
  double get currentYearTotalPayments {
    final currentYear = DateTime.now().year;
    return _taxPayments
        .where((payment) => payment.date.year == currentYear)
        .fold(0.0, (sum, payment) => sum + payment.amount);
  }

  /// Get tax payments by payment method
  Map<String, double> getTaxPaymentsByMethod() {
    final Map<String, double> paymentsByMethod = {};

    for (final payment in _taxPayments) {
      final method = payment.paymentMethod ?? 'Unknown';
      paymentsByMethod[method] =
          (paymentsByMethod[method] ?? 0.0) + payment.amount;
    }

    return paymentsByMethod;
  }

  /// Clear selected tax payment
  void clearSelectedTaxPayment() {
    _selectedTaxPayment = null;
    notifyListeners();
  }

  /// Helper method to set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = null;
    }
    notifyListeners();
  }

  /// Helper method to set error state
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Helper method to clear error state
  void _clearError() {
    _errorMessage = null;
  }
}
