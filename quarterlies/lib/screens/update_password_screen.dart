import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class UpdatePasswordScreen extends StatefulWidget {
  const UpdatePasswordScreen({super.key});

  @override
  State<UpdatePasswordScreen> createState() => _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends State<UpdatePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = AuthService();
  String? _errorMessage;
  bool _passwordUpdated = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _updatePassword() async {
    if (_formKey.currentState!.validate()) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'updatePassword',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.updatePassword(_passwordController.text);

            if (mounted) {
              setState(() {
                _passwordUpdated = true;
              });

              // Show success feedback
              ErrorDisplay.showSuccess(
                context,
                'Password updated successfully',
              );
            }
          },
          message: 'Updating password...',
          errorMessage: 'Failed to update password',
        );
      } catch (e) {
        if (mounted) {
          setState(() {
            _errorMessage = e.toString();
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Update Password',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 20.0 : 24.0,
              ),
              child:
                  _passwordUpdated
                      ? _buildSuccessMessage()
                      : _buildUpdateForm(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpdateForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 32),
          Icon(
            Icons.lock_reset,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 24),
          const Text(
            'Create New Password',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'Your password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          CustomTextField(
            controller: _passwordController,
            labelText: 'New Password',
            hintText: 'Enter your new password',
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a new password';
              }
              if (!_authService.isPasswordStrong(value)) {
                return 'Password must be at least 8 characters and include uppercase, lowercase, numbers, and special characters';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomTextField(
            controller: _confirmPasswordController,
            labelText: 'Confirm Password',
            hintText: 'Confirm your new password',
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),
          if (_errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              return CustomButton(
                text: 'Update Password',
                onPressed: _updatePassword,
                isLoading: loadingProvider.isLoading('updatePassword'),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessMessage() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 32),
        Icon(Icons.check_circle_outline, size: 64, color: Colors.green),
        const SizedBox(height: 24),
        const Text(
          'Password Updated!',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        const Text(
          'Your password has been successfully updated. You can now log in with your new password.',
          style: TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        CustomButton(
          text: 'Go to Login',
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/login');
          },
        ),
      ],
    );
  }
}
