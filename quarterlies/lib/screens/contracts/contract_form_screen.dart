import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:uuid/uuid.dart';

class ContractFormScreen extends StatefulWidget {
  final Contract? contract;
  final Estimate? estimate;

  const ContractFormScreen({super.key, this.contract, this.estimate});

  @override
  State<ContractFormScreen> createState() => _ContractFormScreenState();
}

class _ContractFormScreenState extends State<ContractFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final DataRepository _dataRepository = DataRepository();
  final VoiceRecordingService _voiceRecordingService = VoiceRecordingService();

  bool _isEditing = false;
  bool _isRecording = false;
  bool _isOffline = false;
  String? _errorMessage;
  String? _selectedCustomerId;
  String? _selectedJobId;
  List<Customer> _customers = [];
  List<Job> _jobs = [];
  List<ContractItem> _lineItems = [];

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.contract != null;
    _setupConnectivityListener();
    _loadData();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(
            context,
            'Back online. Data will sync automatically.',
          );
        } else {
          ErrorDisplay.showSync(
            context,
            'Working offline. Changes saved locally.',
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadContractFormData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load customers and jobs
          final customers = await _dataRepository.getCustomers();
          final jobs = await _dataRepository.getJobs();

          if (!mounted) return;

          setState(() {
            _customers = customers;
            _jobs = jobs;
          });

          // If editing an existing contract, load its data
          if (_isEditing && widget.contract != null) {
            _selectedCustomerId = widget.contract!.customerId;
            _selectedJobId = widget.contract!.jobId;
            _lineItems = List.from(widget.contract!.lineItems);
            _notesController.text = widget.contract!.notes ?? '';
          }
          // If creating from an estimate, load estimate data
          else if (widget.estimate != null) {
            _selectedCustomerId = widget.estimate!.customerId;
            _selectedJobId = widget.estimate!.jobId;
            _lineItems =
                widget.estimate!.lineItems
                    .map((item) => ContractItem.fromEstimateItem(item))
                    .toList();
            _notesController.text = widget.estimate!.notes ?? '';
          }
        },
        message: 'Loading contract data...',
        errorMessage: 'Failed to load contract data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load contract data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _saveContract() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedJobId == null) {
      ErrorDisplay.showWarning(context, 'Please select a job');
      return;
    }
    if (_selectedCustomerId == null) {
      ErrorDisplay.showWarning(context, 'Please select a customer');
      return;
    }
    if (_lineItems.isEmpty) {
      ErrorDisplay.showWarning(context, 'Please add at least one line item');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveContract',
        () async {
          final userId = await _dataRepository.getCurrentUserId();
          final totalAmount = _calculateTotal();

          if (_isEditing) {
            // Update existing contract
            final updatedContract = widget.contract!.copyWith(
              jobId: _selectedJobId,
              customerId: _selectedCustomerId,
              lineItems: _lineItems,
              totalAmount: totalAmount,
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              updatedAt: DateTime.now(),
            );

            await _dataRepository.updateContract(updatedContract);
          } else {
            // Create new contract
            final newContract = Contract(
              id: const Uuid().v4(),
              userId: userId,
              jobId: _selectedJobId!,
              customerId: _selectedCustomerId!,
              estimateId: widget.estimate?.id,
              lineItems: _lineItems,
              totalAmount: totalAmount,
              status: 'draft',
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _dataRepository.addContract(newContract);
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'contract',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context, true);
          }
        },
        message: _isEditing ? 'Updating contract...' : 'Creating contract...',
        errorMessage: 'Failed to save contract',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to save contract: ${e.toString()}';
        });
      }
    }
  }

  double _calculateTotal() {
    return _lineItems.fold(
      0,
      (sum, item) => sum + (item.quantity * item.unitPrice),
    );
  }

  void _addLineItem() {
    showDialog(
      context: context,
      builder:
          (context) => _LineItemDialog(
            onAdd: (item) {
              setState(() {
                _lineItems.add(item);
              });
            },
          ),
    );
  }

  void _editLineItem(int index) {
    showDialog(
      context: context,
      builder:
          (context) => _LineItemDialog(
            initialItem: _lineItems[index],
            onAdd: (item) {
              setState(() {
                _lineItems[index] = item;
              });
            },
          ),
    );
  }

  void _removeLineItem(int index) {
    setState(() {
      _lineItems.removeAt(index);
    });
  }

  // Build a card showing information about the estimate being converted to a contract
  Widget _buildEstimateInfoCard() {
    if (widget.estimate == null) return const SizedBox.shrink();

    // Find the customer and job names
    final customer = _customers.firstWhere(
      (c) => c.id == widget.estimate!.customerId,
      orElse: () => Customer(id: '', name: 'Unknown Customer', userId: ''),
    );

    final job = _jobs.firstWhere(
      (j) => j.id == widget.estimate!.jobId,
      orElse:
          () => Job(id: '', title: 'Unknown Job', customerId: '', userId: ''),
    );

    return Padding(
      padding: EdgeInsets.only(
        bottom: spacing.ResponsiveSpacing.getSpacing(context, base: 16.0),
      ),
      child: Card(
        color: Colors.green.shade50,
        child: Padding(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.description,
                    color: Colors.green.shade700,
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 24,
                    ),
                  ),
                  SizedBox(
                    width: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 8,
                    ),
                  ),
                  ResponsiveSubtitle(
                    'Creating from Approved Estimate',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
              ),
              ResponsiveBody(
                'Estimate ID: ${widget.estimate!.id.substring(0, 8)}',
              ),
              ResponsiveBody('Customer: ${customer.name}'),
              ResponsiveBody('Job: ${job.title}'),
              ResponsiveBody(
                'Amount: \$${widget.estimate!.totalAmount.toStringAsFixed(2)}',
              ),
              ResponsiveBody(
                'Line Items: ${widget.estimate!.lineItems.length}',
              ),
              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
              ),
              ResponsiveLabel(
                'All line items have been imported from the estimate. You can edit, add, or remove items before saving the contract.',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Apply extracted information to form fields
        if (extractedInfo.containsKey('notes')) {
          setState(() {
            _notesController.text = extractedInfo['notes'];
          });
        }

        // If we have line item information, create a new line item
        if (extractedInfo.containsKey('description') ||
            extractedInfo.containsKey('quantity') ||
            extractedInfo.containsKey('unitPrice') ||
            extractedInfo.containsKey('price')) {
          // Extract line item details
          final description = extractedInfo['description'] ?? '';
          final quantity =
              extractedInfo.containsKey('quantity')
                  ? double.tryParse(extractedInfo['quantity']) ?? 1.0
                  : 1.0;
          final unitPrice =
              extractedInfo.containsKey('unitPrice')
                  ? double.tryParse(extractedInfo['unitPrice']) ?? 0.0
                  : extractedInfo.containsKey('price')
                  ? double.tryParse(extractedInfo['price']) ?? 0.0
                  : 0.0;
          final unit = extractedInfo['unit'] ?? '';

          if (description.isNotEmpty) {
            // Create a new line item
            final newItem = ContractItem(
              description: description,
              quantity: quantity,
              unitPrice: unitPrice,
              unit: unit.isNotEmpty ? unit : null,
            );

            setState(() {
              _lineItems.add(newItem);
            });

            if (mounted) {
              ErrorDisplay.showSuccess(
                context,
                'Added line item: $description',
              );
            }
          }
        }
      }

      // Show voice operation feedback
      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Voice recording processed successfully',
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(
          _isEditing
              ? 'Edit Contract'
              : widget.estimate != null
              ? 'Create Contract from Estimate'
              : 'Create Contract',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading =
                  loadingProvider.isLoading('loadContractFormData') ||
                  loadingProvider.isLoading('saveContract');
              return isLoading
                  ? const SizedBox.shrink()
                  : TextButton.icon(
                    icon: Icon(
                      Icons.save,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 20,
                      ),
                    ),
                    label: const ResponsiveBody(
                      'Save',
                      style: TextStyle(color: Colors.white),
                    ),
                    onPressed: _saveContract,
                  );
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadContractFormData');
          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading contract data...',
                  size: 32.0,
                ),
              )
              : _buildForm();
        },
      ),
    );
  }

  Widget _buildForm() {
    return ResponsiveLayout(
      child: Form(
        key: _formKey,
        child: Consumer<DisplaySettingsProvider>(
          builder: (context, displayProvider, child) {
            return SingleChildScrollView(
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (_errorMessage != null)
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16.0,
                        ),
                      ),
                      child: ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),

                  // Show estimate information if creating from an estimate
                  if (widget.estimate != null) _buildEstimateInfoCard(),

                  // Show contract creation instructions
                  if (!_isEditing)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Card(
                        color: Colors.blue.shade50,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.blue.shade700,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Contract Creation',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'You can edit all line items and details before saving the contract. '
                                'Once saved, the contract can be sent to the customer for electronic signature.',
                              ),
                              const SizedBox(height: 8),
                              if (widget.estimate != null)
                                const Text(
                                  'Tip: Line items from the estimate have been added automatically. '
                                  'You can edit or remove them as needed before saving.',
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: Colors.blue,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  // Customer dropdown
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Customer',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedCustomerId,
                    items:
                        _customers.map((customer) {
                          return DropdownMenuItem<String>(
                            value: customer.id,
                            child: Text(customer.name),
                          );
                        }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCustomerId = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a customer';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Job dropdown
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Job',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedJobId,
                    items:
                        _jobs.map((job) {
                          return DropdownMenuItem<String>(
                            value: job.id,
                            child: Text(job.title),
                          );
                        }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedJobId = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a job';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Line items section
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Line Items',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Row(
                                children: [
                                  if (!_isRecording)
                                    IconButton(
                                      icon: const Icon(Icons.mic),
                                      onPressed: _startRecording,
                                      tooltip: 'Add item using voice',
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.secondary,
                                    ),
                                  if (_isRecording)
                                    IconButton(
                                      icon: const Icon(Icons.stop),
                                      onPressed: _stopRecording,
                                      tooltip: 'Stop recording',
                                      color: Colors.red,
                                    ),
                                  const SizedBox(width: 8),
                                  ElevatedButton.icon(
                                    icon: const Icon(Icons.add),
                                    label: const Text('Add Item'),
                                    onPressed: _addLineItem,
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          _lineItems.isEmpty
                              ? Container(
                                padding: const EdgeInsets.all(16),
                                alignment: Alignment.center,
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.playlist_add,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No line items yet',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Add items using the buttons above',
                                      style: TextStyle(
                                        color: Colors.grey.shade500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _lineItems.length,
                                itemBuilder: (context, index) {
                                  final item = _lineItems[index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    elevation: 1,
                                    child: ListTile(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 8,
                                          ),
                                      title: Text(
                                        item.description,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(height: 4),
                                          Text(
                                            '${item.quantity} ${item.unit ?? ''} @ \$${item.unitPrice.toStringAsFixed(2)}',
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Text(
                                                'Total: \$${(item.quantity * item.unitPrice).toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.edit),
                                            onPressed:
                                                () => _editLineItem(index),
                                            tooltip: 'Edit Item',
                                            color: Colors.blue,
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.delete),
                                            onPressed:
                                                () => _removeLineItem(index),
                                            tooltip: 'Remove Item',
                                            color: Colors.red,
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                          const Divider(),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              'Total: \$${_calculateTotal().toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Notes field with voice recording button
                  Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      TextFormField(
                        controller: _notesController,
                        decoration: InputDecoration(
                          labelText: 'Notes (Optional)',
                          hintText:
                              'Enter any additional notes or tap mic to use voice',
                          border: const OutlineInputBorder(),
                          suffixIcon:
                              _isRecording
                                  ? Container(
                                    margin: const EdgeInsets.only(right: 32),
                                    child: const Icon(
                                      Icons.mic,
                                      color: Colors.red,
                                    ),
                                  )
                                  : null,
                        ),
                        maxLines: 5,
                        validator: (value) {
                          // Notes are optional, so no validation needed
                          return null;
                        },
                      ),
                      Positioned(
                        right: 8,
                        top: 8,
                        child: IconButton(
                          icon: Icon(
                            _isRecording ? Icons.stop : Icons.mic,
                            color:
                                _isRecording
                                    ? Colors.red
                                    : Theme.of(context).colorScheme.primary,
                          ),
                          onPressed:
                              _isRecording ? _stopRecording : _startRecording,
                          tooltip:
                              _isRecording
                                  ? 'Stop recording'
                                  : 'Record notes or line item details',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Save button
                  Consumer<LoadingStateProvider>(
                    builder: (context, loadingProvider, child) {
                      final isSaving = loadingProvider.isLoading(
                        'saveContract',
                      );
                      return Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: ElevatedButton(
                          onPressed: isSaving ? null : _saveContract,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child:
                              isSaving
                                  ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2.0,
                                    ),
                                  )
                                  : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.save),
                                      const SizedBox(width: 8),
                                      Text(
                                        _isEditing
                                            ? 'Update Contract'
                                            : widget.estimate != null
                                            ? 'Create Contract from Estimate'
                                            : 'Create Contract',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class _LineItemDialog extends StatefulWidget {
  final ContractItem? initialItem;
  final Function(ContractItem) onAdd;

  const _LineItemDialog({this.initialItem, required this.onAdd});

  @override
  State<_LineItemDialog> createState() => _LineItemDialogState();
}

class _LineItemDialogState extends State<_LineItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitPriceController = TextEditingController();
  final _unitController = TextEditingController();
  final _voiceRecordingService = VoiceRecordingService();

  bool _isRecording = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    if (widget.initialItem != null) {
      _descriptionController.text = widget.initialItem!.description;
      _quantityController.text = widget.initialItem!.quantity.toString();
      _unitPriceController.text = widget.initialItem!.unitPrice.toString();
      _unitController.text = widget.initialItem!.unit ?? '';
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text specifically for line item information
        final extractedInfo = _voiceRecordingService.processLineItemVoiceInput(
          transcribedText,
        );

        setState(() {
          // Apply extracted information to form fields
          if (extractedInfo.containsKey('description')) {
            _descriptionController.text = extractedInfo['description'];
          }
          if (extractedInfo.containsKey('quantity')) {
            _quantityController.text = extractedInfo['quantity'];
          }
          if (extractedInfo.containsKey('unitPrice') ||
              extractedInfo.containsKey('price')) {
            _unitPriceController.text =
                extractedInfo['unitPrice'] ?? extractedInfo['price'];
          }
          if (extractedInfo.containsKey('unit')) {
            _unitController.text = extractedInfo['unit'];
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  void _saveItem() {
    if (_formKey.currentState!.validate()) {
      final item = ContractItem(
        id: widget.initialItem?.id,
        description: _descriptionController.text,
        quantity: double.parse(_quantityController.text),
        unitPrice: double.parse(_unitPriceController.text),
        unit: _unitController.text.isNotEmpty ? _unitController.text : null,
      );
      widget.onAdd(item);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'Edit Line Item' : 'Add Line Item',
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              // Description field with voice recording button
              Stack(
                alignment: Alignment.centerRight,
                children: [
                  TextFormField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description',
                      border: const OutlineInputBorder(),
                      hintText:
                          'Enter item description or tap mic to use voice',
                      suffixIcon:
                          _isRecording
                              ? Container(
                                margin: const EdgeInsets.only(right: 32),
                                child: const Icon(Icons.mic, color: Colors.red),
                              )
                              : null,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  Positioned(
                    right: 8,
                    child: IconButton(
                      icon: Icon(
                        _isRecording ? Icons.stop : Icons.mic,
                        color:
                            _isRecording
                                ? Colors.red
                                : Theme.of(context).colorScheme.primary,
                      ),
                      onPressed:
                          _isRecording ? _stopRecording : _startRecording,
                      tooltip:
                          _isRecording
                              ? 'Stop recording'
                              : 'Record line item details',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'Quantity',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      controller: _unitController,
                      decoration: const InputDecoration(
                        labelText: 'Unit (optional)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _unitPriceController,
                decoration: const InputDecoration(
                  labelText: 'Unit Price',
                  border: OutlineInputBorder(),
                  prefixText: '\$',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Required';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Invalid number';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        if (!_isRecording)
          TextButton.icon(
            icon: const Icon(Icons.mic),
            label: const Text('Voice'),
            onPressed: _startRecording,
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ElevatedButton(onPressed: _saveItem, child: const Text('Save')),
      ],
    );
  }
}
