import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/user_settings.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

/// A settings section for managing display density and detail level
///
/// This widget provides UI for switching between Field Mode (default) and
/// Office Mode, allowing users to control the display density across the app.
class DisplaySettingsSection extends StatelessWidget {
  const DisplaySettingsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Section header
                Row(
                  children: [
                    const Icon(Icons.display_settings, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      'Display Settings',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Description
                ResponsiveBody(
                  'Choose how information is displayed throughout the app',
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 16,
                  ),
                ),

                // Display mode options
                _buildDisplayModeOption(
                  context,
                  displayProvider,
                  DisplayMode.field,
                  'Field Mode',
                  'Large touch targets, increased spacing',
                  'Optimized for outdoor use and challenging conditions',
                  Icons.agriculture,
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 12,
                  ),
                ),
                _buildDisplayModeOption(
                  context,
                  displayProvider,
                  DisplayMode.office,
                  'Office Mode',
                  'Compact layout, more details visible',
                  'Optimized for office use and desktop viewing',
                  Icons.business,
                ),

                // Loading indicator
                if (displayProvider.isLoading) ...[
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  const Center(child: CircularProgressIndicator()),
                ],

                // Error message
                if (displayProvider.errorMessage != null) ...[
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  Container(
                    padding: spacing.ResponsiveSpacing.getPadding(
                      context,
                      base: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(
                        spacing.ResponsiveSpacing.getBorderRadius(
                          context,
                          base: 8,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Theme.of(context).colorScheme.onErrorContainer,
                          size: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 20,
                          ),
                        ),
                        SizedBox(
                          width: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: 8,
                          ),
                        ),
                        Expanded(
                          child: ResponsiveBody(
                            displayProvider.errorMessage!,
                            style: TextStyle(
                              color:
                                  Theme.of(
                                    context,
                                  ).colorScheme.onErrorContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDisplayModeOption(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    DisplayMode mode,
    String title,
    String subtitle,
    String description,
    IconData icon,
  ) {
    final isSelected = displayProvider.displayMode == mode;
    final theme = Theme.of(context);

    return InkWell(
      onTap:
          displayProvider.isLoading
              ? null
              : () => displayProvider.updateDisplayMode(mode),
      borderRadius: BorderRadius.circular(
        spacing.ResponsiveSpacing.getBorderRadius(context, base: 12),
      ),
      child: Container(
        padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(
            spacing.ResponsiveSpacing.getBorderRadius(context, base: 12),
          ),
          color:
              isSelected
                  ? theme.colorScheme.primaryContainer.withValues(alpha: 0.1)
                  : null,
        ),
        child: Row(
          children: [
            // Icon
            Container(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(
                  spacing.ResponsiveSpacing.getBorderRadius(context, base: 8),
                ),
              ),
              child: Icon(
                icon,
                color:
                    isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onSurfaceVariant,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 20),
              ),
            ),
            SizedBox(
              width: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
            ),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveLabel(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? theme.colorScheme.primary : null,
                    ),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 4,
                    ),
                  ),
                  ResponsiveBody(subtitle),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 4,
                    ),
                  ),
                  ResponsiveLabel(
                    description,
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),

            // Selection indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
              ),
          ],
        ),
      ),
    );
  }
}
