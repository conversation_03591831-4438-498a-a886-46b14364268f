import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class AboutSection extends StatefulWidget {
  const AboutSection({super.key});

  @override
  State<AboutSection> createState() => _AboutSectionState();
}

class _AboutSectionState extends State<AboutSection> {
  String _version = 'Loading...';
  String _buildNumber = '';

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
      });
    } catch (e) {
      setState(() {
        _version = '1.0.0';
        _buildNumber = '1';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Card(
            margin: EdgeInsets.symmetric(
              horizontal: spacing.ResponsiveSpacing.getSpacing(
                context,
                base: 16,
              ),
              vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
            ),
            child: Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveSubtitle(
                    'About',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),

                  // App Icon and Name
                  Row(
                    children: [
                      Container(
                        width: spacing.ResponsiveSpacing.getIconSize(
                          context,
                          base: 48,
                        ),
                        height: spacing.ResponsiveSpacing.getIconSize(
                          context,
                          base: 48,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(
                              context,
                              base: 12,
                            ),
                          ),
                        ),
                        child: Icon(
                          Icons.receipt_long,
                          color: Colors.white,
                          size: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 24,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ResponsiveSubtitle(
                              'Quarterlies',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            ResponsiveLabel(
                              'Business Management App',
                              style: TextStyle(
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 24,
                    ),
                  ),

                  // Version Information
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.info_outline,
                    label: 'Version',
                    value: '$_version ($_buildNumber)',
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 12,
                    ),
                  ),

                  // Developer Information
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.code,
                    label: 'Developer',
                    value: 'Quarterlies Team',
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 12,
                    ),
                  ),

                  // Copyright
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.copyright,
                    label: 'Copyright',
                    value: '© ${DateTime.now().year} Quarterlies',
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 24,
                    ),
                  ),

                  // Description
                  Container(
                    padding: spacing.ResponsiveSpacing.getPadding(
                      context,
                      base: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(
                        spacing.ResponsiveSpacing.getBorderRadius(
                          context,
                          base: 8,
                        ),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.description,
                              color: Theme.of(context).colorScheme.primary,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 16,
                              ),
                            ),
                            SizedBox(
                              width: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8,
                              ),
                            ),
                            ResponsiveBody(
                              'About Quarterlies',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: 8,
                          ),
                        ),
                        ResponsiveLabel(
                          'Quarterlies is a comprehensive business management tool designed for contractors and small businesses. Manage customers, jobs, estimates, contracts, invoices, expenses, time tracking, and tax payments all in one place.',
                        ),
                      ],
                    ),
                  ),

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),

                  // Action Buttons
                  context.isMobile
                      ? Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            height: spacing.ResponsiveSpacing.getButtonHeight(
                              context,
                            ),
                            child: OutlinedButton.icon(
                              onPressed:
                                  () => _showLicenses(context, displayProvider),
                              icon: Icon(
                                Icons.article_outlined,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 18,
                                ),
                              ),
                              label: ResponsiveBody('Licenses'),
                              style: OutlinedButton.styleFrom(
                                padding: spacing.ResponsiveSpacing.getPadding(
                                  context,
                                  base: 12,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12,
                            ),
                          ),
                          SizedBox(
                            width: double.infinity,
                            height: spacing.ResponsiveSpacing.getButtonHeight(
                              context,
                            ),
                            child: OutlinedButton.icon(
                              onPressed: () => _copyVersionInfo(),
                              icon: Icon(
                                Icons.copy,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 18,
                                ),
                              ),
                              label: ResponsiveBody('Copy Info'),
                              style: OutlinedButton.styleFrom(
                                padding: spacing.ResponsiveSpacing.getPadding(
                                  context,
                                  base: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                      : Row(
                        children: [
                          Expanded(
                            child: SizedBox(
                              height: spacing.ResponsiveSpacing.getButtonHeight(
                                context,
                              ),
                              child: OutlinedButton.icon(
                                onPressed:
                                    () =>
                                        _showLicenses(context, displayProvider),
                                icon: Icon(
                                  Icons.article_outlined,
                                  size: spacing.ResponsiveSpacing.getIconSize(
                                    context,
                                    base: 18,
                                  ),
                                ),
                                label: ResponsiveBody('Licenses'),
                                style: OutlinedButton.styleFrom(
                                  padding: spacing.ResponsiveSpacing.getPadding(
                                    context,
                                    base: 12,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12,
                            ),
                          ),
                          Expanded(
                            child: SizedBox(
                              height: spacing.ResponsiveSpacing.getButtonHeight(
                                context,
                              ),
                              child: OutlinedButton.icon(
                                onPressed: () => _copyVersionInfo(),
                                icon: Icon(
                                  Icons.copy,
                                  size: spacing.ResponsiveSpacing.getIconSize(
                                    context,
                                    base: 18,
                                  ),
                                ),
                                label: ResponsiveBody('Copy Info'),
                                style: OutlinedButton.styleFrom(
                                  padding: spacing.ResponsiveSpacing.getPadding(
                                    context,
                                    base: 12,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(
    DisplaySettingsProvider displayProvider, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 16),
        ),
        SizedBox(width: spacing.ResponsiveSpacing.getSpacing(context, base: 8)),
        ResponsiveBody(
          '$label:',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        SizedBox(width: spacing.ResponsiveSpacing.getSpacing(context, base: 8)),
        Expanded(
          child: ResponsiveBody(
            value,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      ],
    );
  }

  void _showLicenses(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
  ) {
    showLicensePage(
      context: context,
      applicationName: 'Quarterlies',
      applicationVersion: '$_version ($_buildNumber)',
      applicationIcon: Container(
        width: spacing.ResponsiveSpacing.getIconSize(context, base: 48),
        height: spacing.ResponsiveSpacing.getIconSize(context, base: 48),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(
            spacing.ResponsiveSpacing.getBorderRadius(context, base: 12),
          ),
        ),
        child: Icon(
          Icons.receipt_long,
          color: Colors.white,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
        ),
      ),
    );
  }

  void _copyVersionInfo() {
    final versionInfo = '''
Quarterlies v$_version ($_buildNumber)
Business Management App
© ${DateTime.now().year} Quarterlies Team
''';

    Clipboard.setData(ClipboardData(text: versionInfo));

    if (mounted) {
      ErrorDisplay.showOperation(
        context,
        'Version information copied to clipboard',
      );
    }
  }
}
