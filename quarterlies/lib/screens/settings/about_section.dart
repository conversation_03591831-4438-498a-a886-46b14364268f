import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class AboutSection extends StatefulWidget {
  const AboutSection({super.key});

  @override
  State<AboutSection> createState() => _AboutSectionState();
}

class _AboutSectionState extends State<AboutSection> {
  String _version = 'Loading...';
  String _buildNumber = '';

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
      });
    } catch (e) {
      setState(() {
        _version = '1.0.0';
        _buildNumber = '1';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Card(
            margin: EdgeInsets.symmetric(
              horizontal: displayProvider.isOfficeMode ? 12 : 16,
              vertical: displayProvider.isOfficeMode ? 6 : 8,
            ),
            child: Padding(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'About',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // App Icon and Name
                  Row(
                    children: [
                      Container(
                        width: displayProvider.isOfficeMode ? 40 : 48,
                        height: displayProvider.isOfficeMode ? 40 : 48,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(
                            displayProvider.isOfficeMode ? 10 : 12,
                          ),
                        ),
                        child: Icon(
                          Icons.receipt_long,
                          color: Colors.white,
                          size: displayProvider.isOfficeMode ? 20 : 24,
                        ),
                      ),
                      SizedBox(width: displayProvider.isOfficeMode ? 12 : 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Quarterlies',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 16 : 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Business Management App',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 12 : 14,
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 18 : 24),

                  // Version Information
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.info_outline,
                    label: 'Version',
                    value: '$_version ($_buildNumber)',
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),

                  // Developer Information
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.code,
                    label: 'Developer',
                    value: 'Quarterlies Team',
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),

                  // Copyright
                  _buildInfoRow(
                    displayProvider,
                    icon: Icons.copyright,
                    label: 'Copyright',
                    value: '© ${DateTime.now().year} Quarterlies',
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 18 : 24),

                  // Description
                  Container(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 10 : 12,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.description,
                              color: Theme.of(context).colorScheme.primary,
                              size: displayProvider.isOfficeMode ? 14 : 16,
                            ),
                            SizedBox(
                              width: displayProvider.isOfficeMode ? 6 : 8,
                            ),
                            Text(
                              'About Quarterlies',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize:
                                    displayProvider.isOfficeMode ? 12 : 14,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                        Text(
                          'Quarterlies is a comprehensive business management tool designed for contractors and small businesses. Manage customers, jobs, estimates, contracts, invoices, expenses, time tracking, and tax payments all in one place.',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 11 : 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // Action Buttons
                  context.isMobile
                      ? Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed:
                                  () => _showLicenses(context, displayProvider),
                              icon: Icon(
                                Icons.article_outlined,
                                size: displayProvider.isOfficeMode ? 16 : 18,
                              ),
                              label: Text(
                                'Licenses',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 12 : 14,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 10 : 12,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: displayProvider.isOfficeMode ? 8 : 12,
                          ),
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed: () => _copyVersionInfo(),
                              icon: Icon(
                                Icons.copy,
                                size: displayProvider.isOfficeMode ? 16 : 18,
                              ),
                              label: Text(
                                'Copy Info',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 12 : 14,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 10 : 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                      : Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed:
                                  () => _showLicenses(context, displayProvider),
                              icon: Icon(
                                Icons.article_outlined,
                                size: displayProvider.isOfficeMode ? 16 : 18,
                              ),
                              label: Text(
                                'Licenses',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 12 : 14,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 10 : 12,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: displayProvider.isOfficeMode ? 8 : 12,
                          ),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _copyVersionInfo(),
                              icon: Icon(
                                Icons.copy,
                                size: displayProvider.isOfficeMode ? 16 : 18,
                              ),
                              label: Text(
                                'Copy Info',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 12 : 14,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      displayProvider.isOfficeMode ? 10 : 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(
    DisplaySettingsProvider displayProvider, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: displayProvider.isOfficeMode ? 14 : 16,
        ),
        SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
        Text(
          '$label:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: displayProvider.isOfficeMode ? 12 : 14,
          ),
        ),
        SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: displayProvider.isOfficeMode ? 12 : 14,
            ),
          ),
        ),
      ],
    );
  }

  void _showLicenses(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
  ) {
    showLicensePage(
      context: context,
      applicationName: 'Quarterlies',
      applicationVersion: '$_version ($_buildNumber)',
      applicationIcon: Container(
        width: displayProvider.isOfficeMode ? 40 : 48,
        height: displayProvider.isOfficeMode ? 40 : 48,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(
            displayProvider.isOfficeMode ? 10 : 12,
          ),
        ),
        child: Icon(
          Icons.receipt_long,
          color: Colors.white,
          size: displayProvider.isOfficeMode ? 20 : 24,
        ),
      ),
    );
  }

  void _copyVersionInfo() {
    final versionInfo = '''
Quarterlies v$_version ($_buildNumber)
Business Management App
© ${DateTime.now().year} Quarterlies Team
''';

    Clipboard.setData(ClipboardData(text: versionInfo));

    if (mounted) {
      ErrorDisplay.showOperation(
        context,
        'Version information copied to clipboard',
      );
    }
  }
}
