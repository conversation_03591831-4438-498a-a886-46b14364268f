import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/screens/settings/mileage_settings_section.dart';
import 'package:quarterlies/screens/settings/about_section.dart';
import 'package:quarterlies/screens/settings/data_management_section.dart';
import 'package:quarterlies/screens/settings/display_settings_section.dart';
import 'package:quarterlies/widgets/collapsible_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/error_handler.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final DataRepository _dataRepository = DataRepository();
  String? _errorMessage;
  UserSettings? _userSettings;

  @override
  void initState() {
    super.initState();
    _loadUserSettings();
  }

  Future<void> _loadUserSettings() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadUserSettings',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final settings = await _dataRepository.getUserSettings();
          if (!mounted) return;

          setState(() {
            _userSettings = settings;
          });
        },
        message: 'Loading settings...',
        errorMessage: 'Failed to load settings',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load settings: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _updateSyncSetting(String settingName, bool value) async {
    if (_userSettings == null) return;

    try {
      late UserSettings updatedSettings;

      switch (settingName) {
        case 'expenses':
          updatedSettings = _userSettings!.copyWith(
            syncExpenses: value,
            defaultLiveJobCostSync: value && _userSettings!.syncLaborCosts,
            updatedAt: DateTime.now(),
          );
          break;
        case 'labor':
          updatedSettings = _userSettings!.copyWith(
            syncLaborCosts: value,
            defaultLiveJobCostSync: _userSettings!.syncExpenses && value,
            updatedAt: DateTime.now(),
          );
          break;
        case 'all':
          updatedSettings = _userSettings!.copyWith(
            syncExpenses: value,
            syncLaborCosts: value,
            defaultLiveJobCostSync: value,
            updatedAt: DateTime.now(),
          );
          break;
        default:
          throw Exception('Invalid setting name');
      }

      await _dataRepository.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _userSettings = updatedSettings;
      });

      if (mounted) {
        // Use centralized feedback system
        ErrorDisplay.showSuccess(
          context,
          '${_getSettingDisplayName(settingName)} ${value ? 'enabled' : 'disabled'}',
        );
      }
    } catch (e) {
      if (mounted) {
        // Use centralized error handling
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'updateSyncSetting',
            'settingName': settingName,
            'value': value,
          },
        );
        ErrorHandler.logError(appError);
        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _updateGlareResistanceSetting(
    String settingName,
    bool value,
  ) async {
    if (_userSettings == null) return;

    try {
      late UserSettings updatedSettings;

      switch (settingName) {
        case 'automatic_brightness':
          updatedSettings = _userSettings!.copyWith(
            automaticBrightnessDetection: value,
            updatedAt: DateTime.now(),
          );
          break;
        case 'dynamic_color':
          updatedSettings = _userSettings!.copyWith(
            dynamicColorAdjustment: value,
            updatedAt: DateTime.now(),
          );
          break;
        case 'enhanced_contrast':
          updatedSettings = _userSettings!.copyWith(
            enhancedContrastMode: value,
            updatedAt: DateTime.now(),
          );
          break;
        default:
          throw Exception('Invalid glare resistance setting name');
      }

      await _dataRepository.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _userSettings = updatedSettings;
      });

      if (mounted) {
        // Use centralized feedback system
        ErrorDisplay.showSuccess(
          context,
          '${_getGlareResistanceDisplayName(settingName)} ${value ? 'enabled' : 'disabled'}',
        );
      }
    } catch (e) {
      if (mounted) {
        // Use centralized error handling
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'updateGlareResistanceSetting',
            'settingName': settingName,
            'value': value,
          },
        );
        ErrorHandler.logError(appError);
        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _updateNotificationSetting(
    String settingType, [
    bool? enableValue,
    int? daysValue,
  ]) async {
    if (_userSettings == null) return;

    try {
      late UserSettings updatedSettings;

      if (settingType == 'enable' && enableValue != null) {
        updatedSettings = _userSettings!.copyWith(
          enableDueDateNotifications: enableValue,
          updatedAt: DateTime.now(),
        );

        if (mounted) {
          // Use centralized feedback system
          ErrorDisplay.showSuccess(
            context,
            'Due date notifications ${enableValue ? 'enabled' : 'disabled'}',
          );
        }
      } else if (settingType == 'days' && daysValue != null) {
        updatedSettings = _userSettings!.copyWith(
          dueDateNotificationDays: daysValue,
          updatedAt: DateTime.now(),
        );

        if (mounted) {
          // Use centralized feedback system
          ErrorDisplay.showSuccess(
            context,
            'Notification lead time set to $daysValue days',
          );
        }
      } else {
        throw Exception('Invalid notification setting update');
      }

      await _dataRepository.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _userSettings = updatedSettings;
      });
    } catch (e) {
      if (mounted) {
        // Use centralized error handling
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'updateNotificationSettings',
            'settingType': settingType,
            'enableValue': enableValue,
            'daysValue': daysValue,
          },
        );
        ErrorHandler.logError(appError);
        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  String _getSettingDisplayName(String settingName) {
    switch (settingName) {
      case 'expenses':
        return 'Expense Syncing';
      case 'labor':
        return 'Labor Cost Syncing';
      case 'all':
        return 'Live Job Cost Sync';
      case 'notifications':
        return 'Due Date Notifications';
      default:
        return 'Setting';
    }
  }

  String _getGlareResistanceDisplayName(String settingName) {
    switch (settingName) {
      case 'automatic_brightness':
        return 'Automatic Brightness Detection';
      case 'dynamic_color':
        return 'Dynamic Color Adjustment';
      case 'enhanced_contrast':
        return 'Enhanced Contrast Mode';
      default:
        return 'Glare Resistance Setting';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle('Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadUserSettings');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading settings...',
                    size: 32.0,
                    showOfflineStatus: true,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadUserSettings,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            120,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: ResponsiveLabel('Retry'),
                      ),
                    ],
                  ),
                )
                : _userSettings == null
                ? Center(child: ResponsiveBody('Settings not found'))
                : Consumer<DisplaySettingsProvider>(
                  builder: (context, displayProvider, child) {
                    return ListView(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            bottom: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 16,
                            ),
                          ),
                          child: ResponsiveSubtitle(
                            'Application Settings',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),

                        // Invoice Settings Section (Expanded by default)
                        SettingsSection(
                          title: 'Invoice Settings',
                          icon: Icons.receipt_long,
                          initiallyExpanded: true,
                          sectionKey: 'invoice_settings',
                          children: [
                            // Sync Expenses Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.receipt_long,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: spacing
                                                .ResponsiveSpacing.getIconSize(
                                              context,
                                              base: 16,
                                            ),
                                          ),
                                          SizedBox(
                                            width: spacing
                                                .ResponsiveSpacing.getSpacing(
                                              context,
                                              base: 8,
                                            ),
                                          ),
                                          ResponsiveLabel(
                                            'Sync Job Expenses',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: spacing
                                            .ResponsiveSpacing.getSpacing(
                                          context,
                                          base: 4,
                                        ),
                                      ),
                                      ResponsiveLabel(
                                        'Automatically include job expenses when creating new invoices',
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value: _userSettings?.syncExpenses ?? false,
                                  onChanged:
                                      (value) =>
                                          _updateSyncSetting('expenses', value),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                            SizedBox(
                              height: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 16,
                              ),
                            ),

                            // Sync Labor Costs Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.timer,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: spacing
                                                .ResponsiveSpacing.getIconSize(
                                              context,
                                              base: 16,
                                            ),
                                          ),
                                          SizedBox(
                                            width: spacing
                                                .ResponsiveSpacing.getSpacing(
                                              context,
                                              base: 8,
                                            ),
                                          ),
                                          ResponsiveLabel(
                                            'Sync Labor Costs',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: spacing
                                            .ResponsiveSpacing.getSpacing(
                                          context,
                                          base: 4,
                                        ),
                                      ),
                                      ResponsiveLabel(
                                        'Automatically include labor costs when creating new invoices',
                                        style: const TextStyle(
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value: _userSettings?.syncLaborCosts ?? false,
                                  onChanged:
                                      (value) =>
                                          _updateSyncSetting('labor', value),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Notification Settings Section (Collapsed by default)
                        SettingsSection(
                          title: 'Notification Settings',
                          icon: Icons.notifications,
                          initiallyExpanded: false,
                          sectionKey: 'notification_settings',
                          children: [
                            // Enable Due Date Notifications Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.notifications,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Enable Due Date Notifications',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      const Text(
                                        'Receive notifications when invoices are coming due',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value:
                                      _userSettings
                                          ?.enableDueDateNotifications ??
                                      false,
                                  onChanged:
                                      (value) => _updateNotificationSetting(
                                        'enable',
                                        value,
                                      ),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Glare Resistance Settings Section (Collapsed by default)
                        SettingsSection(
                          title: 'Glare Resistance',
                          icon: Icons.brightness_6,
                          initiallyExpanded: false,
                          sectionKey: 'glare_resistance_settings',
                          children: [
                            // Automatic Brightness Detection Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.brightness_auto,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Automatic Brightness Detection',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      const Text(
                                        'Use device brightness to automatically adjust app appearance (defaults to device setting)',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value:
                                      _userSettings
                                          ?.automaticBrightnessDetection ??
                                      true,
                                  onChanged:
                                      (value) => _updateGlareResistanceSetting(
                                        'automatic_brightness',
                                        value,
                                      ),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Dynamic Color Adjustment Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.palette,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Dynamic Color Adjustment',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      const Text(
                                        'Automatically adjust colors based on ambient light conditions (default on)',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value:
                                      _userSettings?.dynamicColorAdjustment ??
                                      true,
                                  onChanged:
                                      (value) => _updateGlareResistanceSetting(
                                        'dynamic_color',
                                        value,
                                      ),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Enhanced Contrast Mode Toggle
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.contrast,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Enhanced Contrast Mode',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      const Text(
                                        'Boost contrast and text weight for better outdoor visibility (default on)',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value:
                                      _userSettings?.enhancedContrastMode ??
                                      true,
                                  onChanged:
                                      (value) => _updateGlareResistanceSetting(
                                        'enhanced_contrast',
                                        value,
                                      ),
                                  activeColor:
                                      Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Display Settings Section (Collapsed by default)
                        const SettingsSection(
                          title: 'Display Settings',
                          icon: Icons.display_settings,
                          initiallyExpanded: false,
                          sectionKey: 'display_settings',
                          children: [DisplaySettingsSection()],
                        ),

                        // Mileage Settings Section (Collapsed by default)
                        SettingsSection(
                          title: 'Mileage Tracking',
                          icon: Icons.directions_car,
                          initiallyExpanded: false,
                          sectionKey: 'mileage_settings',
                          children: [
                            MileageSettingsSection(
                              userSettings: _userSettings!,
                              onSettingsUpdated: (updatedSettings) {
                                setState(() {
                                  _userSettings = updatedSettings;
                                });
                              },
                            ),
                          ],
                        ),

                        // Data Management Section (Collapsed by default)
                        const SettingsSection(
                          title: 'Data Management',
                          icon: Icons.storage,
                          initiallyExpanded: false,
                          sectionKey: 'data_management',
                          children: [DataManagementSection()],
                        ),

                        // About Section (Collapsed by default)
                        const SettingsSection(
                          title: 'About',
                          icon: Icons.info,
                          initiallyExpanded: false,
                          sectionKey: 'about_section',
                          children: [AboutSection()],
                        ),

                        SizedBox(
                          height: spacing.ResponsiveSpacing.getSpacing(
                            context,
                            base: 32,
                          ),
                        ),
                      ],
                    );
                  },
                );
          },
        ),
      ),
    );
  }
}
