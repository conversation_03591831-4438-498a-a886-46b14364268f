import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class MileageSettingsSection extends StatefulWidget {
  final UserSettings userSettings;
  final Function(UserSettings) onSettingsUpdated;

  const MileageSettingsSection({
    super.key,
    required this.userSettings,
    required this.onSettingsUpdated,
  });

  @override
  State<MileageSettingsSection> createState() => _MileageSettingsSectionState();
}

class _MileageSettingsSectionState extends State<MileageSettingsSection> {
  final SupabaseService _supabaseService = SupabaseService();
  bool _isUpdating = false;
  int _idleTimeoutMinutes = MileageConstants.defaultIdleTimeoutMinutes;
  bool _enableMileageTracking = false;

  @override
  void initState() {
    super.initState();
    _enableMileageTracking = widget.userSettings.enableMileageTracking;
    _idleTimeoutMinutes =
        widget.userSettings.mileageIdleTimeoutMinutes ??
        MileageConstants.defaultIdleTimeoutMinutes;
  }

  Future<void> _updateMileageTrackingSetting(bool value) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final updatedSettings = widget.userSettings.copyWith(
        enableMileageTracking: value,
        updatedAt: DateTime.now(),
      );

      await _supabaseService.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _enableMileageTracking = value;
        _isUpdating = false;
      });

      widget.onSettingsUpdated(updatedSettings);

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Mileage tracking ${value ? 'enabled' : 'disabled'}',
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isUpdating = false;
      });

      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update setting: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _updateIdleTimeoutSetting(int minutes) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final updatedSettings = widget.userSettings.copyWith(
        mileageIdleTimeoutMinutes: minutes,
        updatedAt: DateTime.now(),
      );

      await _supabaseService.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _idleTimeoutMinutes = minutes;
        _isUpdating = false;
      });

      widget.onSettingsUpdated(updatedSettings);

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Idle timeout updated to $minutes minutes',
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isUpdating = false;
      });

      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update setting: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Card(
            margin: EdgeInsets.symmetric(
              vertical: displayProvider.isOfficeMode ? 6.0 : 8.0,
            ),
            child: Padding(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mileage Tracking',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 16 : 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12.0 : 16.0),

                  // Enable/disable mileage tracking
                  SwitchListTile(
                    title: Text(
                      'Enable Auto-Tracking',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      ),
                    ),
                    subtitle: Text(
                      'Automatically track mileage using GPS',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    value: _enableMileageTracking,
                    onChanged:
                        _isUpdating ? null : _updateMileageTrackingSetting,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: displayProvider.isOfficeMode ? 8.0 : 16.0,
                    ),
                  ),

                  // Idle timeout setting
                  if (_enableMileageTracking) ...[
                    Padding(
                      padding: EdgeInsets.only(
                        left: displayProvider.isOfficeMode ? 12.0 : 16.0,
                        top: displayProvider.isOfficeMode ? 6.0 : 8.0,
                      ),
                      child: Text(
                        'Auto-tracking idle timeout (minutes):',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 13 : 14,
                        ),
                      ),
                    ),
                    Slider(
                      value: _idleTimeoutMinutes.toDouble(),
                      min: 1,
                      max: 15,
                      divisions: 14,
                      label: _idleTimeoutMinutes.toString(),
                      onChanged:
                          _isUpdating
                              ? null
                              : (value) {
                                setState(() {
                                  _idleTimeoutMinutes = value.round();
                                });
                              },
                      onChangeEnd:
                          _isUpdating
                              ? null
                              : (value) {
                                _updateIdleTimeoutSetting(value.round());
                              },
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: displayProvider.isOfficeMode ? 12.0 : 16.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '1 min',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 11 : 12,
                            ),
                          ),
                          Text(
                            '${_idleTimeoutMinutes.round()} min',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 12 : 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '15 min',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 11 : 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: displayProvider.isOfficeMode ? 12.0 : 16.0,
                      ),
                      child: Text(
                        'When your vehicle stops moving for this amount of time, '
                        'tracking will automatically end and you\'ll be notified.',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 11 : 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],

                  SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
                  const Divider(),
                  SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),

                  // IRS rate information
                  FutureBuilder<double?>(
                    future: _supabaseService.getMileageRate(),
                    builder: (context, snapshot) {
                      final ratePerMile =
                          snapshot.data ??
                          double.tryParse(MileageConstants.defaultIrsRate) ??
                          0.655;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current IRS Mileage Rate',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            ),
                          ),
                          SizedBox(
                            height: displayProvider.isOfficeMode ? 6.0 : 8.0,
                          ),
                          Text(
                            '\$${ratePerMile.toStringAsFixed(3)} per mile',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            ),
                          ),
                          SizedBox(
                            height: displayProvider.isOfficeMode ? 3.0 : 4.0,
                          ),
                          Text(
                            'This rate is used to calculate mileage deductions and is updated by the app developer.',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 11 : 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
