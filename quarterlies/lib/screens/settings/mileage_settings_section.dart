import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class MileageSettingsSection extends StatefulWidget {
  final UserSettings userSettings;
  final Function(UserSettings) onSettingsUpdated;

  const MileageSettingsSection({
    super.key,
    required this.userSettings,
    required this.onSettingsUpdated,
  });

  @override
  State<MileageSettingsSection> createState() => _MileageSettingsSectionState();
}

class _MileageSettingsSectionState extends State<MileageSettingsSection> {
  final SupabaseService _supabaseService = SupabaseService();
  bool _isUpdating = false;
  int _idleTimeoutMinutes = MileageConstants.defaultIdleTimeoutMinutes;
  bool _enableMileageTracking = false;

  @override
  void initState() {
    super.initState();
    _enableMileageTracking = widget.userSettings.enableMileageTracking;
    _idleTimeoutMinutes =
        widget.userSettings.mileageIdleTimeoutMinutes ??
        MileageConstants.defaultIdleTimeoutMinutes;
  }

  Future<void> _updateMileageTrackingSetting(bool value) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final updatedSettings = widget.userSettings.copyWith(
        enableMileageTracking: value,
        updatedAt: DateTime.now(),
      );

      await _supabaseService.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _enableMileageTracking = value;
        _isUpdating = false;
      });

      widget.onSettingsUpdated(updatedSettings);

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Mileage tracking ${value ? 'enabled' : 'disabled'}',
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isUpdating = false;
      });

      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update setting: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _updateIdleTimeoutSetting(int minutes) async {
    if (_isUpdating) return;

    setState(() {
      _isUpdating = true;
    });

    try {
      final updatedSettings = widget.userSettings.copyWith(
        mileageIdleTimeoutMinutes: minutes,
        updatedAt: DateTime.now(),
      );

      await _supabaseService.updateUserSettings(updatedSettings);

      if (!mounted) return;

      setState(() {
        _idleTimeoutMinutes = minutes;
        _isUpdating = false;
      });

      widget.onSettingsUpdated(updatedSettings);

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Idle timeout updated to $minutes minutes',
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isUpdating = false;
      });

      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update setting: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Card(
            margin: EdgeInsets.symmetric(
              vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
            ),
            child: Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveSubtitle(
                    'Mileage Tracking',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),

                  // Enable/disable mileage tracking
                  SwitchListTile(
                    title: ResponsiveBody('Enable Auto-Tracking'),
                    subtitle: ResponsiveLabel(
                      'Automatically track mileage using GPS',
                    ),
                    value: _enableMileageTracking,
                    onChanged:
                        _isUpdating ? null : _updateMileageTrackingSetting,
                    contentPadding: spacing.ResponsiveSpacing.getPadding(
                      context,
                      base: 16,
                    ),
                  ),

                  // Idle timeout setting
                  if (_enableMileageTracking) ...[
                    Padding(
                      padding: EdgeInsets.only(
                        left: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                        top: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 8,
                        ),
                      ),
                      child: ResponsiveLabel(
                        'Auto-tracking idle timeout (minutes):',
                      ),
                    ),
                    Slider(
                      value: _idleTimeoutMinutes.toDouble(),
                      min: 1,
                      max: 15,
                      divisions: 14,
                      label: _idleTimeoutMinutes.toString(),
                      onChanged:
                          _isUpdating
                              ? null
                              : (value) {
                                setState(() {
                                  _idleTimeoutMinutes = value.round();
                                });
                              },
                      onChangeEnd:
                          _isUpdating
                              ? null
                              : (value) {
                                _updateIdleTimeoutSetting(value.round());
                              },
                    ),
                    Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ResponsiveLabel('1 min'),
                          ResponsiveLabel(
                            '${_idleTimeoutMinutes.round()} min',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          ResponsiveLabel('15 min'),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 8,
                      ),
                    ),
                    Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      child: ResponsiveLabel(
                        'When your vehicle stops moving for this amount of time, '
                        'tracking will automatically end and you\'ll be notified.',
                        style: const TextStyle(fontStyle: FontStyle.italic),
                      ),
                    ),
                  ],

                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 8,
                    ),
                  ),
                  const Divider(),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 8,
                    ),
                  ),

                  // IRS rate information
                  FutureBuilder<double?>(
                    future: _supabaseService.getMileageRate(),
                    builder: (context, snapshot) {
                      final ratePerMile =
                          snapshot.data ??
                          double.tryParse(MileageConstants.defaultIrsRate) ??
                          0.655;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveBody(
                            'Current IRS Mileage Rate',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 8,
                            ),
                          ),
                          ResponsiveBody(
                            '\$${ratePerMile.toStringAsFixed(3)} per mile',
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 4,
                            ),
                          ),
                          ResponsiveLabel(
                            'This rate is used to calculate mileage deductions and is updated by the app developer.',
                            style: const TextStyle(fontStyle: FontStyle.italic),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
