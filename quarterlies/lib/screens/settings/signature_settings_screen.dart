import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/signature_storage_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/signature_creation_widget.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class SignatureSettingsScreen extends StatefulWidget {
  const SignatureSettingsScreen({super.key});

  @override
  State<SignatureSettingsScreen> createState() =>
      _SignatureSettingsScreenState();
}

class _SignatureSettingsScreenState extends State<SignatureSettingsScreen> {
  final DataRepository _dataRepository = DataRepository();
  final SignatureStorageService _signatureStorageService =
      SignatureStorageService();
  UserProfile? _userProfile;
  String? _errorMessage;
  Uint8List? _newSignatureBytes;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadUserProfile',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final profile = await _dataRepository.getUserProfile();

          setState(() {
            _userProfile = profile;
          });
        },
        message: 'Loading profile...',
        errorMessage: 'Failed to load profile',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }

  void _onSignatureChanged(Uint8List? signatureBytes) {
    setState(() {
      _newSignatureBytes = signatureBytes;
    });
  }

  Future<void> _saveSignature() async {
    if (_userProfile == null || _newSignatureBytes == null) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveSignature',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Store signature locally first (offline-first)
          await _dataRepository.storeUserSignatureLocally(_newSignatureBytes!);

          // Try to sync to remote storage in background
          final signatureBytesToSync = _newSignatureBytes!;

          setState(() {
            _newSignatureBytes = null;
          });

          if (mounted) {
            // Show success feedback
            ErrorDisplay.showSuccess(context, 'Signature saved successfully');
          }

          _syncSignatureToRemote(signatureBytesToSync);
        },
        message: 'Saving signature...',
        errorMessage: 'Failed to save signature',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    }
  }

  /// Sync signature to remote storage (background operation)
  Future<void> _syncSignatureToRemote(Uint8List signatureBytes) async {
    try {
      // Upload to Supabase storage
      final signatureUrl = await _signatureStorageService.updateUserSignature(
        signatureBytes,
        _userProfile!.signatureImageUrl,
      );

      final updatedProfile = _userProfile!.copyWith(
        signatureImageUrl: signatureUrl,
      );

      await _dataRepository.updateUserProfile(updatedProfile);

      setState(() {
        _userProfile = updatedProfile;
      });
    } catch (e) {
      debugPrint('Failed to sync signature to remote storage: $e');
      // Don't show error to user since local storage succeeded
    }
  }

  Future<void> _removeSignature() async {
    if (_userProfile == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove Signature'),
            content: const Text(
              'Are you sure you want to remove your signature? This will affect all future documents.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Remove'),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'removeSignature',
          () async {
            setState(() {
              _errorMessage = null;
            });

            // Delete from storage
            if (_userProfile!.signatureImageUrl != null) {
              await _signatureStorageService.deleteUserSignature(
                _userProfile!.signatureImageUrl!,
              );
            }

            final updatedProfile = _userProfile!.copyWith(
              signatureImageUrl: null,
            );

            await _dataRepository.updateUserProfile(updatedProfile);

            setState(() {
              _userProfile = updatedProfile;
              _newSignatureBytes = null;
            });

            if (mounted) {
              // Show success feedback
              ErrorDisplay.showSuccess(
                context,
                'Signature removed successfully',
              );
            }
          },
          message: 'Removing signature...',
          errorMessage: 'Failed to remove signature',
        );
      } catch (e) {
        if (mounted) {
          setState(() {
            _errorMessage = e.toString();
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle('Signature Settings'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                if (loadingProvider.isLoading('loadUserProfile')) {
                  return const Center(child: CircularProgressIndicator());
                }
                return _buildContent(displayProvider, loadingProvider);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildContent(
    DisplaySettingsProvider displayProvider,
    LoadingStateProvider loadingProvider,
  ) {
    return Padding(
      padding: spacing.ResponsiveSpacing.getPadding(context, base: 24),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  ResponsiveSubtitle(
                    'Manage Your Signature',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 12,
                    ),
                  ),
                  ResponsiveBody(
                    'Your signature is automatically applied to all documents you create. Update it here to change how it appears on contracts, estimates, and other documents.',
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                      height: 1.4,
                    ),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 32,
                    ),
                  ),

                  // Current signature display
                  if (_userProfile?.signatureImageUrl != null) ...[
                    Container(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 20,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(
                          spacing.ResponsiveSpacing.getBorderRadius(
                            context,
                            base: 12,
                          ),
                        ),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.verified,
                                color: Theme.of(context).colorScheme.primary,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 20,
                                ),
                              ),
                              SizedBox(
                                width: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: 12,
                                ),
                              ),
                              ResponsiveLabel(
                                'Current Signature',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 16,
                            ),
                          ),
                          Container(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 80,
                            ),
                            width: double.infinity,
                            padding: spacing.ResponsiveSpacing.getPadding(
                              context,
                              base: 12,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                spacing.ResponsiveSpacing.getBorderRadius(
                                  context,
                                  base: 8,
                                ),
                              ),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: _buildSignatureImage(
                              _userProfile!.signatureImageUrl!,
                            ),
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 16,
                            ),
                          ),
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              onPressed:
                                  loadingProvider.isLoading('removeSignature')
                                      ? null
                                      : _removeSignature,
                              icon: Icon(
                                Icons.delete_outline,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 18,
                                ),
                              ),
                              label: ResponsiveLabel('Remove Signature'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.red,
                                side: const BorderSide(color: Colors.red),
                                minimumSize: Size(
                                  double.infinity,
                                  spacing.ResponsiveSpacing.getButtonHeight(
                                    context,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 32,
                      ),
                    ),
                  ],

                  // Signature creation/update widget
                  SignatureCreationWidget(
                    onSignatureChanged: _onSignatureChanged,
                    title:
                        _userProfile?.signatureImageUrl != null
                            ? 'Update Your Signature'
                            : 'Create Your Signature',
                    subtitle: 'Draw your new signature below',
                    showInstructions: _userProfile?.signatureImageUrl == null,
                    showClearButton: true,
                  ),

                  // Error message
                  if (_errorMessage != null) ...[
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 24,
                      ),
                    ),
                    Container(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        border: Border.all(color: Colors.red[300]!),
                        borderRadius: BorderRadius.circular(
                          spacing.ResponsiveSpacing.getBorderRadius(
                            context,
                            base: 8,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error,
                            color: Colors.red[700],
                            size: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 20,
                            ),
                          ),
                          SizedBox(
                            width: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12,
                            ),
                          ),
                          Expanded(
                            child: ResponsiveBody(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Save button
          if (_newSignatureBytes != null) ...[
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
            ),
            SizedBox(
              width: double.infinity,
              height: spacing.ResponsiveSpacing.getButtonHeight(context),
              child: ElevatedButton(
                onPressed:
                    loadingProvider.isLoading('saveSignature')
                        ? null
                        : _saveSignature,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      spacing.ResponsiveSpacing.getBorderRadius(
                        context,
                        base: 12,
                      ),
                    ),
                  ),
                  elevation: spacing.ResponsiveSpacing.getElevation(context),
                ),
                child:
                    loadingProvider.isLoading('saveSignature')
                        ? SizedBox(
                          height: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 24,
                          ),
                          width: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 24,
                          ),
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : ResponsiveLabel(
                          'Save Signature',
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSignatureImage(String signatureUrl) {
    if (signatureUrl.startsWith('data:image/png;base64,')) {
      final base64String = signatureUrl.substring(
        'data:image/png;base64,'.length,
      );
      try {
        final bytes = _base64Decode(base64String);
        return Image.memory(bytes, fit: BoxFit.contain);
      } catch (e) {
        return const Icon(Icons.error, color: Colors.red);
      }
    } else {
      // Handle URL-based signatures (if implemented in the future)
      return Image.network(
        signatureUrl,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(Icons.error, color: Colors.red);
        },
      );
    }
  }

  // Helper function for base64 decoding (for legacy base64 signatures)
  // New signatures will be stored as URLs in Supabase storage

  Uint8List _base64Decode(String base64String) {
    const String chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    String cleanString = base64String.replaceAll('=', '');

    List<int> bytes = [];

    for (int i = 0; i < cleanString.length; i += 4) {
      int char1 = chars.indexOf(cleanString[i]);
      int char2 =
          i + 1 < cleanString.length ? chars.indexOf(cleanString[i + 1]) : 0;
      int char3 =
          i + 2 < cleanString.length ? chars.indexOf(cleanString[i + 2]) : 0;
      int char4 =
          i + 3 < cleanString.length ? chars.indexOf(cleanString[i + 3]) : 0;

      int combined = (char1 << 18) | (char2 << 12) | (char3 << 6) | char4;

      bytes.add((combined >> 16) & 255);
      if (i + 2 < cleanString.length) bytes.add((combined >> 8) & 255);
      if (i + 3 < cleanString.length) bytes.add(combined & 255);
    }

    return Uint8List.fromList(bytes);
  }
}
