import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/sync_status.dart'; // Import SyncStatus from here
import 'package:quarterlies/models/user_settings.dart'; // Import UserSettings
import 'package:quarterlies/services/sync_manager.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

/// A settings section that displays synchronization options and status.
///
/// This widget provides UI for:
/// - Viewing current sync status
/// - Manually triggering synchronization
/// - Configuring background sync options
class SyncSettingsSection extends StatefulWidget {
  final UserSettings userSettings;
  final Function(UserSettings) onSettingsChanged;

  const SyncSettingsSection({
    super.key,
    required this.userSettings,
    required this.onSettingsChanged,
  });

  @override
  State<SyncSettingsSection> createState() => _SyncSettingsSectionState();
}

class _SyncSettingsSectionState extends State<SyncSettingsSection> {
  final SyncManager _syncManager = SyncManager();
  bool _isSyncing = false;
  SyncStatus _syncStatus = SyncStatus.synced;

  @override
  void initState() {
    super.initState();
    _syncManager.syncStatusStream.listen((status) {
      setState(() {
        _syncStatus = status;
        _isSyncing = status == SyncStatus.pending;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Card(
          margin: EdgeInsets.symmetric(
            vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
            horizontal: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
          ),
          child: Padding(
            padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.sync,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 8,
                      ),
                    ),
                    ResponsiveSubtitle('Synchronization'),
                    const Spacer(),
                    _buildSyncStatusIndicator(),
                  ],
                ),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 16,
                  ),
                ),
                _buildSyncNowButton(displayProvider),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 16,
                  ),
                ),
                const Divider(),
                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 8,
                  ),
                ),
                _buildBackgroundSyncSettings(displayProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSyncStatusIndicator() {
    IconData icon;
    Color color;
    String tooltip;

    switch (_syncStatus) {
      case SyncStatus.synced:
        icon = Icons.check_circle;
        color = Colors.green;
        tooltip = 'All data is synced';
        break;
      case SyncStatus.pending:
        icon = Icons.sync;
        color = Colors.blue;
        tooltip = 'Sync in progress';
        break;
      case SyncStatus.error:
        icon = Icons.error;
        color = Colors.red;
        tooltip = 'Sync error';
        break;
      case SyncStatus.conflict:
        icon = Icons.warning;
        color = Colors.orange;
        tooltip = 'Sync conflicts detected';
        break;
    }

    return Tooltip(message: tooltip, child: Icon(icon, color: color));
  }

  Widget _buildSyncNowButton(DisplaySettingsProvider displayProvider) {
    return SizedBox(
      width: double.infinity,
      height: spacing.ResponsiveSpacing.getButtonHeight(context),
      child: ElevatedButton.icon(
        onPressed: _isSyncing ? null : _syncNow,
        icon:
            _isSyncing
                ? SizedBox(
                  width: spacing.ResponsiveSpacing.getIconSize(
                    context,
                    base: 20,
                  ),
                  height: spacing.ResponsiveSpacing.getIconSize(
                    context,
                    base: 20,
                  ),
                  child: SyncLoadingIndicator(
                    operation: 'Syncing...',
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 20,
                    ),
                    showSyncIcon: false,
                  ),
                )
                : Icon(
                  Icons.sync,
                  size: spacing.ResponsiveSpacing.getIconSize(
                    context,
                    base: 20,
                  ),
                ),
        label: ResponsiveBody(_isSyncing ? 'Syncing...' : 'Sync Now'),
        style: ElevatedButton.styleFrom(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 12),
        ),
      ),
    );
  }

  Widget _buildBackgroundSyncSettings(DisplaySettingsProvider displayProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveLabel(
          'Background Sync',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        SizedBox(
          height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
        ),
        SwitchListTile(
          title: ResponsiveBody('Enable Background Sync'),
          subtitle: ResponsiveLabel(
            'Automatically sync data in the background',
          ),
          value: true, // This would come from user settings
          onChanged: (value) {
            // Update user settings
            // This is a placeholder - actual implementation would update the setting
          },
          contentPadding: EdgeInsets.zero,
        ),
        ListTile(
          title: ResponsiveBody('Sync Frequency'),
          subtitle: ResponsiveLabel(
            'Every 15 minutes',
          ), // This would come from user settings
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: spacing.ResponsiveSpacing.getIconSize(context, base: 16),
          ),
          contentPadding: EdgeInsets.zero,
          onTap: () {
            // Show sync frequency options
            // This is a placeholder - actual implementation would show options
          },
        ),
        SwitchListTile(
          title: ResponsiveBody('Sync on Wi-Fi Only'),
          subtitle: ResponsiveLabel('Only sync when connected to Wi-Fi'),
          value: true, // This would come from user settings
          onChanged: (value) {
            // Update user settings
            // This is a placeholder - actual implementation would update the setting
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Future<void> _syncNow() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      await _syncManager.syncData();
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Sync completed successfully');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Sync failed: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }
}
