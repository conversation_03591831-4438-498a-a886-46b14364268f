import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/sync_status.dart'; // Import SyncStatus from here
import 'package:quarterlies/models/user_settings.dart'; // Import UserSettings
import 'package:quarterlies/services/sync_manager.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

/// A settings section that displays synchronization options and status.
///
/// This widget provides UI for:
/// - Viewing current sync status
/// - Manually triggering synchronization
/// - Configuring background sync options
class SyncSettingsSection extends StatefulWidget {
  final UserSettings userSettings;
  final Function(UserSettings) onSettingsChanged;

  const SyncSettingsSection({
    super.key,
    required this.userSettings,
    required this.onSettingsChanged,
  });

  @override
  State<SyncSettingsSection> createState() => _SyncSettingsSectionState();
}

class _SyncSettingsSectionState extends State<SyncSettingsSection> {
  final SyncManager _syncManager = SyncManager();
  bool _isSyncing = false;
  SyncStatus _syncStatus = SyncStatus.synced;

  @override
  void initState() {
    super.initState();
    _syncManager.syncStatusStream.listen((status) {
      setState(() {
        _syncStatus = status;
        _isSyncing = status == SyncStatus.pending;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Card(
          margin: EdgeInsets.symmetric(
            vertical: displayProvider.isOfficeMode ? 6.0 : 8.0,
            horizontal: displayProvider.isOfficeMode ? 12.0 : 16.0,
          ),
          child: Padding(
            padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.sync,
                      size: displayProvider.isOfficeMode ? 20 : 24,
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
                    Text(
                      'Synchronization',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      ),
                    ),
                    const Spacer(),
                    _buildSyncStatusIndicator(),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                _buildSyncNowButton(displayProvider),
                SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                const Divider(),
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                _buildBackgroundSyncSettings(displayProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSyncStatusIndicator() {
    IconData icon;
    Color color;
    String tooltip;

    switch (_syncStatus) {
      case SyncStatus.synced:
        icon = Icons.check_circle;
        color = Colors.green;
        tooltip = 'All data is synced';
        break;
      case SyncStatus.pending:
        icon = Icons.sync;
        color = Colors.blue;
        tooltip = 'Sync in progress';
        break;
      case SyncStatus.error:
        icon = Icons.error;
        color = Colors.red;
        tooltip = 'Sync error';
        break;
      case SyncStatus.conflict:
        icon = Icons.warning;
        color = Colors.orange;
        tooltip = 'Sync conflicts detected';
        break;
    }

    return Tooltip(message: tooltip, child: Icon(icon, color: color));
  }

  Widget _buildSyncNowButton(DisplaySettingsProvider displayProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isSyncing ? null : _syncNow,
        icon:
            _isSyncing
                ? SizedBox(
                  width: displayProvider.isOfficeMode ? 16 : 20,
                  height: displayProvider.isOfficeMode ? 16 : 20,
                  child: SyncLoadingIndicator(
                    operation: 'Syncing...',
                    size: displayProvider.isOfficeMode ? 16.0 : 20.0,
                    showSyncIcon: false,
                  ),
                )
                : Icon(
                  Icons.sync,
                  size: displayProvider.isOfficeMode ? 16 : 20,
                ),
        label: Text(
          _isSyncing ? 'Syncing...' : 'Sync Now',
          style: TextStyle(fontSize: displayProvider.isOfficeMode ? 12 : 14),
        ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            vertical: displayProvider.isOfficeMode ? 8 : 12,
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundSyncSettings(DisplaySettingsProvider displayProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Background Sync',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontSize: displayProvider.isOfficeMode ? 12 : 14,
          ),
        ),
        SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
        SwitchListTile(
          title: Text(
            'Enable Background Sync',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 12 : 14),
          ),
          subtitle: Text(
            'Automatically sync data in the background',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 10 : 12),
          ),
          value: true, // This would come from user settings
          onChanged: (value) {
            // Update user settings
            // This is a placeholder - actual implementation would update the setting
          },
          contentPadding: EdgeInsets.zero,
          dense: displayProvider.isOfficeMode,
        ),
        ListTile(
          title: Text(
            'Sync Frequency',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 12 : 14),
          ),
          subtitle: Text(
            'Every 15 minutes',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 10 : 12),
          ), // This would come from user settings
          trailing: Icon(
            Icons.arrow_forward_ios,
            size: displayProvider.isOfficeMode ? 12 : 16,
          ),
          contentPadding: EdgeInsets.zero,
          dense: displayProvider.isOfficeMode,
          onTap: () {
            // Show sync frequency options
            // This is a placeholder - actual implementation would show options
          },
        ),
        SwitchListTile(
          title: Text(
            'Sync on Wi-Fi Only',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 12 : 14),
          ),
          subtitle: Text(
            'Only sync when connected to Wi-Fi',
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 10 : 12),
          ),
          value: true, // This would come from user settings
          onChanged: (value) {
            // Update user settings
            // This is a placeholder - actual implementation would update the setting
          },
          contentPadding: EdgeInsets.zero,
          dense: displayProvider.isOfficeMode,
        ),
      ],
    );
  }

  Future<void> _syncNow() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      await _syncManager.syncData();
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Sync completed successfully');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Sync failed: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }
}
