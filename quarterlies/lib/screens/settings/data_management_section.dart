import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/services/cache_manager.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class DataManagementSection extends StatefulWidget {
  const DataManagementSection({super.key});

  @override
  State<DataManagementSection> createState() => _DataManagementSectionState();
}

class _DataManagementSectionState extends State<DataManagementSection> {
  final CacheManager _cacheManager = CacheManager();
  final DataRepository _dataRepository = DataRepository();

  bool _isExporting = false;
  bool _isClearing = false;
  bool _isDeleting = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return ResponsiveContainer(
          child: Card(
            margin: EdgeInsets.symmetric(
              horizontal: displayProvider.isOfficeMode ? 12 : 16,
              vertical: displayProvider.isOfficeMode ? 6 : 8,
            ),
            child: Padding(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Data Management',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // Clear Cache Option
                  _buildDataOption(
                    displayProvider,
                    icon: Icons.cleaning_services,
                    title: 'Clear Cache',
                    subtitle: 'Clear locally stored data to free up space',
                    isLoading: _isClearing,
                    onTap: _showClearCacheDialog,
                    color: Colors.orange,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),

                  // Export Data Option
                  _buildDataOption(
                    displayProvider,
                    icon: Icons.download,
                    title: 'Export Data',
                    subtitle: 'Export all your data as a backup file',
                    isLoading: _isExporting,
                    onTap: _showExportDataDialog,
                    color: Colors.blue,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),

                  // Delete Account Option
                  _buildDataOption(
                    displayProvider,
                    icon: Icons.delete_forever,
                    title: 'Delete Account',
                    subtitle: 'Permanently delete your account and all data',
                    isLoading: _isDeleting,
                    onTap: _showDeleteAccountDialog,
                    color: Colors.red,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),

                  // Warning Information
                  Container(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 10 : 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.amber.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber,
                          color: Colors.amber,
                          size: displayProvider.isOfficeMode ? 14 : 16,
                        ),
                        SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
                        Expanded(
                          child: Text(
                            'Data management actions cannot be undone. Please ensure you have backups before proceeding.',
                            style: TextStyle(
                              fontSize: displayProvider.isOfficeMode ? 11 : 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDataOption(
    DisplaySettingsProvider displayProvider, {
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isLoading,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 10 : 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(displayProvider.isOfficeMode ? 6 : 8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: color,
                size: displayProvider.isOfficeMode ? 18 : 20,
              ),
            ),
            SizedBox(width: displayProvider.isOfficeMode ? 10 : 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: displayProvider.isOfficeMode ? 13 : 14,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 11 : 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            if (isLoading)
              SizedBox(
                width: displayProvider.isOfficeMode ? 18 : 20,
                height: displayProvider.isOfficeMode ? 18 : 20,
                child: const CircularProgressIndicator(strokeWidth: 2),
              )
            else
              Icon(
                Icons.arrow_forward_ios,
                size: displayProvider.isOfficeMode ? 14 : 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
          ],
        ),
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Cache'),
            content: const Text(
              'This will clear all locally stored data. You will need to sync with the server when you go back online.\n\nAre you sure you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _clearCache();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Cache'),
              ),
            ],
          ),
    );
  }

  void _showExportDataDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Export Data'),
            content: const Text(
              'This will create a backup file containing all your data. The file will be saved and can be shared.\n\nDo you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _exportData();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Export'),
              ),
            ],
          ),
    );
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Account'),
            content: const Text(
              'WARNING: This will permanently delete your account and ALL data. This action cannot be undone.\n\nType "DELETE" to confirm:',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showDeleteConfirmationDialog();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Continue'),
              ),
            ],
          ),
    );
  }

  void _showDeleteConfirmationDialog() {
    final TextEditingController confirmController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Final Confirmation'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Type "DELETE" to confirm account deletion:'),
                const SizedBox(height: 16),
                TextField(
                  controller: confirmController,
                  decoration: const InputDecoration(
                    hintText: 'Type DELETE here',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (confirmController.text.trim().toUpperCase() == 'DELETE') {
                    Navigator.pop(context);
                    _deleteAccount();
                  } else {
                    ErrorDisplay.showWarning(
                      context,
                      'Please type "DELETE" to confirm',
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('DELETE ACCOUNT'),
              ),
            ],
          ),
    );
  }

  Future<void> _clearCache() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'clearCache',
        () async {
          setState(() {
            _isClearing = true;
          });

          await _cacheManager.clearAllCache();

          if (mounted) {
            ErrorDisplay.showOperation(context, 'Cache cleared successfully');
          }
        },
        message: 'Clearing cache...',
        errorMessage: 'Failed to clear cache',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error clearing cache: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isClearing = false;
        });
      }
    }
  }

  Future<void> _exportData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'exportData',
        () async {
          setState(() {
            _isExporting = true;
          });

          // Collect all data
          final customers = await _dataRepository.getCustomers();
          final jobs = await _dataRepository.getJobs();
          final estimates = await _dataRepository.getEstimates();
          final invoices = await _dataRepository.getInvoices();
          final expenses = await _dataRepository.getExpenses();
          final timeLogs = await _dataRepository.getTimeLogs();
          final taxPayments = await _dataRepository.getTaxPayments();

          final exportData = {
            'export_date': DateTime.now().toIso8601String(),
            'app_version': '1.0.0',
            'data': {
              'customers': customers.map((c) => c.toJson()).toList(),
              'jobs': jobs.map((j) => j.toJson()).toList(),
              'estimates': estimates.map((e) => e.toJson()).toList(),
              'invoices': invoices.map((i) => i.toJson()).toList(),
              'expenses': expenses.map((e) => e.toJson()).toList(),
              'time_logs': timeLogs.map((t) => t.toJson()).toList(),
              'tax_payments': taxPayments.map((t) => t.toJson()).toList(),
            },
          };

          // Create JSON file
          final jsonString = const JsonEncoder.withIndent(
            '  ',
          ).convert(exportData);

          // Save to temporary file
          final directory = await getTemporaryDirectory();
          final fileName =
              'quarterlies_backup_${DateTime.now().millisecondsSinceEpoch}.json';
          final file = File('${directory.path}/$fileName');
          await file.writeAsString(jsonString);

          // Share the file
          await SharePlus.instance.share(
            ShareParams(
              files: [XFile(file.path)],
              text: 'Quarterlies Data Backup',
              subject: 'Quarterlies Data Export',
            ),
          );

          if (mounted) {
            ErrorDisplay.showOperation(context, 'Data exported successfully');
          }
        },
        message: 'Exporting data...',
        errorMessage: 'Failed to export data',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error exporting data: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _deleteAccount() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'deleteAccount',
        () async {
          setState(() {
            _isDeleting = true;
          });

          // This would call a Supabase function to delete all user data
          // For now, we'll show a placeholder
          await Future.delayed(const Duration(seconds: 2));

          if (mounted) {
            ErrorDisplay.showInfo(
              context,
              'Account deletion initiated. Please contact support to complete the process.',
            );
          }
        },
        message: 'Deleting account...',
        errorMessage: 'Failed to delete account',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error deleting account: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
      }
    }
  }
}
