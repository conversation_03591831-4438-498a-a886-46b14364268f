import 'dart:async'; // Added for StreamSubscription
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart'; // Changed from supabase_service
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'invoice_detail_screen.dart';
import 'invoice_form_screen.dart';
import 'package:intl/intl.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  State<InvoiceListScreen> createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen> {
  final DataRepository _dataRepository = DataRepository(); // Use DataRepository
  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  bool _isOffline = false; // Added for offline status
  String? _errorMessage;
  String _filterStatus = 'All';
  StreamSubscription<bool>?
  _connectivitySubscription; // Added for connectivity listener

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener(); // Added
    _loadInvoices();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel(); // Added
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());
    if (mounted) setState(() {});

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadInvoices(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadInvoices() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadInvoices',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final invoices =
              await _dataRepository.getInvoices(); // Use DataRepository
          if (!mounted) return;

          setState(() {
            _invoices = invoices;
            _applyFilters();
          });
        },
        message: 'Loading invoices...',
        errorMessage: 'Failed to load invoices',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load invoices: ${e.toString()}';
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      if (_filterStatus == 'All') {
        _filteredInvoices = List.from(_invoices);
      } else {
        _filteredInvoices =
            _invoices
                .where(
                  (invoice) => invoice.status == _filterStatus.toLowerCase(),
                )
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveTitle('Invoices'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: 8.0,
              ).copyWith(left: 0, top: 0, bottom: 0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 20,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed: _loadInvoices,
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadInvoices');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading invoices...',
                    size: 32.0,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: ResponsiveBody(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                )
                : Column(
                  children: [
                    // Filter options
                    Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16.0,
                      ),
                      child: Row(
                        children: [
                          const ResponsiveLabel('Status: '),
                          SizedBox(
                            width: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 8,
                            ),
                          ),
                          DropdownButton<String>(
                            value: _filterStatus,
                            items:
                                ['All', 'Open', 'Paid', 'Overdue']
                                    .map(
                                      (status) => DropdownMenuItem(
                                        value: status,
                                        child: ResponsiveBody(status),
                                      ),
                                    )
                                    .toList(),
                            onChanged: (value) {
                              setState(() {
                                _filterStatus = value!;
                                _applyFilters();
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    // Invoice list
                    Expanded(
                      child:
                          _filteredInvoices.isEmpty
                              ? Center(
                                child: ResponsiveBody('No invoices found'),
                              )
                              : RefreshIndicator(
                                onRefresh: _loadInvoices,
                                child: ListView.builder(
                                  itemCount: _filteredInvoices.length,
                                  itemBuilder: (context, index) {
                                    final invoice = _filteredInvoices[index];
                                    return Consumer<DisplaySettingsProvider>(
                                      builder: (
                                        context,
                                        displayProvider,
                                        child,
                                      ) {
                                        return AdaptiveListTile(
                                          title: ResponsiveSubtitle(
                                            'Invoice #${invoice.id != null && invoice.id!.length >= 8 ? invoice.id!.substring(0, 8) : (invoice.id ?? "Unknown")}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                          subtitle: ResponsiveBody(
                                            'Amount: \$${invoice.totalAmount.toStringAsFixed(2)} • Due: ${DateFormat('MM/dd/yyyy').format(invoice.dueDate)}',
                                          ),
                                          leading: Container(
                                            padding: spacing
                                                .ResponsiveSpacing.getPadding(
                                              context,
                                              base: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getStatusColor(
                                                invoice.status,
                                              ),
                                              borderRadius: BorderRadius.circular(
                                                spacing
                                                    .ResponsiveSpacing.getBorderRadius(
                                                  context,
                                                  base: 8,
                                                ),
                                              ),
                                            ),
                                            child: Icon(
                                              _getStatusIcon(invoice.status),
                                              color: Colors.white,
                                              size: spacing
                                                  .ResponsiveSpacing.getIconSize(
                                                context,
                                                base: 20,
                                              ),
                                            ),
                                          ),
                                          trailing: Chip(
                                            label: ResponsiveLabel(
                                              invoice.status,
                                              style: const TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            backgroundColor: _getStatusColor(
                                              invoice.status,
                                            ).withValues(alpha: 0.2),
                                            side: BorderSide(
                                              color: _getStatusColor(
                                                invoice.status,
                                              ),
                                              width: 1,
                                            ),
                                          ),
                                          // Additional info shown only in Office Mode
                                          additionalInfo:
                                              displayProvider.isOfficeMode
                                                  ? OfficeAdditionalInfo(
                                                    items: [
                                                      if (invoice.createdAt !=
                                                          null)
                                                        InfoItem(
                                                          label: 'Created',
                                                          value: DateFormat(
                                                            'MM/dd/yyyy',
                                                          ).format(
                                                            invoice.createdAt!,
                                                          ),
                                                          icon:
                                                              Icons
                                                                  .calendar_today,
                                                        ),
                                                      if (invoice.status
                                                              .toLowerCase() ==
                                                          'paid')
                                                        InfoItem(
                                                          label: 'Amount Paid',
                                                          value:
                                                              '\$${invoice.amountPaid.toStringAsFixed(2)}',
                                                          icon: Icons.payment,
                                                        ),
                                                      InfoItem(
                                                        label: 'Items',
                                                        value:
                                                            '${invoice.lineItems?.length ?? 0} items',
                                                        icon: Icons.list,
                                                      ),
                                                    ],
                                                  )
                                                  : null,
                                          // Office actions shown only in Office Mode
                                          officeActions:
                                              displayProvider.isOfficeMode
                                                  ? [
                                                    OfficeActionButton(
                                                      icon: Icons.edit,
                                                      label: 'Edit',
                                                      onPressed: () async {
                                                        final result =
                                                            await Navigator.push(
                                                              context,
                                                              MaterialPageRoute(
                                                                builder:
                                                                    (
                                                                      context,
                                                                    ) => InvoiceFormScreen(
                                                                      invoice:
                                                                          invoice,
                                                                    ),
                                                              ),
                                                            );
                                                        if (result == true) {
                                                          _loadInvoices();
                                                        }
                                                      },
                                                    ),
                                                    OfficeActionButton(
                                                      icon: Icons.share,
                                                      label: 'Share',
                                                      onPressed: () {
                                                        ErrorDisplay.showInfo(
                                                          context,
                                                          'Share functionality coming soon!',
                                                        );
                                                      },
                                                    ),
                                                    if (invoice.status
                                                            .toLowerCase() ==
                                                        'open')
                                                      OfficeActionButton(
                                                        icon: Icons.payment,
                                                        label: 'Mark Paid',
                                                        onPressed: () {
                                                          ErrorDisplay.showInfo(
                                                            context,
                                                            'Mark paid functionality coming soon!',
                                                          );
                                                        },
                                                      ),
                                                  ]
                                                  : null,
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder:
                                                    (context) =>
                                                        InvoiceDetailScreen(
                                                          invoiceId:
                                                              invoice.id!,
                                                        ),
                                              ),
                                            ).then((_) => _loadInvoices());
                                          },
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                    ),
                  ],
                );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InvoiceFormScreen()),
          ).then((_) => _loadInvoices());
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Icons.description;
      case 'paid':
        return Icons.check_circle;
      case 'overdue':
        return Icons.warning;
      default:
        return Icons.receipt;
    }
  }
}
