import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authService = AuthService();
  final _dataRepository = DataRepository();
  String? _errorMessage;
  bool _rememberMe = false; // Added for "Remember Me" functionality

  @override
  void initState() {
    super.initState();
    _loadRememberedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Load remembered credentials if remember me is enabled
  Future<void> _loadRememberedCredentials() async {
    try {
      final isRememberMeEnabled = await _authService.isRememberMeEnabled();
      if (isRememberMeEnabled) {
        final credentials = await _authService.getRememberedCredentials();
        final email = credentials['email'];
        final password = credentials['password'];

        if (email != null && password != null && mounted) {
          setState(() {
            _emailController.text = email;
            _passwordController.text = password;
            _rememberMe = true;
          });
        }
      }
    } catch (e) {
      // Silently fail - remember me is not critical functionality
    }
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'login',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.signInWithRememberMe(
              email: _emailController.text.trim(),
              password: _passwordController.text,
              rememberMe: _rememberMe,
            );

            if (mounted) {
              // Check if user has completed onboarding
              final isOnboardingComplete =
                  await _dataRepository.isOnboardingComplete();

              if (mounted) {
                if (isOnboardingComplete) {
                  // Navigate to home page after successful login
                  Navigator.pushReplacementNamed(context, '/home');
                } else {
                  // Navigate to onboarding flow
                  Navigator.pushReplacementNamed(context, '/onboarding');
                }
              }
            }
          },
          message: 'Signing in...',
          errorMessage: 'Failed to sign in',
        );
      } catch (e) {
        if (mounted) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'login',
              'email': _emailController.text.trim(),
              'rememberMe': _rememberMe,
            },
          );
          ErrorHandler.logError(appError);

          setState(() {
            _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          });

          // Show error dialog for authentication errors
          if (appError.type == ErrorType.authentication) {
            ErrorDisplay.showErrorDialog(
              context,
              appError,
              onRetry: () => _login(),
            );
          } else {
            // Show snackbar for other errors
            ErrorDisplay.showSnackBar(
              context,
              appError,
              onRetry: () => _login(),
            );
          }
        }
      }
    }
  }

  void _navigateToPasswordReset() {
    Navigator.pushNamed(context, '/password-reset');
  }

  void _toggleRememberMe(bool? value) {
    final newValue = value ?? false;
    setState(() {
      _rememberMe = newValue;
    });

    // Clear remembered credentials if remember me is disabled
    if (!newValue) {
      _authService.clearRememberedCredentials();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: const ResponsiveTitle(
              'Login',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 32.0,
                    ),
                  ),
                  // App logo or icon for better visual identity
                  Icon(
                    Icons.account_balance,
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 64.0,
                    ),
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 24.0,
                    ),
                  ),
                  const ResponsiveTitle(
                    'Welcome Back',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 32.0,
                    ),
                  ),
                  CustomTextField(
                    controller: _emailController,
                    labelText: 'Email',
                    hintText: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) => InputValidators.validateEmail(value),
                  ),
                  CustomTextField(
                    controller: _passwordController,
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    obscureText: true,
                    validator:
                        (value) => InputValidators.validatePassword(value),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16.0,
                    ),
                  ),
                  context.isMobile
                      ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: _toggleRememberMe,
                              ),
                              const Expanded(
                                child: ResponsiveBody('Remember Me'),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12.0,
                            ),
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: _navigateToPasswordReset,
                              child: const ResponsiveBody('Forgot Password?'),
                            ),
                          ),
                        ],
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Checkbox(
                                  value: _rememberMe,
                                  onChanged: _toggleRememberMe,
                                ),
                                const Flexible(
                                  child: ResponsiveBody('Remember Me'),
                                ),
                              ],
                            ),
                          ),
                          TextButton(
                            onPressed: _navigateToPasswordReset,
                            child: const ResponsiveBody('Forgot Password?'),
                          ),
                        ],
                      ),
                  if (_errorMessage != null)
                    Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16.0,
                      ).copyWith(top: 0, left: 0, right: 0),
                      child: Container(
                        padding: spacing.ResponsiveSpacing.getPadding(
                          context,
                          base: 12.0,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(context),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red.shade700,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 20.0,
                              ),
                            ),
                            SizedBox(
                              width: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8.0,
                              ),
                            ),
                            Expanded(
                              child: ResponsiveBody(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red.shade700),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              iconSize: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 18.0,
                              ),
                              color: Colors.red.shade700,
                              onPressed: () {
                                setState(() {
                                  _errorMessage = null;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  Consumer<LoadingStateProvider>(
                    builder: (context, loadingProvider, child) {
                      return CustomButton(
                        text: 'Login',
                        onPressed: _login,
                        isLoading: loadingProvider.isLoading('login'),
                      );
                    },
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16.0,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/signup');
                    },
                    child: const ResponsiveBody(
                      'Don\'t have an account? Sign up',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
