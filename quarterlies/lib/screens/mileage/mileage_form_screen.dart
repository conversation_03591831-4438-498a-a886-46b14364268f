import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class MileageFormScreen extends StatefulWidget {
  final Mileage? mileage; // Null for new mileage, non-null for editing
  final String? jobId; // Optional job ID when creating from job screen

  const MileageFormScreen({super.key, this.mileage, this.jobId});

  @override
  State<MileageFormScreen> createState() => _MileageFormScreenState();
}

class _MileageFormScreenState extends State<MileageFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _startLocationController = TextEditingController();
  final _endLocationController = TextEditingController();
  final _milesController = TextEditingController();
  final _purposeController = TextEditingController();
  final _dataRepository = DataRepository();
  final _voiceRecordingService = VoiceRecordingService();

  String? _selectedJobId;
  DateTime _selectedDate = DateTime.now();
  List<Job> _jobs = [];
  double _ratePerMile =
      double.tryParse(MileageConstants.defaultIrsRate) ?? 0.655;
  String? _startCoordinates;
  String? _endCoordinates;
  bool _isAutoTracked = false;

  bool _isCapturingStart = false;
  bool _isCapturingEnd = false;
  bool _isRecording = false;
  bool _isOffline = false;
  String? _errorMessage;
  String? _voiceNoteUrl;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  bool get _isEditing => widget.mileage != null;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _loadJobs();
    _loadMileageRate();

    if (_isEditing) {
      // Populate form fields with existing mileage data
      _startLocationController.text = widget.mileage!.startLocation;
      _endLocationController.text = widget.mileage!.endLocation;
      _milesController.text = widget.mileage!.miles.toString();
      _purposeController.text = widget.mileage!.purpose ?? '';
      _selectedDate = widget.mileage!.date;
      _selectedJobId = widget.mileage!.jobId;
      _ratePerMile = widget.mileage!.ratePerMile;
      _startCoordinates = widget.mileage!.startCoordinates;
      _endCoordinates = widget.mileage!.endCoordinates;
      _isAutoTracked = widget.mileage!.isAutoTracked;
    } else if (widget.jobId != null) {
      _selectedJobId = widget.jobId;
    }
  }

  @override
  void dispose() {
    _startLocationController.dispose();
    _endLocationController.dispose();
    _milesController.dispose();
    _purposeController.dispose();
    _voiceRecordingService.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadJobs() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobs',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final jobs = await _dataRepository.getJobs();
          setState(() {
            _jobs = jobs;
          });
        },
        message: 'Loading jobs...',
        errorMessage: 'Failed to load jobs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load jobs: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _loadMileageRate() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadMileageRate',
        () async {
          // Try to get the IRS mileage rate from the database
          final rate = await _dataRepository.getMileageRate();
          if (rate != null) {
            setState(() {
              _ratePerMile = rate;
            });
          } else {
            // Use default rate if not found
            setState(() {
              _ratePerMile =
                  double.tryParse(MileageConstants.defaultIrsRate) ?? 0.655;
            });
          }
        },
        message: 'Loading mileage rate...',
        errorMessage: 'Failed to load mileage rate',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _ratePerMile =
              double.tryParse(MileageConstants.defaultIrsRate) ?? 0.655;
        });
      }
    }
  }

  Future<void> _captureLocation(bool isStart) async {
    setState(() {
      if (isStart) {
        _isCapturingStart = true;
      } else {
        _isCapturingEnd = true;
      }
      _errorMessage = null;
    });

    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permission denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      // Convert coordinates to address
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        final address = [
          placemark.street,
          placemark.locality,
          placemark.administrativeArea,
          placemark.postalCode,
        ].where((element) => element != null && element.isNotEmpty).join(', ');

        final coordinates = '${position.latitude},${position.longitude}';

        setState(() {
          if (isStart) {
            _startLocationController.text = address;
            _startCoordinates = coordinates;
            _isCapturingStart = false;
          } else {
            _endLocationController.text = address;
            _endCoordinates = coordinates;
            _isCapturingEnd = false;
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to capture location: ${e.toString()}';
        if (isStart) {
          _isCapturingStart = false;
        } else {
          _isCapturingEnd = false;
        }
      });
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Update form fields with extracted information
        setState(() {
          if (extractedInfo.containsKey('description') &&
              _purposeController.text.isEmpty) {
            _purposeController.text = extractedInfo['description'];
          }

          if (extractedInfo.containsKey('amount') &&
              _milesController.text.isEmpty) {
            _milesController.text = extractedInfo['amount'];
          }

          if (extractedInfo.containsKey('jobName') && _selectedJobId == null) {
            // Try to find a job with a matching name
            final jobName = extractedInfo['jobName'];
            try {
              final matchingJob = _jobs.firstWhere(
                (job) =>
                    job.title.toLowerCase().contains(jobName.toLowerCase()),
              );
              _selectedJobId = matchingJob.id;
            } catch (e) {
              // No matching job found, use first job if available
              if (_jobs.isNotEmpty) {
                _selectedJobId = _jobs.first.id;
              }
            }
          }
        });

        // Upload the audio file to storage
        final recordId = widget.mileage?.id ?? const Uuid().v4();
        final recordingPath = _voiceRecordingService.getRecordingPath();
        if (recordingPath != null && recordingPath.isNotEmpty) {
          _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
            recordingPath,
            'mileage',
            recordId,
          );
        }
      }
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'stopRecording'},
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  // Helper method to create a new Mileage with updated fields
  Mileage _copyWithMileage(Mileage original) {
    return Mileage(
      id: original.id,
      userId: original.userId,
      jobId: _selectedJobId,
      startLocation: _startLocationController.text,
      endLocation: _endLocationController.text,
      miles: double.parse(_milesController.text),
      ratePerMile: _ratePerMile,
      date: _selectedDate,
      purpose:
          _purposeController.text.isNotEmpty ? _purposeController.text : null,
      startCoordinates: _startCoordinates,
      endCoordinates: _endCoordinates,
      isAutoTracked: _isAutoTracked,
      voiceNoteUrl: _voiceNoteUrl ?? original.voiceNoteUrl,
      description:
          'Mileage: ${_startLocationController.text} to ${_endLocationController.text}',
      updatedAt: DateTime.now(),
    );
  }

  Future<void> _saveMileage() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedJobId == null) {
      setState(() {
        _errorMessage = 'Please select a job';
      });
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveMileage',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final mileage =
              _isEditing
                  ? _copyWithMileage(widget.mileage!)
                  : Mileage(
                    userId: Supabase.instance.client.auth.currentUser!.id,
                    jobId: _selectedJobId!,
                    startLocation: _startLocationController.text,
                    endLocation: _endLocationController.text,
                    miles: double.parse(_milesController.text),
                    ratePerMile: _ratePerMile,
                    date: _selectedDate,
                    purpose:
                        _purposeController.text.isNotEmpty
                            ? _purposeController.text
                            : null,
                    startCoordinates: _startCoordinates,
                    endCoordinates: _endCoordinates,
                    isAutoTracked: _isAutoTracked,
                    voiceNoteUrl: _voiceNoteUrl,
                  );

          // Since Mileage is a type of Expense, we use the expense methods
          if (_isEditing) {
            await _dataRepository.addExpense(
              mileage,
            ); // Update by adding a new version
          } else {
            await _dataRepository.addExpense(mileage);
          }

          if (mounted) {
            // Show success feedback using centralized system
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'mileage',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context, true);
          }
        },
        message: _isEditing ? 'Updating mileage...' : 'Creating mileage...',
        errorMessage: 'Failed to save mileage',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveMileage',
            'isEditing': _isEditing,
            'selectedJobId': _selectedJobId,
            'isAutoTracked': _isAutoTracked,
            'hasVoiceNote': _voiceNoteUrl != null,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(_isEditing ? 'Edit Mileage' : 'Add Mileage'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(
                context,
                base: 16.0,
              ),
              child: Tooltip(
                message:
                    'You are offline. Changes will be saved locally and synced when back online.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            if (loadingProvider.isLoading('loadJobs') ||
                loadingProvider.isLoading('loadMileageRate')) {
              return Center(
                child: CircularProgressIndicator(
                  strokeWidth: spacing.ResponsiveSpacing.getIconSize(
                    context,
                    base: 4,
                  ),
                ),
              );
            }

            return Consumer<DisplaySettingsProvider>(
              builder: (context, displayProvider, child) {
                return SingleChildScrollView(
                  padding: spacing.ResponsiveSpacing.getPadding(
                    context,
                    base: displayProvider.isOfficeMode ? 12.0 : 16.0,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_errorMessage != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red.shade50,
                                border: Border.all(color: Colors.red.shade200),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    color: Colors.red.shade700,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage!,
                                      style: TextStyle(
                                        color: Colors.red.shade700,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.close),
                                    iconSize: 18,
                                    color: Colors.red.shade700,
                                    onPressed: () {
                                      setState(() {
                                        _errorMessage = null;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // Basic Information Section
                        AdaptiveFormSection(
                          title: 'Basic Information',
                          icon: Icons.info,
                          children: [
                            if (displayProvider.isOfficeMode) ...[
                              // Office Mode: Job and Date in same row
                              Row(
                                children: [
                                  Expanded(
                                    flex: 2,
                                    child: DropdownButtonFormField<String>(
                                      decoration: const InputDecoration(
                                        labelText: 'Job',
                                        border: OutlineInputBorder(),
                                      ),
                                      value: _selectedJobId,
                                      items:
                                          _jobs.map((job) {
                                            return DropdownMenuItem<String>(
                                              value: job.id,
                                              child: Text(job.title),
                                            );
                                          }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedJobId = value;
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Please select a job';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: InkWell(
                                      onTap: () async {
                                        final pickedDate = await showDatePicker(
                                          context: context,
                                          initialDate: _selectedDate,
                                          firstDate: DateTime(2020),
                                          lastDate: DateTime.now().add(
                                            const Duration(days: 365),
                                          ),
                                        );
                                        if (pickedDate != null) {
                                          setState(() {
                                            _selectedDate = pickedDate;
                                          });
                                        }
                                      },
                                      child: InputDecorator(
                                        decoration: const InputDecoration(
                                          labelText: 'Date',
                                          border: OutlineInputBorder(),
                                        ),
                                        child: Text(
                                          DateFormat(
                                            'MM/dd/yyyy',
                                          ).format(_selectedDate),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ] else ...[
                              // Field Mode: Separate fields
                              DropdownButtonFormField<String>(
                                decoration: const InputDecoration(
                                  labelText: 'Job',
                                  border: OutlineInputBorder(),
                                ),
                                value: _selectedJobId,
                                items:
                                    _jobs.map((job) {
                                      return DropdownMenuItem<String>(
                                        value: job.id,
                                        child: Text(job.title),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedJobId = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a job';
                                  }
                                  return null;
                                },
                              ),
                              InkWell(
                                onTap: () async {
                                  final pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: _selectedDate,
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now().add(
                                      const Duration(days: 365),
                                    ),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      _selectedDate = pickedDate;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    DateFormat(
                                      'MM/dd/yyyy',
                                    ).format(_selectedDate),
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),

                        // Start location with GPS button
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _startLocationController,
                                decoration: const InputDecoration(
                                  labelText: 'Start Location',
                                  border: OutlineInputBorder(),
                                ),
                                validator:
                                    (value) => InputValidators.validateRequired(
                                      value,
                                      'Start location',
                                    ),
                              ),
                            ),
                            const SizedBox(width: 8.0),
                            IconButton(
                              icon:
                                  _isCapturingStart
                                      ? const CircularProgressIndicator()
                                      : const Icon(Icons.my_location),
                              onPressed:
                                  _isCapturingStart
                                      ? null
                                      : () => _captureLocation(true),
                              tooltip: 'Capture current location',
                            ),
                          ],
                        ),
                        const SizedBox(height: 16.0),

                        // End location with GPS button
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _endLocationController,
                                decoration: const InputDecoration(
                                  labelText: 'End Location',
                                  border: OutlineInputBorder(),
                                ),
                                validator:
                                    (value) => InputValidators.validateRequired(
                                      value,
                                      'End location',
                                    ),
                              ),
                            ),
                            const SizedBox(width: 8.0),
                            IconButton(
                              icon:
                                  _isCapturingEnd
                                      ? const CircularProgressIndicator()
                                      : const Icon(Icons.my_location),
                              onPressed:
                                  _isCapturingEnd
                                      ? null
                                      : () => _captureLocation(false),
                              tooltip: 'Capture current location',
                            ),
                          ],
                        ),
                        const SizedBox(height: 16.0),

                        // Miles driven
                        TextFormField(
                          controller: _milesController,
                          decoration: const InputDecoration(
                            labelText: 'Miles Driven',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator:
                              (value) => InputValidators.validateNumber(
                                value,
                                required: true,
                                fieldName: 'Miles driven',
                                min: 0.1,
                                max: 1000.0,
                              ),
                        ),
                        const SizedBox(height: 16.0),

                        // Purpose/notes with voice recording button
                        TextFormField(
                          controller: _purposeController,
                          decoration: InputDecoration(
                            labelText: 'Purpose/Notes (Optional)',
                            border: const OutlineInputBorder(),
                            suffixIcon:
                                _isRecording
                                    ? const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: CircularProgressIndicator(),
                                    )
                                    : IconButton(
                                      icon: Icon(
                                        _isRecording ? Icons.stop : Icons.mic,
                                        color:
                                            _isRecording
                                                ? Colors.red
                                                : Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                      ),
                                      onPressed:
                                          _isRecording
                                              ? _stopRecording
                                              : _startRecording,
                                      tooltip:
                                          _isRecording
                                              ? 'Stop recording'
                                              : 'Record voice note',
                                    ),
                          ),
                          maxLines: 3,
                        ),
                        if (_isRecording)
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.0),
                            child: Text(
                              'Recording... Speak clearly to capture notes, miles, and job information.',
                              style: TextStyle(
                                color: Colors.red,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                        const SizedBox(height: 16.0),

                        // Display the current mileage rate
                        Text(
                          'Current IRS Rate: \$${_ratePerMile.toStringAsFixed(3)}/mile',
                          style: const TextStyle(fontStyle: FontStyle.italic),
                        ),
                        Text(
                          'Deduction Amount: \$${(double.tryParse(_milesController.text) ?? 0 * _ratePerMile).toStringAsFixed(2)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 24.0),

                        // Save button
                        Consumer<LoadingStateProvider>(
                          builder: (context, loadingProvider, child) {
                            return SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed:
                                    loadingProvider.isLoading('saveMileage')
                                        ? null
                                        : _saveMileage,
                                child:
                                    loadingProvider.isLoading('saveMileage')
                                        ? const CircularProgressIndicator(
                                          color: Colors.white,
                                        )
                                        : Text(
                                          _isEditing
                                              ? 'Update Mileage'
                                              : 'Save Mileage',
                                        ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
