import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/screens/mileage/mileage_form_screen.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class MileageListScreen extends StatefulWidget {
  final String? jobId; // Optional job ID to filter mileage entries

  const MileageListScreen({super.key, this.jobId});

  @override
  State<MileageListScreen> createState() => _MileageListScreenState();
}

class _MileageListScreenState extends State<MileageListScreen> {
  final DataRepository _dataRepository = DataRepository();
  List<Mileage> _mileageEntries = [];
  List<Mileage> _filteredMileageEntries = [];
  List<Job> _jobs = [];
  String? _selectedJobId;
  bool _isOffline = false;
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _selectedJobId = widget.jobId;
    _loadData();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadMileageData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs for filtering
          final jobs = await _dataRepository.getJobs();

          // Load all mileage entries
          final mileageEntries = await _dataRepository.search<Mileage>(
            keyword: '',
            filters: _selectedJobId != null ? {'job_id': _selectedJobId} : null,
          );

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _mileageEntries = mileageEntries;
            _applyFilters();
          });
        },
        message: 'Loading mileage entries...',
        errorMessage: 'Failed to load mileage entries',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load mileage entries: ${e.toString()}';
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredMileageEntries =
          _mileageEntries.where((mileage) {
            // Apply job filter if selected
            if (_selectedJobId != null && mileage.jobId != _selectedJobId) {
              return false;
            }
            return true;
          }).toList();
    });
  }

  String _getJobTitle(String? jobId) {
    if (jobId == null) return 'No Job';

    final job = _jobs.firstWhere(
      (job) => job.id == jobId,
      orElse:
          () => Job(id: '', userId: '', customerId: '', title: 'Unknown Job'),
    );
    return job.title;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle('Mileage Tracking'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          if (_selectedJobId != null)
            IconButton(
              icon: Icon(
                Icons.filter_alt_off,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
              ),
              onPressed: () {
                setState(() {
                  _selectedJobId = null;
                  _applyFilters();
                });
              },
              tooltip: 'Clear job filter',
            ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadMileageData');

            return isLoading
                ? Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading mileage entries...',
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 32,
                    ),
                  ),
                )
                : _errorMessage != null
                ? Center(child: ResponsiveBody(_errorMessage!))
                : _filteredMileageEntries.isEmpty
                ? Center(child: ResponsiveBody('No mileage entries found'))
                : RefreshIndicator(
                  onRefresh: _loadData,
                  child: ListView.builder(
                    itemCount: _filteredMileageEntries.length,
                    itemBuilder: (context, index) {
                      final mileage = _filteredMileageEntries[index];
                      return Consumer<DisplaySettingsProvider>(
                        builder: (context, displayProvider, child) {
                          return AdaptiveListTile(
                            title: ResponsiveSubtitle(
                              '${mileage.startLocation} to ${mileage.endLocation}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: ResponsiveBody(
                              '${_getJobTitle(mileage.jobId)} • ${mileage.miles.toStringAsFixed(1)} miles • ${DateFormat('MM/dd/yyyy').format(mileage.date)}${mileage.purpose != null && mileage.purpose!.isNotEmpty ? ' • ${mileage.purpose}' : ''}',
                            ),
                            leading: Container(
                              padding: spacing.ResponsiveSpacing.getPadding(
                                context,
                                base: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(
                                  spacing.ResponsiveSpacing.getBorderRadius(
                                    context,
                                    base: 8,
                                  ),
                                ),
                              ),
                              child: Icon(
                                Icons.directions_car,
                                color: Colors.white,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 20,
                                ),
                              ),
                            ),
                            trailing: ResponsiveLabel(
                              '\$${mileage.amount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            // Additional info shown only in Office Mode
                            additionalInfo:
                                displayProvider.isOfficeMode
                                    ? OfficeAdditionalInfo(
                                      items: [
                                        InfoItem(
                                          label: 'Rate',
                                          value:
                                              '\$${(mileage.amount / mileage.miles).toStringAsFixed(3)}/mile',
                                          icon: Icons.attach_money,
                                        ),
                                        InfoItem(
                                          label: 'Date',
                                          value: DateFormat(
                                            'MMM d, yyyy',
                                          ).format(mileage.date),
                                          icon: Icons.calendar_today,
                                        ),
                                        if (mileage.purpose != null &&
                                            mileage.purpose!.isNotEmpty)
                                          InfoItem(
                                            label: 'Purpose',
                                            value: mileage.purpose!,
                                            icon: Icons.description,
                                          ),
                                      ],
                                    )
                                    : null,
                            // Office actions shown only in Office Mode
                            officeActions:
                                displayProvider.isOfficeMode
                                    ? [
                                      OfficeActionButton(
                                        icon: Icons.edit,
                                        label: 'Edit',
                                        onPressed: () async {
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      MileageFormScreen(
                                                        mileage: mileage,
                                                      ),
                                            ),
                                          );
                                          if (result == true) {
                                            _loadData();
                                          }
                                        },
                                      ),
                                      OfficeActionButton(
                                        icon: Icons.map,
                                        label: 'Route',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Route viewer coming soon!',
                                          );
                                        },
                                      ),
                                      OfficeActionButton(
                                        icon: Icons.copy,
                                        label: 'Duplicate',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Duplicate functionality coming soon!',
                                          );
                                        },
                                      ),
                                    ]
                                    : null,
                            onTap: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) =>
                                          MileageFormScreen(mileage: mileage),
                                ),
                              );
                              if (result == true) {
                                _loadData();
                              }
                            },
                          );
                        },
                      );
                    },
                  ),
                );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MileageFormScreen(jobId: _selectedJobId),
            ),
          );
          if (result == true) {
            _loadData();
          }
        },
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
        ),
      ),
    );
  }
}
