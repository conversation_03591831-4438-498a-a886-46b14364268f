import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class WelcomeStep extends StatelessWidget {
  final VoidCallback onNext;
  final bool isLoading;

  const WelcomeStep({super.key, required this.onNext, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Padding(
          padding: spacing.ResponsiveSpacing.getPadding(context, base: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App logo/icon
              Container(
                width: spacing.ResponsiveSpacing.getIconSize(context, base: 80),
                height: spacing.ResponsiveSpacing.getIconSize(
                  context,
                  base: 80,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(
                    spacing.ResponsiveSpacing.getBorderRadius(
                      context,
                      base: 16,
                    ),
                  ),
                ),
                child: Icon(
                  Icons.business_center,
                  size: spacing.ResponsiveSpacing.getIconSize(
                    context,
                    base: 40,
                  ),
                  color: Colors.white,
                ),
              ),

              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 20),
              ),

              // Welcome title
              ResponsiveTitle(
                'Welcome to Quarterlies!',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 12),
              ),

              // Welcome description
              ResponsiveBody(
                'Your comprehensive business management solution for contractors and small businesses.',
                style: TextStyle(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.8),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 20),
              ),

              // Features list
              Container(
                padding: spacing.ResponsiveSpacing.getPadding(
                  context,
                  base: 16,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(
                    spacing.ResponsiveSpacing.getBorderRadius(context, base: 8),
                  ),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  children: [
                    ResponsiveBody(
                      'What you can do with Quarterlies:',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 12,
                      ),
                    ),
                    _buildFeatureItem(
                      context,
                      displayProvider,
                      Icons.people,
                      'Manage customers and jobs',
                    ),
                    _buildFeatureItem(
                      context,
                      displayProvider,
                      Icons.receipt_long,
                      'Create estimates, contracts & invoices',
                    ),
                    _buildFeatureItem(
                      context,
                      displayProvider,
                      Icons.access_time,
                      'Track time, expenses & mileage',
                    ),
                    _buildFeatureItem(
                      context,
                      displayProvider,
                      Icons.analytics,
                      'Generate business reports',
                    ),
                    _buildFeatureItem(
                      context,
                      displayProvider,
                      Icons.cloud_off,
                      'Work offline with automatic sync',
                    ),
                  ],
                ),
              ),

              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 20),
              ),

              // Setup description
              ResponsiveLabel(
                'Let\'s get you set up! We\'ll collect some basic information to personalize your experience.',
                style: TextStyle(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(
                height: spacing.ResponsiveSpacing.getSpacing(context, base: 24),
              ),

              // Get started button
              SizedBox(
                width: double.infinity,
                height: spacing.ResponsiveSpacing.getButtonHeight(context),
                child: ElevatedButton(
                  onPressed: isLoading ? null : onNext,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        spacing.ResponsiveSpacing.getBorderRadius(
                          context,
                          base: 12,
                        ),
                      ),
                    ),
                    elevation: spacing.ResponsiveSpacing.getElevation(context),
                  ),
                  child:
                      isLoading
                          ? SizedBox(
                            height: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 24,
                            ),
                            width: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 24,
                            ),
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : ResponsiveSubtitle(
                            'Get Started',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    IconData icon,
    String text,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: spacing.ResponsiveSpacing.getSpacing(context, base: 3),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: spacing.ResponsiveSpacing.getIconSize(context, base: 18),
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(
            width: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
          ),
          Expanded(
            child: ResponsiveBody(
              text,
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
            ),
          ),
        ],
      ),
    );
  }
}
