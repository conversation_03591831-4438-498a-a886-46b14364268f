import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/address_autocomplete_field.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';

class PersonalInfoStep extends StatefulWidget {
  final UserProfile userProfile;
  final Function(UserProfile) onProfileUpdated;
  final VoidCallback onNext;
  final VoidCallback onPrevious;
  final bool isLoading;

  const PersonalInfoStep({
    super.key,
    required this.userProfile,
    required this.onProfileUpdated,
    required this.onNext,
    required this.onPrevious,
    required this.isLoading,
  });

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _zipCodeController;
  late TextEditingController _countryController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _firstNameController = TextEditingController(
      text: widget.userProfile.firstName,
    );
    _lastNameController = TextEditingController(
      text: widget.userProfile.lastName,
    );
    _emailController = TextEditingController(text: widget.userProfile.email);
    _phoneController = TextEditingController(text: widget.userProfile.phone);
    _addressController = TextEditingController(
      text: widget.userProfile.address,
    );
    _cityController = TextEditingController(text: widget.userProfile.city);
    _stateController = TextEditingController(text: widget.userProfile.state);
    _zipCodeController = TextEditingController(
      text: widget.userProfile.zipCode,
    );
    _countryController = TextEditingController(
      text: widget.userProfile.country ?? 'United States',
    );
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  void _updateProfile() {
    if (_formKey.currentState!.validate()) {
      final updatedProfile = widget.userProfile.copyWith(
        firstName:
            _firstNameController.text.trim().isEmpty
                ? null
                : _firstNameController.text.trim(),
        lastName:
            _lastNameController.text.trim().isEmpty
                ? null
                : _lastNameController.text.trim(),
        email:
            _emailController.text.trim().isEmpty
                ? null
                : _emailController.text.trim(),
        phone:
            _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
        address:
            _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
        city:
            _cityController.text.trim().isEmpty
                ? null
                : _cityController.text.trim(),
        state:
            _stateController.text.trim().isEmpty
                ? null
                : _stateController.text.trim(),
        zipCode:
            _zipCodeController.text.trim().isEmpty
                ? null
                : _zipCodeController.text.trim(),
        country:
            _countryController.text.trim().isEmpty
                ? null
                : _countryController.text.trim(),
      );

      widget.onProfileUpdated(updatedProfile);
      widget.onNext();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Padding(
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 24.0),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Text(
                          'Personal Information',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 20 : 24,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 8.0 : 12.0,
                        ),
                        Text(
                          'Tell us about yourself. This information will be used for your profile and can be updated later.',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                            height: 1.4,
                          ),
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 20.0 : 32.0,
                        ),

                        // Name section
                        AdaptiveFormSection(
                          title: 'Name',
                          icon: Icons.person,
                          useGridLayout: displayProvider.isOfficeMode,
                          children: [
                            AdaptiveFormField(
                              label: 'First Name',
                              required: true,
                              child: CustomTextField(
                                controller: _firstNameController,
                                labelText: 'First Name',
                                hintText: 'Enter your first name',
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Please enter your first name';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Last Name',
                              required: true,
                              child: CustomTextField(
                                controller: _lastNameController,
                                labelText: 'Last Name',
                                hintText: 'Enter your last name',
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Please enter your last name';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),

                        // Contact section
                        AdaptiveFormSection(
                          title: 'Contact Information',
                          icon: Icons.contact_mail,
                          useGridLayout: displayProvider.isOfficeMode,
                          children: [
                            AdaptiveFormField(
                              label: 'Email',
                              required: true,
                              child: CustomTextField(
                                controller: _emailController,
                                labelText: 'Email Address',
                                hintText: 'Enter your email address',
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Please enter your email address';
                                  }
                                  if (!RegExp(
                                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                  ).hasMatch(value)) {
                                    return 'Please enter a valid email address';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Phone',
                              child: CustomTextField(
                                controller: _phoneController,
                                labelText: 'Phone Number',
                                hintText: 'Enter your phone number',
                                keyboardType: TextInputType.phone,
                              ),
                            ),
                          ],
                        ),

                        // Address section
                        AdaptiveFormSection(
                          title: 'Address (Optional)',
                          icon: Icons.location_on,
                          children: [
                            AdaptiveFormField(
                              label: 'Street Address',
                              child: AddressAutocompleteField(
                                controller: _addressController,
                                labelText: 'Street Address',
                                hintText: 'Enter your street address',
                                onAddressSelected: (address) {
                                  // Auto-fill city, state, zip if available
                                  if (address.city.isNotEmpty) {
                                    _cityController.text = address.city;
                                  }
                                  if (address.state.isNotEmpty) {
                                    _stateController.text = address.state;
                                  }
                                  if (address.zipCode.isNotEmpty) {
                                    _zipCodeController.text = address.zipCode;
                                  }
                                  if (address.country.isNotEmpty) {
                                    _countryController.text = address.country;
                                  }
                                },
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'City',
                              child: CustomTextField(
                                controller: _cityController,
                                labelText: 'City',
                                hintText: 'Enter your city',
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'State',
                              child: CustomTextField(
                                controller: _stateController,
                                labelText: 'State',
                                hintText: 'Enter your state',
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'ZIP Code',
                              child: CustomTextField(
                                controller: _zipCodeController,
                                labelText: 'ZIP Code',
                                hintText: 'Enter your ZIP code',
                              ),
                            ),
                            AdaptiveFormField(
                              label: 'Country',
                              child: CustomTextField(
                                controller: _countryController,
                                labelText: 'Country',
                                hintText: 'Enter your country',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Navigation buttons
                SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 24.0),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: widget.isLoading ? null : widget.onPrevious,
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Back',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 16 : 18,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 12.0 : 16.0),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: widget.isLoading ? null : _updateProfile,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: displayProvider.isOfficeMode ? 12 : 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child:
                            widget.isLoading
                                ? SizedBox(
                                  height:
                                      displayProvider.isOfficeMode ? 20 : 24,
                                  width: displayProvider.isOfficeMode ? 20 : 24,
                                  child: const CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                                : Text(
                                  'Continue',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 16 : 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
