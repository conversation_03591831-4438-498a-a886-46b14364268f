import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/signature_creation_widget.dart';
import 'package:quarterlies/services/signature_storage_service.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class SignatureStep extends StatefulWidget {
  final UserProfile userProfile;
  final Function(UserProfile) onProfileUpdated;
  final VoidCallback onNext;
  final VoidCallback onPrevious;
  final bool isLoading;

  const SignatureStep({
    super.key,
    required this.userProfile,
    required this.onProfileUpdated,
    required this.onNext,
    required this.onPrevious,
    required this.isLoading,
  });

  @override
  State<SignatureStep> createState() => _SignatureStepState();
}

class _SignatureStepState extends State<SignatureStep> {
  final SignatureStorageService _signatureStorageService =
      SignatureStorageService();
  Uint8List? _signatureBytes;
  bool _hasSignature = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    // Check if user already has a signature
    _hasSignature = widget.userProfile.signatureImageUrl != null;
  }

  void _onSignatureChanged(Uint8List? signatureBytes) {
    setState(() {
      _signatureBytes = signatureBytes;
      _hasSignature = signatureBytes != null;
    });
  }

  void _skipSignature() {
    // Allow users to skip signature creation
    widget.onNext();
  }

  Future<void> _saveSignature() async {
    if (_signatureBytes != null) {
      setState(() {
        _isUploading = true;
      });

      try {
        // Upload signature to Supabase storage
        final signatureUrl = await _signatureStorageService.uploadUserSignature(
          _signatureBytes!,
        );

        final updatedProfile = widget.userProfile.copyWith(
          signatureImageUrl: signatureUrl,
        );

        widget.onProfileUpdated(updatedProfile);

        setState(() {
          _isUploading = false;
        });

        widget.onNext();
      } catch (e) {
        setState(() {
          _isUploading = false;
        });

        if (mounted) {
          ErrorDisplay.showWarning(
            context,
            'Failed to save signature: ${e.toString()}',
          );
        }
      }
    } else {
      widget.onNext();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Padding(
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 24.0),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Text(
                        'Create Your Signature',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 20 : 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      SizedBox(
                        height: displayProvider.isOfficeMode ? 8.0 : 12.0,
                      ),
                      Text(
                        'Create your digital signature to automatically sign documents you generate. This signature will be applied to contracts, estimates, and other documents.',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 14 : 16,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                          height: 1.4,
                        ),
                      ),
                      SizedBox(
                        height: displayProvider.isOfficeMode ? 20.0 : 32.0,
                      ),

                      // Signature creation widget
                      SignatureCreationWidget(
                        onSignatureChanged: _onSignatureChanged,
                        title: 'Your Digital Signature',
                        subtitle:
                            'This will appear on all documents you create',
                        showInstructions: true,
                        showClearButton: true,
                      ),

                      SizedBox(
                        height: displayProvider.isOfficeMode ? 20.0 : 32.0,
                      ),

                      // Benefits section
                      Container(
                        padding: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 16.0 : 20.0,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(
                              context,
                            ).colorScheme.outline.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.verified,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: displayProvider.isOfficeMode ? 18 : 20,
                                ),
                                SizedBox(
                                  width:
                                      displayProvider.isOfficeMode ? 8.0 : 12.0,
                                ),
                                Text(
                                  'Benefits of Digital Signatures',
                                  style: TextStyle(
                                    fontSize:
                                        displayProvider.isOfficeMode ? 16 : 18,
                                    fontWeight: FontWeight.w600,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height:
                                  displayProvider.isOfficeMode ? 12.0 : 16.0,
                            ),
                            _buildBenefitItem(
                              context,
                              displayProvider,
                              Icons.speed,
                              'Automatic Application',
                              'Your signature is automatically added to all documents',
                            ),
                            _buildBenefitItem(
                              context,
                              displayProvider,
                              Icons.security,
                              'Professional Appearance',
                              'Consistent, professional signature on all documents',
                            ),
                            _buildBenefitItem(
                              context,
                              displayProvider,
                              Icons.access_time,
                              'Time Saving',
                              'No need to manually sign each document',
                            ),
                            _buildBenefitItem(
                              context,
                              displayProvider,
                              Icons.edit,
                              'Easy Updates',
                              'Change your signature anytime in Settings',
                            ),
                          ],
                        ),
                      ),

                      SizedBox(
                        height: displayProvider.isOfficeMode ? 16.0 : 24.0,
                      ),

                      // Optional note
                      Container(
                        padding: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 12.0 : 16.0,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber[300]!),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Colors.amber[700],
                              size: displayProvider.isOfficeMode ? 18 : 20,
                            ),
                            SizedBox(
                              width: displayProvider.isOfficeMode ? 8.0 : 12.0,
                            ),
                            Expanded(
                              child: Text(
                                'You can skip this step and create your signature later in Settings if you prefer.',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                  color: Colors.amber[800],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Navigation buttons
              SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 24.0),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: widget.isLoading ? null : widget.onPrevious,
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 12 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Back',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 16 : 18,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: widget.isLoading ? null : _skipSignature,
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 12 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Skip',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 16 : 18,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed:
                          (widget.isLoading || _isUploading)
                              ? null
                              : _saveSignature,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 12 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child:
                          (widget.isLoading || _isUploading)
                              ? SizedBox(
                                height: displayProvider.isOfficeMode ? 20 : 24,
                                width: displayProvider.isOfficeMode ? 20 : 24,
                                child: InlineLoadingIndicator(
                                  text: '',
                                  size:
                                      displayProvider.isOfficeMode
                                          ? 16.0
                                          : 20.0,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                _hasSignature ? 'Save Signature' : 'Continue',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 16 : 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBenefitItem(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    IconData icon,
    String title,
    String description,
  ) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: displayProvider.isOfficeMode ? 8.0 : 12.0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: displayProvider.isOfficeMode ? 16 : 18,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 14 : 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 2.0 : 4.0),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to encode bytes to base64
String base64Encode(Uint8List bytes) {
  const String chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  String result = '';

  for (int i = 0; i < bytes.length; i += 3) {
    int byte1 = bytes[i];
    int byte2 = i + 1 < bytes.length ? bytes[i + 1] : 0;
    int byte3 = i + 2 < bytes.length ? bytes[i + 2] : 0;

    int combined = (byte1 << 16) | (byte2 << 8) | byte3;

    result += chars[(combined >> 18) & 63];
    result += chars[(combined >> 12) & 63];
    result += i + 1 < bytes.length ? chars[(combined >> 6) & 63] : '=';
    result += i + 2 < bytes.length ? chars[combined & 63] : '=';
  }

  return result;
}
