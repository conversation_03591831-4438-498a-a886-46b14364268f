import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';

class SettingsReviewStep extends StatefulWidget {
  final UserProfile userProfile;
  final UserSettings userSettings;
  final Function(UserProfile) onProfileUpdated;
  final Function(UserSettings) onSettingsUpdated;
  final VoidCallback onComplete;
  final VoidCallback onPrevious;
  final bool isLoading;

  const SettingsReviewStep({
    super.key,
    required this.userProfile,
    required this.userSettings,
    required this.onProfileUpdated,
    required this.onSettingsUpdated,
    required this.onComplete,
    required this.onPrevious,
    required this.isLoading,
  });

  @override
  State<SettingsReviewStep> createState() => _SettingsReviewStepState();
}

class _SettingsReviewStepState extends State<SettingsReviewStep> {
  late UserSettings _currentSettings;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.userSettings;
  }

  void _updateSetting<T>(T value, UserSettings Function(T) updater) {
    setState(() {
      _currentSettings = updater(value);
    });
    widget.onSettingsUpdated(_currentSettings);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Padding(
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 24.0),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Text(
                        'Review & Customize Settings',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 20 : 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      SizedBox(
                        height: displayProvider.isOfficeMode ? 8.0 : 12.0,
                      ),
                      Text(
                        'Review your information and customize default settings. You can change these anytime in Settings.',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 14 : 16,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                          height: 1.4,
                        ),
                      ),
                      SizedBox(
                        height: displayProvider.isOfficeMode ? 20.0 : 32.0,
                      ),

                      // Profile summary
                      AdaptiveFormSection(
                        title: 'Your Profile',
                        icon: Icons.person,
                        children: [
                          _buildInfoRow(
                            context,
                            displayProvider,
                            'Name',
                            widget.userProfile.fullName.isNotEmpty
                                ? widget.userProfile.fullName
                                : 'Not provided',
                          ),
                          _buildInfoRow(
                            context,
                            displayProvider,
                            'Email',
                            widget.userProfile.email ?? 'Not provided',
                          ),
                          if (widget.userProfile.phone?.isNotEmpty == true)
                            _buildInfoRow(
                              context,
                              displayProvider,
                              'Phone',
                              widget.userProfile.phone!,
                            ),
                        ],
                      ),

                      // Business summary
                      if (widget.userSettings.businessName?.isNotEmpty ==
                              true ||
                          widget.userSettings.businessEmail?.isNotEmpty == true)
                        AdaptiveFormSection(
                          title: 'Business Information',
                          icon: Icons.business,
                          children: [
                            if (widget.userSettings.businessName?.isNotEmpty ==
                                true)
                              _buildInfoRow(
                                context,
                                displayProvider,
                                'Business Name',
                                widget.userSettings.businessName!,
                              ),
                            if (widget.userSettings.businessEmail?.isNotEmpty ==
                                true)
                              _buildInfoRow(
                                context,
                                displayProvider,
                                'Business Email',
                                widget.userSettings.businessEmail!,
                              ),
                          ],
                        ),

                      // App preferences
                      AdaptiveFormSection(
                        title: 'App Preferences',
                        icon: Icons.settings,
                        children: [
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'Voice Input',
                            'Enable voice input for text fields',
                            _currentSettings.enableVoiceInput,
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                enableVoiceInput: val,
                              ),
                            ),
                          ),
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'Offline Mode',
                            'Work offline with automatic sync',
                            _currentSettings.enableOfflineMode,
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                enableOfflineMode: val,
                              ),
                            ),
                          ),
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'WiFi Only Sync',
                            'Only sync data when connected to WiFi',
                            _currentSettings.wifiOnlySync,
                            (value) => _updateSetting(
                              value,
                              (val) =>
                                  _currentSettings.copyWith(wifiOnlySync: val),
                            ),
                          ),
                        ],
                      ),

                      // Display settings
                      AdaptiveFormSection(
                        title: 'Display Settings',
                        icon: Icons.display_settings,
                        children: [
                          _buildDisplayModeSelector(context, displayProvider),
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'Enhanced Contrast',
                            'Better visibility in bright conditions',
                            _currentSettings.enhancedContrastMode,
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                enhancedContrastMode: val,
                              ),
                            ),
                          ),
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'Dynamic Color Adjustment',
                            'Automatically adjust colors based on lighting',
                            _currentSettings.dynamicColorAdjustment,
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                dynamicColorAdjustment: val,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Invoice settings
                      AdaptiveFormSection(
                        title: 'Invoice Settings',
                        icon: Icons.receipt_long,
                        children: [
                          _buildDropdownTile(
                            context,
                            displayProvider,
                            'Default Due Days',
                            'Default number of days for invoice due dates',
                            _currentSettings.defaultInvoiceDueDays,
                            [15, 30, 45, 60, 90],
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                defaultInvoiceDueDays: val,
                              ),
                            ),
                          ),
                          _buildSwitchTile(
                            context,
                            displayProvider,
                            'Due Date Notifications',
                            'Get notified about upcoming invoice due dates',
                            _currentSettings.enableDueDateNotifications,
                            (value) => _updateSetting(
                              value,
                              (val) => _currentSettings.copyWith(
                                enableDueDateNotifications: val,
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Completion note
                      Container(
                        margin: EdgeInsets.only(
                          top: displayProvider.isOfficeMode ? 16.0 : 24.0,
                        ),
                        padding: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 12.0 : 16.0,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).colorScheme.primaryContainer.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Theme.of(
                              context,
                            ).colorScheme.primary.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              color: Theme.of(context).colorScheme.primary,
                              size: displayProvider.isOfficeMode ? 20 : 24,
                            ),
                            SizedBox(
                              width: displayProvider.isOfficeMode ? 8.0 : 12.0,
                            ),
                            Expanded(
                              child: Text(
                                'You\'re all set! Click "Complete Setup" to start using Quarterlies.',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.8),
                                  height: 1.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Navigation buttons
              SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 24.0),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: widget.isLoading ? null : widget.onPrevious,
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 12 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Back',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 16 : 18,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 12.0 : 16.0),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: widget.isLoading ? null : widget.onComplete,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          vertical: displayProvider.isOfficeMode ? 12 : 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child:
                          widget.isLoading
                              ? SizedBox(
                                height: displayProvider.isOfficeMode ? 20 : 24,
                                width: displayProvider.isOfficeMode ? 20 : 24,
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(
                                'Complete Setup',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 16 : 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    String label,
    String value,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 4.0 : 6.0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: displayProvider.isOfficeMode ? 80 : 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 14 : 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle.isNotEmpty) ...[
                  SizedBox(height: displayProvider.isOfficeMode ? 2.0 : 4.0),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    String title,
    String subtitle,
    int value,
    List<int> options,
    Function(int) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 14 : 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle.isNotEmpty) ...[
                  SizedBox(height: displayProvider.isOfficeMode ? 2.0 : 4.0),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ],
            ),
          ),
          DropdownButton<int>(
            value: value,
            onChanged: (newValue) {
              if (newValue != null) {
                onChanged(newValue);
              }
            },
            items:
                options.map((option) {
                  return DropdownMenuItem<int>(
                    value: option,
                    child: Text(
                      '$option days',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayModeSelector(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Display Mode',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 14 : 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),
          Row(
            children: [
              Expanded(
                child: _buildDisplayModeOption(
                  context,
                  displayProvider,
                  'Field Mode',
                  'Large touch targets, ideal for field work',
                  DisplayMode.field,
                  Icons.touch_app,
                ),
              ),
              SizedBox(width: displayProvider.isOfficeMode ? 8.0 : 12.0),
              Expanded(
                child: _buildDisplayModeOption(
                  context,
                  displayProvider,
                  'Office Mode',
                  'Compact layout, more information density',
                  DisplayMode.office,
                  Icons.desktop_windows,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayModeOption(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    String title,
    String description,
    DisplayMode mode,
    IconData icon,
  ) {
    final isSelected = _currentSettings.displayMode == mode;

    return GestureDetector(
      onTap:
          () => _updateSetting(
            mode,
            (val) => _currentSettings.copyWith(displayMode: val),
          ),
      child: Container(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withValues(alpha: 0.3)
                  : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: displayProvider.isOfficeMode ? 24 : 32,
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 6.0 : 8.0),
            Text(
              title,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 12 : 14,
                fontWeight: FontWeight.w600,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 4.0 : 6.0),
            Text(
              description,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 10 : 12,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
