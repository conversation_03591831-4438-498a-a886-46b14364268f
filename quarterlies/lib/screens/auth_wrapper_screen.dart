import 'package:flutter/material.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';

/// A wrapper screen that checks onboarding completion status
/// and redirects authenticated users to the appropriate screen
class AuthWrapperScreen extends StatefulWidget {
  const AuthWrapperScreen({super.key});

  @override
  State<AuthWrapperScreen> createState() => _AuthWrapperScreenState();
}

class _AuthWrapperScreenState extends State<AuthWrapperScreen> {
  final DataRepository _dataRepository = DataRepository();
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  Future<void> _checkOnboardingStatus() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Check if user has completed onboarding
      final isOnboardingComplete = await _dataRepository.isOnboardingComplete();

      if (mounted) {
        if (isOnboardingComplete) {
          // User has completed onboarding, go to main app
          Navigator.pushReplacementNamed(context, '/main');
        } else {
          // User needs to complete onboarding
          Navigator.pushReplacementNamed(context, '/onboarding');
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  void _retry() {
    _checkOnboardingStatus();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: QuarterliesLoadingIndicator(
            message: 'Setting up your account...',
            size: 32.0,
            showOfflineStatus: true,
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Setup Error',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _retry,
                  child: const Text('Try Again'),
                ),
                const SizedBox(height: 12),
                TextButton(
                  onPressed: () {
                    // Force go to onboarding if there's an error
                    Navigator.pushReplacementNamed(context, '/onboarding');
                  },
                  child: const Text('Continue to Setup'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // This should not be reached, but just in case
    return const Scaffold(
      body: Center(
        child: QuarterliesLoadingIndicator(message: 'Loading...', size: 32.0),
      ),
    );
  }
}
