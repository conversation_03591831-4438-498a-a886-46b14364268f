import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/customer.dart';
import 'package:quarterlies/models/job.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/customers/customer_detail_screen.dart';
import 'package:quarterlies/screens/jobs/job_detail_screen.dart';
import 'package:quarterlies/services/voice_search_service.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

/// A screen for searching customers and jobs using voice input
class VoiceSearchScreen extends StatefulWidget {
  const VoiceSearchScreen({super.key});

  @override
  State<VoiceSearchScreen> createState() => _VoiceSearchScreenState();
}

class _VoiceSearchScreenState extends State<VoiceSearchScreen> {
  final VoiceSearchService _voiceSearchService = VoiceSearchService();
  final TextEditingController _searchController = TextEditingController();

  bool _isSearching = false;
  String? _searchQuery;
  List<Customer> _customers = [];
  List<Job> _jobs = [];
  AppError? _currentError;

  @override
  void initState() {
    super.initState();

    // Listen for search results
    _voiceSearchService.searchResults.listen((results) {
      setState(() {
        if (results.containsKey('error')) {
          _currentError = AppError.fromException(
            Exception(results['error']),
            context: {
              'operation': 'voiceSearch',
              'screen': 'VoiceSearchScreen',
            },
          );
          _isSearching = false;
        } else if (results['status'] == 'processing') {
          _searchQuery = results['query'];
          _isSearching = true;
          _currentError = null;
        } else if (results['status'] == 'completed') {
          _searchQuery = results['query'];
          _customers = results['customers'];
          _jobs = results['jobs'];
          _isSearching = false;
          _currentError = null;
        } else if (results['status'] == 'empty') {
          _searchQuery = null;
          _customers = [];
          _jobs = [];
          _isSearching = false;
          _currentError = null;
        }
      });
    });
  }

  @override
  void dispose() {
    _voiceSearchService.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _startVoiceSearch() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithVoiceLoading(
        () async {
          setState(() {
            _currentError = null;
          });

          await _voiceSearchService.startVoiceSearch();

          setState(() {
            _isSearching = true;
          });
        },
        operationName: 'Voice search',
        isRecording: true,
      );
    } catch (e) {
      setState(() {
        _currentError = AppError.fromException(
          e,
          context: {'operation': 'startVoiceSearch'},
        );
      });
    }
  }

  void _stopVoiceSearch() async {
    await _voiceSearchService.stopVoiceSearch();
  }

  void _performTextSearch() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'textSearch',
        () async {
          setState(() {
            _currentError = null;
            _isSearching = true;
          });

          _voiceSearchService.textSearch(query);
        },
        message: 'Searching...',
        errorMessage: 'Search failed',
      );
    } catch (e) {
      setState(() {
        _currentError = AppError.fromException(
          e,
          context: {'operation': 'textSearch', 'query': query},
        );
        _isSearching = false;
      });
    }
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = null;
      _customers = [];
      _jobs = [];
      _currentError = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Search',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          body: Column(
            children: [
              // Search bar
              Padding(
                padding: EdgeInsets.all(
                  displayProvider.isOfficeMode ? 12.0 : 16.0,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search customers or jobs...',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          suffixIcon:
                              _searchController.text.isNotEmpty
                                  ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: _clearSearch,
                                  )
                                  : null,
                        ),
                        onSubmitted: (_) => _performTextSearch(),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Voice search button
                    IconButton(
                      icon:
                          _voiceSearchService.isRecording
                              ? const Icon(Icons.stop_circle, color: Colors.red)
                              : const Icon(Icons.mic),
                      onPressed:
                          _voiceSearchService.isRecording
                              ? _stopVoiceSearch
                              : _startVoiceSearch,
                      tooltip:
                          _voiceSearchService.isRecording
                              ? 'Stop voice search'
                              : 'Search with voice',
                      iconSize: 32,
                      style: IconButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.primaryContainer,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // Error message
              if (_currentError != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: ErrorDisplay.buildErrorArea(
                    _currentError!,
                    onRetry: () {
                      setState(() {
                        _currentError = null;
                      });
                      // Retry the last search if there was a query
                      if (_searchQuery != null) {
                        _voiceSearchService.textSearch(_searchQuery!);
                      }
                    },
                    onDismiss: () {
                      setState(() {
                        _currentError = null;
                      });
                    },
                  ),
                ),

              // Search status with voice loading indicator
              Consumer<LoadingStateProvider>(
                builder: (context, loadingProvider, child) {
                  if (loadingProvider.isProcessingVoice) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: VoiceLoadingIndicator(
                          operation:
                              loadingProvider.voiceOperation ??
                              'Processing voice...',
                          isRecording: loadingProvider.isRecording,
                        ),
                      ),
                    );
                  } else if (_isSearching ||
                      loadingProvider.isLoading('textSearch')) {
                    return Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: QuarterliesLoadingIndicator(
                          message:
                              loadingProvider.getLoadingMessage('textSearch') ??
                              'Searching...',
                          size: 32.0,
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Search query
              if (_searchQuery != null && !_isSearching)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    'Results for: "$_searchQuery"',
                    style: const TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),

              // Results
              if (!_isSearching && (_customers.isNotEmpty || _jobs.isNotEmpty))
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(16.0),
                    children: [
                      // Customers section
                      if (_customers.isNotEmpty) ...[
                        const Text(
                          'Customers',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._customers.map(
                          (customer) => _buildCustomerItem(customer),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Jobs section
                      if (_jobs.isNotEmpty) ...[
                        const Text(
                          'Jobs',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._jobs.map((job) => _buildJobItem(job)),
                      ],
                    ],
                  ),
                ),

              // No results
              if (!_isSearching &&
                  _searchQuery != null &&
                  _customers.isEmpty &&
                  _jobs.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: Text(
                      'No results found',
                      style: TextStyle(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCustomerItem(Customer customer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        title: Text(customer.name),
        subtitle: Text(customer.address ?? 'No address'),
        leading: const CircleAvatar(child: Icon(Icons.person)),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => CustomerDetailScreen(customerId: customer.id),
            ),
          );
        },
      ),
    );
  }

  Widget _buildJobItem(Job job) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        title: Text(job.title),
        subtitle: Text(job.address ?? 'No address'),
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(job.status),
          child: const Icon(Icons.work, color: Colors.white),
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => JobDetailScreen(jobId: job.id),
            ),
          );
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'estimate':
        return Colors.blue;
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.orange;
      case 'invoiced':
        return Colors.purple;
      case 'paid':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
