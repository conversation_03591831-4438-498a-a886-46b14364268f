import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:share_plus/share_plus.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class DocumentViewerScreen extends StatefulWidget {
  final String title;
  final Uint8List documentBytes;

  const DocumentViewerScreen({
    super.key,
    required this.title,
    required this.documentBytes,
  });

  @override
  State<DocumentViewerScreen> createState() => _DocumentViewerScreenState();
}

class _DocumentViewerScreenState extends State<DocumentViewerScreen> {
  String? _pdfPath;
  String? _errorMessage;
  int _totalPages = 0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadDocument',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get temporary directory to store the PDF file
          final directory = await getTemporaryDirectory();
          final filePath =
              '${directory.path}/document_${DateTime.now().millisecondsSinceEpoch}.pdf';

          // Write document bytes to file
          final file = File(filePath);
          await file.writeAsBytes(widget.documentBytes);

          if (!mounted) return;

          setState(() {
            _pdfPath = filePath;
          });
        },
        message: 'Loading document...',
        errorMessage: 'Failed to load document',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load document: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.title,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            actions: [
              // Print button
              IconButton(
                icon: const Icon(Icons.print),
                onPressed: _pdfPath != null ? _printDocument : null,
                tooltip: 'Print Document',
              ),
              // Share button
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _pdfPath != null ? _shareDocument : null,
                tooltip: 'Share Document',
              ),
              // Page indicator
              if (_totalPages > 0)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Center(
                    child: Text(
                      'Page ${_currentPage + 1} of $_totalPages',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
            ],
          ),
          body: Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading = loadingProvider.isLoading('loadDocument');

              return isLoading
                  ? const Center(
                    child: QuarterliesLoadingIndicator(
                      message: 'Loading document...',
                      size: 32.0,
                      showOfflineStatus: true,
                    ),
                  )
                  : _errorMessage != null
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadDocument,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                  : _pdfPath == null
                  ? const Center(child: Text('Document not available'))
                  : PDFView(
                    filePath: _pdfPath!,
                    enableSwipe: true,
                    swipeHorizontal: true,
                    autoSpacing: true,
                    pageFling: true,
                    pageSnap: true,
                    onRender: (pages) {
                      setState(() {
                        _totalPages = pages!;
                      });
                    },
                    onPageChanged: (page, total) {
                      setState(() {
                        _currentPage = page!;
                      });
                    },
                    onError: (error) {
                      setState(() {
                        _errorMessage = error.toString();
                      });
                    },
                  );
            },
          ),
        );
      },
    );
  }

  Future<void> _printDocument() async {
    try {
      await Printing.layoutPdf(
        onLayout: (format) async => widget.documentBytes,
        name: widget.title,
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error printing document: $e');
      }
    }
  }

  Future<void> _shareDocument() async {
    try {
      if (_pdfPath != null) {
        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(_pdfPath!)],
            text: widget.title,
            subject: widget.title,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error sharing document: $e');
      }
    }
  }

  @override
  void dispose() {
    // Clean up temporary file when done
    if (_pdfPath != null) {
      try {
        File(_pdfPath!).delete();
      } catch (e) {
        // Ignore errors on cleanup
      }
    }
    super.dispose();
  }
}
