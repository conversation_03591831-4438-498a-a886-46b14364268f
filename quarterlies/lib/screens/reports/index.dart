import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/screens/tax_export/index.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/models/report_models.dart' as report_models;
import 'package:quarterlies/services/report_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final DataRepository _dataRepository = DataRepository();
  late final ReportService _reportService;

  // Filter state
  report_models.TimePeriod _selectedTimePeriod =
      report_models.TimePeriod.currentYear;
  report_models.DateRange? _customDateRange;
  List<Job> _availableJobs = [];
  final List<String> _selectedJobIds = [];
  bool _includeAllJobs = true;
  report_models.ReportType? _selectedReportType;

  // UI state
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _reportService = ReportService(_dataRepository);
    _loadJobs();
  }

  Future<void> _loadJobs() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobsForReports',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final jobs = await _dataRepository.getJobs();
          setState(() {
            _availableJobs = jobs;
          });
        },
        message: 'Loading report data...',
        errorMessage: 'Failed to load report data',
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {'operation': 'loadJobsForReports', 'screen': 'ReportsScreen'},
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });
      }
    }
  }

  Future<void> _generateAndShareReport() async {
    if (_selectedReportType == null) {
      ErrorDisplay.showWarning(context, 'Please select a report type');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        setState(() {
          _errorMessage = null;
        });

        final filters = report_models.ReportFilters(
          timePeriod: _selectedTimePeriod,
          customDateRange: _customDateRange,
          selectedJobIds: _selectedJobIds,
          includeAllJobs: _includeAllJobs,
        );

        final reportData = await _reportService.generateReport(
          reportType: _selectedReportType!,
          filters: filters,
        );

        final pdfBytes = await _reportService.generateReportPdf(reportData);
        final filePath = await _reportService.savePdfToFile(
          pdfBytes,
          reportData.fileName,
        );

        // Share the PDF
        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(filePath)],
            text: 'Business Report: ${reportData.title}',
            subject: reportData.title,
          ),
        );

        if (mounted) {
          ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);
        }
      }, documentType: _selectedReportType!.displayName);
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'generateAndShareReport',
            'reportType': _selectedReportType?.displayName,
            'timePeriod': _selectedTimePeriod.displayName,
            'includeAllJobs': _includeAllJobs,
            'selectedJobIds': _selectedJobIds,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _generateAndShareReport(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DisplaySettingsProvider, LoadingStateProvider>(
      builder: (context, displayProvider, loadingProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle('Business Reports'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: spacing.ResponsiveSpacing.getElevation(context),
            toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          ),
          body: ResponsiveLayout(
            child:
                loadingProvider.isLoading('loadJobsForReports')
                    ? const Center(
                      child: QuarterliesLoadingIndicator(
                        message: 'Loading report data...',
                        size: 32.0,
                      ),
                    )
                    : SingleChildScrollView(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 16,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Error message
                          if (_errorMessage != null)
                            Padding(
                              padding: EdgeInsets.only(
                                bottom: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: 16,
                                ),
                              ),
                              child: ErrorDisplay.buildErrorArea(
                                AppError(
                                  type: ErrorType.unknown,
                                  severity: ErrorSeverity.medium,
                                  message: _errorMessage!,
                                  userFriendlyMessage: _errorMessage!,
                                  context: {
                                    'screen': 'ReportsScreen',
                                    'operation': 'displayError',
                                  },
                                ),
                                onRetry: () => _loadJobs(),
                                onDismiss:
                                    () => setState(() => _errorMessage = null),
                              ),
                            ),

                          // Tax Export Card (existing functionality)
                          Card(
                            elevation: 2,
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const TaxExportScreen(),
                                  ),
                                );
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Padding(
                                padding: EdgeInsets.all(
                                  displayProvider.isOfficeMode ? 16.0 : 20.0,
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(
                                        displayProvider.isOfficeMode
                                            ? 12.0
                                            : 16.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary.withAlpha(51),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.file_download,
                                        size:
                                            displayProvider.isOfficeMode
                                                ? 24
                                                : 32,
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                      ),
                                    ),
                                    SizedBox(
                                      width:
                                          displayProvider.isOfficeMode
                                              ? 12
                                              : 16,
                                    ),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Tax Data Export',
                                            style: TextStyle(
                                              fontSize:
                                                  displayProvider.isOfficeMode
                                                      ? 16
                                                      : 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(
                                            height:
                                                displayProvider.isOfficeMode
                                                    ? 4
                                                    : 8,
                                          ),
                                          Text(
                                            'Export income, expenses, mileage, and tax payments for tax preparation',
                                            style: TextStyle(
                                              fontSize:
                                                  displayProvider.isOfficeMode
                                                      ? 12
                                                      : 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.chevron_right,
                                      size:
                                          displayProvider.isOfficeMode
                                              ? 20
                                              : 24,
                                      color: Colors.grey[400],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),

                          SizedBox(
                            height: displayProvider.isOfficeMode ? 24 : 32,
                          ),

                          // Business Reports Section
                          ResponsiveSubtitle(
                            'Business Reports',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 12,
                            ),
                          ),
                          ResponsiveBody(
                            'Generate comprehensive business reports with downloadable PDFs',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 24,
                            ),
                          ),

                          // Report Type Selection
                          _buildReportTypeSelection(displayProvider),

                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 24,
                            ),
                          ),

                          // Time Period Selection
                          _buildTimePeriodSelection(displayProvider),

                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 24,
                            ),
                          ),

                          // Job Selection
                          _buildJobSelection(displayProvider),

                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 32,
                            ),
                          ),

                          // Generate Report Button
                          _buildGenerateReportButton(displayProvider),

                          SizedBox(
                            height: spacing.ResponsiveSpacing.getSpacing(
                              context,
                              base: 24,
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  Widget _buildReportTypeSelection(DisplaySettingsProvider displayProvider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Report Type',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
            ...report_models.ReportType.values.map((reportType) {
              return RadioListTile<report_models.ReportType>(
                title: Text(
                  reportType.displayName,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 14 : 16,
                  ),
                ),
                subtitle: Text(
                  reportType.description,
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    color: Colors.grey[600],
                  ),
                ),
                value: reportType,
                groupValue: _selectedReportType,
                onChanged: (value) {
                  setState(() {
                    _selectedReportType = value;
                  });
                },
                contentPadding: EdgeInsets.zero,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTimePeriodSelection(DisplaySettingsProvider displayProvider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Time Period',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
            DropdownButtonFormField<report_models.TimePeriod>(
              value: _selectedTimePeriod,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Select Time Period',
              ),
              items:
                  report_models.TimePeriod.values.map((period) {
                    return DropdownMenuItem(
                      value: period,
                      child: Text(period.displayName),
                    );
                  }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTimePeriod = value!;
                  if (value != report_models.TimePeriod.custom) {
                    _customDateRange = null;
                  }
                });
              },
            ),
            if (_selectedTimePeriod == report_models.TimePeriod.custom) ...[
              SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectCustomDate(true),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          labelText: 'Start Date',
                        ),
                        child: Text(
                          _customDateRange?.startDate != null
                              ? DateFormat(
                                'MM/dd/yyyy',
                              ).format(_customDateRange!.startDate)
                              : 'Select start date',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectCustomDate(false),
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          labelText: 'End Date',
                        ),
                        child: Text(
                          _customDateRange?.endDate != null
                              ? DateFormat(
                                'MM/dd/yyyy',
                              ).format(_customDateRange!.endDate)
                              : 'Select end date',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _selectCustomDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isStartDate
              ? (_customDateRange?.startDate ?? DateTime.now())
              : (_customDateRange?.endDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _customDateRange = report_models.DateRange(
            startDate: picked,
            endDate:
                _customDateRange?.endDate ??
                picked.add(const Duration(days: 30)),
          );
        } else {
          _customDateRange = report_models.DateRange(
            startDate:
                _customDateRange?.startDate ??
                picked.subtract(const Duration(days: 30)),
            endDate: picked,
          );
        }
      });
    }
  }

  Widget _buildJobSelection(DisplaySettingsProvider displayProvider) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Job Selection',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
            SwitchListTile(
              title: Text(
                'Include All Jobs',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 14 : 16,
                ),
              ),
              subtitle: Text(
                'Include data from all jobs in the report',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                  color: Colors.grey[600],
                ),
              ),
              value: _includeAllJobs,
              onChanged: (value) {
                setState(() {
                  _includeAllJobs = value;
                  if (value) {
                    _selectedJobIds.clear();
                  }
                });
              },
              contentPadding: EdgeInsets.zero,
            ),
            if (!_includeAllJobs && _availableJobs.isNotEmpty) ...[
              SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),
              Text(
                'Select Jobs:',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 14 : 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: displayProvider.isOfficeMode ? 8 : 12),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _availableJobs.length,
                  itemBuilder: (context, index) {
                    final job = _availableJobs[index];
                    return CheckboxListTile(
                      title: Text(
                        job.title,
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 14 : 16,
                        ),
                      ),
                      subtitle: Text(
                        'Status: ${job.status}',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      value: _selectedJobIds.contains(job.id),
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            _selectedJobIds.add(job.id);
                          } else {
                            _selectedJobIds.remove(job.id);
                          }
                        });
                      },
                      dense: displayProvider.isOfficeMode,
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateReportButton(DisplaySettingsProvider displayProvider) {
    return Consumer<LoadingStateProvider>(
      builder: (context, loadingProvider, child) {
        return SizedBox(
          width: double.infinity,
          height: displayProvider.isOfficeMode ? 48 : 56,
          child: ElevatedButton.icon(
            onPressed:
                loadingProvider.isGeneratingPdf
                    ? null
                    : _generateAndShareReport,
            icon:
                loadingProvider.isGeneratingPdf
                    ? PdfLoadingIndicator(
                      documentType: loadingProvider.pdfDocumentType ?? 'Report',
                      progress: loadingProvider.pdfProgress,
                    )
                    : const Icon(Icons.file_download),
            label: Text(
              loadingProvider.isGeneratingPdf
                  ? 'Generating ${loadingProvider.pdfDocumentType ?? 'Report'} PDF...'
                  : 'Generate & Share Report',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        );
      },
    );
  }
}
