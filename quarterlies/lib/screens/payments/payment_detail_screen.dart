import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'payment_form_screen.dart';

class PaymentDetailScreen extends StatefulWidget {
  final String paymentId;

  const PaymentDetailScreen({super.key, required this.paymentId});

  @override
  State<PaymentDetailScreen> createState() => _PaymentDetailScreenState();
}

class _PaymentDetailScreenState extends State<PaymentDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  Payment? _payment;
  dynamic _invoice; // Can be Invoice or Map<String, dynamic>
  Job? _job;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPaymentData();
  }

  Future<void> _loadPaymentData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadPaymentData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get payment by ID
          final payments = await _supabaseService.getPayments();
          final payment = payments.firstWhere((p) => p.id == widget.paymentId);

          // Get related invoice if available
          dynamic invoice;
          if (payment.invoiceId != null && payment.invoiceId!.isNotEmpty) {
            invoice = await _supabaseService.getInvoiceById(payment.invoiceId!);
          }

          // Get related job
          Job? job;
          if (payment.jobId != null) {
            job = await _supabaseService.getJobById(payment.jobId!);
          }

          if (!mounted) return;

          setState(() {
            _payment = payment;
            _invoice = invoice;
            _job = job;
          });
        },
        message: 'Loading payment data...',
        errorMessage: 'Failed to load payment data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load payment data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deletePayment() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this payment? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
        ) ??
        false;

    if (confirmed) {
      try {
        await _supabaseService.deletePayment(widget.paymentId);

        // If this payment was linked to an invoice, update the invoice's amount paid
        if (_payment?.invoiceId != null &&
            _invoice != null &&
            _payment != null) {
          // Calculate the updated amount paid
          double updatedAmountPaid;
          if (_invoice is Map) {
            updatedAmountPaid =
                (_invoice['amount_paid'] ?? 0.0) - _payment!.amountReceived;
          } else {
            updatedAmountPaid = _invoice!.amountPaid - _payment!.amountReceived;
          }
          // Ensure amount paid is not negative
          updatedAmountPaid = updatedAmountPaid < 0 ? 0.0 : updatedAmountPaid;
          // Determine the updated status based on payment amount and due date
          String updatedStatus;
          final totalAmount =
              _invoice is Map
                  ? _invoice['total_amount']
                  : _invoice!.totalAmount;

          if (updatedAmountPaid >= totalAmount) {
            updatedStatus = 'paid';
          } else {
            // Check if invoice is overdue
            DateTime dueDate;
            if (_invoice is Map) {
              dueDate = DateTime.parse(_invoice['due_date']);
            } else {
              dueDate = _invoice!.dueDate;
            }

            updatedStatus =
                dueDate.isBefore(DateTime.now()) ? 'overdue' : 'open';
          }

          // If invoice is a Map, we need to update it differently
          if (_invoice is Map<String, dynamic>) {
            final invoiceId = _invoice['id'];
            final updatedData = {
              'amount_paid': updatedAmountPaid,
              'status': updatedStatus,
            };
            await _supabaseService.updateInvoice(invoiceId, data: updatedData);
          } else {
            final updatedInvoice = _invoice!.copyWith(
              amountPaid: updatedAmountPaid,
              status: updatedStatus,
            );

            // Convert to Map for the API
            final invoiceId = updatedInvoice.id;
            final updatedData = {
              'amount_paid': updatedAmountPaid,
              'status': updatedStatus,
            };
            await _supabaseService.updateInvoice(invoiceId!, data: updatedData);
          }
        }

        if (mounted) {
          ErrorDisplay.showDataOperation(context, 'Payment', 'deleted');
          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ErrorDisplay.showWarning(
            context,
            'Error deleting payment: ${e.toString()}',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PaymentFormScreen(payment: _payment),
                ),
              ).then((_) => _loadPaymentData());
            },
          ),
          IconButton(icon: const Icon(Icons.delete), onPressed: _deletePayment),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadPaymentData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading payment data...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadPaymentData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _payment == null
              ? const Center(child: Text('Payment not found'))
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Payment details section
                        AdaptiveDetailSection(
                          title: 'Payment Details',
                          icon: Icons.payment,
                          alwaysExpandedInOffice: true,
                          children: [
                            if (displayProvider.isOfficeMode) ...[
                              // Office Mode: Compact grid layout
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Amount',
                                      value:
                                          '\$${_payment!.amountReceived.toStringAsFixed(2)}',
                                      icon: Icons.attach_money,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Date',
                                      value: DateFormat(
                                        'MM/dd/yyyy',
                                      ).format(_payment!.dateReceived),
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Method',
                                      value: _payment!.paymentMethod,
                                      icon: Icons.credit_card,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Job',
                                      value: _job?.title ?? 'Unknown',
                                      icon: Icons.work,
                                    ),
                                  ),
                                ],
                              ),
                              if (_invoice != null) ...[
                                const SizedBox(height: 8),
                                AdaptiveInfoRow(
                                  label: 'Invoice',
                                  value: '#${_getInvoiceNumber(_invoice)}',
                                  icon: Icons.receipt_long,
                                ),
                              ],
                              if (_payment!.notes != null &&
                                  _payment!.notes!.isNotEmpty) ...[
                                const SizedBox(height: 8),
                                AdaptiveInfoRow(
                                  label: 'Notes',
                                  value: _payment!.notes!,
                                  icon: Icons.note,
                                ),
                              ],
                            ] else ...[
                              // Field Mode: Existing layout
                              Text(
                                'Payment Amount: \$${_payment!.amountReceived.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Date Received: ${DateFormat('MM/dd/yyyy').format(_payment!.dateReceived)}',
                              ),
                              Text(
                                'Payment Method: ${_payment!.paymentMethod}',
                              ),
                              const SizedBox(height: 16),
                              Text('Job: ${_job?.title ?? 'Unknown'}'),
                              if (_invoice != null)
                                Text(
                                  'Invoice: #${_getInvoiceNumber(_invoice)}',
                                ),
                              if (_payment!.notes != null &&
                                  _payment!.notes!.isNotEmpty) ...[
                                const SizedBox(height: 16),
                                const Text(
                                  'Notes:',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(_payment!.notes!),
                              ],
                            ],
                          ],
                        ),

                        // Related invoice details if available
                        if (_invoice != null) ...[
                          const SizedBox(height: 24),
                          const Text(
                            'Related Invoice',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Invoice #${_getInvoiceNumber(_invoice)}',
                                      ),
                                      Chip(
                                        label: Text(
                                          _invoice is Map
                                              ? _invoice['status']
                                              : _invoice!.status,
                                        ),
                                        backgroundColor: _getStatusColor(
                                          _invoice is Map
                                              ? _invoice['status']
                                              : _invoice!.status,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Total Amount: \$${_getInvoiceAmount(_invoice, 'total_amount')}',
                                  ),
                                  Text(
                                    'Amount Paid: \$${_getInvoiceAmount(_invoice, 'amount_paid')}',
                                  ),
                                  Text(
                                    'Balance Due: \$${_getInvoiceBalanceDue(_invoice)}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          _getInvoiceBalanceDue(
                                                    _invoice,
                                                    asDouble: true,
                                                  ) >
                                                  0
                                              ? Colors.red
                                              : Colors.green,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  ElevatedButton(
                                    onPressed: () {
                                      Navigator.pushNamed(
                                        context,
                                        '/invoice_detail',
                                        arguments:
                                            _invoice is Map
                                                ? _invoice['id']
                                                : _invoice!.id,
                                      );
                                    },
                                    child: const Text('View Invoice'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              );
        },
      ),
    );
  }

  // Helper method to get invoice amount
  String _getInvoiceAmount(dynamic invoice, String field) {
    if (invoice == null) return "0.00";

    // Handle Map<String, dynamic> from Supabase
    if (invoice is Map<String, dynamic>) {
      final amount = invoice[field] ?? 0.0;
      return amount.toStringAsFixed(2);
    }

    // Handle Invoice object
    if (field == 'total_amount') {
      return invoice.totalAmount.toStringAsFixed(2);
    } else if (field == 'amount_paid') {
      return invoice.amountPaid.toStringAsFixed(2);
    }

    return "0.00";
  }

  // Helper method to get invoice balance due
  dynamic _getInvoiceBalanceDue(dynamic invoice, {bool asDouble = false}) {
    if (invoice == null) return asDouble ? 0.0 : "0.00";

    double totalAmount = 0.0;
    double amountPaid = 0.0;

    // Handle Map<String, dynamic> from Supabase
    if (invoice is Map<String, dynamic>) {
      totalAmount = invoice['total_amount'] ?? 0.0;
      amountPaid = invoice['amount_paid'] ?? 0.0;
    } else {
      // Handle Invoice object
      totalAmount = invoice.totalAmount;
      amountPaid = invoice.amountPaid;
    }

    final balanceDue = totalAmount - amountPaid;
    return asDouble ? balanceDue : balanceDue.toStringAsFixed(2);
  }

  // Helper method to get invoice number from either Invoice object or Map
  String _getInvoiceNumber(dynamic invoice) {
    if (invoice == null) return "Unknown";

    // Handle Map<String, dynamic> from Supabase
    if (invoice is Map<String, dynamic>) {
      final id = invoice['id'];
      if (id != null && id.toString().length >= 8) {
        return id.toString().substring(0, 8);
      }
      return "Unknown";
    }

    // Handle Invoice object
    if (invoice.id != null && invoice.id.toString().length >= 8) {
      return invoice.id.toString().substring(0, 8);
    }

    return "Unknown";
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
