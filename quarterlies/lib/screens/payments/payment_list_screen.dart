import 'dart:async'; // Added for StreamSubscription
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart'; // Changed from supabase_service
// Kept for now, might be removable if DataRepository handles all Supabase interactions
import 'package:intl/intl.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'payment_detail_screen.dart';
import 'payment_form_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

class PaymentListScreen extends StatefulWidget {
  final String? jobId;
  final String? invoiceId;

  const PaymentListScreen({super.key, this.jobId, this.invoiceId});

  @override
  State<PaymentListScreen> createState() => _PaymentListScreenState();
}

class _PaymentListScreenState extends State<PaymentListScreen> {
  final DataRepository _dataRepository = DataRepository(); // Use DataRepository
  List<Payment> _payments = [];
  bool _isOffline = false; // Added for offline status
  String? _errorMessage;
  StreamSubscription<bool>?
  _connectivitySubscription; // Added for connectivity listener

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener(); // Added
    _loadPayments();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel(); // Added
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());
    if (mounted) setState(() {});

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadPayments(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadPayments() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadPayments',
        () async {
          setState(() {
            _errorMessage = null;
          });

          List<Payment> payments;

          // Filter payments based on provided parameters
          if (widget.invoiceId != null) {
            payments = await _dataRepository.getPaymentsByInvoice(
              widget.invoiceId!,
            );
          } else if (widget.jobId != null) {
            payments = await _dataRepository.getPaymentsByJob(widget.jobId!);
          } else {
            payments = await _dataRepository.getPayments();
          }

          if (!mounted) return;

          setState(() {
            _payments = payments;
          });
        },
        message: 'Loading payments...',
        errorMessage: 'Failed to load payments',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load payments: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.invoiceId != null
              ? 'Invoice Payments'
              : widget.jobId != null
              ? 'Job Payments'
              : 'All Payments',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Icon(Icons.cloud_off, color: Colors.white),
                    SizedBox(width: 4),
                    Text('Offline', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadPayments),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadPayments');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading payments...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadPayments,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _payments.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('No payments found'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => PaymentFormScreen(
                                  jobId: widget.jobId,
                                  invoiceId: widget.invoiceId,
                                ),
                          ),
                        ).then((_) => _loadPayments());
                      },
                      child: const Text('Add Payment'),
                    ),
                  ],
                ),
              )
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return RefreshIndicator(
                    onRefresh: _loadPayments,
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(
                        horizontal: displayProvider.isOfficeMode ? 8.0 : 16.0,
                        vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
                      ),
                      itemCount: _payments.length,
                      itemBuilder: (context, index) {
                        final payment = _payments[index];
                        return AdaptiveListTile(
                          title: Text(
                            '\$${payment.amountReceived.toStringAsFixed(2)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            'Date: ${DateFormat('MM/dd/yyyy').format(payment.dateReceived)}',
                          ),
                          leading: const Icon(
                            Icons.payment,
                            color: Colors.green,
                          ),
                          trailing: Chip(
                            label: Text(payment.paymentMethod),
                            backgroundColor: Colors.green.withValues(
                              alpha: 0.1,
                            ),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => PaymentDetailScreen(
                                      paymentId: payment.id,
                                    ),
                              ),
                            ).then((_) => _loadPayments());
                          },
                          // Additional info shown only in Office Mode
                          additionalInfo:
                              displayProvider.isOfficeMode
                                  ? OfficeAdditionalInfo(
                                    items: [
                                      if (payment.notes != null &&
                                          payment.notes!.isNotEmpty)
                                        InfoItem(
                                          label: 'Notes',
                                          value:
                                              payment.notes!.length > 30
                                                  ? '${payment.notes!.substring(0, 30)}...'
                                                  : payment.notes!,
                                          icon: Icons.note,
                                        ),
                                      InfoItem(
                                        label: 'Created',
                                        value: DateFormat(
                                          'MM/dd/yyyy',
                                        ).format(payment.createdAt),
                                        icon: Icons.calendar_today,
                                      ),
                                      if (payment.voiceNoteUrl != null)
                                        const InfoItem(
                                          label: 'Voice Note',
                                          value: 'Available',
                                          icon: Icons.mic,
                                        ),
                                    ],
                                  )
                                  : null,
                          // Office actions shown only in Office Mode
                          officeActions:
                              displayProvider.isOfficeMode
                                  ? [
                                    OfficeActionButton(
                                      icon: Icons.edit,
                                      label: 'Edit',
                                      onPressed: () async {
                                        final result = await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) => PaymentFormScreen(
                                                  payment: payment,
                                                ),
                                          ),
                                        );
                                        if (result == true) {
                                          _loadPayments();
                                        }
                                      },
                                    ),
                                    OfficeActionButton(
                                      icon: Icons.receipt_long,
                                      label: 'Invoice',
                                      onPressed: () {
                                        ErrorDisplay.showInfo(
                                          context,
                                          'Invoice view coming soon!',
                                        );
                                      },
                                    ),
                                  ]
                                  : null,
                        );
                      },
                    ),
                  );
                },
              );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => PaymentFormScreen(
                    jobId: widget.jobId,
                    invoiceId: widget.invoiceId,
                  ),
            ),
          ).then((_) => _loadPayments());
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
