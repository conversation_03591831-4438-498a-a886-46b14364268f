import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = AuthService();
  String? _errorMessage;
  bool _acceptTerms = false; // Added for Terms of Service acceptance

  void _toggleAcceptTerms(bool? value) {
    setState(() {
      _acceptTerms = value ?? false;
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _signUp() async {
    if (_formKey.currentState!.validate() && _acceptTerms) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'signup',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.signUp(
              email: _emailController.text.trim(),
              password: _passwordController.text,
            );
            if (mounted) {
              // Automatically log in the user after successful registration
              await _authService.signIn(
                email: _emailController.text.trim(),
                password: _passwordController.text,
              );
              if (mounted) {
                // Redirect new users to onboarding flow
                Navigator.pushReplacementNamed(context, '/onboarding');
              }
            }
          },
          message: 'Creating account...',
          errorMessage: 'Failed to create account',
        );
      } catch (e) {
        if (mounted) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'signup',
              'email': _emailController.text.trim(),
            },
          );
          ErrorHandler.logError(appError);

          setState(() {
            _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          });

          // Show error dialog for authentication errors
          if (appError.type == ErrorType.authentication) {
            ErrorDisplay.showErrorDialog(
              context,
              appError,
              onRetry: () => _signUp(),
            );
          } else {
            // Show snackbar for other errors
            ErrorDisplay.showSnackBar(
              context,
              appError,
              onRetry: () => _signUp(),
            );
          }
        }
      }
    } else if (!_acceptTerms) {
      setState(() {
        _errorMessage = 'You must accept the terms of service to sign up';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Sign Up',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'Create Account',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    CustomTextField(
                      controller: _emailController,
                      labelText: 'Email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      validator:
                          (value) => InputValidators.validateEmail(value),
                    ),
                    CustomTextField(
                      controller: _passwordController,
                      labelText: 'Password',
                      hintText: 'Enter your password',
                      obscureText: true,
                      validator:
                          (value) => InputValidators.validatePassword(value),
                    ),
                    CustomTextField(
                      controller: _confirmPasswordController,
                      labelText: 'Confirm Password',
                      hintText: 'Confirm your password',
                      obscureText: true,
                      validator:
                          (value) => InputValidators.validateMatch(
                            value,
                            _passwordController.text,
                            'Password confirmation',
                          ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Checkbox(
                          value: _acceptTerms,
                          onChanged: _toggleAcceptTerms,
                        ),
                        const Expanded(
                          child: Text(
                            'I accept the Terms of Service',
                            style: TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    Consumer<LoadingStateProvider>(
                      builder: (context, loadingProvider, child) {
                        return CustomButton(
                          text: 'Sign Up',
                          onPressed: _signUp,
                          isLoading: loadingProvider.isLoading('signup'),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed: () {
                        Navigator.pushReplacementNamed(context, '/login');
                      },
                      child: const Text('Already have an account? Login'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
