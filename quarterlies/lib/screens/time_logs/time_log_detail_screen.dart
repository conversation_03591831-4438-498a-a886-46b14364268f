import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

class TimeLogDetailScreen extends StatefulWidget {
  final TimeLog timeLog;

  const TimeLogDetailScreen({super.key, required this.timeLog});

  @override
  State<TimeLogDetailScreen> createState() => _TimeLogDetailScreenState();
}

class _TimeLogDetailScreenState extends State<TimeLogDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  String? _errorMessage;
  Job? _job;

  @override
  void initState() {
    super.initState();
    _loadJobDetails();
  }

  Future<void> _loadJobDetails() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobDetails',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final job = await _supabaseService.getJobById(widget.timeLog.jobId);
          setState(() {
            _job = job;
          });
        },
        message: 'Loading job details...',
        errorMessage: 'Failed to load job details',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load job details: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deleteTimeLog() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this time log? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (!confirmed || !mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'deleteTimeLog',
        () async {
          setState(() {
            _errorMessage = null;
          });

          await _supabaseService.deleteTimeLog(widget.timeLog.id);
          if (mounted) {
            ErrorDisplay.showDataOperation(context, 'Time log', 'deleted');
            Navigator.pop(context, true); // Return true to indicate deletion
          }
        },
        message: 'Deleting time log...',
        errorMessage: 'Failed to delete time log',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to delete time log: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Log Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _navigateToEditScreen(),
          ),
          IconButton(icon: const Icon(Icons.delete), onPressed: _deleteTimeLog),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Consumer<LoadingStateProvider>(
      builder: (context, loadingProvider, child) {
        if (loadingProvider.isLoading('loadJobDetails') ||
            loadingProvider.isLoading('deleteTimeLog')) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_errorMessage != null) {
          return Center(
            child: Text(_errorMessage!, style: TextStyle(color: Colors.red)),
          );
        }

        return Consumer<DisplaySettingsProvider>(
          builder: (context, displayProvider, child) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(
                displayProvider.isOfficeMode ? 12.0 : 16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Time log details section
                  AdaptiveDetailSection(
                    title: 'Time Log Details',
                    icon: Icons.access_time,
                    alwaysExpandedInOffice: true,
                    children: [
                      if (displayProvider.isOfficeMode) ...[
                        // Office Mode: Compact grid layout
                        Row(
                          children: [
                            Expanded(
                              child: AdaptiveInfoRow(
                                label: 'Date',
                                value: DateFormat(
                                  'MMM d, yyyy',
                                ).format(widget.timeLog.date),
                                icon: Icons.calendar_today,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: AdaptiveInfoRow(
                                label: 'Hours',
                                value: '${widget.timeLog.hours}',
                                icon: Icons.schedule,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        if (_job != null)
                          AdaptiveInfoRow(
                            label: 'Job',
                            value: _job!.title,
                            icon: Icons.work,
                          ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: AdaptiveInfoRow(
                                label:
                                    widget.timeLog.isFlatRate
                                        ? 'Flat Rate'
                                        : 'Hourly Rate',
                                value:
                                    widget.timeLog.isFlatRate
                                        ? '\$${widget.timeLog.laborCost.toStringAsFixed(2)}'
                                        : '\$${widget.timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                                icon: Icons.attach_money,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: AdaptiveInfoRow(
                                label: 'Total Cost',
                                value:
                                    '\$${widget.timeLog.laborCost.toStringAsFixed(2)}',
                                icon: Icons.payment,
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        // Field Mode: Existing layout
                        Text(
                          'Date: ${DateFormat('MMMM d, yyyy').format(widget.timeLog.date)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Hours Worked: ${widget.timeLog.hours}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Job: ${_job?.title ?? 'Loading...'}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 16),
                        const Divider(),
                        const SizedBox(height: 8),
                        Text(
                          widget.timeLog.isFlatRate
                              ? 'Flat Rate: \$${widget.timeLog.laborCost.toStringAsFixed(2)}'
                              : 'Hourly Rate: \$${widget.timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Total Labor Cost: \$${widget.timeLog.laborCost.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                      ],
                    ],
                  ),
                  // Notes section
                  if (widget.timeLog.notes != null &&
                      widget.timeLog.notes!.isNotEmpty)
                    AdaptiveDetailSection(
                      title: 'Notes',
                      icon: Icons.note,
                      alwaysExpandedInOffice: true,
                      children: [
                        Text(
                          widget.timeLog.notes!,
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 13 : 16,
                          ),
                        ),
                      ],
                    ),

                  // Record information section
                  AdaptiveDetailSection(
                    title: 'Record Information',
                    icon: Icons.info,
                    alwaysExpandedInOffice: true,
                    children: [
                      if (displayProvider.isOfficeMode) ...[
                        // Office Mode: Compact grid layout
                        Row(
                          children: [
                            Expanded(
                              child: AdaptiveInfoRow(
                                label: 'Created',
                                value: DateFormat(
                                  'MMM d, yyyy h:mm a',
                                ).format(widget.timeLog.createdAt),
                                icon: Icons.calendar_today,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: AdaptiveInfoRow(
                                label: 'Updated',
                                value:
                                    widget.timeLog.updatedAt != null
                                        ? DateFormat(
                                          'MMM d, yyyy h:mm a',
                                        ).format(widget.timeLog.updatedAt!)
                                        : 'Not available',
                                icon: Icons.update,
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        // Field Mode: Existing layout
                        Text(
                          'Created: ${DateFormat('MMM d, yyyy h:mm a').format(widget.timeLog.createdAt)}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        Text(
                          'Last Updated: ${widget.timeLog.updatedAt != null ? DateFormat('MMM d, yyyy h:mm a').format(widget.timeLog.updatedAt!) : 'Not available'}',
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _navigateToEditScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeLogFormScreen(timeLog: widget.timeLog),
      ),
    ).then((updated) {
      if (updated == true) {
        _loadJobDetails();
      }
    });
  }
}
