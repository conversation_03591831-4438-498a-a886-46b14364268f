import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/sync_manager.dart'; // Import SyncManager
import 'package:quarterlies/screens/time_logs/time_log_detail_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/widgets/sync_status_indicator.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class TimeLogListScreen extends StatefulWidget {
  final String? jobId; // Optional job ID to filter time logs

  const TimeLogListScreen({super.key, this.jobId});

  @override
  State<TimeLogListScreen> createState() => _TimeLogListScreenState();
}

class _TimeLogListScreenState extends State<TimeLogListScreen> {
  final DataRepository _dataRepository = DataRepository();
  final SyncManager _syncManager = SyncManager(); // Inject SyncManager
  List<TimeLog> _timeLogs = [];
  List<TimeLog> _filteredTimeLogs = [];
  List<Job> _jobs = [];
  String? _selectedJobId;
  bool _isOffline = false;
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _selectedJobId = widget.jobId;
    _loadData();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTimeLogData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs for filtering
          final jobs = await _dataRepository.getJobs();

          // Load time logs based on job ID filter
          final timeLogs =
              _selectedJobId != null
                  ? await _dataRepository.getTimeLogsByJob(_selectedJobId!)
                  : await _dataRepository.getTimeLogs();

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _timeLogs = timeLogs;
            _applyFilters();
          });
        },
        message: 'Loading time logs...',
        errorMessage: 'Failed to load time logs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load time logs: ${e.toString()}';
        });
      }
    }
  }

  // Sync data with server
  Future<void> _syncData() async {
    if (_isOffline) {
      ErrorDisplay.showWarning(
        context,
        'Cannot sync while offline. Please check your connection.',
      );
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithSyncLoading(() async {
        // Attempt to sync all pending time logs
        await _syncManager.syncData(); // Use SyncManager for sync

        // Reload data after sync
        await _loadData();
      }, operationName: 'Syncing time logs...');

      // Check if widget is still mounted before using context
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Time logs synced successfully');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to sync time logs: ${e.toString()}';
        });

        ErrorDisplay.showWarning(context, 'Sync failed: ${e.toString()}');
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredTimeLogs =
          _timeLogs.where((timeLog) {
            // Apply job filter if selected
            if (_selectedJobId != null && timeLog.jobId != _selectedJobId) {
              return false;
            }

            return true;
          }).toList();

      // Sort by date, most recent first
      _filteredTimeLogs.sort((a, b) => b.date.compareTo(a.date));
    });
  }

  String _getJobTitle(String jobId) {
    final job = _jobs.firstWhere(
      (job) => job.id == jobId,
      orElse:
          () => Job(id: '', userId: '', customerId: '', title: 'Unknown Job'),
    );
    return job.title;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(
          widget.jobId != null ? 'Time Logs for Job' : 'All Time Logs',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          if (_jobs.isNotEmpty && widget.jobId == null)
            IconButton(
              icon: Icon(
                Icons.filter_list,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
              ),
              onPressed: () => _showFilterDialog(),
            ),
          // Manual sync button
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              return IconButton(
                icon:
                    loadingProvider.isSyncing
                        ? SizedBox(
                          width: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 20,
                          ),
                          height: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 20,
                          ),
                          child: SyncLoadingIndicator(
                            operation: 'Syncing...',
                            size: spacing.ResponsiveSpacing.getIconSize(
                              context,
                              base: 16,
                            ),
                            showSyncIcon: false,
                          ),
                        )
                        : Icon(
                          Icons.sync,
                          size: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 24,
                          ),
                        ),
                tooltip:
                    loadingProvider.isSyncing
                        ? loadingProvider.syncOperation ?? 'Syncing...'
                        : 'Sync data with server',
                onPressed: loadingProvider.isSyncing ? null : _syncData,
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToFormScreen(),
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return ResponsiveLayout(
      child: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadTimeLogData');

          if (isLoading) {
            return Center(
              child: QuarterliesLoadingIndicator(
                message: 'Loading time logs...',
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 32),
                showOfflineStatus: true,
              ),
            );
          }

          if (_errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ResponsiveBody(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _loadData,
                    style: ElevatedButton.styleFrom(
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                    ),
                    child: ResponsiveBody('Retry'),
                  ),
                ],
              ),
            );
          }

          if (_filteredTimeLogs.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ResponsiveBody('No time logs found'),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () => _navigateToFormScreen(),
                    style: ElevatedButton.styleFrom(
                      minimumSize: Size(
                        double.infinity,
                        spacing.ResponsiveSpacing.getButtonHeight(context),
                      ),
                    ),
                    child: ResponsiveBody('Add Time Log'),
                  ),
                ],
              ),
            );
          }

          // Use RefreshIndicator for pull-to-refresh functionality
          return RefreshIndicator(
            onRefresh: () async {
              await _syncData();
            },
            child: ListView.builder(
              itemCount: _filteredTimeLogs.length,
              itemBuilder: (context, index) {
                final timeLog = _filteredTimeLogs[index];
                return Consumer<DisplaySettingsProvider>(
                  builder: (context, displayProvider, child) {
                    return AdaptiveListTile(
                      title: Row(
                        children: [
                          Expanded(
                            child: ResponsiveSubtitle(
                              '${DateFormat('MMM d, yyyy').format(timeLog.date)} - ${timeLog.hours} hours',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          // Enhanced sync status indicator with conflict resolution
                          SyncStatusIndicator(
                            status: timeLog.syncStatus,
                            onTap:
                                timeLog.syncStatus == SyncStatus.conflict
                                    ? () =>
                                        _showConflictResolutionDialog(timeLog)
                                    : null,
                          ),
                        ],
                      ),
                      subtitle: ResponsiveBody(
                        'Job: ${_getJobTitle(timeLog.jobId)} • \$${timeLog.laborCost.toStringAsFixed(2)}${timeLog.notes != null && timeLog.notes!.isNotEmpty ? ' • ${timeLog.notes}' : ''}',
                      ),
                      leading: Container(
                        padding: spacing.ResponsiveSpacing.getPadding(
                          context,
                          base: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(
                            spacing.ResponsiveSpacing.getBorderRadius(
                              context,
                              base: 8,
                            ),
                          ),
                        ),
                        child: Icon(
                          Icons.timer,
                          color: Colors.white,
                          size: spacing.ResponsiveSpacing.getIconSize(
                            context,
                            base: 20,
                          ),
                        ),
                      ),
                      trailing: ResponsiveLabel(
                        timeLog.isFlatRate
                            ? 'Flat Rate'
                            : '\$${timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                        style: TextStyle(
                          color: Colors.blue[800],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Additional info shown only in Office Mode
                      additionalInfo:
                          displayProvider.isOfficeMode
                              ? OfficeAdditionalInfo(
                                items: [
                                  InfoItem(
                                    label: 'Rate',
                                    value:
                                        '\$${timeLog.hourlyRate.toStringAsFixed(2)}/hr',
                                    icon: Icons.attach_money,
                                  ),
                                  if (timeLog.isFlatRate)
                                    const InfoItem(
                                      label: 'Type',
                                      value: 'Flat Rate',
                                      icon: Icons.payment,
                                    ),
                                  if (timeLog.voiceNoteUrl != null)
                                    const InfoItem(
                                      label: 'Voice Note',
                                      value: 'Available',
                                      icon: Icons.mic,
                                    ),
                                  if (timeLog.syncStatus != SyncStatus.synced)
                                    InfoItem(
                                      label: 'Sync',
                                      value:
                                          timeLog.syncStatus
                                              .toString()
                                              .split('.')
                                              .last,
                                      icon: Icons.sync_problem,
                                    ),
                                ],
                              )
                              : null,
                      // Office actions shown only in Office Mode
                      officeActions:
                          displayProvider.isOfficeMode
                              ? [
                                OfficeActionButton(
                                  icon: Icons.edit,
                                  label: 'Edit',
                                  onPressed: () async {
                                    final result = await Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (context) => TimeLogFormScreen(
                                              timeLog: timeLog,
                                            ),
                                      ),
                                    );
                                    if (result == true) {
                                      _loadData();
                                    }
                                  },
                                ),
                                if (timeLog.voiceNoteUrl != null)
                                  OfficeActionButton(
                                    icon: Icons.play_arrow,
                                    label: 'Play',
                                    onPressed: () {
                                      ErrorDisplay.showInfo(
                                        context,
                                        'Voice note player coming soon!',
                                      );
                                    },
                                  ),
                                OfficeActionButton(
                                  icon: Icons.copy,
                                  label: 'Duplicate',
                                  onPressed: () {
                                    ErrorDisplay.showInfo(
                                      context,
                                      'Duplicate functionality coming soon!',
                                    );
                                  },
                                ),
                              ]
                              : null,
                      onTap: () => _navigateToDetailScreen(timeLog),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: ResponsiveSubtitle('Filter Time Logs'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String?>(
                  decoration: const InputDecoration(labelText: 'Filter by Job'),
                  value: _selectedJobId,
                  items: [
                    DropdownMenuItem<String?>(
                      value: null,
                      child: ResponsiveBody('All Jobs'),
                    ),
                    ..._jobs.map(
                      (job) => DropdownMenuItem<String?>(
                        value: job.id,
                        child: ResponsiveBody(job.title),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedJobId = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: ResponsiveBody('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _applyFilters();
              },
              child: ResponsiveBody('Apply'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToDetailScreen(TimeLog timeLog) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeLogDetailScreen(timeLog: timeLog),
      ),
    ).then((_) => _loadData());
  }

  void _navigateToFormScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimeLogFormScreen(jobId: _selectedJobId),
      ),
    ).then((_) => _loadData());
  }

  // Show conflict resolution dialog
  void _showConflictResolutionDialog(TimeLog timeLog) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: ResponsiveSubtitle('Resolve Sync Conflict'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveBody(
                    'This time log has conflicting versions between your device and the server.',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  ResponsiveBody('Choose which version to keep:'),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 8,
                    ),
                  ),
                  Card(
                    color: Colors.blue.shade50,
                    child: Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 8.0,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveSubtitle(
                            'Local Version (This Device)',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          ResponsiveBody(
                            'Date: ${DateFormat('MMM d, yyyy').format(timeLog.date)}',
                          ),
                          ResponsiveBody('Hours: ${timeLog.hours}'),
                          ResponsiveBody(
                            'Rate: \$${timeLog.hourlyRate.toStringAsFixed(2)}',
                          ),
                          if (timeLog.notes != null &&
                              timeLog.notes!.isNotEmpty)
                            ResponsiveBody('Notes: ${timeLog.notes}'),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),
                  // Note: In a real implementation, you would fetch the server version
                  // and display it here for comparison
                  Card(
                    color: Colors.green.shade50,
                    child: Padding(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: 8.0,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveSubtitle(
                            'Server Version',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          ResponsiveBody('Loading server version...'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: ResponsiveBody('Cancel'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  minimumSize: Size(
                    double.infinity,
                    spacing.ResponsiveSpacing.getButtonHeight(context),
                  ),
                ),
                onPressed: () {
                  // Keep local version
                  _resolveConflict(timeLog, keepLocal: true);
                  Navigator.pop(context);
                },
                child: ResponsiveBody('Keep Local Version'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  minimumSize: Size(
                    double.infinity,
                    spacing.ResponsiveSpacing.getButtonHeight(context),
                  ),
                ),
                onPressed: () {
                  // Keep server version
                  _resolveConflict(timeLog, keepLocal: false);
                  Navigator.pop(context);
                },
                child: ResponsiveBody('Keep Server Version'),
              ),
            ],
          ),
    );
  }

  // Resolve a sync conflict
  Future<void> _resolveConflict(
    TimeLog timeLog, {
    required bool keepLocal,
  }) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'resolveConflict',
        () async {
          // In a real implementation, you would call a method in your DataRepository
          // to resolve the conflict based on the user's choice
          await _syncManager.resolveTimeLogConflict(
            // Use SyncManager for conflict resolution
            timeLog.id,
            keepLocal: keepLocal,
          );

          // Reload data after resolving conflict
          await _loadData();
        },
        message: 'Resolving conflict...',
        errorMessage: 'Failed to resolve conflict',
      );

      // Check if widget is still mounted before using context
      if (mounted) {
        ErrorDisplay.showOperation(context, 'Conflict resolved successfully');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to resolve conflict: ${e.toString()}',
        );
      }
    }
  }
}
