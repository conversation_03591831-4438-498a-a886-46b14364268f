import 'package:flutter/material.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/expenses/expense_detail_screen.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class ExpenseListScreen extends StatefulWidget {
  final String? jobId; // Optional job ID to filter expenses

  const ExpenseListScreen({super.key, this.jobId});

  @override
  State<ExpenseListScreen> createState() => _ExpenseListScreenState();
}

class _ExpenseListScreenState extends State<ExpenseListScreen> {
  final DataRepository _dataRepository = DataRepository();
  List<Expense> _expenses = [];
  List<Expense> _filteredExpenses = [];
  List<Job> _jobs = [];
  String? _selectedJobId;
  String? _selectedCategory; // Changed from _selectedType to _selectedCategory
  String? _selectedExpenseType; // New filter for overhead vs job expenses
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _selectedJobId = widget.jobId;
    _loadData();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    await _dataRepository.isOnline();

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadExpenseData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs for filtering
          final jobs = await _dataRepository.getJobs();

          // Load expenses based on job ID filter
          final expenses =
              _selectedJobId != null
                  ? await _dataRepository.getExpensesByJob(_selectedJobId!)
                  : await _dataRepository.getExpenses();

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _expenses = expenses;
            _applyFilters();
          });
        },
        message: 'Loading expenses...',
        errorMessage: 'Failed to load expenses',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load expenses: ${e.toString()}';
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredExpenses =
          _expenses.where((expense) {
            // Apply job filter if selected
            if (_selectedJobId != null && expense.jobId != _selectedJobId) {
              return false;
            }

            // Apply category filter if selected (instead of type)
            if (_selectedCategory != null &&
                expense.category != _selectedCategory) {
              return false;
            }

            // Apply expense type filter (overhead vs job expenses)
            if (_selectedExpenseType != null) {
              if (_selectedExpenseType == 'overhead' && !expense.isOverhead) {
                return false;
              }
              if (_selectedExpenseType == 'job' && expense.isOverhead) {
                return false;
              }
            }

            return true;
          }).toList();
    });
  }

  String _getJobTitle(String? jobId) {
    if (jobId == null) return 'Overhead Expense';

    final job = _jobs.firstWhere(
      (job) => job.id == jobId,
      orElse:
          () => Job(id: '', userId: '', customerId: '', title: 'Unknown Job'),
    );
    return job.title;
  }

  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey[300]!;

    // Use different colors for different categories
    // This is just an example, you can adjust the colors as needed
    switch (category) {
      case ExpenseCategory.advertising:
        return Colors.blue[100]!;
      case ExpenseCategory.carAndTruck:
        return Colors.green[100]!;
      case ExpenseCategory.contractLabor:
        return Colors.orange[100]!;
      case ExpenseCategory.officeExpense:
        return Colors.purple[100]!;
      case ExpenseCategory.legalAndProfessional:
        return Colors.red[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(
          widget.jobId != null ? 'Job Expenses' : 'All Expenses',
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Filter button
          IconButton(
            icon: Icon(
              Icons.filter_list,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadExpenseData');

            return isLoading
                ? Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading expenses...',
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 32,
                    ),
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadData,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            120,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: const ResponsiveLabel('Retry'),
                      ),
                    ],
                  ),
                )
                : _filteredExpenses.isEmpty
                ? const Center(child: ResponsiveBody('No expenses found'))
                : RefreshIndicator(
                  onRefresh: _loadData,
                  child: ListView.builder(
                    itemCount: _filteredExpenses.length,
                    itemBuilder: (context, index) {
                      final expense = _filteredExpenses[index];
                      return Consumer<DisplaySettingsProvider>(
                        builder: (context, displayProvider, child) {
                          return AdaptiveListTile(
                            title: Row(
                              children: [
                                Expanded(
                                  child: ResponsiveSubtitle(
                                    expense.description,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                if (expense.isOverhead)
                                  Container(
                                    padding: spacing
                                        .ResponsiveSpacing.getPadding(
                                      context,
                                      base: 6,
                                    ).copyWith(
                                      top: spacing.ResponsiveSpacing.getSpacing(
                                        context,
                                        base: 2,
                                      ),
                                      bottom: spacing
                                          .ResponsiveSpacing.getSpacing(
                                        context,
                                        base: 2,
                                      ),
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.orange[100],
                                      borderRadius: BorderRadius.circular(
                                        spacing
                                            .ResponsiveSpacing.getBorderRadius(
                                          context,
                                          base: 4,
                                        ),
                                      ),
                                      border: Border.all(
                                        color: Colors.orange[300]!,
                                      ),
                                    ),
                                    child: ResponsiveLabel(
                                      'OVERHEAD',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange[800],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            subtitle: ResponsiveBody(
                              '\$${expense.amount.toStringAsFixed(2)} • ${DateFormat('MM/dd/yyyy').format(expense.date)}${expense.category != null ? ' • ${expense.category}' : ''}',
                            ),
                            leading: Container(
                              padding: spacing.ResponsiveSpacing.getPadding(
                                context,
                                base: 8,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    expense.isOverhead
                                        ? Colors.orange
                                        : Colors.green,
                                borderRadius: BorderRadius.circular(
                                  spacing.ResponsiveSpacing.getBorderRadius(
                                    context,
                                    base: 8,
                                  ),
                                ),
                              ),
                              child: Icon(
                                expense.isOverhead
                                    ? Icons.business
                                    : Icons.receipt,
                                color: Colors.white,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 20,
                                ),
                              ),
                            ),
                            trailing: Chip(
                              label: ResponsiveLabel(
                                expense.category ?? 'Uncategorized',
                              ),
                              backgroundColor: _getCategoryColor(
                                expense.category,
                              ),
                            ),
                            // Additional info shown only in Office Mode
                            additionalInfo:
                                displayProvider.isOfficeMode
                                    ? OfficeAdditionalInfo(
                                      items: [
                                        if (expense.jobId != null &&
                                            !expense.isOverhead)
                                          InfoItem(
                                            label: 'Job',
                                            value: _getJobTitle(expense.jobId),
                                            icon: Icons.work,
                                          ),
                                        if (expense.tags != null &&
                                            expense.tags!.isNotEmpty)
                                          InfoItem(
                                            label: 'Tags',
                                            value: expense.tags!.join(', '),
                                            icon: Icons.tag,
                                          ),
                                        if (expense.receiptPhotoUrl != null)
                                          const InfoItem(
                                            label: 'Receipt',
                                            value: 'Photo attached',
                                            icon: Icons.photo,
                                          ),
                                      ],
                                    )
                                    : null,
                            // Office actions shown only in Office Mode
                            officeActions:
                                displayProvider.isOfficeMode
                                    ? [
                                      OfficeActionButton(
                                        icon: Icons.edit,
                                        label: 'Edit',
                                        onPressed: () async {
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      ExpenseFormScreen(
                                                        expense: expense,
                                                        jobId: widget.jobId,
                                                      ),
                                            ),
                                          );
                                          if (result == true) {
                                            _loadData();
                                          }
                                        },
                                      ),
                                      if (expense.receiptPhotoUrl != null)
                                        OfficeActionButton(
                                          icon: Icons.photo,
                                          label: 'Receipt',
                                          onPressed: () {
                                            ErrorDisplay.showInfo(
                                              context,
                                              'Receipt viewer coming soon!',
                                            );
                                          },
                                        ),
                                      OfficeActionButton(
                                        icon: Icons.copy,
                                        label: 'Duplicate',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Duplicate functionality coming soon!',
                                          );
                                        },
                                      ),
                                    ]
                                    : null,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => ExpenseDetailScreen(
                                        expenseId: expense.id,
                                      ),
                                ),
                              ).then((_) => _loadData());
                            },
                          );
                        },
                      );
                    },
                  ),
                );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ExpenseFormScreen(jobId: widget.jobId),
            ),
          ).then((_) => _loadData());
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const ResponsiveSubtitle('Filter Expenses'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Job filter dropdown
                  if (widget.jobId == null) ...[
                    const ResponsiveLabel('Job'),
                    DropdownButton<String?>(
                      isExpanded: true,
                      value: _selectedJobId,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: ResponsiveBody('All Jobs'),
                        ),
                        ..._jobs.map((job) {
                          return DropdownMenuItem<String?>(
                            value: job.id,
                            child: ResponsiveBody(job.title),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedJobId = value;
                        });
                      },
                    ),
                    SizedBox(
                      height: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 16,
                      ),
                    ),
                  ],

                  // Expense type filter dropdown (overhead vs job expenses)
                  const ResponsiveLabel('Expense Type'),
                  DropdownButton<String?>(
                    isExpanded: true,
                    value: _selectedExpenseType,
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: ResponsiveBody('All Expenses'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'job',
                        child: ResponsiveBody('Job Expenses'),
                      ),
                      DropdownMenuItem<String?>(
                        value: 'overhead',
                        child: ResponsiveBody('Overhead Expenses'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedExpenseType = value;
                      });
                    },
                  ),
                  SizedBox(
                    height: spacing.ResponsiveSpacing.getSpacing(
                      context,
                      base: 16,
                    ),
                  ),

                  // Category filter dropdown
                  const ResponsiveLabel('Expense Category'),
                  DropdownButton<String?>(
                    isExpanded: true,
                    value: _selectedCategory,
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: ResponsiveBody('All Categories'),
                      ),
                      ...ExpenseCategory.values.map((category) {
                        return DropdownMenuItem<String?>(
                          value: category,
                          child: ResponsiveBody(category),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: TextButton.styleFrom(
                    minimumSize: Size(
                      80,
                      spacing.ResponsiveSpacing.getButtonHeight(context),
                    ),
                  ),
                  child: const ResponsiveLabel('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  style: ElevatedButton.styleFrom(
                    minimumSize: Size(
                      80,
                      spacing.ResponsiveSpacing.getButtonHeight(context),
                    ),
                  ),
                  child: const ResponsiveLabel('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
