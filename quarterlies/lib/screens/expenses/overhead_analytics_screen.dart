import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';

class OverheadAnalyticsScreen extends StatefulWidget {
  const OverheadAnalyticsScreen({super.key});

  @override
  State<OverheadAnalyticsScreen> createState() =>
      _OverheadAnalyticsScreenState();
}

class _OverheadAnalyticsScreenState extends State<OverheadAnalyticsScreen> {
  final DataRepository _dataRepository = DataRepository();

  List<Expense> _overheadExpenses = [];
  Map<String, double> _categoryTotals = {};
  Map<String, double> _allocationMap = {};
  double _totalOverheadAmount = 0.0;
  int _selectedYear = DateTime.now().year;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadOverheadData();
  }

  Future<void> _loadOverheadData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadOverheadData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get overhead expenses for the selected year
          final startDate = DateTime(_selectedYear, 1, 1);
          final endDate = DateTime(_selectedYear, 12, 31);

          final overheadExpenses = await _dataRepository
              .getOverheadExpensesByDateRange(startDate, endDate);

          // Calculate category totals
          final categoryTotals = <String, double>{};
          for (final expense in overheadExpenses) {
            final category = expense.category ?? 'Uncategorized';
            categoryTotals[category] =
                (categoryTotals[category] ?? 0.0) + expense.amount;
          }

          // Get overhead allocation across jobs
          final allocationMap = await _dataRepository.allocateOverheadExpenses(
            year: _selectedYear,
          );

          // Calculate total overhead amount
          final totalAmount = overheadExpenses.fold(
            0.0,
            (sum, expense) => sum + expense.amount,
          );

          if (!mounted) return;

          setState(() {
            _overheadExpenses = overheadExpenses;
            _categoryTotals = categoryTotals;
            _allocationMap = allocationMap;
            _totalOverheadAmount = totalAmount;
          });
        },
        message: 'Loading overhead analytics...',
        errorMessage: 'Failed to load overhead data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load overhead data: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Overhead Analytics'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Year selector
          PopupMenuButton<int>(
            icon: const Icon(Icons.calendar_today),
            onSelected: (year) {
              setState(() {
                _selectedYear = year;
              });
              _loadOverheadData();
            },
            itemBuilder: (context) {
              final currentYear = DateTime.now().year;
              return List.generate(5, (index) {
                final year = currentYear - index;
                return PopupMenuItem<int>(
                  value: year,
                  child: Text(year.toString()),
                );
              });
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadOverheadData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading overhead analytics...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadOverheadData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Year and total summary
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Overhead Summary for $_selectedYear',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Total Overhead: \$${_totalOverheadAmount.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  'Number of Expenses: ${_overheadExpenses.length}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Category breakdown
                        if (_categoryTotals.isNotEmpty) ...[
                          const Text(
                            'Breakdown by Category',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            child: Column(
                              children:
                                  _categoryTotals.entries
                                      .map(
                                        (entry) => ListTile(
                                          title: Text(entry.key),
                                          trailing: Text(
                                            '\$${entry.value.toStringAsFixed(2)}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          subtitle: Text(
                                            '${(entry.value / _totalOverheadAmount * 100).toStringAsFixed(1)}% of total',
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        // Job allocation
                        if (_allocationMap.isNotEmpty) ...[
                          const Text(
                            'Allocation Across Jobs',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            child: Column(
                              children:
                                  _allocationMap.entries
                                      .map(
                                        (entry) => FutureBuilder<Job?>(
                                          future: _getJobById(entry.key),
                                          builder: (context, snapshot) {
                                            final jobTitle =
                                                snapshot.data?.title ??
                                                'Unknown Job';
                                            return ListTile(
                                              title: Text(jobTitle),
                                              trailing: Text(
                                                '\$${entry.value.toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              subtitle: Text(
                                                '${(entry.value / _totalOverheadAmount * 100).toStringAsFixed(1)}% of total overhead',
                                              ),
                                            );
                                          },
                                        ),
                                      )
                                      .toList(),
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        // Recent overhead expenses
                        const Text(
                          'Recent Overhead Expenses',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Card(
                          child: Column(
                            children:
                                _overheadExpenses
                                    .take(10) // Show only the 10 most recent
                                    .map(
                                      (expense) => ListTile(
                                        title: Text(expense.description),
                                        subtitle: Text(
                                          '${expense.category ?? 'Uncategorized'} • ${DateFormat('MM/dd/yyyy').format(expense.date)}',
                                        ),
                                        trailing: Text(
                                          '\$${expense.amount.toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    )
                                    .toList(),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
        },
      ),
    );
  }

  Future<Job?> _getJobById(String jobId) async {
    try {
      return await _dataRepository.getJobById(jobId);
    } catch (e) {
      return null;
    }
  }
}
