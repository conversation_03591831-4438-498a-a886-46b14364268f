import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/services/sync_manager.dart';
import 'package:quarterlies/services/local_database_service.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';

/// A screen that displays all expenses with sync conflicts and allows the user to resolve them.
///
/// This screen demonstrates how to use the ConflictResolutionDialog with a specific entity type.
class ExpenseConflictScreen extends StatefulWidget {
  const ExpenseConflictScreen({super.key});

  @override
  State<ExpenseConflictScreen> createState() => _ExpenseConflictScreenState();
}

class _ExpenseConflictScreenState extends State<ExpenseConflictScreen> {
  final LocalDatabaseService _localDatabaseService = LocalDatabaseService();
  final SyncManager _syncManager = SyncManager();
  List<Expense> _conflictExpenses = [];

  @override
  void initState() {
    super.initState();
    _loadConflictExpenses();
  }

  Future<void> _loadConflictExpenses() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadConflictExpenses',
        () async {
          final expenses =
              await _localDatabaseService.getExpensesWithConflicts();

          if (!mounted) return;

          setState(() {
            _conflictExpenses = expenses;
          });
        },
        message: 'Loading conflict expenses...',
        errorMessage: 'Failed to load conflict expenses',
      );
    } catch (e) {
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'loadConflictExpenses',
          'screen': 'ExpenseConflictScreen',
        },
      );
      ErrorHandler.logError(appError);

      if (mounted) {
        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _loadConflictExpenses(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Expense Conflicts',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            toolbarHeight: displayProvider.isOfficeMode ? 56 : 64,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _loadConflictExpenses,
                tooltip: 'Refresh',
              ),
            ],
          ),
          body: Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading = loadingProvider.isLoading(
                'loadConflictExpenses',
              );

              return isLoading
                  ? const Center(
                    child: QuarterliesLoadingIndicator(
                      message: 'Loading conflict expenses...',
                      size: 32.0,
                    ),
                  )
                  : _conflictExpenses.isEmpty
                  ? _buildEmptyState(displayProvider)
                  : _buildConflictList(displayProvider);
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(DisplaySettingsProvider displayProvider) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: displayProvider.isOfficeMode ? 56 : 64,
              color: Colors.green,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
            Text(
              'No Conflicts',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontSize: displayProvider.isOfficeMode ? 20 : 24,
              ),
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
            Text(
              'All your expenses are synced correctly.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConflictList(DisplaySettingsProvider displayProvider) {
    return ListView.builder(
      itemCount: _conflictExpenses.length,
      padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
      itemBuilder: (context, index) {
        final expense = _conflictExpenses[index];
        return Card(
          margin: EdgeInsets.only(
            bottom: displayProvider.isOfficeMode ? 12.0 : 16.0,
          ),
          elevation: displayProvider.isOfficeMode ? 2 : 3,
          child: ListTile(
            contentPadding: EdgeInsets.all(
              displayProvider.isOfficeMode ? 12.0 : 16.0,
            ),
            title: Text(
              expense.description,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                if (displayProvider.isOfficeMode) ...[
                  // Office Mode: Compact grid layout
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Amount: \$${expense.amount.toStringAsFixed(2)}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Date: ${_formatDate(expense.date)}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Category: ${expense.category ?? "Uncategorized"}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ] else ...[
                  // Field Mode: Existing layout
                  Text('Amount: \$${expense.amount.toStringAsFixed(2)}'),
                  Text('Date: ${_formatDate(expense.date)}'),
                  Text('Category: ${expense.category ?? "Uncategorized"}'),
                ],
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.orange,
                      size: displayProvider.isOfficeMode ? 14 : 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Sync Conflict',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: ElevatedButton(
              onPressed: () => _resolveConflict(expense),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: displayProvider.isOfficeMode ? 12 : 16,
                  vertical: displayProvider.isOfficeMode ? 6 : 8,
                ),
              ),
              child: Text(
                'Resolve',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _resolveConflict(Expense expense) async {
    try {
      await _syncManager.showExpenseConflictDialog(context, expense.id);
      // Refresh the list after resolution
      _loadConflictExpenses();
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'resolveExpenseConflict',
            'expenseId': expense.id,
            'expenseDescription': expense.description,
          },
        );
        ErrorHandler.logError(appError);

        ErrorDisplay.showSnackBar(
          context,
          appError,
          onRetry: () => _resolveConflict(expense),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
}
