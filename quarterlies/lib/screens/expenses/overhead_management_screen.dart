import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/screens/expenses/expense_detail_screen.dart';
import 'package:quarterlies/screens/expenses/overhead_analytics_screen.dart';

class OverheadManagementScreen extends StatefulWidget {
  const OverheadManagementScreen({super.key});

  @override
  State<OverheadManagementScreen> createState() =>
      _OverheadManagementScreenState();
}

class _OverheadManagementScreenState extends State<OverheadManagementScreen> {
  final DataRepository _dataRepository = DataRepository();

  List<Expense> _overheadExpenses = [];
  String? _selectedCategory;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadOverheadExpenses();
  }

  Future<void> _loadOverheadExpenses() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadOverheadExpenses',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final overheadExpenses = await _dataRepository.getOverheadExpenses();

          if (!mounted) return;

          setState(() {
            _overheadExpenses = overheadExpenses;
            _applyFilters();
          });
        },
        message: 'Loading overhead expenses...',
        errorMessage: 'Failed to load overhead expenses',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load overhead expenses: ${e.toString()}';
        });
      }
    }
  }

  void _applyFilters() {
    setState(() {
      // Apply category filter if selected
      if (_selectedCategory != null) {
        _overheadExpenses =
            _overheadExpenses
                .where((expense) => expense.category == _selectedCategory)
                .toList();
      }
    });
  }

  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey[300]!;

    // Use different colors for different categories
    switch (category) {
      case ExpenseCategory.advertising:
        return Colors.blue[100]!;
      case ExpenseCategory.carAndTruck:
        return Colors.green[100]!;
      case ExpenseCategory.contractLabor:
        return Colors.orange[100]!;
      case ExpenseCategory.officeExpense:
        return Colors.purple[100]!;
      case ExpenseCategory.legalAndProfessional:
        return Colors.red[100]!;
      case ExpenseCategory.insurance:
        return Colors.teal[100]!;
      case ExpenseCategory.utilities:
        return Colors.indigo[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Overhead Expenses'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          // Analytics button
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OverheadAnalyticsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadOverheadExpenses');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading overhead expenses...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadOverheadExpenses,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _overheadExpenses.isEmpty
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.business_center_outlined,
                      size: 80,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No overhead expenses found',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Overhead expenses are business costs not tied to specific jobs',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              )
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return Column(
                    children: [
                      // Summary card
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 12.0 : 16.0,
                        ),
                        padding: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 12.0 : 16.0,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Overhead Summary',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Total: \$${_overheadExpenses.fold(0.0, (sum, expense) => sum + expense.amount).toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Count: ${_overheadExpenses.length} expenses',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),

                      // Expense list
                      Expanded(
                        child: ListView.builder(
                          itemCount: _overheadExpenses.length,
                          itemBuilder: (context, index) {
                            final expense = _overheadExpenses[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 4,
                              ),
                              child: ListTile(
                                title: Row(
                                  children: [
                                    Expanded(child: Text(expense.description)),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.orange[100],
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color: Colors.orange[300]!,
                                        ),
                                      ),
                                      child: Text(
                                        'OVERHEAD',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.orange[800],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                subtitle: Text(
                                  'Amount: \$${expense.amount.toStringAsFixed(2)}',
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Chip(
                                      label: Text(
                                        expense.category ?? 'Uncategorized',
                                      ),
                                      backgroundColor: _getCategoryColor(
                                        expense.category,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      DateFormat(
                                        'MM/dd/yyyy',
                                      ).format(expense.date),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => ExpenseDetailScreen(
                                            expenseId: expense.id,
                                          ),
                                    ),
                                  ).then((_) => _loadOverheadExpenses());
                                },
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  );
                },
              );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => const ExpenseFormScreen(
                    isOverheadExpense: true, // Force overhead expense creation
                  ),
            ),
          ).then((_) => _loadOverheadExpenses());
        },
        backgroundColor: Colors.orange[600],
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Overhead Expenses'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Category'),
                  DropdownButton<String?>(
                    isExpanded: true,
                    value: _selectedCategory,
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Categories'),
                      ),
                      ...ExpenseCategory.values.map((category) {
                        return DropdownMenuItem<String?>(
                          value: category,
                          child: Text(category),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _loadOverheadExpenses(); // Reload with filters
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
