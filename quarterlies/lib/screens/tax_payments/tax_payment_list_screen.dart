import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'tax_payment_form_screen.dart';
import 'tax_payment_detail_screen.dart';

class TaxPaymentListScreen extends StatefulWidget {
  const TaxPaymentListScreen({super.key});

  @override
  State<TaxPaymentListScreen> createState() => _TaxPaymentListScreenState();
}

class _TaxPaymentListScreenState extends State<TaxPaymentListScreen> {
  final DataRepository _dataRepository = DataRepository();
  bool _isOffline = false;
  String? _errorMessage;
  List<TaxPayment> _taxPayments = [];
  String? _selectedPeriodFilter;
  List<String> _periodOptions = [];

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _periodOptions = TaxPeriod.getTaxPeriodOptions();
    _loadTaxPayments();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadTaxPayments() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTaxPayments',
        () async {
          setState(() {
            _errorMessage = null;
          });

          List<TaxPayment> payments;

          if (_selectedPeriodFilter != null) {
            payments = await _dataRepository.getTaxPaymentsByPeriod(
              _selectedPeriodFilter!,
            );
          } else {
            payments = await _dataRepository.getTaxPayments();
          }

          if (!mounted) return;

          setState(() {
            _taxPayments = payments;
          });
        },
        message: 'Loading tax payments...',
        errorMessage: 'Failed to load tax payments',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load tax payments: ${e.toString()}';
        });
      }
    }
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setModalState) => Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Filter by Tax Period',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          FilterChip(
                            label: const Text('All Periods'),
                            selected: _selectedPeriodFilter == null,
                            onSelected: (selected) {
                              if (selected) {
                                setModalState(() {
                                  _selectedPeriodFilter = null;
                                });
                                setState(() {
                                  _selectedPeriodFilter = null;
                                });
                                Navigator.pop(context);
                                _loadTaxPayments();
                              }
                            },
                          ),
                          ..._periodOptions.map(
                            (period) => FilterChip(
                              label: Text(period),
                              selected: _selectedPeriodFilter == period,
                              onSelected: (selected) {
                                if (selected) {
                                  setModalState(() {
                                    _selectedPeriodFilter = period;
                                  });
                                  setState(() {
                                    _selectedPeriodFilter = period;
                                  });
                                  Navigator.pop(context);
                                  _loadTaxPayments();
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tax Payments'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Icon(Icons.cloud_off, color: Colors.white),
                    SizedBox(width: 4),
                    Text('Offline', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterOptions,
            tooltip: 'Filter by period',
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadTaxPayments');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading tax payments...',
                  size: 32.0,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadTaxPayments,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _taxPayments.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.receipt_long,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _selectedPeriodFilter != null
                          ? 'No tax payments found for $_selectedPeriodFilter'
                          : 'No tax payments found',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed:
                          () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => const TaxPaymentFormScreen(),
                            ),
                          ).then((_) => _loadTaxPayments()),
                      icon: const Icon(Icons.add),
                      label: const Text('Add Tax Payment'),
                    ),
                  ],
                ),
              )
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return RefreshIndicator(
                    onRefresh: _loadTaxPayments,
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(
                        horizontal: displayProvider.isOfficeMode ? 8.0 : 16.0,
                        vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
                      ),
                      itemCount: _taxPayments.length,
                      itemBuilder: (context, index) {
                        final taxPayment = _taxPayments[index];
                        return AdaptiveListTile(
                          title: Text(
                            '${taxPayment.taxPeriod} - ${NumberFormat.currency(symbol: '\$').format(taxPayment.amount)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text(
                            'Paid on ${DateFormat.yMMMd().format(taxPayment.date)}'
                            '${taxPayment.paymentMethod != null ? ' via ${taxPayment.paymentMethod}' : ''}',
                          ),
                          leading: const Icon(
                            Icons.account_balance,
                            color: Colors.blue,
                          ),
                          trailing: const Icon(Icons.chevron_right),
                          onTap:
                              () => Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => TaxPaymentDetailScreen(
                                        taxPaymentId: taxPayment.id,
                                      ),
                                ),
                              ).then((_) => _loadTaxPayments()),
                          // Additional info shown only in Office Mode
                          additionalInfo:
                              displayProvider.isOfficeMode
                                  ? OfficeAdditionalInfo(
                                    items: [
                                      if (taxPayment.confirmationNumber != null)
                                        InfoItem(
                                          label: 'Confirmation',
                                          value: taxPayment.confirmationNumber!,
                                          icon: Icons.confirmation_number,
                                        ),
                                      InfoItem(
                                        label: 'Created',
                                        value: DateFormat(
                                          'MM/dd/yyyy',
                                        ).format(taxPayment.createdAt),
                                        icon: Icons.calendar_today,
                                      ),
                                      if (taxPayment.notes != null &&
                                          taxPayment.notes!.isNotEmpty)
                                        InfoItem(
                                          label: 'Notes',
                                          value:
                                              taxPayment.notes!.length > 30
                                                  ? '${taxPayment.notes!.substring(0, 30)}...'
                                                  : taxPayment.notes!,
                                          icon: Icons.note,
                                        ),
                                    ],
                                  )
                                  : null,
                          // Office actions shown only in Office Mode
                          officeActions:
                              displayProvider.isOfficeMode
                                  ? [
                                    OfficeActionButton(
                                      icon: Icons.edit,
                                      label: 'Edit',
                                      onPressed: () async {
                                        final result = await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) =>
                                                    TaxPaymentFormScreen(
                                                      taxPayment: taxPayment,
                                                    ),
                                          ),
                                        );
                                        if (result == true) {
                                          _loadTaxPayments();
                                        }
                                      },
                                    ),
                                    OfficeActionButton(
                                      icon: Icons.receipt_long,
                                      label: 'Receipt',
                                      onPressed: () {
                                        ErrorDisplay.showInfo(
                                          context,
                                          'Receipt view coming soon!',
                                        );
                                      },
                                    ),
                                  ]
                                  : null,
                        );
                      },
                    ),
                  );
                },
              );
        },
      ),
      floatingActionButton:
          _taxPayments.isNotEmpty
              ? FloatingActionButton(
                onPressed:
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TaxPaymentFormScreen(),
                      ),
                    ).then((_) => _loadTaxPayments()),
                child: const Icon(Icons.add),
              )
              : null,
    );
  }
}
