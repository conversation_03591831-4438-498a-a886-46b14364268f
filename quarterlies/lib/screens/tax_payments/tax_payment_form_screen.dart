import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class TaxPaymentFormScreen extends StatefulWidget {
  final TaxPayment? taxPayment; // Null for new payment, non-null for editing
  final String? initialTaxPeriod; // Optional initial tax period
  final double? suggestedAmount; // Optional suggested amount

  const TaxPaymentFormScreen({
    super.key,
    this.taxPayment,
    this.initialTaxPeriod,
    this.suggestedAmount,
  });

  @override
  State<TaxPaymentFormScreen> createState() => _TaxPaymentFormScreenState();
}

class _TaxPaymentFormScreenState extends State<TaxPaymentFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _confirmationNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _dataRepository = DataRepository();
  final _voiceRecordingService = VoiceRecordingService();

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;
  bool _isOffline = false;

  DateTime _selectedDate = DateTime.now();
  String _selectedTaxPeriod = '';
  String? _selectedPaymentMethod;
  List<String> _taxPeriodOptions = [];
  final List<String> _paymentMethodOptions = [
    'Direct Payment',
    'EFTPS',
    'Credit Card',
    'Check',
    'Wire Transfer',
    'Other',
  ];

  bool _isRecording = false;
  String? _errorMessage;
  String? _voiceNoteUrl;

  bool get _isEditing => widget.taxPayment != null;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _taxPeriodOptions = TaxPeriod.getTaxPeriodOptions();

    // Set initial tax period from parameter or default to first option
    _selectedTaxPeriod = widget.initialTaxPeriod ?? _taxPeriodOptions.first;

    if (_isEditing) {
      // Populate form fields with existing tax payment data
      _amountController.text = widget.taxPayment!.amount.toString();
      _selectedDate = widget.taxPayment!.date;
    } else if (widget.suggestedAmount != null && widget.suggestedAmount! > 0) {
      // Use suggested amount if provided
      _amountController.text = widget.suggestedAmount!.toString();
      _selectedTaxPeriod = widget.taxPayment!.taxPeriod;
      _selectedPaymentMethod = widget.taxPayment!.paymentMethod;
      _confirmationNumberController.text =
          widget.taxPayment!.confirmationNumber ?? '';
      _notesController.text = widget.taxPayment!.notes ?? '';
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _confirmationNumberController.dispose();
    _notesController.dispose();
    _voiceRecordingService.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show connectivity status feedback using centralized system
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
        } else {
          ErrorDisplay.showSync(context, FeedbackMessages.workingOffline);
        }
      }
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(DateTime.now().year - 2),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();

      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: $e';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();

      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text to extract information
        final extractedInfo = _voiceRecordingService.processTranscribedText(
          transcribedText,
        );

        // Update form fields with extracted information
        if (extractedInfo.containsKey('amount')) {
          _amountController.text = extractedInfo['amount']!;
        }

        if (extractedInfo.containsKey('date')) {
          try {
            _selectedDate = DateTime.parse(extractedInfo['date']!);
          } catch (e) {
            // Handle date parsing error
          }
        }

        if (extractedInfo.containsKey('notes')) {
          _notesController.text = extractedInfo['notes']!;
        }

        // Upload the audio file to storage
        final recordingPath = _voiceRecordingService.getRecordingPath();
        if (recordingPath != null) {
          _voiceNoteUrl = await _voiceRecordingService.uploadAudioToStorage(
            recordingPath,
            'tax_payments',
            const Uuid().v4(),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isRecording = false;
        _errorMessage = 'Failed to process recording: $e';
      });
    }
  }

  Future<void> _saveTaxPayment() async {
    if (!_formKey.currentState!.validate()) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveTaxPayment',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final amount = double.parse(_amountController.text);
          final userId = Supabase.instance.client.auth.currentUser!.id;

          if (_isEditing) {
            // Update existing tax payment
            final updatedPayment = widget.taxPayment!.copyWith(
              date: _selectedDate,
              amount: amount,
              taxPeriod: _selectedTaxPeriod,
              paymentMethod: _selectedPaymentMethod,
              confirmationNumber:
                  _confirmationNumberController.text.isEmpty
                      ? null
                      : _confirmationNumberController.text,
              notes:
                  _notesController.text.isEmpty ? null : _notesController.text,
              voiceNoteUrl: _voiceNoteUrl ?? widget.taxPayment!.voiceNoteUrl,
              updatedAt: DateTime.now(),
            );

            // DataRepository will handle saving to local DB and syncing when online
            await _dataRepository.updateTaxPayment(updatedPayment);
          } else {
            // Create new tax payment
            final newPayment = TaxPayment(
              userId: userId,
              date: _selectedDate,
              amount: amount,
              taxPeriod: _selectedTaxPeriod,
              paymentMethod: _selectedPaymentMethod,
              confirmationNumber:
                  _confirmationNumberController.text.isEmpty
                      ? null
                      : _confirmationNumberController.text,
              notes:
                  _notesController.text.isEmpty ? null : _notesController.text,
              voiceNoteUrl: _voiceNoteUrl,
            );

            // DataRepository will handle saving to local DB and syncing when online
            await _dataRepository.addTaxPayment(newPayment);
          }

          if (mounted) {
            // Show success feedback using centralized system
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'tax payment',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context);
          }
        },
        message:
            _isEditing ? 'Updating tax payment...' : 'Creating tax payment...',
        errorMessage: 'Failed to save tax payment',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to save tax payment: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Tax Payment' : 'Add Tax Payment'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Tooltip(
                message:
                    'You are offline. Changes will be saved locally and synced when back online.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Icon(Icons.cloud_off, color: Colors.white),
                    SizedBox(width: 4),
                    Text('Offline', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
            ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          return loadingProvider.isLoading('saveTaxPayment')
              ? const Center(child: CircularProgressIndicator())
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Tax Period Dropdown
                          const Text(
                            'Tax Period',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            value: _selectedTaxPeriod,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                            ),
                            items:
                                _taxPeriodOptions
                                    .map(
                                      (period) => DropdownMenuItem(
                                        value: period,
                                        child: Text(period),
                                      ),
                                    )
                                    .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedTaxPeriod = value;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a tax period';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Payment Date
                          const Text(
                            'Payment Date',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () => _selectDate(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    DateFormat.yMMMd().format(_selectedDate),
                                  ),
                                  const Icon(Icons.calendar_today),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Amount
                          const Text(
                            'Payment Amount',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _amountController,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              prefixText: '\$',
                              hintText: '0.00',
                            ),
                            keyboardType: const TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter an amount';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Please enter a valid number';
                              }
                              if (double.parse(value) <= 0) {
                                return 'Amount must be greater than zero';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Payment Method
                          const Text(
                            'Payment Method (Optional)',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            value: _selectedPaymentMethod,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              hintText: 'Select payment method',
                            ),
                            items:
                                _paymentMethodOptions
                                    .map(
                                      (method) => DropdownMenuItem(
                                        value: method,
                                        child: Text(method),
                                      ),
                                    )
                                    .toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedPaymentMethod = value;
                              });
                            },
                          ),
                          const SizedBox(height: 16),

                          // Confirmation Number
                          const Text(
                            'Confirmation Number (Optional)',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _confirmationNumberController,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              hintText: 'Enter confirmation number',
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Notes
                          const Text(
                            'Notes (Optional)',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _notesController,
                            decoration: InputDecoration(
                              border: const OutlineInputBorder(),
                              hintText: 'Add any additional notes',
                              alignLabelWithHint: true,
                              suffixIcon:
                                  _isRecording
                                      ? const Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: CircularProgressIndicator(),
                                      )
                                      : IconButton(
                                        icon: Icon(
                                          _isRecording ? Icons.stop : Icons.mic,
                                          color:
                                              _isRecording
                                                  ? Colors.red
                                                  : Theme.of(
                                                    context,
                                                  ).colorScheme.primary,
                                        ),
                                        onPressed:
                                            _isRecording
                                                ? _stopRecording
                                                : _startRecording,
                                        tooltip:
                                            _isRecording
                                                ? 'Stop recording'
                                                : 'Record voice note',
                                      ),
                            ),
                            maxLines: 3,
                          ),
                          if (_isRecording)
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                'Recording... Speak clearly to capture payment details.',
                                style: TextStyle(color: Colors.red),
                              ),
                            ),
                          const SizedBox(height: 24),

                          if (_errorMessage != null)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(color: Colors.red),
                              ),
                            ),

                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _saveTaxPayment,
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                              ),
                              child: Text(
                                _isEditing ? 'Update Payment' : 'Save Payment',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
        },
      ),
    );
  }
}
