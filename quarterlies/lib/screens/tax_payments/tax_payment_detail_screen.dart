import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'tax_payment_form_screen.dart';

class TaxPaymentDetailScreen extends StatefulWidget {
  final String taxPaymentId;

  const TaxPaymentDetailScreen({super.key, required this.taxPaymentId});

  @override
  State<TaxPaymentDetailScreen> createState() => _TaxPaymentDetailScreenState();
}

class _TaxPaymentDetailScreenState extends State<TaxPaymentDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  String? _errorMessage;
  TaxPayment? _taxPayment;

  @override
  void initState() {
    super.initState();
    _loadTaxPaymentDetails();
  }

  Future<void> _loadTaxPaymentDetails() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadTaxPaymentDetails',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final taxPayment = await _supabaseService.getTaxPaymentById(
            widget.taxPaymentId,
          );

          if (!mounted) return;

          setState(() {
            _taxPayment = taxPayment;
          });
        },
        message: 'Loading tax payment details...',
        errorMessage: 'Failed to load tax payment details',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load tax payment details: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deleteTaxPayment() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this tax payment? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (!confirmed || !mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'deleteTaxPayment',
        () async {
          await _supabaseService.deleteTaxPayment(widget.taxPaymentId);
          if (mounted) {
            // Show success feedback
            ErrorDisplay.showSuccess(
              context,
              'Tax payment deleted successfully',
            );
            Navigator.pop(context); // Return to the list screen
          }
        },
        message: 'Deleting tax payment...',
        errorMessage: 'Failed to delete tax payment',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to delete tax payment: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tax Payment Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading =
                  loadingProvider.isLoading('loadTaxPaymentDetails') ||
                  loadingProvider.isLoading('deleteTaxPayment');

              return isLoading || _taxPayment == null
                  ? const SizedBox.shrink()
                  : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed:
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => TaxPaymentFormScreen(
                                      taxPayment: _taxPayment,
                                    ),
                              ),
                            ).then((_) => _loadTaxPaymentDetails()),
                        tooltip: 'Edit payment',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: _deleteTaxPayment,
                        tooltip: 'Delete payment',
                      ),
                    ],
                  );
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadTaxPaymentDetails');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading tax payment details...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadTaxPaymentDetails,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _taxPayment == null
              ? const Center(child: Text('Tax payment not found'))
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Tax payment details section
                        AdaptiveDetailSection(
                          title: 'Tax Payment Details',
                          icon: Icons.account_balance,
                          alwaysExpandedInOffice: true,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    _taxPayment!.taxPeriod,
                                    style: TextStyle(
                                      fontSize:
                                          displayProvider.isOfficeMode
                                              ? 16
                                              : 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal:
                                        displayProvider.isOfficeMode ? 10 : 12,
                                    vertical:
                                        displayProvider.isOfficeMode ? 4 : 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    NumberFormat.currency(
                                      symbol: '\$',
                                    ).format(_taxPayment!.amount),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize:
                                          displayProvider.isOfficeMode
                                              ? 12
                                              : 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: displayProvider.isOfficeMode ? 12 : 16,
                            ),

                            if (displayProvider.isOfficeMode) ...[
                              // Office Mode: Compact grid layout
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Payment Date',
                                      value: DateFormat.yMMMMd().format(
                                        _taxPayment!.date,
                                      ),
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Amount',
                                      value: NumberFormat.currency(
                                        symbol: '\$',
                                      ).format(_taxPayment!.amount),
                                      icon: Icons.attach_money,
                                    ),
                                  ),
                                ],
                              ),
                              if (_taxPayment!.paymentMethod != null) ...[
                                const SizedBox(height: 8),
                                AdaptiveInfoRow(
                                  label: 'Payment Method',
                                  value: _taxPayment!.paymentMethod!,
                                  icon: Icons.credit_card,
                                ),
                              ],
                              if (_taxPayment!.confirmationNumber != null) ...[
                                const SizedBox(height: 8),
                                AdaptiveInfoRow(
                                  label: 'Confirmation Number',
                                  value: _taxPayment!.confirmationNumber!,
                                  icon: Icons.confirmation_number,
                                ),
                              ],
                            ] else ...[
                              // Field Mode: Existing layout
                              _buildDetailRow(
                                'Payment Date',
                                DateFormat.yMMMMd().format(_taxPayment!.date),
                              ),
                              if (_taxPayment!.paymentMethod != null)
                                _buildDetailRow(
                                  'Payment Method',
                                  _taxPayment!.paymentMethod!,
                                ),
                              if (_taxPayment!.confirmationNumber != null)
                                _buildDetailRow(
                                  'Confirmation Number',
                                  _taxPayment!.confirmationNumber!,
                                ),
                            ],
                          ],
                        ),

                        if (_taxPayment!.notes != null &&
                            _taxPayment!.notes!.isNotEmpty) ...[
                          const Text(
                            'Notes',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Text(_taxPayment!.notes!),
                            ),
                          ),
                        ],

                        const SizedBox(height: 16),
                        const Text(
                          'Payment Information',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                _buildDetailRow(
                                  'Created',
                                  DateFormat.yMMMd().add_jm().format(
                                    _taxPayment!.createdAt,
                                  ),
                                ),
                                _buildDetailRow(
                                  'Last Updated',
                                  DateFormat.yMMMd().add_jm().format(
                                    _taxPayment!.updatedAt,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
        },
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
