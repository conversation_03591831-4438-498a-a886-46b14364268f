import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';

class CreateSigningRequestScreen extends StatefulWidget {
  final String documentType; // 'estimate' or 'contract'
  final String documentId;
  final Uint8List documentBytes;
  final Customer customer;
  final Job job;

  const CreateSigningRequestScreen({
    super.key,
    required this.documentType,
    required this.documentId,
    required this.documentBytes,
    required this.customer,
    required this.job,
  });

  @override
  State<CreateSigningRequestScreen> createState() =>
      _CreateSigningRequestScreenState();
}

class _CreateSigningRequestScreenState
    extends State<CreateSigningRequestScreen> {
  String? _errorMessage;

  final DocumentSigningService _documentSigningService =
      DocumentSigningService();

  @override
  void initState() {
    super.initState();
    // Automatically create the signing request when the screen loads
    _createSigningRequest();
  }

  Future<void> _createSigningRequest() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'createSigningRequest',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Create the signing request with automatic email sending
          await _documentSigningService.createSigningRequest(
            pdfBytes: widget.documentBytes,
            documentType: widget.documentType,
            documentId: widget.documentId,
            customerId: widget.customer.id,
            customerEmail: widget.customer.email ?? '',
            customerName: widget.customer.name,
            job: widget.job,
          );

          // Show email sent feedback
          if (mounted) {
            ErrorDisplay.showOperation(context, FeedbackMessages.emailSent);
          }
        },
        message: 'Sending ${widget.documentType} for signature...',
        errorMessage: 'Failed to create signing request',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error creating signing request: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<DisplaySettingsProvider, LoadingStateProvider>(
      builder: (context, displayProvider, loadingProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'Sending ${widget.documentType.toUpperCase()} for Signature',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 16 : 18,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            toolbarHeight: displayProvider.isOfficeMode ? 56 : 64,
          ),
          body: Padding(
            padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
            child: _buildContent(displayProvider, loadingProvider),
          ),
        );
      },
    );
  }

  Widget _buildContent(
    DisplaySettingsProvider displayProvider,
    LoadingStateProvider loadingProvider,
  ) {
    final isLoading = loadingProvider.isLoading('createSigningRequest');

    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            QuarterliesLoadingIndicator(
              message:
                  'Sending ${widget.documentType} for electronic signature...',
              size: 32.0,
              showOfflineStatus: true,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
            Text(
              'Please wait while we prepare your document for signing...',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: displayProvider.isOfficeMode ? 56 : 64,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
            Text(
              'Error sending signature request',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 18 : 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red,
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
            ElevatedButton(
              onPressed: _createSigningRequest,
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: displayProvider.isOfficeMode ? 20 : 24,
                  vertical: displayProvider.isOfficeMode ? 10 : 12,
                ),
              ),
              child: Text(
                'Try Again',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 14 : 16,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Success view
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: displayProvider.isOfficeMode ? 56 : 64,
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
          Text(
            '${widget.documentType.toUpperCase()} Sent Successfully',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 18 : 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
          Text(
            'An email has been sent to ${widget.customer.email} with a link to sign the ${widget.documentType}.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: displayProvider.isOfficeMode ? 14 : 16),
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
          Text(
            'What happens next?',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 14 : 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
          Container(
            padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.blue.shade700,
                      size: displayProvider.isOfficeMode ? 18 : 20,
                    ),
                    SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
                    Text(
                      'Electronic Signature Process',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 10 : 12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '1. ',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Your customer will receive an email with signing instructions and a secure link',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '2. ',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'They can review the document and sign it electronically with a legally binding signature',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '3. ',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'A certification document will be generated to verify the authenticity of the signature',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '4. ',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Both you and your customer will receive email confirmations with the signed document',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '5. ',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'The signed document with certification will be available in your account',
                        style: TextStyle(
                          fontSize: displayProvider.isOfficeMode ? 12 : 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: displayProvider.isOfficeMode ? 28 : 32,
                vertical: displayProvider.isOfficeMode ? 10 : 12,
              ),
            ),
            child: Text(
              'Done',
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 14 : 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
