import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/document_viewer_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:quarterlies/widgets/error_display_widgets.dart';

class SignedDocumentsScreen extends StatefulWidget {
  const SignedDocumentsScreen({super.key});

  @override
  State<SignedDocumentsScreen> createState() => _SignedDocumentsScreenState();
}

class _SignedDocumentsScreenState extends State<SignedDocumentsScreen> {
  final DocumentSigningService _documentSigningService =
      DocumentSigningService();

  String? _errorMessage;
  List<Map<String, dynamic>> _signingRequests = [];

  @override
  void initState() {
    super.initState();
    _loadSigningRequests();
  }

  Future<void> _loadSigningRequests() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadSigningRequests',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final requests = await _documentSigningService.getSigningRequests();

          // Sort by created_at date, newest first
          requests.sort((a, b) {
            final aDate = DateTime.parse(a['created_at']);
            final bDate = DateTime.parse(b['created_at']);
            return bDate.compareTo(aDate);
          });

          if (!mounted) return;

          setState(() {
            _signingRequests = requests;
          });
        },
        message: 'Loading signing requests...',
        errorMessage: 'Failed to load signing requests',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error loading signing requests: $e';
        });
      }
    }
  }

  Future<void> _openDocument(String documentUrl) async {
    try {
      final uri = Uri.parse(documentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $documentUrl';
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error opening document: $e');
      }
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'viewed':
        return 'Viewed';
      case 'signed':
        return 'Signed';
      case 'expired':
        return 'Expired';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'viewed':
        return Colors.blue;
      case 'signed':
        return Colors.green;
      case 'expired':
        return Colors.red;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Signing Requests'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSigningRequests,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Stack(
        children: [
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isLoading = loadingProvider.isLoading(
                'loadSigningRequests',
              );

              return isLoading
                  ? const Center(
                    child: QuarterliesLoadingIndicator(
                      message: 'Loading signing requests...',
                      size: 32.0,
                    ),
                  )
                  : _errorMessage != null
                  ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadSigningRequests,
                            child: const Text('Try Again'),
                          ),
                        ],
                      ),
                    ),
                  )
                  : _signingRequests.isEmpty
                  ? const Center(
                    child: Text(
                      'No document signing requests found',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                  : Consumer<DisplaySettingsProvider>(
                    builder: (context, displayProvider, child) {
                      return ListView.builder(
                        padding: EdgeInsets.symmetric(
                          horizontal: displayProvider.isOfficeMode ? 8.0 : 16.0,
                          vertical: displayProvider.isOfficeMode ? 4.0 : 8.0,
                        ),
                        itemCount: _signingRequests.length,
                        itemBuilder: (context, index) {
                          final request = _signingRequests[index];
                          final documentType =
                              request['document_type'] as String;
                          final documentId = request['document_id'] as String;
                          final customerName =
                              request['customer_name'] as String;
                          final status = request['status'] as String;
                          final createdAt = DateTime.parse(
                            request['created_at'],
                          );
                          final signedAt =
                              request['signed_at'] != null
                                  ? DateTime.parse(request['signed_at'])
                                  : null;

                          return AdaptiveListTile(
                            title: Text(
                              '${documentType.toUpperCase()} #${documentId.substring(0, 8)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Text('Customer: $customerName'),
                            leading: Icon(
                              status == 'signed'
                                  ? Icons.verified
                                  : Icons.description,
                              color: _getStatusColor(status),
                            ),
                            trailing: Chip(
                              label: Text(_getStatusText(status)),
                              backgroundColor: _getStatusColor(
                                status,
                              ).withValues(alpha: 0.2),
                              labelStyle: TextStyle(
                                color: _getStatusColor(status),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            onTap: () {
                              if (status == 'signed') {
                                _showSignedDocumentOptions(request);
                              } else if (request['document_url'] != null) {
                                _openDocument(request['document_url']);
                              }
                            },
                            // Additional info shown only in Office Mode
                            additionalInfo:
                                displayProvider.isOfficeMode
                                    ? OfficeAdditionalInfo(
                                      items: [
                                        InfoItem(
                                          label: 'Created',
                                          value: DateFormat(
                                            'MM/dd/yyyy',
                                          ).format(createdAt),
                                          icon: Icons.calendar_today,
                                        ),
                                        if (signedAt != null)
                                          InfoItem(
                                            label: 'Signed',
                                            value: DateFormat(
                                              'MM/dd/yyyy',
                                            ).format(signedAt),
                                            icon: Icons.check_circle,
                                          ),
                                      ],
                                    )
                                    : null,
                            // Office actions shown only in Office Mode
                            officeActions:
                                displayProvider.isOfficeMode &&
                                        (status == 'pending' ||
                                            status == 'viewed')
                                    ? [
                                      OfficeActionButton(
                                        icon: Icons.send,
                                        label: 'Resend',
                                        onPressed:
                                            () =>
                                                _resendSigningRequest(request),
                                      ),
                                      OfficeActionButton(
                                        icon: Icons.cancel,
                                        label: 'Cancel',
                                        onPressed:
                                            () =>
                                                _cancelSigningRequest(request),
                                      ),
                                    ]
                                    : null,
                          );
                        },
                      );
                    },
                  );
            },
          ),
          // Loading overlay for operations
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              if (loadingProvider.isGeneratingPdf) {
                return LoadingOverlay(
                  isLoading: true,
                  child: PdfLoadingIndicator(
                    documentType: loadingProvider.pdfDocumentType ?? 'Document',
                    progress: loadingProvider.pdfProgress,
                  ),
                );
              } else if (loadingProvider.isLoading('downloadDocument') ||
                  loadingProvider.isLoading('shareDocument')) {
                return LoadingOverlay(
                  isLoading: true,
                  child: QuarterliesLoadingIndicator(
                    message:
                        loadingProvider.isLoading('downloadDocument')
                            ? 'Downloading document...'
                            : 'Preparing to share...',
                    size: 32.0,
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _resendSigningRequest(Map<String, dynamic> request) async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Resend Signing Request'),
              content: Text(
                'Are you sure you want to resend the signing request for ${request['document_type'].toUpperCase()} #${request['document_id'].substring(0, 8)}?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Resend'),
                ),
              ],
            ),
      );

      if (confirmed != true) return;

      // Use the document signing service to resend the request
      await _documentSigningService.resendSigningRequest(request['id']);

      // Trigger email resend (this would typically call an edge function)
      // For now, we'll just show a success message
      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Signing request resent successfully',
        );

        // Refresh the list
        _loadSigningRequests();
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error resending request: $e');
      }
    }
  }

  Future<void> _cancelSigningRequest(Map<String, dynamic> request) async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Cancel Signing Request'),
              content: Text(
                'Are you sure you want to cancel the signing request for ${request['document_type'].toUpperCase()} #${request['document_id'].substring(0, 8)}?\n\nThis action cannot be undone.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Keep Request'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Cancel Request'),
                ),
              ],
            ),
      );

      if (confirmed != true) return;

      // Use the document signing service to cancel the request
      await _documentSigningService.cancelSigningRequest(request['id']);

      if (mounted) {
        ErrorDisplay.showOperation(context, 'Signing request cancelled');

        // Refresh the list
        _loadSigningRequests();
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error cancelling request: $e');
      }
    }
  }

  /// Show options for signed documents (customer copy, contractor copy with certification)
  Future<void> _showSignedDocumentOptions(Map<String, dynamic> request) async {
    final customerUrl = request['signed_document_customer_url'] as String?;
    final contractorUrl = request['signed_document_contractor_url'] as String?;
    final documentType = request['document_type'] as String;
    final documentId = request['document_id'] as String;
    final customerEmail = request['customer_email'] as String;

    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Signed ${documentType.toUpperCase()} Options',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),

                // View Customer Copy
                if (customerUrl != null)
                  ListTile(
                    leading: const Icon(Icons.visibility, color: Colors.blue),
                    title: const Text('View Customer Copy'),
                    subtitle: const Text('Document without certification'),
                    onTap: () {
                      Navigator.pop(context);
                      _viewDocument(customerUrl, 'Customer Copy - $documentId');
                    },
                  ),

                // View Contractor Copy (with certification)
                if (contractorUrl != null)
                  ListTile(
                    leading: const Icon(Icons.verified, color: Colors.green),
                    title: const Text('View Certified Copy'),
                    subtitle: const Text(
                      'Document with signature certification',
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _viewDocument(
                        contractorUrl,
                        'Certified Copy - $documentId',
                      );
                    },
                  ),

                // Download Customer Copy
                if (customerUrl != null)
                  ListTile(
                    leading: const Icon(Icons.download, color: Colors.orange),
                    title: const Text('Download Customer Copy'),
                    subtitle: const Text('Download PDF without certification'),
                    onTap: () {
                      Navigator.pop(context);
                      _downloadDocument(
                        customerUrl,
                        'customer_copy_$documentId.pdf',
                      );
                    },
                  ),

                // Download Certified Copy
                if (contractorUrl != null)
                  ListTile(
                    leading: const Icon(
                      Icons.download_for_offline,
                      color: Colors.green,
                    ),
                    title: const Text('Download Certified Copy'),
                    subtitle: const Text('Download PDF with certification'),
                    onTap: () {
                      Navigator.pop(context);
                      _downloadDocument(
                        contractorUrl,
                        'certified_copy_$documentId.pdf',
                      );
                    },
                  ),

                // Resend to Customer
                ListTile(
                  leading: const Icon(Icons.send, color: Colors.purple),
                  title: const Text('Resend to Customer'),
                  subtitle: Text('Send copy to $customerEmail'),
                  onTap: () {
                    Navigator.pop(context);
                    _resendSignedDocument(request);
                  },
                ),

                // Share Customer Copy
                if (customerUrl != null)
                  ListTile(
                    leading: const Icon(Icons.share, color: Colors.teal),
                    title: const Text('Share Customer Copy'),
                    subtitle: const Text('Share PDF without certification'),
                    onTap: () {
                      Navigator.pop(context);
                      _shareDocument(customerUrl, 'signed_$documentId.pdf');
                    },
                  ),

                // Share Certified Copy
                if (contractorUrl != null)
                  ListTile(
                    leading: const Icon(
                      Icons.share_outlined,
                      color: Colors.green,
                    ),
                    title: const Text('Share Certified Copy'),
                    subtitle: const Text('Share PDF with certification'),
                    onTap: () {
                      Navigator.pop(context);
                      _shareDocument(
                        contractorUrl,
                        'certified_$documentId.pdf',
                      );
                    },
                  ),
              ],
            ),
          ),
    );
  }

  /// View a document using the document viewer
  Future<void> _viewDocument(String documentUrl, String title) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Download the document
        final response = await http.get(Uri.parse(documentUrl));
        if (response.statusCode != 200) {
          throw Exception(
            'Failed to download document: ${response.statusCode}',
          );
        }

        if (!mounted) return;

        // Open in document viewer
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => DocumentViewerScreen(
                  title: title,
                  documentBytes: response.bodyBytes,
                ),
          ),
        );
      }, documentType: 'Document');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error viewing document: $e');
      }
    }
  }

  /// Download a document
  Future<void> _downloadDocument(String documentUrl, String fileName) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'downloadDocument',
        () async {
          if (await canLaunchUrl(Uri.parse(documentUrl))) {
            await launchUrl(
              Uri.parse(documentUrl),
              mode: LaunchMode.externalApplication,
            );
          } else {
            throw 'Could not launch $documentUrl';
          }
        },
        message: 'Downloading $fileName...',
        errorMessage: 'Failed to download document',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error downloading document: $e');
      }
    }
  }

  /// Share a document
  Future<void> _shareDocument(String documentUrl, String fileName) async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'shareDocument',
        () async {
          // For sharing URLs, we use SharePlus.instance.share
          await SharePlus.instance.share(
            ShareParams(
              text: 'Signed Document: $fileName\n\nView document: $documentUrl',
              subject: 'Signed Document: $fileName',
            ),
          );
        },
        message: 'Preparing to share $fileName...',
        errorMessage: 'Failed to share document',
      );
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error sharing document: $e');
      }
    }
  }

  /// Resend a signed document to the customer
  Future<void> _resendSignedDocument(Map<String, dynamic> request) async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Resend Signed Document'),
              content: Text(
                'Are you sure you want to resend the signed ${request['document_type']} to ${request['customer_email']}?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('Resend'),
                ),
              ],
            ),
      );

      if (confirmed != true) return;

      // Call the document signing service to resend the signed document
      await _documentSigningService.resendSignedDocumentToCustomer(
        signingRequestId: request['id'],
        customerEmail: request['customer_email'],
        customerName: request['customer_name'],
        documentType: request['document_type'],
        documentUrl: request['signed_document_customer_url'],
      );

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Signed document resent to ${request['customer_email']}',
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Error resending document: $e');
      }
    }
  }
}
