import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;
import 'job_detail_screen.dart';
import 'job_form_screen.dart';

class JobListScreen extends StatefulWidget {
  const JobListScreen({super.key});

  @override
  State<JobListScreen> createState() => _JobListScreenState();
}

class _JobListScreenState extends State<JobListScreen> {
  final DataRepository _dataRepository = DataRepository(); // Use DataRepository
  List<Job> _jobs = [];
  bool _isOffline = false; // Added for offline status
  String? _errorMessage;
  StreamSubscription<bool>?
  _connectivitySubscription; // Added for connectivity listener

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener(); // Added
    _loadJobs();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel(); // Added
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());
    if (mounted) setState(() {});

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadJobs(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadJobs() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobs',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final jobs = await _dataRepository.getJobs(); // Use DataRepository
          if (!mounted) return;

          setState(() {
            _jobs = jobs;
          });
        },
        message: 'Loading jobs...',
        errorMessage: 'Failed to load jobs',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load jobs: ${e.toString()}';
        });
      }
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Not set';
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColorValue(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'completed':
        return Colors.blue;
      case 'estimate':
        return Colors.orange;
      case 'invoiced':
        return Colors.purple;
      case 'paid':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Icons.play_circle;
      case 'completed':
        return Icons.check_circle;
      case 'estimate':
        return Icons.description;
      case 'invoiced':
        return Icons.receipt;
      case 'paid':
        return Icons.payment;
      default:
        return Icons.work;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveTitle('Jobs'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
            ),
            onPressed: _loadJobs,
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadJobs');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading jobs...',
                    size: 32.0,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: ResponsiveBody(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                )
                : _jobs.isEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const ResponsiveBody('No jobs found'),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const JobFormScreen(),
                            ),
                          ).then((_) => _loadJobs());
                        },
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            double.infinity,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: const ResponsiveBody('Add Job'),
                      ),
                    ],
                  ),
                )
                : RefreshIndicator(
                  onRefresh: _loadJobs,
                  child: ListView.builder(
                    itemCount: _jobs.length,
                    itemBuilder: (context, index) {
                      final job = _jobs[index];
                      return Consumer<DisplaySettingsProvider>(
                        builder: (context, displayProvider, child) {
                          return AdaptiveListTile(
                            title: ResponsiveSubtitle(
                              job.title,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: ResponsiveBody(
                              'Status: ${job.status}${job.estimatedPrice != null ? ' • Est: \$${job.estimatedPrice!.toStringAsFixed(0)}' : ''}',
                            ),
                            leading: Container(
                              padding: spacing.ResponsiveSpacing.getPadding(
                                context,
                                base: 8,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColorValue(job.status),
                                borderRadius: BorderRadius.circular(
                                  spacing.ResponsiveSpacing.getBorderRadius(
                                    context,
                                    base: 8,
                                  ),
                                ),
                              ),
                              child: Icon(
                                _getStatusIcon(job.status),
                                color: Colors.white,
                                size: spacing.ResponsiveSpacing.getIconSize(
                                  context,
                                  base: 20,
                                ),
                              ),
                            ),
                            trailing: ResponsiveLabel(
                              _formatDate(job.startDate),
                            ),
                            // Additional info shown only in Office Mode
                            additionalInfo:
                                displayProvider.isOfficeMode
                                    ? OfficeAdditionalInfo(
                                      items: [
                                        if (job.address != null &&
                                            job.address!.isNotEmpty)
                                          InfoItem(
                                            label: 'Location',
                                            value: job.address!,
                                            icon: Icons.location_on,
                                          ),
                                        if (job.actualIncome != null &&
                                            job.actualIncome! > 0)
                                          InfoItem(
                                            label: 'Income',
                                            value:
                                                '\$${job.actualIncome!.toStringAsFixed(0)}',
                                            icon: Icons.attach_money,
                                          ),
                                        if (job.actualExpenses != null &&
                                            job.actualExpenses! > 0)
                                          InfoItem(
                                            label: 'Expenses',
                                            value:
                                                '\$${job.actualExpenses!.toStringAsFixed(0)}',
                                            icon: Icons.receipt,
                                          ),
                                        InfoItem(
                                          label: 'Profit',
                                          value:
                                              '\$${job.profitLoss.toStringAsFixed(0)}',
                                          icon:
                                              job.profitLoss >= 0
                                                  ? Icons.trending_up
                                                  : Icons.trending_down,
                                        ),
                                      ],
                                    )
                                    : null,
                            // Office actions shown only in Office Mode
                            officeActions:
                                displayProvider.isOfficeMode
                                    ? [
                                      OfficeActionButton(
                                        icon: Icons.edit,
                                        label: 'Edit',
                                        onPressed: () async {
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      JobFormScreen(job: job),
                                            ),
                                          );
                                          if (result == true) {
                                            _loadJobs();
                                          }
                                        },
                                      ),
                                      OfficeActionButton(
                                        icon: Icons.receipt_long,
                                        label: 'Invoices',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Invoices view coming soon!',
                                          );
                                        },
                                      ),
                                      OfficeActionButton(
                                        icon: Icons.attach_money,
                                        label: 'Expenses',
                                        onPressed: () {
                                          ErrorDisplay.showInfo(
                                            context,
                                            'Expenses view coming soon!',
                                          );
                                        },
                                      ),
                                    ]
                                    : null,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) =>
                                          JobDetailScreen(jobId: job.id),
                                ),
                              ).then((_) => _loadJobs());
                            },
                          );
                        },
                      );
                    },
                  ),
                );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const JobFormScreen()),
          ).then((_) => _loadJobs());
        },
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
        ),
      ),
    );
  }
}
