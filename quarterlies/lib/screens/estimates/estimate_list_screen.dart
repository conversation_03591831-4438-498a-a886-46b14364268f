import 'dart:async'; // Added for StreamSubscription
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart'; // Changed from supabase_service
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
// Kept for now, might be removable if DataRepository handles all Supabase interactions
import 'estimate_detail_screen.dart';
import 'estimate_form_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class EstimateListScreen extends StatefulWidget {
  const EstimateListScreen({super.key});

  @override
  State<EstimateListScreen> createState() => _EstimateListScreenState();
}

class _EstimateListScreenState extends State<EstimateListScreen> {
  final DataRepository _dataRepository = DataRepository(); // Use DataRepository
  List<Estimate> _estimates = [];
  List<Estimate> _templates = [];
  bool _isOffline = false; // Added for offline status
  String? _errorMessage;
  bool _showTemplates = false;
  StreamSubscription<bool>?
  _connectivitySubscription; // Added for connectivity listener

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener(); // Added
    _loadEstimates();
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel(); // Added
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());
    if (mounted) setState(() {});

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadEstimates(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadEstimates() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadEstimates',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final estimates =
              await _dataRepository.getEstimates(); // Use DataRepository

          // Separate templates from regular estimates
          final regularEstimates =
              estimates.where((e) => !e.isTemplate).toList();
          final templates = estimates.where((e) => e.isTemplate).toList();

          if (!mounted) return; // Added mounted check
          setState(() {
            _estimates = regularEstimates;
            _templates = templates;
          });
        },
        message: 'Loading estimates...',
        errorMessage: 'Failed to load estimates',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load estimates: ${e.toString()}';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveTitle('Estimates'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Templates toggle button
          IconButton(
            icon: Icon(
              _showTemplates ? Icons.list : Icons.bookmark,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
            ),
            onPressed: () {
              setState(() {
                _showTemplates = !_showTemplates;
              });
            },
            tooltip: _showTemplates ? 'Show Estimates' : 'Show Templates',
          ),
          // Offline status indicator
          if (_isOffline)
            Padding(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 8.0),
              child: Tooltip(
                message: 'You are offline. Using locally stored data.',
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      color: Colors.white,
                      size: spacing.ResponsiveSpacing.getIconSize(
                        context,
                        base: 24,
                      ),
                    ),
                    SizedBox(
                      width: spacing.ResponsiveSpacing.getSpacing(
                        context,
                        base: 4,
                      ),
                    ),
                    ResponsiveLabel(
                      'Offline',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
            ),
            onPressed: _loadEstimates,
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadEstimates');

            return isLoading
                ? const Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading estimates...',
                    size: 32.0,
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadEstimates,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            double.infinity,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: const ResponsiveBody('Retry'),
                      ),
                    ],
                  ),
                )
                : _showTemplates
                ? _buildTemplatesView()
                : _buildEstimatesView();
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => EstimateFormScreen()),
          ).then((_) => _loadEstimates());
        },
        child: Icon(
          Icons.add,
          size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
        ),
      ),
    );
  }

  Widget _buildEstimatesView() {
    if (_estimates.isEmpty) {
      return const Center(child: ResponsiveBody('No estimates found'));
    }

    return RefreshIndicator(
      onRefresh: _loadEstimates,
      child: ListView.builder(
        itemCount: _estimates.length,
        itemBuilder: (context, index) {
          final estimate = _estimates[index];
          return Consumer<DisplaySettingsProvider>(
            builder: (context, displayProvider, child) {
              return AdaptiveListTile(
                title: ResponsiveSubtitle(
                  'Estimate #${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: ResponsiveBody(
                  'Amount: \$${estimate.totalAmount.toStringAsFixed(2)} • Status: ${estimate.status}',
                ),
                leading: Container(
                  padding: spacing.ResponsiveSpacing.getPadding(
                    context,
                    base: 8,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(estimate.status),
                    borderRadius: BorderRadius.circular(
                      spacing.ResponsiveSpacing.getBorderRadius(
                        context,
                        base: 8,
                      ),
                    ),
                  ),
                  child: Icon(
                    _getStatusIcon(estimate.status),
                    color: Colors.white,
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 20,
                    ),
                  ),
                ),
                trailing: Chip(
                  label: ResponsiveLabel(estimate.status),
                  backgroundColor: _getStatusColor(estimate.status),
                ),
                // Additional info shown only in Office Mode
                additionalInfo:
                    displayProvider.isOfficeMode
                        ? OfficeAdditionalInfo(
                          items: [
                            InfoItem(
                              label: 'Line Items',
                              value: '${estimate.lineItems.length} items',
                              icon: Icons.list,
                            ),
                            InfoItem(
                              label: 'Created',
                              value:
                                  '${estimate.createdAt.day}/${estimate.createdAt.month}/${estimate.createdAt.year}',
                              icon: Icons.calendar_today,
                            ),
                            if (estimate.notes != null &&
                                estimate.notes!.isNotEmpty)
                              InfoItem(
                                label: 'Notes',
                                value:
                                    estimate.notes!.length > 30
                                        ? '${estimate.notes!.substring(0, 30)}...'
                                        : estimate.notes!,
                                icon: Icons.note,
                              ),
                          ],
                        )
                        : null,
                // Office actions shown only in Office Mode
                officeActions:
                    displayProvider.isOfficeMode
                        ? [
                          OfficeActionButton(
                            icon: Icons.edit,
                            label: 'Edit',
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => EstimateFormScreen(
                                        estimate: estimate,
                                      ),
                                ),
                              );
                              if (result == true) {
                                _loadEstimates();
                              }
                            },
                          ),
                          OfficeActionButton(
                            icon: Icons.content_copy,
                            label: 'Duplicate',
                            onPressed: () {
                              ErrorDisplay.showInfo(
                                context,
                                'Duplicate functionality coming soon!',
                              );
                            },
                          ),
                          if (estimate.status.toLowerCase() == 'draft')
                            OfficeActionButton(
                              icon: Icons.send,
                              label: 'Send',
                              onPressed: () {
                                ErrorDisplay.showInfo(
                                  context,
                                  'Send functionality coming soon!',
                                );
                              },
                            ),
                        ]
                        : null,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) =>
                              EstimateDetailScreen(estimateId: estimate.id),
                    ),
                  ).then((_) => _loadEstimates());
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildTemplatesView() {
    if (_templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_border,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 64),
              color: Colors.grey,
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 16),
            ),
            ResponsiveSubtitle(
              'No templates found',
              style: const TextStyle(color: Colors.grey),
            ),
            SizedBox(
              height: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
            ),
            ResponsiveBody(
              'Create an estimate and save it as a template',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadEstimates,
      child: ListView.builder(
        itemCount: _templates.length,
        itemBuilder: (context, index) {
          final template = _templates[index];
          return Card(
            margin: spacing.ResponsiveSpacing.getPadding(
              context,
              base: 16,
            ).copyWith(top: 8, bottom: 8),
            child: ListTile(
              leading: Icon(
                Icons.bookmark,
                color: Colors.orange,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 24),
              ),
              title: ResponsiveSubtitle(
                template.templateName ?? 'Template #${index + 1}',
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveBody(
                    'Amount: \$${template.totalAmount.toStringAsFixed(2)}',
                  ),
                  ResponsiveBody('${template.lineItems.length} line items'),
                ],
              ),
              trailing: PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'use') {
                    _useTemplate(template);
                  } else if (value == 'edit') {
                    _editTemplate(template);
                  }
                },
                itemBuilder:
                    (context) => [
                      PopupMenuItem(
                        value: 'use',
                        child: Row(
                          children: [
                            Icon(
                              Icons.add,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 20,
                              ),
                            ),
                            SizedBox(
                              width: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8,
                              ),
                            ),
                            const ResponsiveBody('Use Template'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(
                              Icons.edit,
                              size: spacing.ResponsiveSpacing.getIconSize(
                                context,
                                base: 20,
                              ),
                            ),
                            SizedBox(
                              width: spacing.ResponsiveSpacing.getSpacing(
                                context,
                                base: 8,
                              ),
                            ),
                            const ResponsiveBody('Edit Template'),
                          ],
                        ),
                      ),
                    ],
              ),
              onTap: () => _useTemplate(template),
            ),
          );
        },
      ),
    );
  }

  void _useTemplate(Estimate template) {
    // Create a new estimate based on the template
    final newEstimate = template.copyWith(
      id: null, // Will generate new ID
      isTemplate: false,
      status: 'draft',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EstimateFormScreen(estimate: newEstimate),
      ),
    ).then((_) => _loadEstimates());
  }

  void _editTemplate(Estimate template) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EstimateDetailScreen(estimateId: template.id),
      ),
    ).then((_) => _loadEstimates());
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return Colors.grey;
      case 'sent':
        return Colors.blue;
      case 'accepted':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return Icons.edit;
      case 'sent':
        return Icons.send;
      case 'accepted':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.description;
    }
  }
}
