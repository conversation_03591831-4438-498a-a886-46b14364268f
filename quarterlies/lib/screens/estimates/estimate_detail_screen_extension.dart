import 'package:flutter/material.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/screens/document_viewer_screen.dart';
import 'package:quarterlies/services/estimate_pdf_service.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';

/// This extension file contains methods to add document viewing functionality
/// to the EstimateDetailScreen. Import this file in your estimate_detail_screen.dart
/// and use the methods to implement document viewing.

/// Example usage in EstimateDetailScreen:
///
/// ```dart
/// // Add to imports
/// import 'estimate_detail_screen_extension.dart';
///
/// // Add to your AppBar actions
/// actions: [
///   // View PDF button
///   if (_estimate != null)
///     IconButton(
///       icon: _isGeneratingPdf
///         ? const SizedBox(
///             width: 24,
///             height: 24,
///             child: CircularProgressIndicator(
///               color: Colors.white,
///               strokeWidth: 2,
///             ),
///           )
///         : const Icon(Icons.visibility),
///       tooltip: 'View Estimate',
///       onPressed: _isGeneratingPdf ? null : () => viewEstimatePdf(
///         context: context,
///         estimate: _estimate!,
///         customer: _customer!,
///         job: _job!,
///         estimatePdfService: _estimatePdfService,
///         onPdfGenerationStarted: () => setState(() => _isGeneratingPdf = true),
///         onPdfGenerationCompleted: () => setState(() => _isGeneratingPdf = false),
///       ),
///     ),
/// ]
/// ```

/// View estimate as PDF in the app
Future<void> viewEstimatePdf({
  required BuildContext context,
  required Estimate estimate,
  required Customer customer,
  required Job job,
  required EstimatePdfService estimatePdfService,
  required VoidCallback onPdfGenerationStarted,
  required VoidCallback onPdfGenerationCompleted,
}) async {
  // Validate required data is available
  onPdfGenerationStarted();

  try {
    // Generate PDF document
    final pdfBytes = await estimatePdfService.generateEstimatePdf(
      estimate: estimate,
      customer: customer,
      job: job,
    );

    if (!context.mounted) return;

    // Open the PDF in the document viewer
    final estimateNumber = estimate.id.substring(0, 8);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => DocumentViewerScreen(
              title: 'Estimate #$estimateNumber',
              documentBytes: pdfBytes,
            ),
      ),
    );
  } catch (e) {
    if (context.mounted) {
      ErrorDisplay.showWarning(
        context,
        'Error generating PDF: ${e.toString()}',
      );
    }
  } finally {
    onPdfGenerationCompleted();
  }
}
