import 'package:flutter/material.dart';

/// Helper class for responsive design and mobile-first experience
class ResponsiveHelper {
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;

  // Minimum touch target sizes (following Material Design guidelines)
  static const double minTouchTarget = 48.0;
  static const double preferredTouchTarget = 56.0;

  // Field-friendly spacing
  static const double fieldSpacingSmall = 8.0;
  static const double fieldSpacingMedium = 16.0;
  static const double fieldSpacingLarge = 24.0;

  // Text sizes optimized for outdoor visibility
  static const double textSizeSmall = 12.0;
  static const double textSizeMedium = 16.0;
  static const double textSizeLarge = 18.0;
  static const double textSizeXLarge = 20.0;

  /// Check if the current screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < _mobileBreakpoint;
  }

  /// Check if the current screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= _mobileBreakpoint && width < _tabletBreakpoint;
  }

  /// Check if the current screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= _tabletBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(fieldSpacingMedium);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(fieldSpacingLarge);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.symmetric(
        horizontal: fieldSpacingMedium,
        vertical: fieldSpacingSmall,
      );
    } else {
      return const EdgeInsets.symmetric(
        horizontal: fieldSpacingLarge,
        vertical: fieldSpacingMedium,
      );
    }
  }

  /// Get responsive button height for better touch targets
  static double getButtonHeight(BuildContext context) {
    return isMobile(context) ? preferredTouchTarget : minTouchTarget;
  }

  /// Get responsive icon size
  static double getIconSize(BuildContext context, {bool isLarge = false}) {
    if (isMobile(context)) {
      return isLarge ? 32.0 : 24.0;
    } else {
      return isLarge ? 28.0 : 20.0;
    }
  }

  /// Get responsive text size
  static double getTextSize(BuildContext context, TextSizeType type) {
    final scaleFactor = isMobile(context) ? 1.0 : 0.9;

    switch (type) {
      case TextSizeType.small:
        return textSizeSmall * scaleFactor;
      case TextSizeType.medium:
        return textSizeMedium * scaleFactor;
      case TextSizeType.large:
        return textSizeLarge * scaleFactor;
      case TextSizeType.xLarge:
        return textSizeXLarge * scaleFactor;
    }
  }

  /// Get responsive column count for grid layouts
  static int getColumnCount(BuildContext context) {
    if (isMobile(context)) {
      return 1;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 3;
    }
  }

  /// Get field-friendly card elevation
  static double getCardElevation(BuildContext context) {
    // Higher elevation for better visibility in bright conditions
    return isMobile(context) ? 6.0 : 4.0;
  }

  /// Get responsive border radius
  static double getBorderRadius(BuildContext context) {
    return isMobile(context) ? 12.0 : 8.0;
  }

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Check if keyboard is visible
  static bool isKeyboardVisible(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }

  /// Get responsive app bar height
  static double getAppBarHeight(BuildContext context) {
    return isMobile(context) ? 64.0 : 56.0;
  }

  /// Get responsive bottom navigation height
  static double getBottomNavHeight(BuildContext context) {
    return isMobile(context) ? 72.0 : 64.0;
  }

  /// Get responsive floating action button size
  static double getFabSize(BuildContext context) {
    return isMobile(context) ? 64.0 : 56.0;
  }

  /// Get responsive list tile height
  static double getListTileHeight(BuildContext context) {
    return isMobile(context) ? 72.0 : 64.0;
  }

  /// Get responsive spacing between form fields
  static double getFormFieldSpacing(BuildContext context) {
    return isMobile(context) ? fieldSpacingLarge : fieldSpacingMedium;
  }

  /// Get responsive dialog width
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (isMobile(context)) {
      return screenWidth * 0.9;
    } else if (isTablet(context)) {
      return screenWidth * 0.7;
    } else {
      return 600.0;
    }
  }

  /// Get responsive maximum width for content
  static double getMaxContentWidth(BuildContext context) {
    if (isMobile(context)) {
      return double.infinity;
    } else if (isTablet(context)) {
      return 800.0;
    } else {
      return 1200.0;
    }
  }

  /// Get adaptive grid layout based on screen size and orientation
  static GridLayoutConfig getGridLayout(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (isMobile(context)) {
      return GridLayoutConfig(
        crossAxisCount: isLandscapeMode ? 2 : 1,
        childAspectRatio: isLandscapeMode ? 1.5 : 1.2,
        mainAxisSpacing: fieldSpacingMedium,
        crossAxisSpacing: fieldSpacingMedium,
      );
    } else if (isTablet(context)) {
      return GridLayoutConfig(
        crossAxisCount: isLandscapeMode ? 4 : 3,
        childAspectRatio: isLandscapeMode ? 1.3 : 1.1,
        mainAxisSpacing: fieldSpacingLarge,
        crossAxisSpacing: fieldSpacingLarge,
      );
    } else {
      return GridLayoutConfig(
        crossAxisCount: isLandscapeMode ? 6 : 4,
        childAspectRatio: 1.2,
        mainAxisSpacing: fieldSpacingLarge,
        crossAxisSpacing: fieldSpacingLarge,
      );
    }
  }

  /// Get tablet-specific layout configuration
  static TabletLayoutConfig getTabletLayout(BuildContext context) {
    if (!isTablet(context)) {
      throw Exception(
        'getTabletLayout should only be called on tablet devices',
      );
    }

    final isLandscapeMode = isLandscape(context);

    return TabletLayoutConfig(
      useSideNavigation: isLandscapeMode,
      sideNavigationWidth: 280.0,
      contentPadding: EdgeInsets.symmetric(
        horizontal: isLandscapeMode ? 32.0 : 24.0,
        vertical: 24.0,
      ),
      useTwoColumnLayout: isLandscapeMode,
      primaryColumnFlex: 2,
      secondaryColumnFlex: 1,
    );
  }

  /// Get orientation-specific optimizations
  static OrientationConfig getOrientationConfig(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    return OrientationConfig(
      isLandscape: isLandscapeMode,
      preferHorizontalLayout: isLandscapeMode && !isMobile(context),
      adjustedPadding: EdgeInsets.symmetric(
        horizontal: isLandscapeMode ? fieldSpacingLarge : fieldSpacingMedium,
        vertical: isLandscapeMode ? fieldSpacingMedium : fieldSpacingLarge,
      ),
      compactVerticalSpacing: isLandscapeMode,
      enhancedHorizontalSpacing: isLandscapeMode,
    );
  }

  /// Get adaptive form layout
  static FormLayoutConfig getFormLayout(BuildContext context) {
    final isLandscapeMode = isLandscape(context);

    if (isMobile(context)) {
      return FormLayoutConfig(
        fieldsPerRow: isLandscapeMode ? 2 : 1,
        fieldSpacing: fieldSpacingMedium,
        sectionSpacing: fieldSpacingLarge,
        useCompactLabels: isLandscapeMode,
        stackButtons: !isLandscapeMode,
      );
    } else if (isTablet(context)) {
      return FormLayoutConfig(
        fieldsPerRow: isLandscapeMode ? 3 : 2,
        fieldSpacing: fieldSpacingLarge,
        sectionSpacing: 32.0,
        useCompactLabels: false,
        stackButtons: false,
      );
    } else {
      return FormLayoutConfig(
        fieldsPerRow: isLandscapeMode ? 4 : 3,
        fieldSpacing: fieldSpacingLarge,
        sectionSpacing: 40.0,
        useCompactLabels: false,
        stackButtons: false,
      );
    }
  }

  /// Get adaptive navigation configuration
  static NavigationConfig getNavigationConfig(BuildContext context) {
    if (isMobile(context)) {
      return NavigationConfig(
        type: NavigationType.bottomNavigation,
        showLabels: true,
        iconSize: 24.0,
        selectedIconSize: 28.0,
      );
    } else if (isTablet(context)) {
      final isLandscapeMode = isLandscape(context);
      return NavigationConfig(
        type:
            isLandscapeMode
                ? NavigationType.sideNavigation
                : NavigationType.bottomNavigation,
        showLabels: true,
        iconSize: 20.0,
        selectedIconSize: 24.0,
      );
    } else {
      return NavigationConfig(
        type: NavigationType.sideNavigation,
        showLabels: true,
        iconSize: 20.0,
        selectedIconSize: 22.0,
      );
    }
  }
}

/// Enum for text size types
enum TextSizeType { small, medium, large, xLarge }

/// Extension to add responsive methods to BuildContext
extension ResponsiveContext on BuildContext {
  bool get isMobile => ResponsiveHelper.isMobile(this);
  bool get isTablet => ResponsiveHelper.isTablet(this);
  bool get isDesktop => ResponsiveHelper.isDesktop(this);
  bool get isLandscape => ResponsiveHelper.isLandscape(this);
  bool get isKeyboardVisible => ResponsiveHelper.isKeyboardVisible(this);

  EdgeInsets get responsivePadding =>
      ResponsiveHelper.getResponsivePadding(this);
  EdgeInsets get responsiveMargin => ResponsiveHelper.getResponsiveMargin(this);
  EdgeInsets get safeAreaPadding => ResponsiveHelper.getSafeAreaPadding(this);

  double get buttonHeight => ResponsiveHelper.getButtonHeight(this);
  double get cardElevation => ResponsiveHelper.getCardElevation(this);
  double get borderRadius => ResponsiveHelper.getBorderRadius(this);
  double get appBarHeight => ResponsiveHelper.getAppBarHeight(this);
  double get bottomNavHeight => ResponsiveHelper.getBottomNavHeight(this);
  double get fabSize => ResponsiveHelper.getFabSize(this);
  double get listTileHeight => ResponsiveHelper.getListTileHeight(this);
  double get formFieldSpacing => ResponsiveHelper.getFormFieldSpacing(this);
  double get dialogWidth => ResponsiveHelper.getDialogWidth(this);
  double get maxContentWidth => ResponsiveHelper.getMaxContentWidth(this);
  double get keyboardHeight => ResponsiveHelper.getKeyboardHeight(this);

  int get columnCount => ResponsiveHelper.getColumnCount(this);

  double getIconSize({bool isLarge = false}) =>
      ResponsiveHelper.getIconSize(this, isLarge: isLarge);
  double getTextSize(TextSizeType type) =>
      ResponsiveHelper.getTextSize(this, type);
}

/// Widget that provides responsive constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? context.maxContentWidth,
      ),
      padding: padding ?? context.responsivePadding,
      margin: margin ?? context.responsiveMargin,
      child: child,
    );
  }
}

/// Widget that provides responsive spacing
class ResponsiveSpacing extends StatelessWidget {
  final double? height;
  final double? width;
  final SpacingType type;

  const ResponsiveSpacing({
    super.key,
    this.height,
    this.width,
    this.type = SpacingType.medium,
  });

  @override
  Widget build(BuildContext context) {
    double spacing;
    switch (type) {
      case SpacingType.small:
        spacing = ResponsiveHelper.fieldSpacingSmall;
        break;
      case SpacingType.medium:
        spacing = ResponsiveHelper.fieldSpacingMedium;
        break;
      case SpacingType.large:
        spacing = ResponsiveHelper.fieldSpacingLarge;
        break;
    }

    return SizedBox(height: height ?? spacing, width: width ?? spacing);
  }
}

enum SpacingType { small, medium, large }

/// Configuration classes for advanced responsive features

/// Grid layout configuration
class GridLayoutConfig {
  final int crossAxisCount;
  final double childAspectRatio;
  final double mainAxisSpacing;
  final double crossAxisSpacing;

  const GridLayoutConfig({
    required this.crossAxisCount,
    required this.childAspectRatio,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
  });
}

/// Tablet-specific layout configuration
class TabletLayoutConfig {
  final bool useSideNavigation;
  final double sideNavigationWidth;
  final EdgeInsets contentPadding;
  final bool useTwoColumnLayout;
  final int primaryColumnFlex;
  final int secondaryColumnFlex;

  const TabletLayoutConfig({
    required this.useSideNavigation,
    required this.sideNavigationWidth,
    required this.contentPadding,
    required this.useTwoColumnLayout,
    required this.primaryColumnFlex,
    required this.secondaryColumnFlex,
  });
}

/// Orientation-specific configuration
class OrientationConfig {
  final bool isLandscape;
  final bool preferHorizontalLayout;
  final EdgeInsets adjustedPadding;
  final bool compactVerticalSpacing;
  final bool enhancedHorizontalSpacing;

  const OrientationConfig({
    required this.isLandscape,
    required this.preferHorizontalLayout,
    required this.adjustedPadding,
    required this.compactVerticalSpacing,
    required this.enhancedHorizontalSpacing,
  });
}

/// Form layout configuration
class FormLayoutConfig {
  final int fieldsPerRow;
  final double fieldSpacing;
  final double sectionSpacing;
  final bool useCompactLabels;
  final bool stackButtons;

  const FormLayoutConfig({
    required this.fieldsPerRow,
    required this.fieldSpacing,
    required this.sectionSpacing,
    required this.useCompactLabels,
    required this.stackButtons,
  });
}

/// Navigation configuration
class NavigationConfig {
  final NavigationType type;
  final bool showLabels;
  final double iconSize;
  final double selectedIconSize;

  const NavigationConfig({
    required this.type,
    required this.showLabels,
    required this.iconSize,
    required this.selectedIconSize,
  });
}

/// Navigation type enum
enum NavigationType { bottomNavigation, sideNavigation, tabNavigation }
