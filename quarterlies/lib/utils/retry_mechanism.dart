import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/utils/app_constants.dart';

/// Configuration for retry behavior
class RetryConfig {
  final int maxAttempts;
  final Duration initialDelay;
  final Duration maxDelay;
  final double backoffMultiplier;
  final bool useExponentialBackoff;
  final List<ErrorType> retryableErrorTypes;

  const RetryConfig({
    this.maxAttempts = AppConstants.maxErrorRetryAttempts,
    this.initialDelay = const Duration(seconds: AppConstants.errorRetryDelaySeconds),
    this.maxDelay = const Duration(seconds: 30),
    this.backoffMultiplier = 2.0,
    this.useExponentialBackoff = true,
    this.retryableErrorTypes = const [
      ErrorType.network,
      ErrorType.timeout,
      ErrorType.database,
    ],
  });
}

/// Result of a retry operation
class RetryResult<T> {
  final T? data;
  final AppError? error;
  final int attemptCount;
  final bool isSuccess;

  RetryResult._({
    this.data,
    this.error,
    required this.attemptCount,
    required this.isSuccess,
  });

  factory RetryResult.success(T data, int attemptCount) {
    return RetryResult._(
      data: data,
      attemptCount: attemptCount,
      isSuccess: true,
    );
  }

  factory RetryResult.failure(AppError error, int attemptCount) {
    return RetryResult._(
      error: error,
      attemptCount: attemptCount,
      isSuccess: false,
    );
  }
}

/// Utility class for implementing retry mechanisms with exponential backoff
class RetryMechanism {
  RetryMechanism._();

  /// Execute a function with retry logic
  static Future<RetryResult<T>> execute<T>(
    Future<T> Function() operation, {
    RetryConfig config = const RetryConfig(),
    String? operationName,
  }) async {
    int attemptCount = 0;
    AppError? lastError;

    while (attemptCount < config.maxAttempts) {
      attemptCount++;

      try {
        if (kDebugMode && operationName != null) {
          debugPrint('Executing $operationName (attempt $attemptCount/${config.maxAttempts})');
        }

        final result = await operation();
        
        if (kDebugMode && operationName != null && attemptCount > 1) {
          debugPrint('$operationName succeeded on attempt $attemptCount');
        }

        return RetryResult.success(result, attemptCount);
      } catch (error, stackTrace) {
        final appError = AppError.fromException(
          error,
          stackTrace: stackTrace,
          context: {
            'operation': operationName ?? 'unknown',
            'attempt': attemptCount,
            'maxAttempts': config.maxAttempts,
          },
        );

        lastError = appError;
        ErrorHandler.logError(appError);

        // Check if this error type should be retried
        if (!_shouldRetryError(appError, config)) {
          if (kDebugMode && operationName != null) {
            debugPrint('$operationName failed with non-retryable error: ${appError.type}');
          }
          return RetryResult.failure(appError, attemptCount);
        }

        // If this was the last attempt, don't wait
        if (attemptCount >= config.maxAttempts) {
          if (kDebugMode && operationName != null) {
            debugPrint('$operationName failed after $attemptCount attempts');
          }
          break;
        }

        // Calculate delay for next attempt
        final delay = _calculateDelay(attemptCount, config);
        
        if (kDebugMode && operationName != null) {
          debugPrint('$operationName failed on attempt $attemptCount, retrying in ${delay.inMilliseconds}ms');
        }

        await Future.delayed(delay);
      }
    }

    return RetryResult.failure(lastError!, attemptCount);
  }

  /// Execute a function with retry logic and progress callback
  static Future<RetryResult<T>> executeWithProgress<T>(
    Future<T> Function() operation, {
    RetryConfig config = const RetryConfig(),
    String? operationName,
    void Function(int attempt, int maxAttempts, AppError? lastError)? onProgress,
  }) async {
    int attemptCount = 0;
    AppError? lastError;

    while (attemptCount < config.maxAttempts) {
      attemptCount++;

      // Notify progress
      onProgress?.call(attemptCount, config.maxAttempts, lastError);

      try {
        final result = await operation();
        return RetryResult.success(result, attemptCount);
      } catch (error, stackTrace) {
        final appError = AppError.fromException(
          error,
          stackTrace: stackTrace,
          context: {
            'operation': operationName ?? 'unknown',
            'attempt': attemptCount,
            'maxAttempts': config.maxAttempts,
          },
        );

        lastError = appError;
        ErrorHandler.logError(appError);

        if (!_shouldRetryError(appError, config) || attemptCount >= config.maxAttempts) {
          break;
        }

        final delay = _calculateDelay(attemptCount, config);
        await Future.delayed(delay);
      }
    }

    return RetryResult.failure(lastError!, attemptCount);
  }

  /// Execute multiple operations with retry logic
  static Future<List<RetryResult<T>>> executeMultiple<T>(
    List<Future<T> Function()> operations, {
    RetryConfig config = const RetryConfig(),
    bool stopOnFirstFailure = false,
  }) async {
    final results = <RetryResult<T>>[];

    for (int i = 0; i < operations.length; i++) {
      final result = await execute(
        operations[i],
        config: config,
        operationName: 'Operation ${i + 1}',
      );

      results.add(result);

      if (stopOnFirstFailure && !result.isSuccess) {
        break;
      }
    }

    return results;
  }

  /// Check if an error should be retried based on configuration
  static bool _shouldRetryError(AppError error, RetryConfig config) {
    return config.retryableErrorTypes.contains(error.type) &&
           ErrorHandler.shouldRetry(error);
  }

  /// Calculate delay for next retry attempt
  static Duration _calculateDelay(int attemptCount, RetryConfig config) {
    if (!config.useExponentialBackoff) {
      return config.initialDelay;
    }

    // Exponential backoff with jitter
    final exponentialDelay = config.initialDelay.inMilliseconds *
        pow(config.backoffMultiplier, attemptCount - 1);

    // Add jitter (random factor between 0.5 and 1.5)
    final jitter = 0.5 + Random().nextDouble();
    final delayWithJitter = (exponentialDelay * jitter).round();

    // Cap at maximum delay
    final cappedDelay = min(delayWithJitter, config.maxDelay.inMilliseconds);

    return Duration(milliseconds: cappedDelay);
  }

  /// Create a retry config for network operations
  static RetryConfig networkRetryConfig({
    int maxAttempts = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      initialDelay: initialDelay,
      maxDelay: const Duration(seconds: 10),
      retryableErrorTypes: [
        ErrorType.network,
        ErrorType.timeout,
      ],
    );
  }

  /// Create a retry config for database operations
  static RetryConfig databaseRetryConfig({
    int maxAttempts = 2,
    Duration initialDelay = const Duration(seconds: 2),
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      initialDelay: initialDelay,
      maxDelay: const Duration(seconds: 5),
      retryableErrorTypes: [
        ErrorType.database,
        ErrorType.timeout,
      ],
    );
  }

  /// Create a retry config for authentication operations
  static RetryConfig authRetryConfig({
    int maxAttempts = 1, // Usually don't retry auth errors
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts,
      retryableErrorTypes: [], // Don't retry auth errors by default
    );
  }
}
