import 'package:flutter/material.dart';
import 'package:quarterlies/models/user_settings.dart';

/// Field-friendly theme helper for outdoor visibility and glare resistance
/// Supports both Field Mode (default) and Office Mode display densities
class FieldFriendlyTheme {
  // High contrast colors for outdoor visibility
  static const Color primaryLight = Color(
    0xFF4A6741,
  ); // Darker green for better contrast
  static const Color primaryDark = Color(
    0xFF8FB3B8,
  ); // Lighter blue-green for dark theme

  static const Color secondaryLight = Color(0xFFE8E8E0); // High contrast cream
  static const Color secondaryDark = Color(0xFF2A2A2A); // Dark secondary

  static const Color accentColor = Color(0xFFFF5722); // High visibility orange
  static const Color errorColor = Color(0xFFD32F2F); // High contrast red
  static const Color warningColor = Color(0xFFFF9800); // High contrast amber
  static const Color successColor = Color(0xFF388E3C); // High contrast green

  // Background colors optimized for outdoor use
  static const Color backgroundLight = Color(0xFFFAFAFA); // Very light gray
  static const Color backgroundDark = Color(0xFF121212); // Very dark background

  static const Color surfaceLight = Color(
    0xFFFFFFFF,
  ); // Pure white for maximum contrast
  static const Color surfaceDark = Color(0xFF1E1E1E); // Dark surface

  // Text colors with high contrast ratios
  static const Color textPrimaryLight = Color(0xFF212121); // Very dark gray
  static const Color textPrimaryDark = Color(0xFFFFFFFF); // Pure white

  static const Color textSecondaryLight = Color(0xFF424242); // Dark gray
  static const Color textSecondaryDark = Color(0xFFE0E0E0); // Light gray

  /// Get the field-friendly light theme
  static ThemeData getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // High contrast color scheme
      colorScheme: const ColorScheme.light(
        primary: primaryLight,
        secondary: secondaryLight,
        tertiary: accentColor,
        error: errorColor,
        surface: surfaceLight,
        onPrimary: Colors.white,
        onSecondary: textPrimaryLight,
        onTertiary: Colors.white,
        onError: Colors.white,
        onSurface: textPrimaryLight,
        outline: Color(0xFF757575), // Medium gray for borders
        outlineVariant: Color(0xFFBDBDBD), // Light gray for subtle borders
      ),

      // Enhanced app bar theme for outdoor visibility
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryLight,
        foregroundColor: Colors.white,
        elevation: 8, // Higher elevation for better definition
        shadowColor: Colors.black26,
        toolbarHeight: 64,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // Enhanced button themes with larger touch targets
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(double.infinity, 56),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          elevation: 6, // Higher elevation for better visibility
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          minimumSize: const Size(48, 48),
          padding: const EdgeInsets.all(16),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          minimumSize: const Size(double.infinity, 56),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          side: const BorderSide(width: 2), // Thicker border for visibility
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),

      // Enhanced icon button theme
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          minimumSize: const Size(48, 48),
          padding: const EdgeInsets.all(12),
          iconSize: 24,
        ),
      ),

      // Enhanced floating action button theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: accentColor,
        foregroundColor: Colors.white,
        elevation: 8,
        iconSize: 28,
        sizeConstraints: BoxConstraints.tightFor(width: 64, height: 64),
      ),

      // Enhanced card theme for better outdoor visibility
      cardTheme: CardTheme(
        elevation: 8, // Higher elevation for better definition
        shadowColor: Colors.black26,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),

      // Enhanced input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceLight,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: primaryLight.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: primaryLight,
            width: 3, // Thicker border when focused
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 16,
        ),
        labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        hintStyle: TextStyle(fontSize: 16, color: Colors.grey[600]),
      ),

      // Enhanced list tile theme
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        minVerticalPadding: 12,
        titleTextStyle: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        subtitleTextStyle: TextStyle(fontSize: 14, color: textSecondaryLight),
      ),

      // Enhanced expansion tile theme
      expansionTileTheme: const ExpansionTileThemeData(
        tilePadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        iconColor: primaryLight,
        collapsedIconColor: primaryLight,
        textColor: textPrimaryLight,
        collapsedTextColor: textPrimaryLight,
      ),

      // Enhanced switch theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.white;
          }
          return Colors.grey.shade400;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return primaryLight;
          }
          return Colors.grey.shade300;
        }),
      ),

      // Enhanced chip theme
      chipTheme: ChipThemeData(
        backgroundColor: secondaryLight,
        selectedColor: primaryLight,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),

      // Enhanced bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceLight,
        selectedItemColor: primaryLight,
        unselectedItemColor: textSecondaryLight,
        elevation: 8,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),

      // Enhanced text theme with better outdoor visibility
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: textPrimaryLight,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: textPrimaryLight,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: textPrimaryLight,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: textPrimaryLight,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        titleLarge: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textPrimaryLight,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textPrimaryLight,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: textSecondaryLight,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: textPrimaryLight,
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: textSecondaryLight,
        ),
      ),
    );
  }

  /// Get the field-friendly dark theme for low-light conditions
  static ThemeData getDarkTheme() {
    return getLightTheme().copyWith(
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: primaryDark,
        secondary: secondaryDark,
        tertiary: accentColor,
        error: errorColor,
        surface: surfaceDark,
        onPrimary: textPrimaryDark,
        onSecondary: textPrimaryDark,
        onTertiary: textPrimaryDark,
        onError: textPrimaryDark,
        onSurface: textPrimaryDark,
        outline: Color(0xFF757575),
        outlineVariant: Color(0xFF424242),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: surfaceDark,
        foregroundColor: textPrimaryDark,
        elevation: 8,
        shadowColor: Colors.black54,
        toolbarHeight: 64,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: textPrimaryDark,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 12, // Higher elevation for dark theme
        shadowColor: Colors.black54,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade700, width: 1),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceDark,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: primaryDark.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryDark, width: 3),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 16,
        ),
        labelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textPrimaryDark,
        ),
        hintStyle: TextStyle(fontSize: 16, color: Colors.grey[400]),
      ),
    );
  }

  /// Get contrast ratio between two colors
  static double getContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Check if color combination meets WCAG AA standards (4.5:1 ratio)
  static bool meetsWCAGAA(Color foreground, Color background) {
    return getContrastRatio(foreground, background) >= 4.5;
  }

  /// Check if color combination meets WCAG AAA standards (7:1 ratio)
  static bool meetsWCAGAAA(Color foreground, Color background) {
    return getContrastRatio(foreground, background) >= 7.0;
  }

  /// Get theme based on display mode
  static ThemeData getThemeForDisplayMode(
    DisplayMode displayMode, {
    bool isDark = false,
  }) {
    final baseTheme = isDark ? getDarkTheme() : getLightTheme();

    switch (displayMode) {
      case DisplayMode.field:
        // Field Mode uses the existing theme (no changes needed)
        return baseTheme;
      case DisplayMode.office:
        // Office Mode uses more compact spacing and smaller elements
        return _getOfficeTheme(baseTheme);
    }
  }

  /// Get Office Mode theme with more compact spacing
  static ThemeData _getOfficeTheme(ThemeData baseTheme) {
    return baseTheme.copyWith(
      // More compact card theme
      cardTheme: baseTheme.cardTheme.copyWith(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        elevation: 4, // Reduced elevation for cleaner look
      ),

      // More compact list tile theme
      listTileTheme: baseTheme.listTileTheme.copyWith(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 8.0, // Reduced from 12.0
        ),
        dense: true,
      ),

      // More compact app bar
      appBarTheme: baseTheme.appBarTheme.copyWith(
        toolbarHeight: 56, // Reduced from 64
      ),

      // More compact floating action button
      floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        sizeConstraints: const BoxConstraints.tightFor(width: 56, height: 56),
        iconSize: 24, // Reduced from 28
      ),
    );
  }

  /// Get spacing values based on display mode
  static EdgeInsets getCardMargin(DisplayMode displayMode) {
    switch (displayMode) {
      case DisplayMode.field:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case DisplayMode.office:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
    }
  }

  /// Get list tile padding based on display mode
  static EdgeInsets getListTilePadding(DisplayMode displayMode) {
    switch (displayMode) {
      case DisplayMode.field:
        return const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0);
      case DisplayMode.office:
        return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0);
    }
  }

  /// Get section spacing based on display mode
  static double getSectionSpacing(DisplayMode displayMode) {
    switch (displayMode) {
      case DisplayMode.field:
        return 16.0;
      case DisplayMode.office:
        return 12.0;
    }
  }
}
