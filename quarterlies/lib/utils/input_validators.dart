import 'package:quarterlies/utils/error_handler.dart';

/// Comprehensive input validation utilities with user-friendly error messages
class InputValidators {
  InputValidators._();

  /// Validate email address
  static String? validateEmail(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'Email address is required';
      }
      return null;
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate phone number
  static String? validatePhone(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'Phone number is required';
      }
      return null;
    }

    // Remove all non-digit characters for validation
    final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.length < 10) {
      return 'Phone number must be at least 10 digits';
    }

    if (digitsOnly.length > 15) {
      return 'Phone number cannot exceed 15 digits';
    }

    return null;
  }

  /// Validate password strength
  static String? validatePassword(String? value, {bool required = true}) {
    if (value == null || value.isEmpty) {
      if (required) {
        return 'Password is required';
      }
      return null;
    }

    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }

    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  /// Validate required text field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validate numeric input
  static String? validateNumber(
    String? value, {
    bool required = true,
    double? min,
    double? max,
    String fieldName = 'Value',
  }) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return '$fieldName is required';
      }
      return null;
    }

    final number = double.tryParse(value.trim());
    if (number == null) {
      return 'Please enter a valid number';
    }

    if (min != null && number < min) {
      return '$fieldName must be at least $min';
    }

    if (max != null && number > max) {
      return '$fieldName cannot exceed $max';
    }

    return null;
  }

  /// Validate currency amount
  static String? validateCurrency(
    String? value, {
    bool required = true,
    double? min,
    double? max,
    String fieldName = 'Amount',
  }) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return '$fieldName is required';
      }
      return null;
    }

    // Remove currency symbols and commas
    final cleanValue = value.replaceAll(RegExp(r'[\$,]'), '').trim();
    
    final amount = double.tryParse(cleanValue);
    if (amount == null) {
      return 'Please enter a valid amount';
    }

    if (amount < 0) {
      return '$fieldName cannot be negative';
    }

    if (min != null && amount < min) {
      return '$fieldName must be at least \$${min.toStringAsFixed(2)}';
    }

    if (max != null && amount > max) {
      return '$fieldName cannot exceed \$${max.toStringAsFixed(2)}';
    }

    return null;
  }

  /// Validate ZIP code
  static String? validateZipCode(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'ZIP code is required';
      }
      return null;
    }

    // US ZIP code format: 12345 or 12345-6789
    final zipRegex = RegExp(r'^\d{5}(-\d{4})?$');
    if (!zipRegex.hasMatch(value.trim())) {
      return 'Please enter a valid ZIP code (12345 or 12345-6789)';
    }

    return null;
  }

  /// Validate date string
  static String? validateDate(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'Date is required';
      }
      return null;
    }

    try {
      DateTime.parse(value.trim());
      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }

  /// Validate URL
  static String? validateUrl(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'URL is required';
      }
      return null;
    }

    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );

    if (!urlRegex.hasMatch(value.trim())) {
      return 'Please enter a valid URL';
    }

    return null;
  }

  /// Validate text length
  static String? validateLength(
    String? value, {
    int? minLength,
    int? maxLength,
    bool required = true,
    String fieldName = 'Field',
  }) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return '$fieldName is required';
      }
      return null;
    }

    final length = value.trim().length;

    if (minLength != null && length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }

    if (maxLength != null && length > maxLength) {
      return '$fieldName cannot exceed $maxLength characters';
    }

    return null;
  }

  /// Validate that two fields match (e.g., password confirmation)
  static String? validateMatch(
    String? value1,
    String? value2,
    String fieldName,
  ) {
    if (value1 != value2) {
      return '$fieldName does not match';
    }
    return null;
  }

  /// Validate business tax ID (EIN)
  static String? validateEIN(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'EIN is required';
      }
      return null;
    }

    // EIN format: 12-3456789
    final einRegex = RegExp(r'^\d{2}-\d{7}$');
    if (!einRegex.hasMatch(value.trim())) {
      return 'Please enter a valid EIN (12-3456789)';
    }

    return null;
  }

  /// Validate Social Security Number
  static String? validateSSN(String? value, {bool required = true}) {
    if (value == null || value.trim().isEmpty) {
      if (required) {
        return 'SSN is required';
      }
      return null;
    }

    // SSN format: ***********
    final ssnRegex = RegExp(r'^\d{3}-\d{2}-\d{4}$');
    if (!ssnRegex.hasMatch(value.trim())) {
      return 'Please enter a valid SSN (***********)';
    }

    return null;
  }

  /// Create a validation error as AppError
  static AppError createValidationError(String message, {Map<String, dynamic>? context}) {
    return AppError(
      type: ErrorType.validation,
      severity: ErrorSeverity.low,
      message: message,
      userFriendlyMessage: message,
      context: context,
    );
  }
}
