import 'package:flutter/foundation.dart';
import 'app_constants.dart';

/// Enum for categorizing different types of errors
enum ErrorType {
  network,
  authentication,
  authorization,
  validation,
  database,
  storage,
  conflict,
  timeout,
  rls,
  unknown,
}

/// Enum for error severity levels
enum ErrorSeverity { low, medium, high, critical }

/// A comprehensive error class that provides context and user-friendly messages
class AppError {
  final ErrorType type;
  final ErrorSeverity severity;
  final String message;
  final String? userFriendlyMessage;
  final String? technicalDetails;
  final dynamic originalError;
  final StackTrace? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic>? context;

  AppError({
    required this.type,
    required this.severity,
    required this.message,
    this.userFriendlyMessage,
    this.technicalDetails,
    this.originalError,
    this.stackTrace,
    Map<String, dynamic>? context,
  }) : timestamp = DateTime.now(),
       context = context ?? {};

  /// Create an AppError from a generic exception
  factory AppError.fromException(
    dynamic error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    return ErrorHandler.categorizeError(
      error,
      stackTrace: stackTrace,
      context: context,
    );
  }

  @override
  String toString() {
    return 'AppError(type: $type, severity: $severity, message: $message)';
  }
}

/// Central error handling utility class
class ErrorHandler {
  ErrorHandler._();

  /// Categorize and create an AppError from any exception
  static AppError categorizeError(
    dynamic error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    final errorString = error.toString().toLowerCase();

    // Network errors
    if (_isNetworkError(errorString)) {
      return AppError(
        type: ErrorType.network,
        severity: ErrorSeverity.medium,
        message: error.toString(),
        userFriendlyMessage:
            'Network connection issue. Please check your internet connection and try again.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Authentication errors
    if (_isAuthError(errorString)) {
      return AppError(
        type: ErrorType.authentication,
        severity: ErrorSeverity.high,
        message: error.toString(),
        userFriendlyMessage: 'Authentication failed. Please log in again.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // RLS (Row Level Security) errors
    if (_isRLSError(errorString)) {
      return AppError(
        type: ErrorType.rls,
        severity: ErrorSeverity.high,
        message: error.toString(),
        userFriendlyMessage: 'You don\'t have permission to access this data.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Timeout errors
    if (_isTimeoutError(errorString)) {
      return AppError(
        type: ErrorType.timeout,
        severity: ErrorSeverity.medium,
        message: error.toString(),
        userFriendlyMessage: 'The operation took too long. Please try again.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Database errors
    if (_isDatabaseError(errorString)) {
      return AppError(
        type: ErrorType.database,
        severity: ErrorSeverity.high,
        message: error.toString(),
        userFriendlyMessage: 'Database error occurred. Please try again later.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Storage errors
    if (_isStorageError(errorString)) {
      return AppError(
        type: ErrorType.storage,
        severity: ErrorSeverity.medium,
        message: error.toString(),
        userFriendlyMessage: 'File storage error. Please try uploading again.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Validation errors
    if (_isValidationError(errorString)) {
      return AppError(
        type: ErrorType.validation,
        severity: ErrorSeverity.low,
        message: error.toString(),
        userFriendlyMessage: 'Please check your input and try again.',
        technicalDetails: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        context: context,
      );
    }

    // Default to unknown error
    return AppError(
      type: ErrorType.unknown,
      severity: ErrorSeverity.medium,
      message: error.toString(),
      userFriendlyMessage: 'An unexpected error occurred. Please try again.',
      technicalDetails: error.toString(),
      originalError: error,
      stackTrace: stackTrace,
      context: context,
    );
  }

  /// Check if error is network-related
  static bool _isNetworkError(String errorString) {
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('socket') ||
        errorString.contains('host') ||
        errorString.contains('dns') ||
        errorString.contains('unreachable');
  }

  /// Check if error is authentication-related
  static bool _isAuthError(String errorString) {
    return errorString.contains('auth') ||
        errorString.contains('unauthorized') ||
        errorString.contains('invalid_grant') ||
        errorString.contains('token') ||
        errorString.contains('session');
  }

  /// Check if error is RLS-related
  static bool _isRLSError(String errorString) {
    return errorString.contains('rls') ||
        errorString.contains('row level security') ||
        errorString.contains('permission denied') ||
        errorString.contains('insufficient_privilege') ||
        errorString.contains('forbidden');
  }

  /// Check if error is timeout-related
  static bool _isTimeoutError(String errorString) {
    return errorString.contains('timeout') ||
        errorString.contains('deadline') ||
        errorString.contains('time out');
  }

  /// Check if error is database-related
  static bool _isDatabaseError(String errorString) {
    return errorString.contains('database') ||
        errorString.contains('sql') ||
        errorString.contains('constraint') ||
        errorString.contains('foreign key') ||
        errorString.contains('unique') ||
        errorString.contains('not null');
  }

  /// Check if error is storage-related
  static bool _isStorageError(String errorString) {
    return errorString.contains('storage') ||
        errorString.contains('bucket') ||
        errorString.contains('file') ||
        errorString.contains('upload') ||
        errorString.contains('download');
  }

  /// Check if error is validation-related
  static bool _isValidationError(String errorString) {
    return errorString.contains('validation') ||
        errorString.contains('invalid') ||
        errorString.contains('required') ||
        errorString.contains('format') ||
        errorString.contains('length');
  }

  /// Log error for debugging and monitoring
  static void logError(AppError error) {
    if (kDebugMode) {
      debugPrint('=== ERROR LOG ===');
      debugPrint('Type: ${error.type}');
      debugPrint('Severity: ${error.severity}');
      debugPrint('Message: ${error.message}');
      debugPrint('User Message: ${error.userFriendlyMessage}');
      debugPrint('Technical Details: ${error.technicalDetails}');
      debugPrint('Timestamp: ${error.timestamp}');
      debugPrint('Context: ${error.context}');
      if (error.stackTrace != null) {
        debugPrint('Stack Trace: ${error.stackTrace}');
      }
      debugPrint('=================');
    }

    // In production, send to error reporting service
    // This can be implemented with services like Crashlytics, Sentry, etc.
    // For now, we rely on local logging for debugging purposes
  }

  /// Get user-friendly message with fallback
  static String getUserFriendlyMessage(AppError error) {
    String message = error.userFriendlyMessage ?? error.message;

    // Truncate if too long
    if (message.length > AppConstants.maxErrorMessageLength) {
      message =
          '${message.substring(0, AppConstants.maxErrorMessageLength - 3)}...';
    }

    return message;
  }

  /// Determine if error should be retried
  static bool shouldRetry(AppError error) {
    switch (error.type) {
      case ErrorType.network:
      case ErrorType.timeout:
      case ErrorType.database:
        return error.severity != ErrorSeverity.critical;
      case ErrorType.authentication:
      case ErrorType.authorization:
      case ErrorType.rls:
      case ErrorType.validation:
        return false;
      default:
        return error.severity == ErrorSeverity.low ||
            error.severity == ErrorSeverity.medium;
    }
  }
}
