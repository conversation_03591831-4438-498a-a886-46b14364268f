/// Centralized feedback message templates for the Quarterlies app
/// Provides consistent, user-friendly messages for various operations
class FeedbackMessages {
  // Private constructor to prevent instantiation
  FeedbackMessages._();

  // ==================== SUCCESS MESSAGES ====================

  /// Data creation success messages
  static const String customerSaved = 'Customer saved successfully';
  static const String jobSaved = 'Job saved successfully';
  static const String estimateSaved = 'Estimate saved successfully';
  static const String contractSaved = 'Contract saved successfully';
  static const String invoiceSaved = 'Invoice saved successfully';
  static const String paymentSaved = 'Payment recorded successfully';
  static const String expenseSaved = 'Expense saved successfully';
  static const String timeLogSaved = 'Time log saved successfully';
  static const String mileageSaved = 'Mileage recorded successfully';
  static const String taxPaymentSaved = 'Tax payment saved successfully';

  /// Data update success messages
  static const String customerUpdated = 'Customer updated successfully';
  static const String jobUpdated = 'Job updated successfully';
  static const String estimateUpdated = 'Estimate updated successfully';
  static const String contractUpdated = 'Contract updated successfully';
  static const String invoiceUpdated = 'Invoice updated successfully';
  static const String paymentUpdated = 'Payment updated successfully';
  static const String expenseUpdated = 'Expense updated successfully';
  static const String timeLogUpdated = 'Time log updated successfully';
  static const String mileageUpdated = 'Mileage updated successfully';
  static const String taxPaymentUpdated = 'Tax payment updated successfully';

  /// Data deletion success messages
  static const String customerDeleted = 'Customer deleted successfully';
  static const String jobDeleted = 'Job deleted successfully';
  static const String estimateDeleted = 'Estimate deleted successfully';
  static const String contractDeleted = 'Contract deleted successfully';
  static const String invoiceDeleted = 'Invoice deleted successfully';
  static const String paymentDeleted = 'Payment deleted successfully';
  static const String expenseDeleted = 'Expense deleted successfully';
  static const String timeLogDeleted = 'Time log deleted successfully';
  static const String mileageDeleted = 'Mileage deleted successfully';
  static const String taxPaymentDeleted = 'Tax payment deleted successfully';

  // ==================== OFFLINE SUCCESS MESSAGES ====================

  /// Offline data creation messages
  static const String customerSavedOffline = 'Customer saved locally. Will sync when online.';
  static const String jobSavedOffline = 'Job saved locally. Will sync when online.';
  static const String estimateSavedOffline = 'Estimate saved locally. Will sync when online.';
  static const String contractSavedOffline = 'Contract saved locally. Will sync when online.';
  static const String invoiceSavedOffline = 'Invoice saved locally. Will sync when online.';
  static const String paymentSavedOffline = 'Payment saved locally. Will sync when online.';
  static const String expenseSavedOffline = 'Expense saved locally. Will sync when online.';
  static const String timeLogSavedOffline = 'Time log saved locally. Will sync when online.';
  static const String mileageSavedOffline = 'Mileage saved locally. Will sync when online.';
  static const String taxPaymentSavedOffline = 'Tax payment saved locally. Will sync when online.';

  /// Offline data update messages
  static const String customerUpdatedOffline = 'Customer updated locally. Will sync when online.';
  static const String jobUpdatedOffline = 'Job updated locally. Will sync when online.';
  static const String estimateUpdatedOffline = 'Estimate updated locally. Will sync when online.';
  static const String contractUpdatedOffline = 'Contract updated locally. Will sync when online.';
  static const String invoiceUpdatedOffline = 'Invoice updated locally. Will sync when online.';
  static const String paymentUpdatedOffline = 'Payment updated locally. Will sync when online.';
  static const String expenseUpdatedOffline = 'Expense updated locally. Will sync when online.';
  static const String timeLogUpdatedOffline = 'Time log updated locally. Will sync when online.';
  static const String mileageUpdatedOffline = 'Mileage updated locally. Will sync when online.';
  static const String taxPaymentUpdatedOffline = 'Tax payment updated locally. Will sync when online.';

  // ==================== SYNC MESSAGES ====================

  /// Sync status messages
  static const String syncStarted = 'Syncing data with cloud...';
  static const String syncCompleted = 'Data synced successfully';
  static const String syncFailed = 'Sync failed - working offline';
  static const String syncConflict = 'Sync conflict detected - needs resolution';
  static const String backOnline = 'Back online - syncing changes...';
  static const String workingOffline = 'Working offline - changes will sync later';

  // ==================== OPERATION MESSAGES ====================

  /// Heavy operation messages
  static const String pdfGenerating = 'Generating PDF...';
  static const String pdfGenerated = 'PDF generated successfully';
  static const String pdfGenerationFailed = 'PDF generation failed';
  
  static const String ocrProcessing = 'Processing image...';
  static const String ocrCompleted = 'Image processed successfully';
  static const String ocrFailed = 'Image processing failed';
  
  static const String emailSending = 'Sending email...';
  static const String emailSent = 'Email sent successfully';
  static const String emailFailed = 'Email sending failed';
  
  static const String documentSigning = 'Processing document signature...';
  static const String documentSigned = 'Document signed successfully';
  static const String documentSigningFailed = 'Document signing failed';

  /// Voice operation messages
  static const String voiceRecording = 'Recording voice...';
  static const String voiceProcessing = 'Processing voice input...';
  static const String voiceCompleted = 'Voice input processed successfully';
  static const String voiceFailed = 'Voice processing failed';

  /// Export operation messages
  static const String exportStarted = 'Exporting data...';
  static const String exportCompleted = 'Data exported successfully';
  static const String exportFailed = 'Data export failed';

  // ==================== INFO MESSAGES ====================

  /// General information messages
  static const String dataLoaded = 'Data loaded successfully';
  static const String settingsUpdated = 'Settings updated successfully';
  static const String profileUpdated = 'Profile updated successfully';
  static const String passwordChanged = 'Password changed successfully';
  static const String signatureUpdated = 'Signature updated successfully';

  /// Connection status messages
  static const String connectionRestored = 'Internet connection restored';
  static const String connectionLost = 'Internet connection lost';
  static const String workingInOfflineMode = 'Working in offline mode';

  // ==================== WARNING MESSAGES ====================

  /// Warning messages for user attention
  static const String unsavedChanges = 'You have unsaved changes';
  static const String lowStorage = 'Device storage is running low';
  static const String syncPending = 'Some changes are pending sync';
  static const String conflictResolutionNeeded = 'Manual conflict resolution needed';
  static const String permissionRequired = 'Permission required for this feature';

  // ==================== HELPER METHODS ====================

  /// Get offline version of a message
  static String getOfflineMessage(String onlineMessage) {
    if (onlineMessage.contains('successfully')) {
      return onlineMessage.replaceAll('successfully', 'locally. Will sync when online.');
    }
    return '$onlineMessage (Offline)';
  }

  /// Get operation-specific message
  static String getOperationMessage(String operation, bool isComplete) {
    if (isComplete) {
      return '$operation completed successfully';
    } else {
      return '$operation in progress...';
    }
  }

  /// Get sync-specific message based on status
  static String getSyncMessage(String status) {
    switch (status.toLowerCase()) {
      case 'started':
      case 'syncing':
        return syncStarted;
      case 'completed':
      case 'success':
        return syncCompleted;
      case 'failed':
      case 'error':
        return syncFailed;
      case 'conflict':
        return syncConflict;
      default:
        return 'Sync status: $status';
    }
  }

  /// Get data operation message
  static String getDataOperationMessage(
    String entityType,
    String operation, {
    bool isOffline = false,
  }) {
    final baseMessage = '${_capitalize(entityType)} ${operation}d successfully';
    return isOffline ? getOfflineMessage(baseMessage) : baseMessage;
  }

  /// Capitalize first letter of a string
  static String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }
}
