/// Application-wide constants for the Quarterlies app
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // ==================== SYNC CONSTANTS ====================

  /// Default retry delay for failed sync operations (in minutes)
  static const int syncRetryDelayMinutes = 5;

  /// Conflict detection threshold (in minutes)
  static const int conflictDetectionThresholdMinutes = 5;

  /// Background sync frequency (in minutes)
  static const int backgroundSyncFrequencyMinutes = 15;

  /// Periodic sync frequency (in minutes)
  static const int periodicSyncFrequencyMinutes = 5;

  // ==================== NOTIFICATION CONSTANTS ====================

  /// Default notification time (9:00 AM)
  static const int defaultNotificationHour = 9;
  static const int defaultNotificationMinute = 0;

  /// Tax deadline notification days
  static const List<int> taxDeadlineNotificationDays = [30, 14, 7, 1];

  // ==================== USER SETTINGS DEFAULTS ====================

  /// Default invoice due days
  static const int defaultInvoiceDueDays = 30;

  /// Default due date notification days
  static const int defaultDueDateNotificationDays = 3;

  /// Default mileage idle timeout (in minutes)
  static const int defaultMileageIdleTimeoutMinutes = 5;

  // ==================== MILEAGE CONSTANTS ====================

  /// Default IRS mileage rate (2023 rate: 65.5 cents per mile)
  static const String defaultIrsRate = '0.655';

  // ==================== GLARE RESISTANCE CONSTANTS ====================

  /// Brightness thresholds
  static const double lowLightThreshold = 0.3;
  static const double brightLightThreshold = 0.7;
  static const double veryBrightThreshold = 0.9;

  /// Color adjustment factors
  static const double contrastBoostFactor = 1.2;
  static const double saturationBoostFactor = 1.1;
  static const double brightnessBoostFactor = 1.15;

  /// Default brightness level
  static const double defaultBrightnessLevel = 0.5;

  // ==================== DATABASE CONSTANTS ====================

  /// Database version
  static const int databaseVersion = 1;

  /// Database name
  static const String databaseName = 'quarterlies.db';

  // ==================== PAGINATION CONSTANTS ====================

  /// Default page size for paginated lists
  static const int defaultPageSize = 20;

  /// Maximum page size
  static const int maxPageSize = 100;

  // ==================== VALIDATION CONSTANTS ====================

  /// Minimum password length
  static const int minPasswordLength = 8;

  /// Maximum file size for uploads (in bytes) - 10MB
  static const int maxFileSize = 10 * 1024 * 1024;

  // ==================== NETWORK CONSTANTS ====================

  /// Request timeout (in seconds)
  static const int requestTimeoutSeconds = 30;

  /// Connection timeout (in seconds)
  static const int connectionTimeoutSeconds = 10;

  // ==================== CACHE CONSTANTS ====================

  /// Cache expiry time (in hours)
  static const int cacheExpiryHours = 24;

  /// Maximum cache size (in MB)
  static const int maxCacheSizeMB = 50;

  // ==================== SHARED PREFERENCES KEYS ====================

  /// Key for storing automatic brightness detection setting
  static const String automaticBrightnessKey = 'automatic_brightness_detection';

  /// Key for storing dynamic color adjustment setting
  static const String dynamicColorAdjustmentKey = 'dynamic_color_adjustment';

  /// Key for storing enhanced contrast mode setting
  static const String enhancedContrastModeKey = 'enhanced_contrast_mode';

  /// Key for storing current brightness level
  static const String currentBrightnessKey = 'current_brightness_level';

  /// Key for storing cached customers
  static const String cachedCustomersKey = 'cached_customers';

  // ==================== WORKMANAGER TASK NAMES ====================

  /// Periodic sync task name
  static const String periodicSyncTaskName = 'com.quarterlies.periodicSync';

  /// One-time sync task name
  static const String oneTimeSyncTaskName = 'com.quarterlies.oneTimeSync';

  /// Daily invoice check task name
  static const String dailyInvoiceCheckTaskName = 'dailyInvoiceCheck';

  /// Daily tax deadline check task name
  static const String dailyTaxDeadlineCheckTaskName = 'dailyTaxDeadlineCheck';

  // ==================== NOTIFICATION CHANNEL IDS ====================

  /// Invoice due notification channel
  static const String invoiceDueChannelId = 'invoice_due_channel';

  /// Tax deadline notification channel
  static const String taxDeadlineChannelId = 'tax_deadline_channel';

  // ==================== METHOD CHANNEL NAMES ====================

  /// Background sync method channel
  static const String backgroundSyncChannel = 'com.quarterlies/background_sync';

  /// Deep linking method channel
  static const String deepLinkingChannel = 'com.quarterlies/deep_linking';

  // ==================== ERROR HANDLING CONSTANTS ====================

  /// Maximum number of retry attempts for failed operations
  static const int maxErrorRetryAttempts = 3;

  /// Delay between retry attempts (in seconds)
  static const int errorRetryDelaySeconds = 2;

  /// Timeout for network operations (in seconds)
  static const int networkTimeoutSeconds = 30;

  /// Duration to display error messages (in seconds)
  static const int errorDisplayDurationSeconds = 5;

  /// Maximum length for user-friendly error messages
  static const int maxErrorMessageLength = 200;

  // ==================== FEEDBACK CONSTANTS ====================

  /// Duration to display success messages (in seconds)
  static const int successDisplayDurationSeconds = 3;

  /// Duration to display info messages (in seconds)
  static const int infoDisplayDurationSeconds = 4;

  /// Duration to display sync messages (in seconds)
  static const int syncDisplayDurationSeconds = 2;

  /// Duration to display operation feedback (in seconds)
  static const int operationDisplayDurationSeconds = 3;

  /// Maximum length for feedback messages
  static const int maxFeedbackMessageLength = 150;

  // ==================== BACKGROUND TASK IDENTIFIERS ====================

  /// Background sync task identifier for iOS
  static const String backgroundSyncTaskId = 'com.quarterlies.backgroundSync';
}
