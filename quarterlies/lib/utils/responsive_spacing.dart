import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';

/// Comprehensive responsive spacing utility that provides:
/// - Dynamic spacing based on screen size, orientation, and device type
/// - Display mode adaptation (Field vs Office)
/// - Aspect ratio considerations (ultra-wide, tall/narrow, foldable)
/// - Consistent spacing patterns across the entire app
class ResponsiveSpacing {
  /// Get responsive padding based on context and display settings
  static EdgeInsets getPadding(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
    bool adaptToOrientation = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    final screenSize = MediaQuery.of(context).size;
    final aspectRatio = screenSize.width / screenSize.height;
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    
    double basePadding = base ?? 16.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      basePadding = displayProvider.isOfficeMode ? basePadding * 0.75 : basePadding * 1.1;
    }

    // Adapt to device type and screen size
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        basePadding *= 1.6;
      } else if (ResponsiveHelper.isTablet(context)) {
        basePadding *= 1.3;
      } else {
        // Mobile: adjust based on screen density
        if (devicePixelRatio > 3.0) {
          basePadding *= 1.05;
        } else if (devicePixelRatio < 2.0) {
          basePadding *= 0.95;
        }
      }
    }

    // Adapt to aspect ratio and orientation
    if (adaptToOrientation) {
      if (isLandscape) {
        if (aspectRatio > 2.0) {
          // Ultra-wide screens
          return EdgeInsets.symmetric(
            horizontal: basePadding * 1.8,
            vertical: basePadding * 0.7,
          );
        } else if (aspectRatio > 1.8) {
          // Wide screens
          return EdgeInsets.symmetric(
            horizontal: basePadding * 1.4,
            vertical: basePadding * 0.8,
          );
        } else {
          // Standard landscape
          return EdgeInsets.symmetric(
            horizontal: basePadding * 1.2,
            vertical: basePadding * 0.85,
          );
        }
      } else {
        // Portrait mode
        if (aspectRatio < 0.5) {
          // Very tall/narrow screens
          return EdgeInsets.symmetric(
            horizontal: basePadding * 0.8,
            vertical: basePadding * 1.1,
          );
        } else if (aspectRatio < 0.6) {
          // Tall screens
          return EdgeInsets.symmetric(
            horizontal: basePadding * 0.9,
            vertical: basePadding * 1.05,
          );
        }
      }
    }

    return EdgeInsets.all(basePadding);
  }

  /// Get responsive margin
  static EdgeInsets getMargin(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
    bool adaptToOrientation = true,
  }) {
    return getPadding(
      context,
      base: (base ?? 8.0),
      adaptToDisplayMode: adaptToDisplayMode,
      adaptToScreenSize: adaptToScreenSize,
      adaptToOrientation: adaptToOrientation,
    );
  }

  /// Get responsive spacing between elements
  static double getSpacing(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseSpacing = base ?? 16.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseSpacing = displayProvider.isOfficeMode ? baseSpacing * 0.8 : baseSpacing * 1.1;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseSpacing *= 1.4;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseSpacing *= 1.2;
      }
    }

    return baseSpacing;
  }

  /// Get responsive icon size
  static double getIconSize(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseSize = base ?? 24.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseSize = displayProvider.isOfficeMode ? baseSize * 0.9 : baseSize * 1.15;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseSize *= 1.3;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseSize *= 1.15;
      }
    }

    return baseSize;
  }

  /// Get responsive button height
  static double getButtonHeight(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseHeight = base ?? 48.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseHeight = displayProvider.isOfficeMode ? baseHeight * 0.85 : baseHeight * 1.1;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseHeight *= 1.2;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseHeight *= 1.1;
      }
    }

    return baseHeight;
  }

  /// Get responsive border radius
  static double getBorderRadius(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseRadius = base ?? 8.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseRadius = displayProvider.isOfficeMode ? baseRadius * 0.8 : baseRadius * 1.1;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseRadius *= 1.3;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseRadius *= 1.15;
      }
    }

    return baseRadius;
  }

  /// Get responsive elevation
  static double getElevation(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseElevation = base ?? 4.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseElevation = displayProvider.isOfficeMode ? baseElevation * 0.75 : baseElevation * 1.1;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseElevation *= 1.2;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseElevation *= 1.1;
      }
    }

    return baseElevation;
  }

  /// Get responsive app bar height
  static double getAppBarHeight(
    BuildContext context, {
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseHeight = 56.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseHeight = displayProvider.isOfficeMode ? 52.0 : 64.0;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseHeight *= 1.15;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseHeight *= 1.1;
      }
    }

    return baseHeight;
  }

  /// Get responsive list item height
  static double getListItemHeight(
    BuildContext context, {
    double? base,
    bool adaptToDisplayMode = true,
    bool adaptToScreenSize = true,
  }) {
    final displayProvider = Provider.of<DisplaySettingsProvider>(context, listen: false);
    double baseHeight = base ?? 72.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      baseHeight = displayProvider.isOfficeMode ? baseHeight * 0.85 : baseHeight * 1.1;
    }

    // Adapt to device type
    if (adaptToScreenSize) {
      if (ResponsiveHelper.isDesktop(context)) {
        baseHeight *= 1.15;
      } else if (ResponsiveHelper.isTablet(context)) {
        baseHeight *= 1.1;
      }
    }

    return baseHeight;
  }
}
