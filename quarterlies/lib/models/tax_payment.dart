import 'package:uuid/uuid.dart';
import 'sync_status.dart'; // Import SyncStatus

class TaxPayment {
  final String id;
  final String userId;
  final DateTime date;
  final double amount;
  final String taxPeriod; // e.g., 'Q1 2023', 'Q2 2023', etc.
  final String? paymentMethod; // Optional: how the payment was made
  final String? confirmationNumber; // Optional: payment confirmation number
  final String? notes; // Optional: additional notes
  final String? voiceNoteUrl; // URL to the recorded voice note
  final DateTime createdAt;
  DateTime updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking

  TaxPayment({
    String? id,
    required this.userId,
    required this.date,
    required this.amount,
    required this.taxPeriod,
    this.paymentMethod,
    this.confirmationNumber,
    this.notes,
    this.voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create a TaxPayment from a JSON object
  factory TaxPayment.fromJson(Map<String, dynamic> json) {
    return TaxPayment(
      id: json['id'],
      userId: json['user_id'],
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      amount: json['amount']?.toDouble() ?? 0.0,
      taxPeriod: json['tax_period'],
      paymentMethod: json['payment_method'],
      confirmationNumber: json['confirmation_number'],
      notes: json['notes'],
      voiceNoteUrl: json['voice_note_url'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(), // Default to now if null
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(), // Default to now if null
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );
  }

  // Convert TaxPayment to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String(),
      'amount': amount,
      'tax_period': taxPeriod,
      'payment_method': paymentMethod,
      'confirmation_number': confirmationNumber,
      'notes': notes,
      'voice_note_url': voiceNoteUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  // Create a copy of TaxPayment with updated fields
  TaxPayment copyWith({
    String? id,
    String? userId,
    DateTime? date,
    double? amount,
    String? taxPeriod,
    String? paymentMethod,
    String? confirmationNumber,
    String? notes,
    String? voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return TaxPayment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      amount: amount ?? this.amount,
      taxPeriod: taxPeriod ?? this.taxPeriod,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      confirmationNumber: confirmationNumber ?? this.confirmationNumber,
      notes: notes ?? this.notes,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }
}

// Helper class for tax periods
class TaxPeriod {
  static const String q1 = 'Q1';
  static const String q2 = 'Q2';
  static const String q3 = 'Q3';
  static const String q4 = 'Q4';
  static const String annual = 'Annual';

  static const List<String> quarters = [q1, q2, q3, q4, annual];

  // Generate tax period options for the current and previous year
  static List<String> getTaxPeriodOptions() {
    final currentYear = DateTime.now().year;
    final previousYear = currentYear - 1;

    List<String> options = [];

    // Add current year options
    for (var quarter in quarters) {
      options.add('$quarter $currentYear');
    }

    // Add previous year options
    for (var quarter in quarters) {
      options.add('$quarter $previousYear');
    }

    return options;
  }
}
