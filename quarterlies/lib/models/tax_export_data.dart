/// Model for tax export data aggregation
class TaxExportData {
  final DateTime startDate;
  final DateTime endDate;
  final String periodDescription;
  final List<TaxIncomeItem> incomeItems;
  final List<TaxExpenseItem> expenseItems;
  final List<TaxMileageItem> mileageItems;
  final List<TaxPaymentItem> taxPaymentItems;
  final TaxSummary summary;

  TaxExportData({
    required this.startDate,
    required this.endDate,
    required this.periodDescription,
    required this.incomeItems,
    required this.expenseItems,
    required this.mileageItems,
    required this.taxPaymentItems,
    required this.summary,
  });
}

/// Income item for tax export
class TaxIncomeItem {
  final String id;
  final DateTime date;
  final String description;
  final String customerName;
  final String jobName;
  final double amount;
  final String source; // 'invoice', 'payment', etc.
  final String? invoiceNumber;

  TaxIncomeItem({
    required this.id,
    required this.date,
    required this.description,
    required this.customerName,
    required this.jobName,
    required this.amount,
    required this.source,
    this.invoiceNumber,
  });

  Map<String, dynamic> toCsvRow() {
    return {
      'Date': date.toIso8601String().split('T')[0],
      'Description': description,
      'Customer': customerName,
      'Job': jobName,
      'Amount': amount.toStringAsFixed(2),
      'Source': source,
      'Invoice Number': invoiceNumber ?? '',
    };
  }
}

/// Expense item for tax export
class TaxExpenseItem {
  final String id;
  final DateTime date;
  final String description;
  final String category;
  final double amount;
  final String? jobName;
  final String? customerName;
  final bool isOverhead;
  final String? receiptUrl;

  TaxExpenseItem({
    required this.id,
    required this.date,
    required this.description,
    required this.category,
    required this.amount,
    this.jobName,
    this.customerName,
    required this.isOverhead,
    this.receiptUrl,
  });

  Map<String, dynamic> toCsvRow() {
    return {
      'Date': date.toIso8601String().split('T')[0],
      'Description': description,
      'Category': category,
      'Amount': amount.toStringAsFixed(2),
      'Job': jobName ?? '',
      'Customer': customerName ?? '',
      'Type': isOverhead ? 'Overhead' : 'Job Expense',
      'Receipt': receiptUrl != null ? 'Yes' : 'No',
    };
  }
}

/// Mileage item for tax export
class TaxMileageItem {
  final String id;
  final DateTime date;
  final String startLocation;
  final String endLocation;
  final double miles;
  final double ratePerMile;
  final double deductionAmount;
  final String? purpose;
  final String? jobName;
  final String? customerName;

  TaxMileageItem({
    required this.id,
    required this.date,
    required this.startLocation,
    required this.endLocation,
    required this.miles,
    required this.ratePerMile,
    required this.deductionAmount,
    this.purpose,
    this.jobName,
    this.customerName,
  });

  Map<String, dynamic> toCsvRow() {
    return {
      'Date': date.toIso8601String().split('T')[0],
      'From': startLocation,
      'To': endLocation,
      'Miles': miles.toStringAsFixed(1),
      'Rate per Mile': ratePerMile.toStringAsFixed(3),
      'Deduction Amount': deductionAmount.toStringAsFixed(2),
      'Purpose': purpose ?? '',
      'Job': jobName ?? '',
      'Customer': customerName ?? '',
    };
  }
}

/// Tax payment item for tax export
class TaxPaymentItem {
  final String id;
  final DateTime date;
  final double amount;
  final String taxPeriod;
  final String? paymentMethod;
  final String? confirmationNumber;
  final String? notes;

  TaxPaymentItem({
    required this.id,
    required this.date,
    required this.amount,
    required this.taxPeriod,
    this.paymentMethod,
    this.confirmationNumber,
    this.notes,
  });

  Map<String, dynamic> toCsvRow() {
    return {
      'Date': date.toIso8601String().split('T')[0],
      'Amount': amount.toStringAsFixed(2),
      'Tax Period': taxPeriod,
      'Payment Method': paymentMethod ?? '',
      'Confirmation Number': confirmationNumber ?? '',
      'Notes': notes ?? '',
    };
  }
}

/// Summary data for tax export
class TaxSummary {
  final double totalIncome;
  final double totalExpenses;
  final double totalMileageDeduction;
  final double totalTaxPayments;
  final double netProfit;
  final double estimatedTaxOwed;
  final double taxBalance; // Estimated tax owed minus payments made

  TaxSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalMileageDeduction,
    required this.totalTaxPayments,
    required this.netProfit,
    required this.estimatedTaxOwed,
    required this.taxBalance,
  });

  Map<String, dynamic> toMap() {
    return {
      'Total Income': totalIncome,
      'Total Expenses': totalExpenses,
      'Total Mileage Deduction': totalMileageDeduction,
      'Net Profit': netProfit,
      'Estimated Tax Owed (25%)': estimatedTaxOwed,
      'Tax Payments Made': totalTaxPayments,
      'Tax Balance': taxBalance,
    };
  }
}

/// Export format options
enum TaxExportFormat { csv, pdf, both }

/// Time period options for tax export
enum TaxExportPeriod {
  currentYear,
  previousYear,
  currentQuarter,
  previousQuarter,
  custom,
}

extension TaxExportPeriodExtension on TaxExportPeriod {
  String get displayName {
    switch (this) {
      case TaxExportPeriod.currentYear:
        return 'Current Year (${DateTime.now().year})';
      case TaxExportPeriod.previousYear:
        return 'Previous Year (${DateTime.now().year - 1})';
      case TaxExportPeriod.currentQuarter:
        final quarter = ((DateTime.now().month - 1) ~/ 3) + 1;
        return 'Current Quarter (Q$quarter ${DateTime.now().year})';
      case TaxExportPeriod.previousQuarter:
        final now = DateTime.now();
        final currentQuarter = ((now.month - 1) ~/ 3) + 1;
        final prevQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
        final prevYear = currentQuarter == 1 ? now.year - 1 : now.year;
        return 'Previous Quarter (Q$prevQuarter $prevYear)';
      case TaxExportPeriod.custom:
        return 'Custom Date Range';
    }
  }

  DateRange getDateRange() {
    final now = DateTime.now();
    switch (this) {
      case TaxExportPeriod.currentYear:
        return DateRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31),
        );
      case TaxExportPeriod.previousYear:
        return DateRange(
          start: DateTime(now.year - 1, 1, 1),
          end: DateTime(now.year - 1, 12, 31),
        );
      case TaxExportPeriod.currentQuarter:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        final startMonth = (quarter - 1) * 3 + 1;
        final endMonth = quarter * 3;
        return DateRange(
          start: DateTime(now.year, startMonth, 1),
          end: DateTime(now.year, endMonth + 1, 0), // Last day of end month
        );
      case TaxExportPeriod.previousQuarter:
        final currentQuarter = ((now.month - 1) ~/ 3) + 1;
        final prevQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
        final prevYear = currentQuarter == 1 ? now.year - 1 : now.year;
        final startMonth = (prevQuarter - 1) * 3 + 1;
        final endMonth = prevQuarter * 3;
        return DateRange(
          start: DateTime(prevYear, startMonth, 1),
          end: DateTime(prevYear, endMonth + 1, 0), // Last day of end month
        );
      case TaxExportPeriod.custom:
        // Return current year as default, will be overridden by user selection
        return DateRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31),
        );
    }
  }
}

/// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange({required this.start, required this.end});

  String get description {
    if (start.year == end.year) {
      if (start.month == end.month) {
        return '${start.day}-${end.day} ${_monthName(start.month)} ${start.year}';
      } else {
        return '${_monthName(start.month)} ${start.day} - ${_monthName(end.month)} ${end.day}, ${start.year}';
      }
    } else {
      return '${_monthName(start.month)} ${start.day}, ${start.year} - ${_monthName(end.month)} ${end.day}, ${end.year}';
    }
  }

  String _monthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[month - 1];
  }
}
