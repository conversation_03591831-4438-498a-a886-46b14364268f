import 'package:uuid/uuid.dart';
import 'sync_status.dart'; // Import SyncStatus

class TimeLog {
  final String id;
  final String userId;
  final String jobId;
  final DateTime date;
  final double hours;
  final String? notes;
  final double hourlyRate; // Rate used for this time log
  final double laborCost; // Calculated labor cost (hours * hourlyRate)
  final bool isFlatRate; // Whether this is a flat rate or hourly
  final String? voiceNoteUrl; // URL to the voice note recording
  final DateTime createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking
  final String?
  invoicedInId; // ID of the paid invoice this time log is included in
  final String?
  pendingInvoiceId; // ID of the open invoice this time log is included in
  final String? pendingInvoiceNumber; // Invoice number for UI display

  TimeLog({
    String? id,
    required this.userId,
    required this.jobId,
    required this.date,
    required this.hours,
    this.notes,
    required this.hourlyRate,
    required this.laborCost,
    this.isFlatRate = false,
    this.voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
    this.invoicedInId, // ID of the paid invoice this time log is included in
    this.pendingInvoiceId, // ID of the open invoice this time log is included in
    this.pendingInvoiceNumber, // Invoice number for UI display
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create a TimeLog from a JSON object
  factory TimeLog.fromJson(Map<String, dynamic> json) {
    return TimeLog(
      id: json['id'],
      userId: json['user_id'],
      jobId: json['job_id'],
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      hours: json['hours']?.toDouble() ?? 0.0,
      notes: json['notes'],
      hourlyRate: json['hourly_rate']?.toDouble() ?? 0.0,
      laborCost: json['labor_cost']?.toDouble() ?? 0.0,
      isFlatRate: json['is_flat_rate'] ?? false,
      voiceNoteUrl: json['voice_note_url'],
      invoicedInId: json['invoiced_in_id'],
      pendingInvoiceId: json['pending_invoice_id'],
      pendingInvoiceNumber: json['pending_invoice_number'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(), // Default to now if null
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(), // Default to now if null
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );
  }

  // Convert TimeLog to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'date': date.toIso8601String(),
      'hours': hours,
      'notes': notes,
      'hourly_rate': hourlyRate,
      'labor_cost': laborCost,
      'is_flat_rate': isFlatRate,
      'voice_note_url': voiceNoteUrl,
      'invoiced_in_id': invoicedInId,
      'pending_invoice_id': pendingInvoiceId,
      'pending_invoice_number': pendingInvoiceNumber,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  // Create a copy of TimeLog with updated fields
  TimeLog copyWith({
    String? id,
    String? userId,
    String? jobId,
    DateTime? date,
    double? hours,
    String? notes,
    double? hourlyRate,
    double? laborCost,
    bool? isFlatRate,
    String? voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
    String? invoicedInId,
    String? pendingInvoiceId,
    String? pendingInvoiceNumber,
  }) {
    return TimeLog(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      date: date ?? this.date,
      hours: hours ?? this.hours,
      notes: notes ?? this.notes,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      laborCost: laborCost ?? this.laborCost,
      isFlatRate: isFlatRate ?? this.isFlatRate,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      invoicedInId: invoicedInId ?? this.invoicedInId,
      pendingInvoiceId: pendingInvoiceId ?? this.pendingInvoiceId,
      pendingInvoiceNumber: pendingInvoiceNumber ?? this.pendingInvoiceNumber,
    );
  }
}
