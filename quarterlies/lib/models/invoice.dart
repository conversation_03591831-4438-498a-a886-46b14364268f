import 'sync_status.dart'; // Import SyncStatus

class Invoice {
  final String? id;
  final String userId;
  final String jobId;
  final String customerId;
  final DateTime issueDate;
  final DateTime dueDate;
  final double totalAmount;
  final double amountPaid;
  final String status; // 'open', 'paid', 'overdue', 'cancelled', 'template'
  final List<InvoiceItem>? lineItems;
  final String? notes;
  final String? voiceNoteUrl; // URL to the recorded voice note
  final bool isTemplate; // Flag to identify templates
  final String? templateName; // Name for the template
  final String? templateId; // For template/cloning functionality
  final DateTime? createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking

  Invoice({
    this.id,
    required this.userId,
    required this.jobId,
    required this.customerId,
    required this.issueDate,
    required this.dueDate,
    required this.totalAmount,
    this.amountPaid = 0.0,
    this.status = 'open',
    this.lineItems,
    this.notes,
    this.voiceNoteUrl, // Added voice note URL field
    this.isTemplate = false,
    this.templateName,
    this.templateId,
    this.createdAt,
    this.updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
  });

  // Getter for balance due
  double get balanceDue => totalAmount - amountPaid;

  // Create an Invoice from a JSON object
  factory Invoice.fromJson(Map<String, dynamic> json) {
    List<InvoiceItem>? items;
    if (json['line_items'] != null) {
      items =
          (json['line_items'] as List)
              .map((item) => InvoiceItem.fromJson(item))
              .toList();
    }

    return Invoice(
      id: json['id'],
      userId: json['user_id'],
      jobId: json['job_id'],
      customerId: json['customer_id'],
      issueDate:
          json['issue_date'] != null
              ? DateTime.parse(json['issue_date'])
              : DateTime.now(),
      dueDate:
          json['due_date'] != null
              ? DateTime.parse(json['due_date'])
              : DateTime.now().add(const Duration(days: 30)),
      totalAmount: json['total_amount']?.toDouble() ?? 0.0,
      amountPaid: json['amount_paid']?.toDouble() ?? 0.0,
      status: json['status'] ?? 'open',
      lineItems: items,
      notes: json['notes'],
      voiceNoteUrl: json['voice_note_url'], // Parse voice note URL from JSON
      isTemplate: json['is_template'] ?? false,
      templateName: json['template_name'],
      templateId: json['template_id'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );
  }

  // Convert Invoice to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'customer_id': customerId,
      'issue_date': issueDate.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'total_amount': totalAmount,
      'amount_paid': amountPaid,
      'status': status,
      'line_items': lineItems?.map((item) => item.toJson()).toList(),
      'notes': notes,
      'voice_note_url': voiceNoteUrl, // Include voice note URL in JSON
      'is_template': isTemplate,
      'template_name': templateName,
      'template_id': templateId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  // Create a copy of Invoice with updated fields
  Invoice copyWith({
    String? id,
    String? userId,
    String? jobId,
    String? customerId,
    DateTime? issueDate,
    DateTime? dueDate,
    double? totalAmount,
    double? amountPaid,
    String? status,
    List<InvoiceItem>? lineItems,
    String? notes,
    String? voiceNoteUrl, // Add voice note URL to copyWith
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Invoice(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      customerId: customerId ?? this.customerId,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      totalAmount: totalAmount ?? this.totalAmount,
      amountPaid: amountPaid ?? this.amountPaid,
      status: status ?? this.status,
      lineItems: lineItems ?? this.lineItems,
      notes: notes ?? this.notes,
      voiceNoteUrl:
          voiceNoteUrl ??
          this.voiceNoteUrl, // Include voice note URL in copyWith
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }
}

class InvoiceItem {
  final String? id;
  final String type; // 'labor', 'material', 'fee', 'other', 'mileage'
  final String description;
  final double quantity;
  final String unit;
  final double unitPrice;
  final double? taxRate;
  final bool? excluded; // Flag to exclude item from total calculation
  final Map<String, dynamic>?
  metadata; // Additional data for UI display and tracking
  final String?
  sourceId; // ID of the source record (expense, time log, mileage)
  final String?
  invoicedInId; // ID of the invoice where this item is included (paid)
  final String?
  pendingInvoiceId; // ID of the open invoice where this item is included

  InvoiceItem({
    this.id,
    required this.type,
    required this.description,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    this.taxRate,
    this.excluded = false,
    this.metadata,
    this.sourceId,
    this.invoicedInId,
    this.pendingInvoiceId,
  });

  // Calculate line item total
  double get total => quantity * unitPrice;

  // Create an InvoiceItem from a JSON object
  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'],
      type: json['type'] ?? 'other',
      description: json['description'] ?? '',
      quantity: json['quantity']?.toDouble() ?? 1.0,
      unit: json['unit'] ?? 'each',
      unitPrice: json['unit_price']?.toDouble() ?? 0.0,
      taxRate: json['tax_rate']?.toDouble(),
      excluded: json['excluded'] ?? false,
      metadata:
          json['metadata'] != null
              ? Map<String, dynamic>.from(json['metadata'])
              : null,
      sourceId: json['source_id'],
      invoicedInId: json['invoiced_in_id'],
      pendingInvoiceId: json['pending_invoice_id'],
    );
  }

  // Convert InvoiceItem to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'quantity': quantity,
      'unit': unit,
      'unit_price': unitPrice,
      'tax_rate': taxRate,
      'excluded': excluded,
      'metadata': metadata,
      'source_id': sourceId,
      'invoiced_in_id': invoicedInId,
      'pending_invoice_id': pendingInvoiceId,
    };
  }

  // Create a copy of InvoiceItem with updated fields
  InvoiceItem copyWith({
    String? id,
    String? type,
    String? description,
    double? quantity,
    String? unit,
    double? unitPrice,
    double? taxRate,
    bool? excluded,
    Map<String, dynamic>? metadata,
    String? sourceId,
    String? invoicedInId,
    String? pendingInvoiceId,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      type: type ?? this.type,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      unitPrice: unitPrice ?? this.unitPrice,
      taxRate: taxRate ?? this.taxRate,
      excluded: excluded ?? this.excluded,
      metadata: metadata ?? this.metadata,
      sourceId: sourceId ?? this.sourceId,
      invoicedInId: invoicedInId ?? this.invoicedInId,
      pendingInvoiceId: pendingInvoiceId ?? this.pendingInvoiceId,
    );
  }
}
