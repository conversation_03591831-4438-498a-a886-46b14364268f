import 'package:quarterlies/models/sync_status.dart';

/// Model for signed documents
class SignedDocument {
  final String id;
  final String userId;
  final String signingRequestId;
  final String documentType; // 'estimate' or 'contract'
  final String documentId;
  final String customerName;
  final String customerEmail;
  final DateTime signedAt;
  final String? ipAddress;
  final String? deviceInfo;
  final String? customerPdfPath;
  final String? contractorPdfPath;
  final String? customerPdfUrl;
  final String? contractorPdfUrl;
  final String? certificationPdfPath;
  final DateTime createdAt;
  DateTime? updatedAt;
  SyncStatus syncStatus;

  SignedDocument({
    String? id,
    required this.userId,
    required this.signingRequestId,
    required this.documentType,
    required this.documentId,
    required this.customerName,
    required this.customerEmail,
    required this.signedAt,
    this.ipAddress,
    this.deviceInfo,
    this.customerPdfPath,
    this.contractorPdfPath,
    this.customerPdfUrl,
    this.contractorPdfUrl,
    this.certificationPdfPath,
    DateTime? createdAt,
    this.updatedAt,
    this.syncStatus = SyncStatus.pending,
  }) : id = id ?? _generateId(),
       createdAt = createdAt ?? DateTime.now();

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  SignedDocument copyWith({
    String? id,
    String? userId,
    String? signingRequestId,
    String? documentType,
    String? documentId,
    String? customerName,
    String? customerEmail,
    DateTime? signedAt,
    String? ipAddress,
    String? deviceInfo,
    String? customerPdfPath,
    String? contractorPdfPath,
    String? customerPdfUrl,
    String? contractorPdfUrl,
    String? certificationPdfPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return SignedDocument(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      signingRequestId: signingRequestId ?? this.signingRequestId,
      documentType: documentType ?? this.documentType,
      documentId: documentId ?? this.documentId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      signedAt: signedAt ?? this.signedAt,
      ipAddress: ipAddress ?? this.ipAddress,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      customerPdfPath: customerPdfPath ?? this.customerPdfPath,
      contractorPdfPath: contractorPdfPath ?? this.contractorPdfPath,
      customerPdfUrl: customerPdfUrl ?? this.customerPdfUrl,
      contractorPdfUrl: contractorPdfUrl ?? this.contractorPdfUrl,
      certificationPdfPath: certificationPdfPath ?? this.certificationPdfPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'signing_request_id': signingRequestId,
      'document_type': documentType,
      'document_id': documentId,
      'customer_name': customerName,
      'customer_email': customerEmail,
      'signed_at': signedAt.toIso8601String(),
      'ip_address': ipAddress,
      'device_info': deviceInfo,
      'customer_pdf_path': customerPdfPath,
      'contractor_pdf_path': contractorPdfPath,
      'customer_pdf_url': customerPdfUrl,
      'contractor_pdf_url': contractorPdfUrl,
      'certification_pdf_path': certificationPdfPath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.name,
    };
  }

  factory SignedDocument.fromJson(Map<String, dynamic> json) {
    return SignedDocument(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      signingRequestId: json['signing_request_id'] as String,
      documentType: json['document_type'] as String,
      documentId: json['document_id'] as String,
      customerName: json['customer_name'] as String,
      customerEmail: json['customer_email'] as String,
      signedAt: DateTime.parse(json['signed_at'] as String),
      ipAddress: json['ip_address'] as String?,
      deviceInfo: json['device_info'] as String?,
      customerPdfPath: json['customer_pdf_path'] as String?,
      contractorPdfPath: json['contractor_pdf_path'] as String?,
      customerPdfUrl: json['customer_pdf_url'] as String?,
      contractorPdfUrl: json['contractor_pdf_url'] as String?,
      certificationPdfPath: json['certification_pdf_path'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      syncStatus: SyncStatusExtension.fromJson(json['sync_status'] as String),
    );
  }

  @override
  String toString() {
    return 'SignedDocument(id: $id, documentType: $documentType, customerName: $customerName, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SignedDocument && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Check if the document has local customer PDF
  bool get hasLocalCustomerPdf => customerPdfPath != null;

  /// Check if the document has local contractor PDF
  bool get hasLocalContractorPdf => contractorPdfPath != null;

  /// Check if the document has remote URLs
  bool get hasRemoteUrls => customerPdfUrl != null && contractorPdfUrl != null;

  /// Check if the document is fully synced
  bool get isFullySynced => syncStatus == SyncStatus.synced && hasRemoteUrls;
}
