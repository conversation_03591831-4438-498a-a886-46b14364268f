import 'package:uuid/uuid.dart';
import 'sync_status.dart'; // Assuming you have a SyncStatus enum

class Payment {
  final String id;
  final String userId;
  final String? invoiceId; // Link to an Invoice
  final String?
  jobId; // Link to a Job (can be null if payment is not job-specific)
  final double amountReceived;
  final DateTime paymentDate;
  final String paymentMethod; // e.g., 'Credit Card', 'Bank Transfer', 'Cash'
  final String? notes;
  final String? voiceNoteUrl; // URL to the recorded voice note
  final DateTime createdAt;
  DateTime? updatedAt;
  SyncStatus syncStatus;

  // Alias for paymentDate to maintain compatibility with existing code
  DateTime get dateReceived => paymentDate;

  // Alias for amountReceived to maintain compatibility with existing code
  double get amount => amountReceived;

  Payment({
    String? id,
    required this.userId,
    this.invoiceId,
    this.jobId,
    required this.amountReceived,
    required this.paymentDate,
    required this.paymentMethod,
    this.notes,
    this.voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      userId: json['user_id'],
      invoiceId: json['invoice_id'],
      jobId: json['job_id'],
      amountReceived: json['amount_received']?.toDouble() ?? 0.0,
      paymentDate:
          json['payment_date'] != null
              ? DateTime.parse(json['payment_date'])
              : DateTime.now(),
      paymentMethod: json['payment_method'] ?? 'Unknown',
      notes: json['notes'],
      voiceNoteUrl: json['voice_note_url'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'invoice_id': invoiceId,
      'job_id': jobId,
      'amount_received': amountReceived,
      'payment_date': paymentDate.toIso8601String(),
      'payment_method': paymentMethod,
      'notes': notes,
      'voice_note_url': voiceNoteUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  Payment copyWith({
    String? id,
    String? userId,
    String? invoiceId,
    String? jobId,
    double? amountReceived,
    DateTime? paymentDate,
    String? paymentMethod,
    String? notes,
    String? voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Payment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      invoiceId: invoiceId ?? this.invoiceId,
      jobId: jobId ?? this.jobId,
      amountReceived: amountReceived ?? this.amountReceived,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }
}
