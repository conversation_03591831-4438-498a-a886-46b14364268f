import 'package:uuid/uuid.dart';
import 'sync_status.dart';

class Expense {
  final String id;
  final String userId;
  final String? jobId; // Nullable for overhead expenses
  final String description;
  final double amount;
  final DateTime date;
  final String? receiptPhotoUrl;
  final List<String>? tags;
  final bool isOverhead; // True for non-job expenses
  final String? category; // IRS Schedule C category
  final String? voiceNoteUrl; // URL to the recorded voice note
  final DateTime createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking
  final String? invoicedInId; // ID of the invoice this expense is included in
  final String?
  pendingInvoiceId; // ID of the invoice this expense is pending in
  final String?
  pendingInvoiceNumber; // Number of the invoice this expense is pending in

  Expense({
    String? id,
    required this.userId,
    this.jobId,
    required this.description,
    required this.amount,
    required this.date,
    this.receiptPhotoUrl,
    this.tags,
    this.isOverhead = false,
    this.category,
    this.voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending,
    this.invoicedInId,
    this.pendingInvoiceId,
    this.pendingInvoiceNumber,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create an Expense from a JSON object
  factory Expense.fromJson(Map<String, dynamic> json) {
    return Expense(
      id: json['id'],
      userId: json['user_id'],
      jobId: json['job_id'],
      description: json['description'],
      amount:
          json['amount'] is int
              ? (json['amount'] as int).toDouble()
              : json['amount'],
      date:
          json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      receiptPhotoUrl: json['receipt_photo_url'],
      tags:
          json['tags'] is List
              ? List<String>.from(json['tags'])
              : json['tags'] != null && json['tags'].toString().isNotEmpty
              ? json['tags'].toString().split(',')
              : null,
      isOverhead: json['is_overhead'] ?? false,
      category: json['category'],
      voiceNoteUrl: json['voice_note_url'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
      invoicedInId: json['invoiced_in_id'],
      pendingInvoiceId: json['pending_invoice_id'],
      pendingInvoiceNumber: json['pending_invoice_number'],
    );
  }

  // Convert Expense to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'description': description,
      'amount': amount,
      'date': date.toIso8601String(),
      'receipt_photo_url': receiptPhotoUrl,
      'tags': tags?.join(','),
      'is_overhead': isOverhead,
      'category': category,
      'voice_note_url': voiceNoteUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
      'invoiced_in_id': invoicedInId,
      'pending_invoice_id': pendingInvoiceId,
      'pending_invoice_number': pendingInvoiceNumber,
    };
  }

  // Create a copy of Expense with updated fields
  Expense copyWith({
    String? id,
    String? userId,
    String? jobId,
    String? description,
    double? amount,
    DateTime? date,
    String? receiptPhotoUrl,
    List<String>? tags,
    bool? isOverhead,
    String? category,
    String? voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
    String? invoicedInId,
    String? pendingInvoiceId,
    String? pendingInvoiceNumber,
  }) {
    return Expense(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      receiptPhotoUrl: receiptPhotoUrl ?? this.receiptPhotoUrl,
      tags: tags ?? this.tags,
      isOverhead: isOverhead ?? this.isOverhead,
      category: category ?? this.category,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      invoicedInId: invoicedInId ?? this.invoicedInId,
      pendingInvoiceId: pendingInvoiceId ?? this.pendingInvoiceId,
      pendingInvoiceNumber: pendingInvoiceNumber ?? this.pendingInvoiceNumber,
    );
  }
}
