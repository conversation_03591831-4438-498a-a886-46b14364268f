// Financial summary model for dashboard and reporting
class FinancialSummary {
  final double totalIncome;
  final double totalExpenses;
  final double netProfit;
  final double profitMargin;
  final DateTime startDate;
  final DateTime endDate;

  FinancialSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netProfit,
    required this.profitMargin,
    required this.startDate,
    required this.endDate,
  });
}

// Generic financial transaction model for reporting
class FinancialTransaction {
  final String id;
  final String type; // 'income', 'expense', 'payment', etc.
  final DateTime date;
  final double amount;
  final String? description;
  final String? category;
  final String? relatedEntityId; // ID of related job, invoice, etc.
  final String?
  relatedEntityType; // Type of related entity (job, invoice, etc.)

  FinancialTransaction({
    required this.id,
    required this.type,
    required this.date,
    required this.amount,
    this.description,
    this.category,
    this.relatedEntityId,
    this.relatedEntityType,
  });
}
