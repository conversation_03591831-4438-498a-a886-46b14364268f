import 'package:uuid/uuid.dart';

enum DisplayMode { field, office }

extension DisplayModeExtension on DisplayMode {
  String get value {
    switch (this) {
      case DisplayMode.field:
        return 'field';
      case DisplayMode.office:
        return 'office';
    }
  }

  static DisplayMode fromString(String value) {
    switch (value.toLowerCase()) {
      case 'office':
        return DisplayMode.office;
      case 'field':
      default:
        return DisplayMode.field;
    }
  }
}

class UserSettings {
  final String id;
  final String userId;
  final bool
  defaultLiveJobCostSync; // Legacy field - kept for backward compatibility
  final bool
  syncExpenses; // Whether to sync job expenses (excluding mileage) to invoices
  final bool syncMileage; // Whether to sync mileage entries to invoices
  final bool syncLaborCosts; // Whether to sync labor costs to invoices
  final bool
  syncEstimateItems; // Whether to sync estimate line items to invoices
  final int
  defaultInvoiceDueDays; // Default number of days for invoice due date
  final bool
  enableDueDateNotifications; // Whether to enable invoice due date notifications
  final int
  dueDateNotificationDays; // Number of days before due date to send notification
  final bool
  enableMileageTracking; // Whether to enable automatic mileage tracking
  final int?
  mileageIdleTimeoutMinutes; // Minutes of idle time before auto-tracking stops
  final bool enableVoiceInput; // Whether to enable voice input for text fields
  final bool enableOfflineMode; // Whether to enable offline mode
  final bool wifiOnlySync; // Whether to sync only when on WiFi
  final String? businessName; // Business name for invoices, estimates, etc.
  final String?
  businessAddress; // Business address for invoices, estimates, etc.
  final String? businessPhone; // Business phone for invoices, estimates, etc.
  final String? businessEmail; // Business email for invoices, estimates, etc.
  final String?
  businessLogo; // URL to business logo for invoices, estimates, etc.
  final String? defaultInvoiceNotes; // Default notes for invoices
  final String? defaultInvoiceTerms; // Default terms for invoices
  final bool
  showMileageAsSummary; // Whether to show mileage as summary on invoices
  final bool
  showHoursAsIndividual; // Whether to show hours as individual items on invoices

  // Glare resistance settings
  final bool
  automaticBrightnessDetection; // Whether to use automatic brightness detection (defaults to device setting)
  final bool
  dynamicColorAdjustment; // Whether to adjust colors based on ambient light (default on)
  final bool
  enhancedContrastMode; // Whether to use enhanced contrast mode (default on)

  // Display settings
  final DisplayMode displayMode; // Field Mode (default) or Office Mode

  final DateTime createdAt;
  final DateTime updatedAt;

  UserSettings({
    String? id,
    required this.userId,
    this.defaultLiveJobCostSync = false, // Default is disabled
    this.syncExpenses = false, // Default is disabled - other expenses
    this.syncMileage =
        false, // Default is disabled - mileage is primarily for tax purposes
    this.syncLaborCosts =
        false, // Default is disabled - time logs are for tracking hours
    this.syncEstimateItems = true, // Default is enabled
    this.defaultInvoiceDueDays = 30, // Default is 30 days
    this.enableDueDateNotifications = true, // Default is enabled
    this.dueDateNotificationDays = 3, // Default is 3 days before due date
    this.enableMileageTracking = false, // Default is disabled
    this.mileageIdleTimeoutMinutes = 5, // Default is 5 minutes
    this.enableVoiceInput = true, // Default is enabled
    this.enableOfflineMode = true, // Default is enabled
    this.wifiOnlySync = true, // Default is enabled
    this.businessName,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.businessLogo,
    this.defaultInvoiceNotes,
    this.defaultInvoiceTerms,
    this.showMileageAsSummary = true, // Default is enabled
    this.showHoursAsIndividual = true, // Default is enabled
    this.automaticBrightnessDetection = true, // Default to device setting
    this.dynamicColorAdjustment = true, // Default on
    this.enhancedContrastMode = true, // Default on
    this.displayMode = DisplayMode.field, // Default is Field Mode
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create UserSettings from a JSON object
  factory UserSettings.fromJson(Map<String, dynamic> json) {
    // For backward compatibility, if the new fields don't exist, use the legacy field
    final bool legacySync = json['default_live_job_cost_sync'] ?? true;

    return UserSettings(
      id: json['id'],
      userId: json['user_id'],
      defaultLiveJobCostSync: legacySync,
      syncExpenses: json['sync_expenses'] ?? legacySync,
      syncMileage: json['sync_mileage'] ?? legacySync,
      syncLaborCosts: json['sync_labor_costs'] ?? legacySync,
      syncEstimateItems: json['sync_estimate_items'] ?? legacySync,
      defaultInvoiceDueDays: json['default_invoice_due_days'] ?? 30,
      enableDueDateNotifications: json['enable_due_date_notifications'] ?? true,
      dueDateNotificationDays: json['due_date_notification_days'] ?? 3,
      enableMileageTracking: json['enable_mileage_tracking'] ?? false,
      mileageIdleTimeoutMinutes: json['mileage_idle_timeout_minutes'] ?? 5,
      enableVoiceInput: json['enable_voice_input'] ?? true,
      enableOfflineMode: json['enable_offline_mode'] ?? true,
      wifiOnlySync: json['wifi_only_sync'] ?? true,
      businessName: json['business_name'],
      businessAddress: json['business_address'],
      businessPhone: json['business_phone'],
      businessEmail: json['business_email'],
      businessLogo: json['business_logo'],
      defaultInvoiceNotes: json['default_invoice_notes'],
      defaultInvoiceTerms: json['default_invoice_terms'],
      showMileageAsSummary: json['show_mileage_as_summary'] ?? true,
      showHoursAsIndividual: json['show_hours_as_individual'] ?? true,
      automaticBrightnessDetection:
          json['automatic_brightness_detection'] ?? true,
      dynamicColorAdjustment: json['dynamic_color_adjustment'] ?? true,
      enhancedContrastMode: json['enhanced_contrast_mode'] ?? true,
      displayMode: DisplayModeExtension.fromString(
        json['display_mode'] ?? 'field',
      ),
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  // Convert UserSettings to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'default_live_job_cost_sync': defaultLiveJobCostSync,
      'sync_expenses': syncExpenses,
      'sync_mileage': syncMileage,
      'sync_labor_costs': syncLaborCosts,
      'sync_estimate_items': syncEstimateItems,
      'default_invoice_due_days': defaultInvoiceDueDays,
      'enable_due_date_notifications': enableDueDateNotifications,
      'due_date_notification_days': dueDateNotificationDays,
      'enable_mileage_tracking': enableMileageTracking,
      'mileage_idle_timeout_minutes': mileageIdleTimeoutMinutes,
      'enable_voice_input': enableVoiceInput,
      'enable_offline_mode': enableOfflineMode,
      'wifi_only_sync': wifiOnlySync,
      'business_name': businessName,
      'business_address': businessAddress,
      'business_phone': businessPhone,
      'business_email': businessEmail,
      'business_logo': businessLogo,
      'default_invoice_notes': defaultInvoiceNotes,
      'default_invoice_terms': defaultInvoiceTerms,
      'show_mileage_as_summary': showMileageAsSummary,
      'show_hours_as_individual': showHoursAsIndividual,
      'automatic_brightness_detection': automaticBrightnessDetection,
      'dynamic_color_adjustment': dynamicColorAdjustment,
      'enhanced_contrast_mode': enhancedContrastMode,
      'display_mode': displayMode.value,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Create a copy of UserSettings with updated fields
  UserSettings copyWith({
    String? id,
    String? userId,
    bool? defaultLiveJobCostSync,
    bool? syncExpenses,
    bool? syncMileage,
    bool? syncLaborCosts,
    bool? syncEstimateItems,
    int? defaultInvoiceDueDays,
    bool? enableDueDateNotifications,
    int? dueDateNotificationDays,
    bool? enableMileageTracking,
    int? mileageIdleTimeoutMinutes,
    bool? enableVoiceInput,
    bool? enableOfflineMode,
    bool? wifiOnlySync,
    String? businessName,
    String? businessAddress,
    String? businessPhone,
    String? businessEmail,
    String? businessLogo,
    String? defaultInvoiceNotes,
    String? defaultInvoiceTerms,
    bool? showMileageAsSummary,
    bool? showHoursAsIndividual,
    bool? automaticBrightnessDetection,
    bool? dynamicColorAdjustment,
    bool? enhancedContrastMode,
    DisplayMode? displayMode,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSettings(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      defaultLiveJobCostSync:
          defaultLiveJobCostSync ?? this.defaultLiveJobCostSync,
      syncExpenses: syncExpenses ?? this.syncExpenses,
      syncMileage: syncMileage ?? this.syncMileage,
      syncLaborCosts: syncLaborCosts ?? this.syncLaborCosts,
      syncEstimateItems: syncEstimateItems ?? this.syncEstimateItems,
      defaultInvoiceDueDays:
          defaultInvoiceDueDays ?? this.defaultInvoiceDueDays,
      enableDueDateNotifications:
          enableDueDateNotifications ?? this.enableDueDateNotifications,
      dueDateNotificationDays:
          dueDateNotificationDays ?? this.dueDateNotificationDays,
      enableMileageTracking:
          enableMileageTracking ?? this.enableMileageTracking,
      mileageIdleTimeoutMinutes:
          mileageIdleTimeoutMinutes ?? this.mileageIdleTimeoutMinutes,
      enableVoiceInput: enableVoiceInput ?? this.enableVoiceInput,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      wifiOnlySync: wifiOnlySync ?? this.wifiOnlySync,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      businessPhone: businessPhone ?? this.businessPhone,
      businessEmail: businessEmail ?? this.businessEmail,
      businessLogo: businessLogo ?? this.businessLogo,
      defaultInvoiceNotes: defaultInvoiceNotes ?? this.defaultInvoiceNotes,
      defaultInvoiceTerms: defaultInvoiceTerms ?? this.defaultInvoiceTerms,
      showMileageAsSummary: showMileageAsSummary ?? this.showMileageAsSummary,
      showHoursAsIndividual:
          showHoursAsIndividual ?? this.showHoursAsIndividual,
      automaticBrightnessDetection:
          automaticBrightnessDetection ?? this.automaticBrightnessDetection,
      dynamicColorAdjustment:
          dynamicColorAdjustment ?? this.dynamicColorAdjustment,
      enhancedContrastMode: enhancedContrastMode ?? this.enhancedContrastMode,
      displayMode: displayMode ?? this.displayMode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
