import 'package:uuid/uuid.dart';
import 'sync_status.dart'; // Import SyncStatus

class Estimate {
  final String id;
  final String userId;
  final String jobId;
  final String customerId;
  final List<EstimateItem> lineItems;
  final double totalAmount;
  final String status; // 'draft', 'sent', 'accepted', 'rejected', 'template'
  final String? notes;
  final String? templateId; // For template/cloning functionality
  final bool isTemplate; // Flag to identify templates
  final String? templateName; // Name for the template
  final DateTime createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking

  Estimate({
    String? id,
    required this.userId,
    required this.jobId,
    required this.customerId,
    required this.lineItems,
    required this.totalAmount,
    this.status = 'draft',
    this.notes,
    this.templateId,
    this.isTemplate = false,
    this.templateName,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create an Estimate from a JSON object
  factory Estimate.fromJson(Map<String, dynamic> json) {
    List<EstimateItem> items = [];
    if (json['line_items'] != null) {
      items =
          (json['line_items'] as List)
              .map((item) => EstimateItem.fromJson(item))
              .toList();
    }

    return Estimate(
      id: json['id'],
      userId: json['user_id'],
      jobId: json['job_id'],
      customerId: json['customer_id'],
      lineItems: items,
      totalAmount: json['total_amount']?.toDouble() ?? 0.0,
      status: json['status'],
      notes: json['notes'],
      templateId: json['template_id'],
      isTemplate: json['is_template'] ?? false,
      templateName: json['template_name'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(), // Default to now if null
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(), // Default to now if null
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );
  }

  // Convert Estimate to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'customer_id': customerId,
      'line_items': lineItems.map((item) => item.toJson()).toList(),
      'total_amount': totalAmount,
      'status': status,
      'notes': notes,
      'template_id': templateId,
      'is_template': isTemplate,
      'template_name': templateName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  // Create a copy of Estimate with updated fields
  Estimate copyWith({
    String? id,
    String? userId,
    String? jobId,
    String? customerId,
    List<EstimateItem>? lineItems,
    double? totalAmount,
    String? status,
    String? notes,
    String? templateId,
    bool? isTemplate,
    String? templateName,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Estimate(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      customerId: customerId ?? this.customerId,
      lineItems: lineItems ?? this.lineItems,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      isTemplate: isTemplate ?? this.isTemplate,
      templateName: templateName ?? this.templateName,
      templateId: templateId ?? this.templateId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  // Calculate total from line items
  double calculateTotal() {
    return lineItems.fold(
      0,
      (sum, item) => sum + (item.quantity * item.unitPrice),
    );
  }

  // Clone estimate (for templates)
  Estimate cloneAsTemplate() {
    return Estimate(
      userId: userId,
      jobId: jobId,
      customerId: customerId,
      lineItems: lineItems.map((item) => item.clone()).toList(),
      totalAmount: totalAmount,
      status: 'draft',
      notes: notes,
      templateId: id, // Reference to original estimate
    );
  }
}

// Enum for Estimate Status
class EstimateStatus {
  static const String draft = 'draft';
  static const String sent = 'sent';
  static const String accepted = 'accepted';
  static const String rejected = 'rejected';

  static const List<String> values = [draft, sent, accepted, rejected];
}

// Class for Estimate Line Items
class EstimateItem {
  final String id;
  final String description;
  final double quantity;
  final String unit; // e.g., 'hours', 'pieces', 'sq ft'
  final double unitPrice;
  final double? taxRate;

  EstimateItem({
    String? id,
    required this.description,
    required this.quantity,
    required this.unit,
    required this.unitPrice,
    this.taxRate,
  }) : id = id ?? const Uuid().v4();

  // Calculate line item total
  double get total => quantity * unitPrice;

  // Calculate tax amount if applicable
  double get taxAmount => taxRate != null ? total * (taxRate! / 100) : 0;

  // Calculate total with tax
  double get totalWithTax => total + taxAmount;

  // Create an EstimateItem from a JSON object
  factory EstimateItem.fromJson(Map<String, dynamic> json) {
    return EstimateItem(
      id: json['id'],
      description: json['description'],
      quantity: json['quantity']?.toDouble() ?? 0.0,
      unit: json['unit'],
      unitPrice: json['unit_price']?.toDouble() ?? 0.0,
      taxRate: json['tax_rate']?.toDouble(),
    );
  }

  // Convert EstimateItem to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'quantity': quantity,
      'unit': unit,
      'unit_price': unitPrice,
      'tax_rate': taxRate,
    };
  }

  // Clone item
  EstimateItem clone() {
    return EstimateItem(
      description: description,
      quantity: quantity,
      unit: unit,
      unitPrice: unitPrice,
      taxRate: taxRate,
    );
  }
}
