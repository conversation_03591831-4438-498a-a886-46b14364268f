import 'package:quarterlies/models/sync_status.dart';

/// Model for document signing requests
class DocumentSigningRequest {
  final String id;
  final String userId;
  final String documentType; // 'estimate' or 'contract'
  final String documentId;
  final String customerId;
  final String customerEmail;
  final String customerName;
  final String jobId;
  final String signingLink;
  final DateTime expirationDate;
  final String status; // 'pending', 'sent', 'signed', 'expired'
  final String? localPdfPath;
  final String? remotePdfUrl;
  final DateTime createdAt;
  DateTime? updatedAt;
  SyncStatus syncStatus;

  DocumentSigningRequest({
    String? id,
    required this.userId,
    required this.documentType,
    required this.documentId,
    required this.customerId,
    required this.customerEmail,
    required this.customerName,
    required this.jobId,
    required this.signingLink,
    required this.expirationDate,
    required this.status,
    this.localPdfPath,
    this.remotePdfUrl,
    DateTime? createdAt,
    this.updatedAt,
    this.syncStatus = SyncStatus.pending,
  }) : id = id ?? _generateId(),
       createdAt = createdAt ?? DateTime.now();

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  DocumentSigningRequest copyWith({
    String? id,
    String? userId,
    String? documentType,
    String? documentId,
    String? customerId,
    String? customerEmail,
    String? customerName,
    String? jobId,
    String? signingLink,
    DateTime? expirationDate,
    String? status,
    String? localPdfPath,
    String? remotePdfUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return DocumentSigningRequest(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentType: documentType ?? this.documentType,
      documentId: documentId ?? this.documentId,
      customerId: customerId ?? this.customerId,
      customerEmail: customerEmail ?? this.customerEmail,
      customerName: customerName ?? this.customerName,
      jobId: jobId ?? this.jobId,
      signingLink: signingLink ?? this.signingLink,
      expirationDate: expirationDate ?? this.expirationDate,
      status: status ?? this.status,
      localPdfPath: localPdfPath ?? this.localPdfPath,
      remotePdfUrl: remotePdfUrl ?? this.remotePdfUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'document_type': documentType,
      'document_id': documentId,
      'customer_id': customerId,
      'customer_email': customerEmail,
      'customer_name': customerName,
      'job_id': jobId,
      'signing_link': signingLink,
      'expiration_date': expirationDate.toIso8601String(),
      'status': status,
      'local_pdf_path': localPdfPath,
      'remote_pdf_url': remotePdfUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.name,
    };
  }

  factory DocumentSigningRequest.fromJson(Map<String, dynamic> json) {
    return DocumentSigningRequest(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      documentType: json['document_type'] as String,
      documentId: json['document_id'] as String,
      customerId: json['customer_id'] as String,
      customerEmail: json['customer_email'] as String,
      customerName: json['customer_name'] as String,
      jobId: json['job_id'] as String,
      signingLink: json['signing_link'] as String,
      expirationDate: DateTime.parse(json['expiration_date'] as String),
      status: json['status'] as String,
      localPdfPath: json['local_pdf_path'] as String?,
      remotePdfUrl: json['remote_pdf_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      syncStatus: SyncStatusExtension.fromJson(json['sync_status'] as String),
    );
  }

  @override
  String toString() {
    return 'DocumentSigningRequest(id: $id, documentType: $documentType, status: $status, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DocumentSigningRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Check if the signing request has expired
  bool get isExpired => DateTime.now().isAfter(expirationDate);

  /// Check if the request is signed
  bool get isSigned => status == 'signed';

  /// Check if the request is pending
  bool get isPending => status == 'pending';

  /// Check if the request has been sent
  bool get isSent => status == 'sent';
}
