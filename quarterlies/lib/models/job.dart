import 'package:uuid/uuid.dart';
import 'sync_status.dart'; // Import SyncStatus
import 'customer.dart'; // Import Customer

class Job {
  final String id;
  final String userId;
  final String customerId;
  final String title;
  final String? description;
  final double? estimatedPrice;
  final double? estimatedExpensesBudget; // Budget for estimated expenses/costs
  final double? actualIncome;
  final double? actualExpenses;
  final double? actualLaborCost;
  final String status; // 'estimate', 'active', 'completed', 'invoiced', 'paid'
  final bool
  liveCostSyncEnabled; // Legacy field - kept for backward compatibility
  final bool
  syncExpenses; // Whether to automatically include job expenses (excluding mileage) in new invoices (user can still choose which to include)
  final bool
  syncMileage; // Whether to automatically include mileage entries in new invoices (user can still choose which to include)
  final bool
  syncLaborCosts; // Whether to automatically include labor costs in new invoices (user can still choose which to include)
  final bool
  syncEstimateItems; // Whether to automatically include estimate line items in new invoices (user can still choose which to include)
  final bool
  summarizeMileage; // Whether to show mileage as a single summary line item in invoices (true) or as individual entries (false)
  final bool
  summarizeHours; // Whether to show hours as a single summary line item in invoices (true) or as individual entries (false)
  final int?
  defaultInvoiceDueDays; // Job-specific default due days for invoices
  final String? voiceNoteUrl; // URL to the recorded voice note
  final String? address; // Job location address
  final String? city; // Job location city
  final String? state; // Job location state
  final String? zipCode; // Job location zip code
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking

  // Customer object for the job (populated from join queries)
  Customer? customer;

  Job({
    String? id,
    required this.userId,
    required this.customerId,
    required this.title,
    this.description,
    this.estimatedPrice,
    this.estimatedExpensesBudget,
    this.actualIncome = 0.0,
    this.actualExpenses = 0.0,
    this.actualLaborCost = 0.0,
    this.status = 'estimate',
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.startDate,
    this.endDate,
    this.liveCostSyncEnabled = false, // Default to disabled
    this.syncExpenses = false, // Default to disabled - other expenses
    this.syncMileage =
        false, // Default to disabled - mileage is primarily for tax purposes
    this.syncLaborCosts =
        false, // Default to disabled - time logs are for tracking hours
    this.syncEstimateItems = true, // Default to enabled for estimate items
    this.summarizeMileage =
        true, // Default to summary for mileage (primarily for tax purposes)
    this.summarizeHours = false, // Default to individual entries for hours
    this.defaultInvoiceDueDays,
    this.voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Calculate profit/loss
  double get profitLoss {
    final income = actualIncome ?? 0.0;
    final expenses = actualExpenses ?? 0.0;
    final laborCost = actualLaborCost ?? 0.0;
    return income - expenses - laborCost;
  }

  // Calculate profit margin percentage
  double? get profitMarginPercentage {
    if ((actualIncome ?? 0) <= 0) return null;
    return (profitLoss / (actualIncome ?? 1)) * 100;
  }

  // Create a Job from a JSON object
  factory Job.fromJson(Map<String, dynamic> json) {
    // For backward compatibility, if the new fields don't exist, use the legacy field
    final bool legacySync = json['live_cost_sync_enabled'] ?? true;

    // Handle customer data if it's included in the JSON (from a join query)
    Customer? customer;
    if (json['customers'] != null) {
      customer = Customer.fromJson(json['customers']);
    }

    final job = Job(
      id: json['id'],
      userId: json['user_id'],
      customerId: json['customer_id'],
      title: json['title'],
      description: json['description'],
      estimatedPrice: json['estimated_price']?.toDouble(),
      estimatedExpensesBudget: json['estimated_expenses_budget']?.toDouble(),
      actualIncome: json['actual_income']?.toDouble(),
      actualExpenses: json['actual_expenses']?.toDouble(),
      actualLaborCost: json['actual_labor_cost']?.toDouble(),
      status: json['status'],
      address: json['address'],
      city: json['city'],
      state: json['state'],
      zipCode: json['zip_code'],
      liveCostSyncEnabled: legacySync,
      syncExpenses: json['sync_expenses'] ?? legacySync,
      syncMileage: json['sync_mileage'] ?? legacySync,
      syncLaborCosts: json['sync_labor_costs'] ?? legacySync,
      syncEstimateItems: json['sync_estimate_items'] ?? legacySync,
      summarizeMileage:
          json['summarize_mileage'] ?? true, // Default to true for mileage
      summarizeHours:
          json['summarize_hours'] ?? false, // Default to false for hours
      defaultInvoiceDueDays: json['default_invoice_due_days'],
      voiceNoteUrl: json['voice_note_url'],
      startDate:
          json['start_date'] != null
              ? DateTime.parse(json['start_date'])
              : null,
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(), // Default to now if null
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(), // Default to now if null
      syncStatus:
          json['sync_status'] != null
              ? SyncStatusExtension.fromJson(json['sync_status'])
              : SyncStatus.pending,
    );

    // Set the customer if it was found
    if (customer != null) {
      job.customer = customer;
    }

    return job;
  }

  // Convert Job to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'customer_id': customerId,
      'title': title,
      'description': description,
      'address': address,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'sync_estimate_items': syncEstimateItems,
      'estimated_price': estimatedPrice,
      'estimated_expenses_budget': estimatedExpensesBudget,
      'actual_income': actualIncome,
      'actual_expenses': actualExpenses,
      'actual_labor_cost': actualLaborCost,
      'status': status,
      'live_cost_sync_enabled': liveCostSyncEnabled,
      'sync_expenses': syncExpenses,
      'sync_mileage': syncMileage,
      'sync_labor_costs': syncLaborCosts,
      'summarize_mileage': summarizeMileage,
      'summarize_hours': summarizeHours,
      'default_invoice_due_days': defaultInvoiceDueDays,
      'voice_note_url': voiceNoteUrl,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toJson(),
    };
  }

  // Create a copy of Job with updated fields
  Job copyWith({
    String? id,
    String? userId,
    String? customerId,
    String? title,
    String? description,
    double? estimatedPrice,
    double? estimatedExpensesBudget,
    double? actualIncome,
    double? actualExpenses,
    double? actualLaborCost,
    String? status,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    DateTime? startDate,
    DateTime? endDate,
    bool? liveCostSyncEnabled,
    bool? syncExpenses,
    bool? syncMileage,
    bool? syncLaborCosts,
    bool? syncEstimateItems,
    bool? summarizeMileage,
    bool? summarizeHours,
    int? defaultInvoiceDueDays,
    String? voiceNoteUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Job(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      customerId: customerId ?? this.customerId,
      title: title ?? this.title,
      description: description ?? this.description,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      estimatedExpensesBudget:
          estimatedExpensesBudget ?? this.estimatedExpensesBudget,
      actualIncome: actualIncome ?? this.actualIncome,
      actualExpenses: actualExpenses ?? this.actualExpenses,
      actualLaborCost: actualLaborCost ?? this.actualLaborCost,
      status: status ?? this.status,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      liveCostSyncEnabled: liveCostSyncEnabled ?? this.liveCostSyncEnabled,
      syncExpenses: syncExpenses ?? this.syncExpenses,
      syncMileage: syncMileage ?? this.syncMileage,
      syncLaborCosts: syncLaborCosts ?? this.syncLaborCosts,
      syncEstimateItems: syncEstimateItems ?? this.syncEstimateItems,
      summarizeMileage: summarizeMileage ?? this.summarizeMileage,
      summarizeHours: summarizeHours ?? this.summarizeHours,
      defaultInvoiceDueDays:
          defaultInvoiceDueDays ?? this.defaultInvoiceDueDays,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }
}

// Enum for Job Status
class JobStatus {
  static const String estimate = 'estimate';
  static const String active = 'active';
  static const String completed = 'completed';
  static const String invoiced = 'invoiced';
  static const String paid = 'paid';

  static const List<String> values = [
    estimate,
    active,
    completed,
    invoiced,
    paid,
  ];
}
