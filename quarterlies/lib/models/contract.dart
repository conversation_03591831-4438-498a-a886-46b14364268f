import 'package:uuid/uuid.dart';
import 'sync_status.dart';
import 'estimate.dart';

class Contract {
  final String id;
  final String userId;
  final String jobId;
  final String customerId;
  final String? estimateId; // Reference to the original estimate
  final List<ContractItem> lineItems;
  final double totalAmount;
  final String status; // 'draft', 'sent', 'signed', 'completed', 'cancelled'
  final String? notes;
  final DateTime createdAt;
  DateTime? updatedAt; // Made non-final to allow updates
  SyncStatus syncStatus; // Added for sync status tracking

  Contract({
    String? id,
    required this.userId,
    required this.jobId,
    required this.customerId,
    this.estimateId,
    required this.lineItems,
    required this.totalAmount,
    this.status = 'draft',
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncStatus = SyncStatus.pending, // Default to pending
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // Create a Contract from a JSON object
  factory Contract.fromJson(Map<String, dynamic> json) {
    List<ContractItem> items = [];
    if (json['line_items'] != null) {
      items =
          (json['line_items'] as List)
              .map((item) => ContractItem.fromJson(item))
              .toList();
    }

    return Contract(
      id: json['id'],
      userId: json['user_id'],
      jobId: json['job_id'],
      customerId: json['customer_id'],
      estimateId: json['estimate_id'],
      lineItems: items,
      totalAmount: json['total_amount']?.toDouble() ?? 0.0,
      status: json['status'] ?? 'draft',
      notes: json['notes'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : DateTime.now(),
      syncStatus: SyncStatusExtension.fromJson(
        json['sync_status'] ?? 'pending',
      ),
    );
  }

  // Convert Contract to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'job_id': jobId,
      'customer_id': customerId,
      'estimate_id': estimateId,
      'line_items': lineItems.map((item) => item.toJson()).toList(),
      'total_amount': totalAmount,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'sync_status': syncStatus.toString(),
    };
  }

  // Create a copy of the Contract with updated fields
  Contract copyWith({
    String? id,
    String? userId,
    String? jobId,
    String? customerId,
    String? estimateId,
    List<ContractItem>? lineItems,
    double? totalAmount,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    SyncStatus? syncStatus,
  }) {
    return Contract(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      customerId: customerId ?? this.customerId,
      estimateId: estimateId ?? this.estimateId,
      lineItems: lineItems ?? this.lineItems,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  // Calculate total from line items
  double calculateTotal() {
    return lineItems.fold(
      0,
      (sum, item) => sum + (item.quantity * item.unitPrice),
    );
  }

  // Create a contract from an estimate
  factory Contract.fromEstimate(Estimate estimate) {
    return Contract(
      userId: estimate.userId,
      jobId: estimate.jobId,
      customerId: estimate.customerId,
      estimateId: estimate.id,
      lineItems:
          estimate.lineItems
              .map((item) => ContractItem.fromEstimateItem(item))
              .toList(),
      totalAmount: estimate.totalAmount,
      status: 'draft',
      notes: estimate.notes,
    );
  }
}

class ContractItem {
  final String id;
  final String description;
  final double quantity;
  final double unitPrice;
  final String? unit;

  ContractItem({
    String? id,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.unit,
  }) : id = id ?? const Uuid().v4();

  // Create a ContractItem from a JSON object
  factory ContractItem.fromJson(Map<String, dynamic> json) {
    return ContractItem(
      id: json['id'],
      description: json['description'],
      quantity: json['quantity']?.toDouble() ?? 0.0,
      unitPrice: json['unit_price']?.toDouble() ?? 0.0,
      unit: json['unit'],
    );
  }

  // Convert ContractItem to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'quantity': quantity,
      'unit_price': unitPrice,
      'unit': unit,
    };
  }

  // Create a ContractItem from an EstimateItem
  factory ContractItem.fromEstimateItem(EstimateItem item) {
    return ContractItem(
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      unit: item.unit,
    );
  }

  // Create a copy of the ContractItem with updated fields
  ContractItem copyWith({
    String? id,
    String? description,
    double? quantity,
    double? unitPrice,
    String? unit,
  }) {
    return ContractItem(
      id: id ?? this.id,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      unit: unit ?? this.unit,
    );
  }
}

// Contract status constants
class ContractStatus {
  static const String draft = 'draft';
  static const String sent = 'sent';
  static const String signed = 'signed';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';
}
