/// A model class representing a structured address
class Address {
  /// The street address (e.g., "123 Main St")
  final String streetAddress;

  /// The city or locality
  final String city;

  /// The state or administrative area
  final String state;

  /// The postal or ZIP code
  final String zipCode;

  /// The country
  final String country;

  /// Optional latitude coordinate
  final double? latitude;

  /// Optional longitude coordinate
  final double? longitude;

  /// Full formatted address string
  String get formattedAddress {
    return [
      streetAddress,
      city,
      '$state $zipCode',
      country,
    ].where((part) => part.isNotEmpty).join(', ');
  }

  /// Formatted address without country
  String get shortFormattedAddress {
    return [
      streetAddress,
      city,
      '$state $zipCode',
    ].where((part) => part.isNotEmpty).join(', ');
  }

  /// Constructor
  const Address({
    required this.streetAddress,
    required this.city,
    required this.state,
    required this.zipCode,
    this.country = 'USA',
    this.latitude,
    this.longitude,
  });

  /// Create an Address from a map
  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      streetAddress: json['street_address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      zipCode: json['zip_code'] ?? '',
      country: json['country'] ?? 'USA',
      latitude:
          json['latitude'] != null
              ? double.tryParse(json['latitude'].toString())
              : null,
      longitude:
          json['longitude'] != null
              ? double.tryParse(json['longitude'].toString())
              : null,
    );
  }

  /// Convert Address to a map
  Map<String, dynamic> toJson() {
    return {
      'street_address': streetAddress,
      'city': city,
      'state': state,
      'zip_code': zipCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  /// Create a copy of this Address with some fields replaced
  Address copyWith({
    String? streetAddress,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    double? latitude,
    double? longitude,
  }) {
    return Address(
      streetAddress: streetAddress ?? this.streetAddress,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  @override
  String toString() {
    return formattedAddress;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Address &&
        other.streetAddress == streetAddress &&
        other.city == city &&
        other.state == state &&
        other.zipCode == zipCode &&
        other.country == country;
  }

  @override
  int get hashCode {
    return streetAddress.hashCode ^
        city.hashCode ^
        state.hashCode ^
        zipCode.hashCode ^
        country.hashCode;
  }
}
