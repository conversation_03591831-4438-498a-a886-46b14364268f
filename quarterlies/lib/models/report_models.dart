import 'package:intl/intl.dart';

/// Enum for different report types
enum ReportType {
  profitAndLoss,
  expenseBreakdown,
  jobProfitability,
  invoiceAging,
  mileageSummary,
  taxSummary,
}

/// Extension to get display names for report types
extension ReportTypeExtension on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.profitAndLoss:
        return 'Profit & Loss';
      case ReportType.expenseBreakdown:
        return 'Expense Breakdown';
      case ReportType.jobProfitability:
        return 'Job Profitability';
      case ReportType.invoiceAging:
        return 'Invoice Aging';
      case ReportType.mileageSummary:
        return 'Mileage Summary';
      case ReportType.taxSummary:
        return 'Tax Summary';
    }
  }

  String get description {
    switch (this) {
      case ReportType.profitAndLoss:
        return 'Income vs expenses with net profit analysis';
      case ReportType.expenseBreakdown:
        return 'Expenses categorized by IRS Schedule C categories';
      case ReportType.jobProfitability:
        return 'Profit analysis for individual jobs';
      case ReportType.invoiceAging:
        return 'Outstanding invoices by age';
      case ReportType.mileageSummary:
        return 'Mileage deductions and tax calculations';
      case ReportType.taxSummary:
        return 'Tax liability estimates and payments';
    }
  }
}

/// Enum for predefined time periods
enum TimePeriod {
  currentYear,
  previousYear,
  yearToDate,
  currentQuarter,
  previousQuarter,
  lastThreeMonths,
  lastSixMonths,
  custom,
}

/// Extension to get display names and date ranges for time periods
extension TimePeriodExtension on TimePeriod {
  String get displayName {
    switch (this) {
      case TimePeriod.currentYear:
        return 'Current Year';
      case TimePeriod.previousYear:
        return 'Previous Year';
      case TimePeriod.yearToDate:
        return 'Year to Date';
      case TimePeriod.currentQuarter:
        return 'Current Quarter';
      case TimePeriod.previousQuarter:
        return 'Previous Quarter';
      case TimePeriod.lastThreeMonths:
        return 'Last 3 Months';
      case TimePeriod.lastSixMonths:
        return 'Last 6 Months';
      case TimePeriod.custom:
        return 'Custom Range';
    }
  }

  DateRange get dateRange {
    final now = DateTime.now();
    final currentYear = now.year;

    switch (this) {
      case TimePeriod.currentYear:
        return DateRange(
          startDate: DateTime(currentYear, 1, 1),
          endDate: DateTime(currentYear, 12, 31),
        );
      case TimePeriod.previousYear:
        return DateRange(
          startDate: DateTime(currentYear - 1, 1, 1),
          endDate: DateTime(currentYear - 1, 12, 31),
        );
      case TimePeriod.yearToDate:
        return DateRange(startDate: DateTime(currentYear, 1, 1), endDate: now);
      case TimePeriod.currentQuarter:
        final quarter = ((now.month - 1) ~/ 3) + 1;
        final quarterStartMonth = (quarter - 1) * 3 + 1;
        return DateRange(
          startDate: DateTime(currentYear, quarterStartMonth, 1),
          endDate: DateTime(
            currentYear,
            quarterStartMonth + 2,
            DateTime(currentYear, quarterStartMonth + 3, 0).day,
          ),
        );
      case TimePeriod.previousQuarter:
        final currentQuarter = ((now.month - 1) ~/ 3) + 1;
        final prevQuarter = currentQuarter == 1 ? 4 : currentQuarter - 1;
        final year = currentQuarter == 1 ? currentYear - 1 : currentYear;
        final quarterStartMonth = (prevQuarter - 1) * 3 + 1;
        return DateRange(
          startDate: DateTime(year, quarterStartMonth, 1),
          endDate: DateTime(
            year,
            quarterStartMonth + 2,
            DateTime(year, quarterStartMonth + 3, 0).day,
          ),
        );
      case TimePeriod.lastThreeMonths:
        return DateRange(
          startDate: DateTime(now.year, now.month - 3, now.day),
          endDate: now,
        );
      case TimePeriod.lastSixMonths:
        return DateRange(
          startDate: DateTime(now.year, now.month - 6, now.day),
          endDate: now,
        );
      case TimePeriod.custom:
        // Return current year as default, will be overridden by user selection
        return DateRange(startDate: DateTime(currentYear, 1, 1), endDate: now);
    }
  }
}

/// Date range model
class DateRange {
  final DateTime startDate;
  final DateTime endDate;

  DateRange({required this.startDate, required this.endDate});

  String get formattedRange {
    final formatter = DateFormat('MM/dd/yyyy');
    return '${formatter.format(startDate)} - ${formatter.format(endDate)}';
  }

  String get description {
    final formatter = DateFormat('MMM dd, yyyy');
    return '${formatter.format(startDate)} to ${formatter.format(endDate)}';
  }
}

/// Report filter configuration
class ReportFilters {
  final TimePeriod timePeriod;
  final DateRange? customDateRange;
  final List<String> selectedJobIds;
  final bool includeAllJobs;

  ReportFilters({
    required this.timePeriod,
    this.customDateRange,
    this.selectedJobIds = const [],
    this.includeAllJobs = true,
  });

  DateRange get effectiveDateRange {
    if (timePeriod == TimePeriod.custom && customDateRange != null) {
      return customDateRange!;
    }
    return timePeriod.dateRange;
  }

  String get description {
    final dateDesc = effectiveDateRange.description;
    final jobDesc =
        includeAllJobs
            ? 'All Jobs'
            : '${selectedJobIds.length} Selected Job${selectedJobIds.length == 1 ? '' : 's'}';
    return '$dateDesc • $jobDesc';
  }

  ReportFilters copyWith({
    TimePeriod? timePeriod,
    DateRange? customDateRange,
    List<String>? selectedJobIds,
    bool? includeAllJobs,
  }) {
    return ReportFilters(
      timePeriod: timePeriod ?? this.timePeriod,
      customDateRange: customDateRange ?? this.customDateRange,
      selectedJobIds: selectedJobIds ?? this.selectedJobIds,
      includeAllJobs: includeAllJobs ?? this.includeAllJobs,
    );
  }
}

/// Base report data model
abstract class ReportData {
  final ReportType reportType;
  final ReportFilters filters;
  final DateTime generatedAt;

  ReportData({
    required this.reportType,
    required this.filters,
    required this.generatedAt,
  });

  String get title => reportType.displayName;
  String get subtitle => filters.description;
  String get fileName {
    final dateStr = DateFormat('yyyy_MM_dd').format(generatedAt);
    final reportName = reportType.name.toLowerCase();
    return '${reportName}_report_$dateStr.pdf';
  }
}

/// Profit & Loss report data
class ProfitLossReportData extends ReportData {
  final double totalIncome;
  final double totalExpenses;
  final double totalLaborCosts;
  final double netProfit;
  final double profitMargin;
  final Map<String, double> incomeByMonth;
  final Map<String, double> expensesByCategory;
  final List<Map<String, dynamic>> topExpenseCategories;

  ProfitLossReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalLaborCosts,
    required this.netProfit,
    required this.profitMargin,
    required this.incomeByMonth,
    required this.expensesByCategory,
    required this.topExpenseCategories,
  });
}

/// Expense breakdown report data
class ExpenseBreakdownReportData extends ReportData {
  final Map<String, double> expensesByCategory;
  final Map<String, List<Map<String, dynamic>>> expenseDetails;
  final double totalExpenses;
  final double totalOverheadExpenses;
  final double totalJobExpenses;

  ExpenseBreakdownReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.expensesByCategory,
    required this.expenseDetails,
    required this.totalExpenses,
    required this.totalOverheadExpenses,
    required this.totalJobExpenses,
  });
}

/// Job profitability report data
class JobProfitabilityReportData extends ReportData {
  final List<Map<String, dynamic>> jobProfitability;
  final double totalRevenue;
  final double totalCosts;
  final double totalProfit;
  final double averageProfitMargin;

  JobProfitabilityReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.jobProfitability,
    required this.totalRevenue,
    required this.totalCosts,
    required this.totalProfit,
    required this.averageProfitMargin,
  });
}

/// Invoice aging report data
class InvoiceAgingReportData extends ReportData {
  final List<Map<String, dynamic>> currentInvoices;
  final List<Map<String, dynamic>> overdueInvoices;
  final Map<String, double> agingBuckets; // 0-30, 31-60, 61-90, 90+ days
  final double totalOutstanding;
  final double totalOverdue;

  InvoiceAgingReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.currentInvoices,
    required this.overdueInvoices,
    required this.agingBuckets,
    required this.totalOutstanding,
    required this.totalOverdue,
  });
}

/// Mileage summary report data
class MileageSummaryReportData extends ReportData {
  final List<Map<String, dynamic>> mileageEntries;
  final double totalMiles;
  final double totalDeduction;
  final double irsRate;
  final Map<String, double> mileageByJob;
  final Map<String, double> mileageByMonth;

  MileageSummaryReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.mileageEntries,
    required this.totalMiles,
    required this.totalDeduction,
    required this.irsRate,
    required this.mileageByJob,
    required this.mileageByMonth,
  });
}

/// Tax summary report data
class TaxSummaryReportData extends ReportData {
  final double totalIncome;
  final double totalDeductions;
  final double netProfit;
  final double estimatedTaxOwed;
  final double taxPaymentsMade;
  final double taxBalance;
  final Map<String, double> quarterlyBreakdown;
  final List<Map<String, dynamic>> taxPayments;

  TaxSummaryReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.totalIncome,
    required this.totalDeductions,
    required this.netProfit,
    required this.estimatedTaxOwed,
    required this.taxPaymentsMade,
    required this.taxBalance,
    required this.quarterlyBreakdown,
    required this.taxPayments,
  });
}

/// Job-specific report data
class JobReportData extends ReportData {
  final Map<String, dynamic> jobDetails;
  final Map<String, dynamic> financialSummary;
  final List<Map<String, dynamic>> expenses;
  final List<Map<String, dynamic>> timeLogs;
  final List<Map<String, dynamic>> invoices;
  final List<Map<String, dynamic>> payments;
  final List<Map<String, dynamic>> estimates;

  JobReportData({
    required super.reportType,
    required super.filters,
    required super.generatedAt,
    required this.jobDetails,
    required this.financialSummary,
    required this.expenses,
    required this.timeLogs,
    required this.invoices,
    required this.payments,
    required this.estimates,
  });

  @override
  String get fileName {
    final dateStr = DateFormat('yyyy_MM_dd').format(generatedAt);
    final jobTitle =
        (jobDetails['title'] as String? ?? 'job')
            .replaceAll(RegExp(r'[^\w\s-]'), '')
            .replaceAll(' ', '_')
            .toLowerCase();
    return 'job_report_${jobTitle}_$dateStr.pdf';
  }
}
