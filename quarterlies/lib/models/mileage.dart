import 'expense.dart';
import 'sync_status.dart'; // Import the file where SyncStatus is defined

class Mileage extends Expense {
  final String startLocation;
  final String endLocation;
  final double miles;
  final double ratePerMile; // IRS mileage rate
  final bool isAutoTracked; // Whether this mileage was auto-tracked via GPS
  final String? startCoordinates; // Optional GPS coordinates for start location
  final String? endCoordinates; // Optional GPS coordinates for end location
  final String? purpose; // Purpose of the trip

  Mileage({
    super.id,
    required super.userId,
    super.jobId, // jobId is now nullable in Expense
    required this.startLocation,
    required this.endLocation,
    required this.miles,
    required this.ratePerMile,
    required super.date,
    this.isAutoTracked = false,
    this.startCoordinates,
    this.endCoordinates,
    this.purpose,
    super.voiceNoteUrl, // voiceNoteUrl is now in Expense
    String? description,
    super.receiptPhotoUrl,
    super.tags,
    super.isOverhead = false, // Added isOverhead from Expense
    super.category, // Added category from Expense
    super.createdAt,
    super.updatedAt,
    super.syncStatus, // Added syncStatus from Expense
    super.invoicedInId, // Added invoicedInId from Expense
    super.pendingInvoiceId, // Added pendingInvoiceId from Expense
    super.pendingInvoiceNumber, // Added pendingInvoiceNumber from Expense
  }) : super(
         description: description ?? 'Mileage: $startLocation to $endLocation',
         amount: miles * ratePerMile,
       );

  // Create a Mileage from a JSON object
  factory Mileage.fromJson(Map<String, dynamic> json) {
    // First create the base expense
    final expense = Expense.fromJson(json);

    // Then extract mileage-specific fields
    return Mileage(
      id: expense.id,
      userId: expense.userId,
      jobId: expense.jobId, // jobId is now nullable in Expense
      startLocation: json['start_location'] ?? '',
      endLocation: json['end_location'] ?? '',
      miles: json['miles']?.toDouble() ?? 0.0,
      ratePerMile: json['rate_per_mile']?.toDouble() ?? 0.0,
      date: expense.date,
      isAutoTracked: json['is_auto_tracked'] ?? false,
      startCoordinates: json['start_coordinates'],
      endCoordinates: json['end_coordinates'],
      purpose: json['purpose'],
      voiceNoteUrl: expense.voiceNoteUrl, // voiceNoteUrl is now in Expense
      description: expense.description,
      receiptPhotoUrl: expense.receiptPhotoUrl,
      tags: expense.tags,
      isOverhead: expense.isOverhead, // Added isOverhead from Expense
      category: expense.category, // Added category from Expense
      createdAt: expense.createdAt,
      updatedAt: expense.updatedAt,
      syncStatus: expense.syncStatus, // Added syncStatus from Expense
      invoicedInId: expense.invoicedInId,
      pendingInvoiceId: expense.pendingInvoiceId,
      pendingInvoiceNumber: expense.pendingInvoiceNumber,
    );
  }

  // Convert Mileage to a JSON object
  @override
  Map<String, dynamic> toJson() {
    final baseJson = super.toJson();
    return {
      ...baseJson,
      'start_location': startLocation,
      'end_location': endLocation,
      'miles': miles,
      'rate_per_mile': ratePerMile,
      'is_auto_tracked': isAutoTracked,
      'start_coordinates': startCoordinates,
      'end_coordinates': endCoordinates,
      'purpose': purpose,
      // voiceNoteUrl, description, receiptPhotoUrl, tags, isOverhead, category, createdAt, updatedAt, syncStatus are already included in baseJson from Expense
    };
  }

  // Create a copy of Mileage with updated fields
  @override
  Mileage copyWith({
    double? amount,
    String? category,
    DateTime? createdAt,
    DateTime? date,
    String? description,
    String? id,
    bool? isOverhead,
    String? jobId,
    String? receiptPhotoUrl,
    SyncStatus? syncStatus,
    List<String>? tags,
    DateTime? updatedAt,
    String? userId,
    String? voiceNoteUrl,
    String? invoicedInId,
    String? pendingInvoiceId,
    String? pendingInvoiceNumber,
    // Mileage-specific fields
    String? startLocation,
    String? endLocation,
    double? miles,
    double? ratePerMile,
    bool? isAutoTracked,
    String? startCoordinates,
    String? endCoordinates,
    String? purpose,
  }) {
    return Mileage(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      jobId: jobId ?? this.jobId,
      startLocation: startLocation ?? this.startLocation,
      endLocation: endLocation ?? this.endLocation,
      miles: miles ?? this.miles,
      ratePerMile: ratePerMile ?? this.ratePerMile,
      date: date ?? this.date,
      isAutoTracked: isAutoTracked ?? this.isAutoTracked,
      startCoordinates: startCoordinates ?? this.startCoordinates,
      endCoordinates: endCoordinates ?? this.endCoordinates,
      purpose: purpose ?? this.purpose,
      description: description ?? this.description,
      receiptPhotoUrl: receiptPhotoUrl ?? this.receiptPhotoUrl,
      tags: tags ?? this.tags,
      isOverhead: isOverhead ?? this.isOverhead,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      voiceNoteUrl: voiceNoteUrl ?? this.voiceNoteUrl,
      invoicedInId: invoicedInId ?? this.invoicedInId,
      pendingInvoiceId: pendingInvoiceId ?? this.pendingInvoiceId,
      pendingInvoiceNumber: pendingInvoiceNumber ?? this.pendingInvoiceNumber,
    );
  }
}

// Constants for mileage tracking
class MileageConstants {
  static const String defaultIrsRate =
      '0.655'; // 2023 IRS mileage rate (65.5 cents per mile)
  static const int defaultIdleTimeoutMinutes =
      5; // Default timeout for auto-tracking
}
