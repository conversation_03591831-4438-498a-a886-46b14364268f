class InvoiceStatus {
  static const String draft = 'draft';
  static const String open = 'open';
  static const String sent = 'sent';
  static const String partiallyPaid = 'partially_paid';
  static const String paid = 'paid';
  static const String overdue = 'overdue';
  static const String cancelled = 'cancelled';
  static const String template = 'template';

  static const List<String> values = [
    draft,
    open,
    sent,
    partiallyPaid,
    paid,
    overdue,
    cancelled,
    template,
  ];

  // Get a display name for the status
  static String getDisplayName(String status) {
    switch (status) {
      case draft:
        return 'Draft';
      case open:
        return 'Open';
      case sent:
        return 'Sent';
      case partiallyPaid:
        return 'Partially Paid';
      case paid:
        return 'Paid';
      case overdue:
        return 'Overdue';
      case cancelled:
        return 'Cancelled';
      case template:
        return 'Template';
      default:
        return 'Unknown';
    }
  }

  // Check if an invoice is considered active (not paid, cancelled, or template)
  static bool isActive(String status) {
    return status == draft ||
        status == open ||
        status == sent ||
        status == partiallyPaid ||
        status == overdue;
  }

  // Check if an invoice can be edited
  static bool canEdit(String status) {
    return status == draft || status == open || status == template;
  }

  // Check if an invoice can be marked as paid
  static bool canMarkAsPaid(String status) {
    return status == open ||
        status == sent ||
        status == partiallyPaid ||
        status == overdue;
  }

  // Get the next status when an invoice is sent
  static String getNextStatusWhenSent(String status) {
    if (status == draft || status == open) {
      return sent;
    }
    return status;
  }

  // Get the next status when a payment is recorded
  static String getNextStatusWhenPaymentRecorded(
    String status,
    double totalAmount,
    double amountPaid,
  ) {
    if (amountPaid >= totalAmount) {
      return paid;
    } else if (amountPaid > 0) {
      return partiallyPaid;
    }
    return status;
  }
}
