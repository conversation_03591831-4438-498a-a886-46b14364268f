/// IRS Schedule C expense categories
///
/// These categories match the IRS Schedule C form for business expenses
/// and are used for categorizing expenses for tax purposes.
class ExpenseCategory {
  // Schedule C expense categories
  static const String advertising = 'Advertising';
  static const String carAndTruck = 'Car and truck expenses';
  static const String commissions = 'Commissions and fees';
  static const String contractLabor = 'Contract labor';
  static const String depletion = 'Depletion';
  static const String depreciation =
      'Depreciation and section 179 expense deduction';
  static const String employeeBenefits = 'Employee benefit programs';
  static const String insurance = 'Insurance';
  static const String interestMortgage = 'Interest - Mortgage';
  static const String interestOther = 'Interest - Other';
  static const String legalAndProfessional = 'Legal and professional services';
  static const String officeExpense = 'Office expense';
  static const String pension = 'Pension and profit-sharing plans';
  static const String rentOrLeaseVehicles =
      'Rent or lease - Vehicles, machinery, and equipment';
  static const String rentOrLeaseOther =
      'Rent or lease - Other business property';
  static const String repairs = 'Repairs and maintenance';
  static const String supplies = 'Supplies';
  static const String taxes = 'Taxes and licenses';
  static const String travel = 'Travel';
  static const String meals = 'Deductible meals';
  static const String utilities = 'Utilities';
  static const String wages = 'Wages';
  static const String other = 'Other expenses';
  static const String energyEfficient =
      'Energy efficient commercial bldgs deduction';
  static const String homeOffice = 'Expenses for business use of your home';

  // List of all categories for iteration
  static const List<String> values = [
    advertising,
    carAndTruck,
    commissions,
    contractLabor,
    depletion,
    depreciation,
    employeeBenefits,
    insurance,
    interestMortgage,
    interestOther,
    legalAndProfessional,
    officeExpense,
    pension,
    rentOrLeaseVehicles,
    rentOrLeaseOther,
    repairs,
    supplies,
    taxes,
    travel,
    meals,
    utilities,
    wages,
    other,
    energyEfficient,
    homeOffice,
  ];

  final String id;
  final String name;
  final String description;
  final int usageCount; // Track how often this category is used

  const ExpenseCategory({
    required this.id,
    required this.name,
    required this.description,
    this.usageCount = 0,
  });

  // Factory constructor to create an ExpenseCategory from a JSON map
  factory ExpenseCategory.fromJson(Map<String, dynamic> json) {
    return ExpenseCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      usageCount: json['usage_count'] as int? ?? 0,
    );
  }

  // Convert an ExpenseCategory to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'usage_count': usageCount,
    };
  }

  // Create a copy of this ExpenseCategory with the given fields replaced with new values
  ExpenseCategory copyWith({
    String? id,
    String? name,
    String? description,
    int? usageCount,
  }) {
    return ExpenseCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      usageCount: usageCount ?? this.usageCount,
    );
  }
}
