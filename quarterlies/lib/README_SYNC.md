# Quarterlies Sync Implementation

## Overview

This document outlines the implementation of conflict resolution UI and background synchronization for the Quarterlies app. These features enable the app to work offline and handle data conflicts when synchronizing with the server.

## Key Components

### 1. Conflict Resolution Dialog

The `ConflictResolutionDialog` widget (`widgets/conflict_resolution_dialog.dart`) provides a reusable UI for resolving data conflicts between local and server versions. It:

- Shows both versions side-by-side
- Allows users to select which version to keep
- Works with different entity types (Invoice, Expense, TaxPayment)
- Provides a consistent UI across the app

### 2. Sync Manager

The `SyncManager` class (`services/sync_manager.dart`) handles background synchronization and conflict resolution for critical entities. It provides:

- Platform-specific background sync (WorkManager for Android, Background App Refresh for iOS)
- Manual sync operations
- Conflict detection and resolution
- Methods to show conflict resolution dialogs

### 3. Offline Search and Filter

The `OfflineSearchFilter` widget (`widgets/offline_search_filter.dart`) provides a reusable component for search and filtering that works with cached data when offline. It:

- Adapts UI based on connectivity status
- Shows offline indicators when appropriate
- Provides consistent search and filter UI

### 4. Sync Settings UI

The `SyncSettingsSection` widget (`screens/settings/sync_settings_section.dart`) provides UI for:

- Viewing current sync status
- Manually triggering synchronization with a 'Sync Now' button
- Configuring background sync options

## Implementation Details

### Conflict Resolution Flow

1. During sync, the app compares local and server versions of entities
2. If both versions have been modified, the entity is marked with `SyncStatus.conflict`
3. When a user views an entity with conflicts, they can trigger conflict resolution
4. The `ConflictResolutionDialog` shows both versions and lets the user choose
5. Based on the user's choice, either the local or server version is kept

### Background Synchronization

- **Android**: Uses WorkManager to schedule periodic sync tasks
- **iOS**: Uses Background App Refresh to sync when the app is in the background
- Sync frequency and conditions (Wi-Fi only, etc.) are configurable in settings

### Offline Support

- All data is cached locally in the device's database
- Search and filtering operations work with cached data when offline
- UI components show appropriate indicators when in offline mode
- Changes made offline are queued for sync when connectivity is restored

## Usage Examples

### Resolving an Invoice Conflict

```dart
// Show conflict resolution dialog for an invoice
final syncManager = SyncManager();
await syncManager.showInvoiceConflictDialog(context, invoiceId);
```

### Manually Triggering Sync

```dart
// Trigger manual sync
final syncManager = SyncManager();
await syncManager.syncData();
```

### Using Offline Search

```dart
// Create an offline-capable search component
OfflineSearchFilter(
  title: 'Search Expenses',
  searchHint: 'Search by description or amount',
  onSearch: (query) {
    // Filter local data based on query
  },
  filterCategories: ['Office Supplies', 'Travel', 'Meals'],
  onFilterChanged: (category) {
    // Filter local data based on category
  },
)
```

## Future Improvements

- Add more granular conflict resolution (field-by-field)
- Implement delta sync to reduce data transfer
- Add conflict resolution for additional entity types
- Enhance offline search with full-text indexing
- Add sync progress indicators and detailed sync history