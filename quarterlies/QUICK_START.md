# Quick Start Guide - Service Migration

Follow these steps to complete the migration from Google Maps API + EmailJS to Mapbox + Amazon SES.

## Step 1: Get Mapbox Access Token (5 minutes)

1. Go to https://www.mapbox.com/
2. Sign up for a free account
3. Navigate to your Account page
4. Click "Create a token"
5. Name it "Quarterlies App"
6. Ensure these scopes are selected:
   - ✅ `styles:read`
   - ✅ `fonts:read` 
   - ✅ `datasets:read`
   - ✅ `geocoding:read`
7. Copy the token (starts with `pk.`)

## Step 2: Update Flutter App (2 minutes)

Run this command with your token:
```bash
cd quarterlies
dart scripts/update_mapbox_token.dart pk.YOUR_ACTUAL_TOKEN_HERE
```

Or manually edit `lib/services/address_service.dart`:
```dart
// Line 8: Replace this
static const String _accessToken = 'YOUR_MAPBOX_ACCESS_TOKEN';

// With your actual token
static const String _accessToken = 'pk.YOUR_ACTUAL_TOKEN_HERE';
```

## Step 3: Set Up Amazon SES (10 minutes)

### 3.1 Create AWS Account
1. Go to https://aws.amazon.com/
2. Sign up for free account
3. Complete verification process

### 3.2 Configure SES
1. Go to AWS Console → Amazon SES
2. Click "Verify a new email address"
3. Enter your business email (e.g., `<EMAIL>`)
4. Check your email and click verification link

### 3.3 Create IAM User
1. Go to AWS Console → IAM
2. Click "Users" → "Add user"
3. Username: `quarterlies-ses-user`
4. Access type: ✅ Programmatic access
5. Attach policy: `AmazonSESFullAccess`
6. **Save the Access Key ID and Secret Access Key**

## Step 4: Configure Supabase (3 minutes)

1. Go to your Supabase project dashboard
2. Navigate to Settings → Edge Functions
3. Add these environment variables:

```
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_from_step_3
AWS_SECRET_ACCESS_KEY=your_secret_key_from_step_3
FROM_EMAIL=<EMAIL>
```

## Step 5: Deploy and Test (5 minutes)

### 5.1 Deploy Supabase Functions
```bash
cd quarterlies/supabase
supabase functions deploy
```

### 5.2 Test Address Autocomplete
1. Run your Flutter app
2. Go to any form with address input
3. Type "123 Main" and verify suggestions appear
4. ✅ Success: You see address suggestions from Mapbox

### 5.3 Test Email Functionality
1. Create a contract in your app
2. Send it for signing
3. Check that email is sent successfully
4. ✅ Success: Email is delivered via Amazon SES

## Troubleshooting

### Address Autocomplete Not Working
- **Check token**: Ensure Mapbox token is correctly set
- **Check network**: Look for API calls to `api.mapbox.com` in browser dev tools
- **Check console**: Look for error messages in app logs

### Emails Not Sending
- **Check SES verification**: Ensure your email is verified in AWS SES
- **Check credentials**: Verify AWS keys are correct in Supabase
- **Check logs**: Look at Supabase Edge Functions logs for errors

### Common Error Messages

**"Invalid access token"**
- Your Mapbox token is incorrect or expired
- Regenerate token with correct permissions

**"Email address not verified"**
- Your sender email isn't verified in AWS SES
- Complete email verification process

**"Access denied"**
- AWS IAM user doesn't have SES permissions
- Attach `AmazonSESFullAccess` policy

## Verification Checklist

- [ ] Mapbox token is set in `address_service.dart`
- [ ] Address autocomplete shows suggestions
- [ ] AWS SES is configured with verified email
- [ ] Supabase environment variables are set
- [ ] Email sending works from the app
- [ ] No console errors in browser/app
- [ ] Monthly costs are reduced

## Cost Monitoring

### Set Up Alerts (Recommended)
1. **Mapbox**: Set usage alert at 80,000 requests/month
2. **AWS**: Set billing alert at $10/month
3. **Supabase**: Monitor function invocations

### Expected Usage (Small Business)
- **Mapbox**: 1,000-10,000 requests/month (well within free tier)
- **SES**: 100-1,000 emails/month (~$0.10-1.00)
- **Total additional cost**: $0-5/month

## Success! 🎉

You've successfully migrated to more cost-effective and reliable services:

- ✅ **67% cost reduction** ($40-60/month → $25-30/month)
- ✅ **Better reliability** with Amazon SES
- ✅ **More generous free tiers** with Mapbox
- ✅ **Professional email delivery**

## Next Steps

1. **Monitor usage** for the first month
2. **Set up domain verification** in SES for better deliverability
3. **Consider custom domain** for professional email addresses
4. **Review and optimize** based on actual usage patterns

## Need Help?

- Check `ENVIRONMENT_SETUP.md` for detailed configuration
- Review `MIGRATION_SUMMARY.md` for technical details
- Contact support if you encounter issues
