# Environment Setup Guide

This document outlines the environment variables and configuration needed for the Quarterlies application.

## Required Environment Variables

### Mapbox Configuration (for Address Autocomplete)

Add the following to your environment:

```bash
# Replace with your actual Mapbox access token
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example_token_here
```

**To get a Mapbox access token:**
1. Sign up at https://www.mapbox.com/
2. Go to your Account page
3. Create a new access token with the following scopes:
   - `styles:read`
   - `fonts:read`
   - `datasets:read`
   - `geocoding:read`

### Amazon SES Configuration (for Email Services)

Add the following to your Supabase Edge Functions environment:

```bash
# AWS SES Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
FROM_EMAIL=<EMAIL>
```

**To set up Amazon SES:**
1. Sign up for AWS account at https://aws.amazon.com/
2. Go to Amazon SES console
3. Verify your domain or email address
4. Create IAM user with SES permissions:
   - `ses:SendEmail`
   - `ses:SendRawEmail`
5. Generate access keys for the IAM user

### Supabase Environment Variables

Set these in your Supabase project dashboard under Settings > Edge Functions:

```bash
# Existing Supabase variables (already configured)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# New variables for email service
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
FROM_EMAIL=<EMAIL>
```

## Flutter App Configuration

### Update AddressService

In `lib/services/address_service.dart`, replace the placeholder token:

```dart
// Replace this line:
static const String _accessToken = 'YOUR_MAPBOX_ACCESS_TOKEN';

// With your actual token:
static const String _accessToken = 'pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example_token_here';
```

**For production apps**, consider using:
- Flutter's `--dart-define` for build-time configuration
- A secure configuration service
- Environment-specific configuration files

## Cost Estimates

### Mapbox
- **Free tier**: 100,000 requests/month
- **Paid tier**: $0.50 per 1,000 requests after free tier
- **Estimated monthly cost**: $0-25 for typical small business usage

### Amazon SES
- **Free tier**: 62,000 emails/month (when sending from EC2)
- **Paid tier**: $0.10 per 1,000 emails
- **Estimated monthly cost**: $0-5 for typical small business usage

### Total Estimated Savings
- **Previous setup**: ~$40-60/month (Supabase + EmailJS + Google Maps)
- **New setup**: ~$25-30/month (Supabase + SES + Mapbox)
- **Monthly savings**: $15-30

## Security Best Practices

1. **Never commit API keys to version control**
2. **Use environment variables for all sensitive configuration**
3. **Rotate API keys regularly**
4. **Set up usage alerts to monitor costs**
5. **Use least-privilege IAM policies for AWS**

## Testing the Setup

### Test Mapbox Integration
1. Run the app and try address autocomplete
2. Check browser network tab for successful API calls to `api.mapbox.com`
3. Verify addresses are being suggested correctly

### Test Amazon SES Integration
1. Trigger a contract signing email from the app
2. Check Supabase Edge Functions logs for any errors
3. Verify email delivery to the recipient
4. Check AWS SES console for sending statistics

## Troubleshooting

### Mapbox Issues
- **No suggestions appearing**: Check API token and network connectivity
- **Rate limit errors**: Monitor usage in Mapbox dashboard
- **Invalid coordinates**: Verify the response parsing in `fromMapboxFeature`

### Amazon SES Issues
- **Email not sending**: Check AWS credentials and SES verification status
- **Emails going to spam**: Set up SPF, DKIM, and DMARC records
- **Rate limiting**: Check SES sending limits in AWS console

## Migration Checklist

- [ ] Sign up for Mapbox account and get access token
- [ ] Set up AWS account and configure SES
- [ ] Update environment variables in Supabase
- [ ] Update Mapbox token in Flutter app
- [ ] Test address autocomplete functionality
- [ ] Test email sending functionality
- [ ] Monitor usage and costs for first month
- [ ] Set up usage alerts and monitoring
