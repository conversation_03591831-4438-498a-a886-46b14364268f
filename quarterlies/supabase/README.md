# Supabase Database Schema for Quarterlies

## Overview

This directory contains the SQL migrations for setting up the Supabase database schema for the Quarterlies application. The schema includes tables for customers, jobs, estimates, expenses, time logs, payments, mileage, invoices, and tax payments.

## Schema Details

The database schema follows these design principles:

- All tables use UUID primary keys generated with `gen_random_uuid()`
- Each table has a `user_id` column linked to the Supabase `auth.users` table
- Foreign key constraints maintain data integrity between related tables
- Appropriate data types are used for each column (TEXT, DECIMAL, DATE, BOOLEAN, UUID, JSONB, etc.)
- All tables include `created_at` timestamps with default values
- Row Level Security (RLS) policies restrict users to accessing only their own data

## Tables

1. **customers** - Store customer information
2. **jobs** - Track jobs associated with customers
3. **estimates** - Store estimates for jobs
4. **expenses** - Track expenses related to jobs
5. **time_logs** - Record time spent on jobs
6. **invoices** - Generate invoices for jobs
7. **payments** - Track payments for invoices
8. **mileage** - Record mileage expenses
9. **tax_payments** - Track tax payments

## How to Apply Migrations

To apply these migrations to your Supabase project:

1. Set up a Supabase project at [https://supabase.com](https://supabase.com)
2. Install the Supabase CLI: `npm install -g supabase`
3. Link your project: `supabase link --project-ref your-project-ref`
4. Push the migrations: `supabase db push`

Alternatively, you can copy the SQL from the migration files and execute it directly in the Supabase SQL Editor in the web dashboard.

## Indexes

The schema includes indexes on foreign key columns to improve query performance.

## Row Level Security

Row Level Security is enabled on all tables with policies that restrict users to accessing only their own data using the `auth.uid()` function.