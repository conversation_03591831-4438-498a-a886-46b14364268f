-- Migration: Add display mode setting to user_settings table
-- Created: 2024-12-02
-- Description: Adds display_mode field to support Field Mode (default) and Office Mode

-- Add display_mode field to user_settings table
ALTER TABLE user_settings
ADD COLUMN IF NOT EXISTS display_mode TEXT NOT NULL DEFAULT 'field';

-- Add check constraint to ensure only valid values
ALTER TABLE user_settings
ADD CONSTRAINT check_display_mode 
CHECK (display_mode IN ('field', 'office'));

-- Add comment for documentation
COMMENT ON COLUMN user_settings.display_mode IS 'Display density mode: field (default, large touch targets) or office (compact, more details)';

-- Update existing user_settings records to have the default value
UPDATE user_settings
SET display_mode = 'field'
WHERE display_mode IS NULL;
