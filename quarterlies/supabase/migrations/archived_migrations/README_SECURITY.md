# Database Security Enhancements

## Overview

This document outlines the security enhancements implemented in the Quarterlies application database through the migration file `20240201000000_enhance_security.sql`. These changes follow best practices for Supabase Row Level Security (RLS) policies.

## Key Improvements

### 1. TO authenticated Clause

All policies now include the `TO authenticated` clause, which explicitly restricts access to authenticated users only. This prevents any access by unauthenticated users, significantly improving security.

### 2. SELECT for auth.uid()

The `auth.uid()` function is now wrapped in a SELECT statement:
```sql
(SELECT auth.uid()) = user_id
```

This allows the Postgres optimizer to cache the results, improving query performance when policies are evaluated.

### 3. Policy Separation

Policies are now separated by operation type (SELECT, INSERT, UPDATE, DELETE) for each table, providing more granular control over data access. This allows for different access rules for different operations if needed in the future.

### 4. WITH CHECK Clauses for INSERT Policies

All INSERT policies use proper WITH CHECK clauses instead of USING clauses:
```sql
CREATE POLICY "Users can insert their own customers" ON customers
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);
```

This ensures that users can only insert records associated with their own user ID, maintaining data integrity and security.

### 5. Indexing

Indexes on all `user_id` columns are verified and created if they don't exist, optimizing the performance of RLS policy evaluation:
```sql
CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);
```

## Implementation

The migration file:
1. Drops existing policies
2. Creates enhanced policies with the improvements listed above
3. Verifies indexes on all `user_id` columns

## How to Apply

To apply these migrations to your Supabase project:

1. Set up a Supabase project at [https://supabase.com](https://supabase.com)
2. Install the Supabase CLI: `npm install -g supabase`
3. Link your project: `supabase link --project-ref your-project-ref`
4. Push the migrations: `supabase db push`

Alternatively, you can copy the SQL from the migration files and execute it directly in the Supabase SQL Editor in the web dashboard.