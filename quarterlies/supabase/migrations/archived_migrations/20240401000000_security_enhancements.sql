-- Migration to implement comprehensive security enhancements

-- 1. Strengthen Password Policy
-- Note: Client-side validation will be implemented in the Flutter app
-- Server-side validation is implemented through database triggers

-- Create a trigger function to validate password complexity
CREATE OR REPLACE FUNCTION auth.validate_password_strength()
 R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Check for minimum length
  IF length(NEW.password) < 8 THEN
    RAISE EXCEPTION 'Password must be at least 8 characters long';
  END IF;
  
  -- Check for uppercase letters
  IF NEW.password !~ '[A-Z]' THEN
    RAISE EXCEPTION 'Password must contain at least one uppercase letter';
  END IF;
  
  -- Check for lowercase letters
  IF NEW.password !~ '[a-z]' THEN
    RAISE EXCEPTION 'Password must contain at least one lowercase letter';
  END IF;
  
  -- Check for numbers
  IF NEW.password !~ '[0-9]' THEN
    RAISE EXCEPTION 'Password must contain at least one number';
  END IF;
  
  -- Check for special characters
  IF NEW.password !~ '[!@#$%^&*(),.?":{}|<>]' THEN
    RAISE EXCEPTION 'Password must contain at least one special character';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to auth.users table
CREATE TRIGGER check_password_strength
  BEFORE INSERT OR UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION auth.validate_password_strength();

-- 2. Create audit logging infrastructure

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  action_type TEXT NOT NULL,
  table_name TEXT,
  record_id UUID,
  old_data JSONB,
  new_data JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create RLS policy for audit_logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only allow users to view their own audit logs
CREATE POLICY "Users can view their own audit logs" ON audit_logs
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Only allow insert, no updates or deletes
CREATE POLICY "System can insert audit logs" ON audit_logs
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- Create function to log financial transactions
CREATE OR REPLACE FUNCTION log_financial_transaction()
 RETURNS trigger AS $$
BEGIN
  INSERT INTO audit_logs (user_id, action_type, table_name, record_id, old_data, new_data, ip_address)
  VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    CASE
      WHEN TG_OP = 'DELETE' THEN OLD.id
      ELSE NEW.id
    END,
    CASE
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN to_jsonb(OLD)
      ELSE NULL
    END,
    CASE
      WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW)
      ELSE NULL
    END,
    current_setting('request.headers', true)::json->>'x-forwarded-for'
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to financial tables
CREATE TRIGGER log_payments_audit
  AFTER INSERT OR UPDATE OR DELETE ON payments
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_invoices_audit
  AFTER INSERT OR UPDATE OR DELETE ON invoices
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_expenses_audit
  AFTER INSERT OR UPDATE OR DELETE ON expenses
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_tax_payments_audit
  AFTER INSERT OR UPDATE OR DELETE ON tax_payments
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

-- 3. Create index on audit_logs for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_record ON audit_logs(table_name, record_id);