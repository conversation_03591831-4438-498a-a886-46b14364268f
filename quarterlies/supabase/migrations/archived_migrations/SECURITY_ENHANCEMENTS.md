# Quarterlies Security Enhancements

This document outlines comprehensive security improvements for the Quarterlies application to protect sensitive financial data and user accounts.

## 1. Strengthen Password Policy

### Implementation Details

```sql
-- Add to migrations file
-- Note: Client-side validation is implemented in the Flutter app
-- Server-side validation is implemented through database triggers

-- Create a trigger function to validate password complexity
CREATE OR REPLACE FUNCTION auth.validate_password_strength()
 RETURNS trigger AS $$
BEGIN
  -- Check for minimum length
  IF length(NEW.password) < 8 THEN
    RAISE EXCEPTION 'Password must be at least 8 characters long';
  END IF;
  
  -- Check for uppercase letters
  IF NEW.password !~ '[A-Z]' THEN
    RAISE EXCEPTION 'Password must contain at least one uppercase letter';
  END IF;
  
  -- Check for lowercase letters
  IF NEW.password !~ '[a-z]' THEN
    RAISE EXCEPTION 'Password must contain at least one lowercase letter';
  END IF;
  
  -- Check for numbers
  IF NEW.password !~ '[0-9]' THEN
    RAISE EXCEPTION 'Password must contain at least one number';
  END IF;
  
  -- Check for special characters
  IF NEW.password !~ '[!@#$%^&*(),.?":{}|<>]' THEN
    RAISE EXCEPTION 'Password must contain at least one special character';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to auth.users table
CREATE TRIGGER check_password_strength
  BEFORE INSERT OR UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION auth.validate_password_strength();
```

### Client-Side Implementation

```dart
// Add to auth_service.dart
bool isPasswordStrong(String password) {
  // Minimum 8 characters
  if (password.length < 8) return false;
  
  // Contains uppercase
  if (!RegExp(r'[A-Z]').hasMatch(password)) return false;
  
  // Contains lowercase
  if (!RegExp(r'[a-z]').hasMatch(password)) return false;
  
  // Contains number
  if (!RegExp(r'[0-9]').hasMatch(password)) return false;
  
  // Contains special character
  if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;
  
  return true;
}
```

## 2. Implement Multi-Factor Authentication

### Implementation Details

```dart
// Add to auth_service.dart
Future<void> enableMFA() async {
  try {
    // Generate TOTP secret
    final response = await _supabaseClient.auth.mfa.enroll();
    final factorId = response.id;
    final totpUri = response.totp.uri;
    
    // Display QR code to user (using a QR code package)
    // ...
    
    // Verify setup with user-provided code
    await _supabaseClient.auth.mfa.challenge(factorId: factorId);
    await _supabaseClient.auth.mfa.verify({
      'factorId': factorId,
      'challengeId': challengeId,
      'code': userProvidedCode,
    });
  } catch (e) {
    rethrow;
  }
}

// Add MFA verification to sign in process
Future<AuthResponse> signInWithMFA({
  required String email,
  required String password,
  required String totpCode,
}) async {
  try {
    // First sign in with password
    final response = await _supabaseClient.auth.signInWithPassword(
      email: email,
      password: password,
    );
    
    // Then verify with TOTP
    if (response.session != null) {
      final mfaResponse = await _supabaseClient.auth.mfa.verify({
        'factorId': factorId, // Retrieved from user's factors
        'challengeId': challengeId, // From challenge response
        'code': totpCode,
      });
      return mfaResponse;
    }
    
    return response;
  } catch (e) {
    rethrow;
  }
}
```

## 3. Enhance Data Encryption

### Implementation Details

```dart
// Create encryption_service.dart
import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class EncryptionService {
  late encrypt.Encrypter _encrypter;
  late encrypt.IV _iv;
  final FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  
  // Initialize encryption with a key stored securely
  Future<void> initialize() async {
    // Get or generate encryption key
    String? storedKey = await _secureStorage.read(key: 'encryption_key');
    if (storedKey == null) {
      final key = encrypt.Key.fromSecureRandom(32);
      await _secureStorage.write(key: 'encryption_key', value: base64Encode(key.bytes));
      storedKey = base64Encode(key.bytes);
    }
    
    // Create encrypter with AES
    final key = encrypt.Key.fromBase64(storedKey);
    _iv = encrypt.IV.fromLength(16); // Generate a secure IV
    _encrypter = encrypt.Encrypter(encrypt.AES(key));
  }
  
  // Encrypt sensitive data
  String encryptData(String plainText) {
    final encrypted = _encrypter.encrypt(plainText, iv: _iv);
    return encrypted.base64;
  }
  
  // Decrypt data
  String decryptData(String encryptedText) {
    final encrypted = encrypt.Encrypted.fromBase64(encryptedText);
    return _encrypter.decrypt(encrypted, iv: _iv);
  }
}
```

## 4. Improve Input Validation

### Implementation Details

```dart
// Create validation_service.dart
class ValidationService {
  // Validate currency amount
  bool isValidAmount(String amount) {
    return RegExp(r'^\$?\d+(\.\d{1,2})?$').hasMatch(amount);
  }
  
  // Validate date format
  bool isValidDate(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      return true;
    } catch (e) {
      return false;
    }
  }
  
  // Validate date range
  bool isValidDateRange(String startDate, String endDate) {
    try {
      final start = DateTime.parse(startDate);
      final end = DateTime.parse(endDate);
      return end.isAfter(start);
    } catch (e) {
      return false;
    }
  }
  
  // Validate payment information
  bool isValidCardNumber(String cardNumber) {
    // Remove spaces and dashes
    cardNumber = cardNumber.replaceAll(RegExp(r'[\s-]'), '');
    
    // Check if only digits
    if (!RegExp(r'^\d+$').hasMatch(cardNumber)) return false;
    
    // Luhn algorithm for card validation
    int sum = 0;
    bool alternate = false;
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int n = int.parse(cardNumber[i]);
      if (alternate) {
        n *= 2;
        if (n > 9) n = n - 9;
      }
      sum += n;
      alternate = !alternate;
    }
    return sum % 10 == 0;
  }
  
  // Validate CVV
  bool isValidCVV(String cvv) {
    return RegExp(r'^\d{3,4}$').hasMatch(cvv);
  }
  
  // Validate expiry date
  bool isValidExpiryDate(String expiryDate) {
    // Format MM/YY
    if (!RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$').hasMatch(expiryDate)) return false;
    
    final parts = expiryDate.split('/');
    final month = int.parse(parts[0]);
    final year = int.parse('20${parts[1]}');
    
    final now = DateTime.now();
    final expiryDateTime = DateTime(year, month + 1, 0);
    
    return expiryDateTime.isAfter(now);
  }
}
```

## 5. Add Data Sanitization

### Implementation Details

```dart
// Create sanitization_service.dart
import 'package:html_unescape/html_unescape.dart';

class SanitizationService {
  final HtmlUnescape _htmlUnescape = HtmlUnescape();
  
  // Sanitize text input to prevent XSS
  String sanitizeInput(String input) {
    // Remove HTML tags
    String sanitized = input.replaceAll(RegExp(r'<[^>]*>'), '');
    
    // Decode HTML entities
    sanitized = _htmlUnescape.convert(sanitized);
    
    // Remove potentially dangerous characters
    sanitized = sanitized.replaceAll(RegExp(r'[\r\n\t]'), ' ');
    
    return sanitized.trim();
  }
  
  // Sanitize SQL input to prevent SQL injection
  String sanitizeSqlInput(String input) {
    // Escape single quotes
    String sanitized = input.replaceAll("'", "''");
    
    // Remove SQL comments
    sanitized = sanitized.replaceAll(RegExp(r'--.*'), '');
    sanitized = sanitized.replaceAll(RegExp(r'/\*.*?\*/', dotAll: true), '');
    
    return sanitized.trim();
  }
  
  // Sanitize filename to prevent path traversal
  String sanitizeFilename(String filename) {
    // Remove path traversal characters
    String sanitized = filename.replaceAll(RegExp(r'[/\\]'), '');
    
    // Remove control characters
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');
    
    return sanitized.trim();
  }
}
```

## 6. Implement Certificate Pinning

### Implementation Details

```dart
// Create network_security_service.dart
import 'package:dio/dio.dart';
import 'package:dio_pinning/dio_pinning.dart';

class NetworkSecurityService {
  late Dio _dio;
  
  NetworkSecurityService() {
    _dio = Dio();
    _configureCertificatePinning();
  }
  
  void _configureCertificatePinning() {
    // Define the certificate hashes for your domains
    final pins = {
      'supabase.co': [
        'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Replace with actual certificate hash
        'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=', // Backup certificate hash
      ],
      'api.stripe.com': [
        'sha256/CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=', // Replace with actual certificate hash
      ],
    };
    
    // Apply certificate pinning to Dio client
    _dio.httpClientAdapter = PinningHttpClientAdapter(
      allowedSHAFingerprints: pins,
    );
  }
  
  // Use this Dio instance for all network requests
  Dio get client => _dio;
}
```

## 7. Add Audit Logging

### Database Implementation

```sql
-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  action_type TEXT NOT NULL,
  table_name TEXT,
  record_id UUID,
  old_data JSONB,
  new_data JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create RLS policy for audit_logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Only allow users to view their own audit logs
CREATE POLICY "Users can view their own audit logs" ON audit_logs
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Only allow insert, no updates or deletes
CREATE POLICY "System can insert audit logs" ON audit_logs
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- Create function to log financial transactions
CREATE OR REPLACE FUNCTION log_financial_transaction()
 RETURNS trigger AS $$
BEGIN
  INSERT INTO audit_logs (user_id, action_type, table_name, record_id, old_data, new_data, ip_address)
  VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    CASE
      WHEN TG_OP = 'DELETE' THEN OLD.id
      ELSE NEW.id
    END,
    CASE
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN to_jsonb(OLD)
      ELSE NULL
    END,
    CASE
      WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW)
      ELSE NULL
    END,
    current_setting('request.headers', true)::json->>'x-forwarded-for'
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to financial tables
CREATE TRIGGER log_payments_audit
  AFTER INSERT OR UPDATE OR DELETE ON payments
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_invoices_audit
  AFTER INSERT OR UPDATE OR DELETE ON invoices
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_expenses_audit
  AFTER INSERT OR UPDATE OR DELETE ON expenses
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();

CREATE TRIGGER log_tax_payments_audit
  AFTER INSERT OR UPDATE OR DELETE ON tax_payments
  FOR EACH ROW EXECUTE FUNCTION log_financial_transaction();
```

### Client Implementation

```dart
// Create audit_service.dart
import 'package:supabase_flutter/supabase_flutter.dart';

class AuditService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;
  
  // Log sensitive data access
  Future<void> logDataAccess({
    required String tableName,
    required String recordId,
    required String action,
  }) async {
    try {
      await _supabaseClient.from('audit_logs').insert({
        'action_type': action,
        'table_name': tableName,
        'record_id': recordId,
      });
    } catch (e) {
      // Log locally if server logging fails
      print('Failed to log audit event: $e');
    }
  }
  
  // Get user's audit history
  Future<List<Map<String, dynamic>>> getUserAuditHistory() async {
    try {
      final response = await _supabaseClient
          .from('audit_logs')
          .select()
          .order('created_at', ascending: false);
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
}
```

## Implementation Plan

1. **Database Migrations**:
   - Create a new migration file `20240401000000_security_enhancements.sql` with the SQL changes
   - Apply migrations using Supabase CLI

2. **Client-Side Implementation**:
   - Update `auth_service.dart` with password validation and MFA support
   - Create new service files for encryption, validation, sanitization, network security, and audit logging
   - Integrate these services into the application workflow

3. **Testing**:
   - Test password policy enforcement
   - Verify MFA enrollment and verification
   - Confirm data encryption and decryption
   - Validate input validation and sanitization
   - Test certificate pinning against invalid certificates
   - Verify audit logging for all financial transactions

4. **Documentation**:
   - Update API documentation with new security requirements
   - Create user guides for MFA setup
   - Document security features for compliance purposes

## Security Best Practices

- **Never store encryption keys in code** - use secure storage
- **Implement rate limiting** for authentication attempts
- **Regularly rotate encryption keys** and certificates
- **Monitor audit logs** for suspicious activity
- **Keep dependencies updated** to patch security vulnerabilities
- **Perform regular security audits** of the codebase
- **Implement session timeouts** for inactive users
- **Use HTTPS for all communications** with backend services