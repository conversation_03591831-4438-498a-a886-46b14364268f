-- Migration: Create user_profiles and user_settings tables
-- Created: 2024-12-20
-- Description: Creates user_profiles table for personal information and user_settings table for app preferences

-- Create user_profiles table for personal information
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  country TEXT,
  profile_photo_url TEXT,
  signature_image_url TEXT,
  is_onboarding_complete BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create user_settings table for app preferences and business settings
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  default_live_job_cost_sync BOOLEAN NOT NULL DEFAULT false,
  sync_expenses BOOLEAN NOT NULL DEFAULT false,
  sync_mileage BOOLEAN NOT NULL DEFAULT false,
  sync_labor_costs BOOLEAN NOT NULL DEFAULT false,
  sync_estimate_items BOOLEAN NOT NULL DEFAULT true,
  default_invoice_due_days INTEGER NOT NULL DEFAULT 30,
  enable_due_date_notifications BOOLEAN NOT NULL DEFAULT true,
  due_date_notification_days INTEGER NOT NULL DEFAULT 3,
  enable_mileage_tracking BOOLEAN NOT NULL DEFAULT false,
  mileage_idle_timeout_minutes INTEGER NOT NULL DEFAULT 5,
  enable_voice_input BOOLEAN NOT NULL DEFAULT true,
  enable_offline_mode BOOLEAN NOT NULL DEFAULT true,
  wifi_only_sync BOOLEAN NOT NULL DEFAULT true,
  business_name TEXT,
  business_address TEXT,
  business_phone TEXT,
  business_email TEXT,
  business_logo TEXT,
  default_invoice_notes TEXT,
  default_invoice_terms TEXT,
  show_mileage_as_summary BOOLEAN NOT NULL DEFAULT true,
  show_hours_as_individual BOOLEAN NOT NULL DEFAULT true,
  automatic_brightness_detection BOOLEAN NOT NULL DEFAULT true,
  dynamic_color_adjustment BOOLEAN NOT NULL DEFAULT true,
  enhanced_contrast_mode BOOLEAN NOT NULL DEFAULT true,
  display_mode TEXT NOT NULL DEFAULT 'field',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Add check constraint for display_mode
ALTER TABLE user_settings
ADD CONSTRAINT check_display_mode
CHECK (display_mode IN ('field', 'office'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(is_onboarding_complete);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own profile" ON user_profiles
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- Create RLS policies for user_settings
CREATE POLICY "Users can view their own settings" ON user_settings
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" ON user_settings
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" ON user_settings
  FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings" ON user_settings
  FOR DELETE TO authenticated
  USING (auth.uid() = user_id);

-- Add comments for documentation
COMMENT ON TABLE user_profiles IS 'Stores personal information for users';
COMMENT ON TABLE user_settings IS 'Stores app preferences and business settings for users';
COMMENT ON COLUMN user_profiles.is_onboarding_complete IS 'Whether the user has completed the onboarding flow';
COMMENT ON COLUMN user_settings.display_mode IS 'Display density mode: field (default, large touch targets) or office (compact, more details)';
