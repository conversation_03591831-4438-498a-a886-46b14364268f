-- Migration to add email_logs table for tracking email sending

-- 1. Create email_logs table for tracking email sending
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email_type TEXT NOT NULL, -- 'signing_request', 'signed_notification', 'signed_customer'
  recipient TEXT NOT NULL,
  subject TEXT NOT NULL,
  document_type TEXT, -- 'estimate' or 'contract'
  document_id TEXT,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Enable RLS on email_logs
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- 3. Create policy for email_logs
CREATE POLICY "Users can only access their own email logs" ON email_logs
  FOR ALL TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);
