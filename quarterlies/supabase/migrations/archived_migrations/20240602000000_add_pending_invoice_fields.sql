-- Add pending_invoice_id and pending_invoice_number columns to time_logs table
ALTER TABLE time_logs
ADD COLUMN pending_invoice_id UUID REFERENCES invoices(id) ON DELETE SET NULL,
ADD COLUMN pending_invoice_number TEXT;

-- Add pending_invoice_id and pending_invoice_number columns to expenses table
ALTER TABLE expenses
ADD COLUMN pending_invoice_id UUID REFERENCES invoices(id) ON DELETE SET NULL,
ADD COLUMN pending_invoice_number TEXT;

-- Create indexes for faster lookups
CREATE INDEX idx_time_logs_pending_invoice_id ON time_logs(pending_invoice_id);
CREATE INDEX idx_expenses_pending_invoice_id ON expenses(pending_invoice_id);
