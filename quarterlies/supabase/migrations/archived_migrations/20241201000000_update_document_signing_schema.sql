-- Update document_signing_requests table to support separate customer and contractor document URLs

-- Add new columns for separate document URLs
ALTER TABLE document_signing_requests 
ADD COLUMN IF NOT EXISTS signed_document_customer_url TEXT,
ADD COLUMN IF NOT EXISTS signed_document_contractor_url TEXT,
ADD COLUMN IF NOT EXISTS certification_url TEXT,
ADD COLUMN IF NOT EXISTS job_id TEXT,
ADD COLUMN IF NOT EXISTS job_title TEXT,
ADD COLUMN IF NOT EXISTS job_address TEXT,
ADD COLUMN IF NOT EXISTS job_city TEXT,
ADD COLUMN IF NOT EXISTS job_state TEXT,
ADD COLUMN IF NOT EXISTS job_zip_code TEXT;

-- Update existing records to populate the new customer URL field from the legacy field
UPDATE document_signing_requests 
SET signed_document_customer_url = signed_document_url 
WHERE signed_document_url IS NOT NULL 
AND signed_document_customer_url IS NULL;

-- Add comments to document the new fields
COMMENT ON COLUMN document_signing_requests.signed_document_customer_url IS 'URL for the signed document sent to the customer (without certification)';
COMMENT ON COLUMN document_signing_requests.signed_document_contractor_url IS 'URL for the signed document with certification merged for the contractor';
COMMENT ON COLUMN document_signing_requests.certification_url IS 'URL for the standalone certification document';
COMMENT ON COLUMN document_signing_requests.job_id IS 'ID of the associated job';
COMMENT ON COLUMN document_signing_requests.job_title IS 'Title of the associated job';
COMMENT ON COLUMN document_signing_requests.job_address IS 'Address of the associated job';
COMMENT ON COLUMN document_signing_requests.job_city IS 'City of the associated job';
COMMENT ON COLUMN document_signing_requests.job_state IS 'State of the associated job';
COMMENT ON COLUMN document_signing_requests.job_zip_code IS 'ZIP code of the associated job';
