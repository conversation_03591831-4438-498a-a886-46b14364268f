-- Add estimated expenses budget field to jobs table
-- This field tracks the estimated costs/expenses for completing the job
-- This is separate from estimated_price which tracks expected revenue from customer

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS estimated_expenses_budget DECIMAL;

-- Add comment for the new field
COMMENT ON COLUMN jobs.estimated_expenses_budget IS 'Estimated budget for expenses and costs to complete the job (separate from estimated_price which is expected revenue)';

-- Update existing jobs to have null budget (users can set this manually)
-- We don't set a default value as budget should be explicitly set by users
