-- Migration: Add glare resistance settings to user_settings table
-- Created: 2024-12-01
-- Description: Adds automatic brightness detection, dynamic color adjustment, and enhanced contrast mode settings

-- Add glare resistance fields to user_settings table
ALTER TABLE user_settings
ADD COLUMN IF NOT EXISTS automatic_brightness_detection BOOLEAN NOT NULL DEFAULT true;

ALTER TABLE user_settings
ADD COLUMN IF NOT EXISTS dynamic_color_adjustment BOOLEAN NOT NULL DEFAULT true;

ALTER TABLE user_settings
ADD COLUMN IF NOT EXISTS enhanced_contrast_mode BOOLEAN NOT NULL DEFAULT true;

-- Add comments for documentation
COMMENT ON COLUMN user_settings.automatic_brightness_detection IS 'Whether to use automatic brightness detection (defaults to device setting)';
COMMENT ON COLUMN user_settings.dynamic_color_adjustment IS 'Whether to adjust colors based on ambient light conditions (default on)';
COMMENT ON COLUMN user_settings.enhanced_contrast_mode IS 'Whether to use enhanced contrast mode for better outdoor visibility (default on)';

-- Update existing user_settings records to have the new default values
UPDATE user_settings
SET
  automatic_brightness_detection = true,
  dynamic_color_adjustment = true,
  enhanced_contrast_mode = true
WHERE
  automatic_brightness_detection IS NULL
  OR dynamic_color_adjustment IS NULL
  OR enhanced_contrast_mode IS NULL;

-- Add missing job fields for sync settings
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS estimated_price DECIMAL;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS actual_income DECIMAL DEFAULT 0.0;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS actual_expenses DECIMAL DEFAULT 0.0;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS actual_labor_cost DECIMAL DEFAULT 0.0;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS live_cost_sync_enabled BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS sync_expenses BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS sync_mileage BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS sync_labor_costs BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS sync_estimate_items BOOLEAN NOT NULL DEFAULT true;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS summarize_mileage BOOLEAN NOT NULL DEFAULT true;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS summarize_hours BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS default_invoice_due_days INTEGER;

ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS voice_note_url TEXT;

-- Add comments for job fields
COMMENT ON COLUMN jobs.estimated_price IS 'Estimated price for the job';
COMMENT ON COLUMN jobs.actual_income IS 'Actual income received for the job';
COMMENT ON COLUMN jobs.actual_expenses IS 'Actual expenses incurred for the job';
COMMENT ON COLUMN jobs.actual_labor_cost IS 'Actual labor cost for the job';
COMMENT ON COLUMN jobs.live_cost_sync_enabled IS 'Legacy field for backward compatibility';
COMMENT ON COLUMN jobs.sync_expenses IS 'Whether to automatically include job expenses in new invoices';
COMMENT ON COLUMN jobs.sync_mileage IS 'Whether to automatically include mileage entries in new invoices';
COMMENT ON COLUMN jobs.sync_labor_costs IS 'Whether to automatically include labor costs in new invoices';
COMMENT ON COLUMN jobs.sync_estimate_items IS 'Whether to automatically include estimate line items in new invoices';
COMMENT ON COLUMN jobs.summarize_mileage IS 'Whether to show mileage as summary or individual entries in invoices';
COMMENT ON COLUMN jobs.summarize_hours IS 'Whether to show hours as summary or individual entries in invoices';
COMMENT ON COLUMN jobs.default_invoice_due_days IS 'Job-specific default due days for invoices';
COMMENT ON COLUMN jobs.voice_note_url IS 'URL to the recorded voice note for the job';
