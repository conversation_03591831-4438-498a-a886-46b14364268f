-- Migration to add missing overhead expense fields to expenses table
-- This migration ensures compatibility with existing databases

-- 1. Add is_overhead column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expenses' AND column_name = 'is_overhead'
    ) THEN
        ALTER TABLE expenses ADD COLUMN is_overhead BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- 2. Add receipt_photo_url column if it doesn't exist (different from receipt_url)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expenses' AND column_name = 'receipt_photo_url'
    ) THEN
        ALTER TABLE expenses ADD COLUMN receipt_photo_url TEXT;
    END IF;
END $$;

-- 3. Add tags column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expenses' AND column_name = 'tags'
    ) THEN
        ALTER TABLE expenses ADD COLUMN tags TEXT;
    END IF;
END $$;

-- 4. Add voice_note_url column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'expenses' AND column_name = 'voice_note_url'
    ) THEN
        ALTER TABLE expenses ADD COLUMN voice_note_url TEXT;
    END IF;
END $$;

-- 5. Create index for overhead expenses for better query performance
CREATE INDEX IF NOT EXISTS idx_expenses_is_overhead ON expenses(is_overhead);

-- 6. Create index for overhead expenses by user for better query performance
CREATE INDEX IF NOT EXISTS idx_expenses_user_overhead ON expenses(user_id, is_overhead);

-- 7. Update any existing expenses that have null job_id to be overhead expenses
-- This is a safe assumption since overhead expenses should not be tied to jobs
UPDATE expenses 
SET is_overhead = TRUE 
WHERE job_id IS NULL AND is_overhead = FALSE;

-- 8. Add a check constraint to ensure overhead expenses don't have job_id
ALTER TABLE expenses 
ADD CONSTRAINT check_overhead_no_job 
CHECK (
    (is_overhead = TRUE AND job_id IS NULL) OR 
    (is_overhead = FALSE)
);
