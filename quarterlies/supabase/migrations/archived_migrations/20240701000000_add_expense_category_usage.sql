-- Migration to add expense category usage tracking

-- 1. Create a table to track expense category usage
CREATE TABLE IF NOT EXISTS expense_category_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  category TEXT NOT NULL,
  usage_count INTEGER NOT NULL DEFAULT 1,
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, category)
);

-- 2. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_user_id ON expense_category_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_category ON expense_category_usage(category);
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_count ON expense_category_usage(usage_count);

-- 3. Enable Row Level Security (RLS)
ALTER TABLE expense_category_usage ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies
CREATE POLICY "Users can view their own expense category usage" ON expense_category_usage
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own expense category usage" ON expense_category_usage
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own expense category usage" ON expense_category_usage
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- 5. Create a function to increment or insert expense category usage
CREATE OR REPLACE FUNCTION increment_expense_category_usage(category_name TEXT)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Try to update existing record
  UPDATE expense_category_usage
  SET 
    usage_count = usage_count + 1,
    last_used = NOW(),
    updated_at = NOW()
  WHERE 
    user_id = auth.uid() AND 
    category = category_name
  RETURNING usage_count INTO current_count;
  
  -- If no record was updated (no rows affected), insert a new one
  IF current_count IS NULL THEN
    INSERT INTO expense_category_usage (user_id, category, usage_count)
    VALUES (auth.uid(), category_name, 1)
    RETURNING usage_count INTO current_count;
  END IF;
  
  RETURN current_count;
END;
$$;

-- 6. Create a function to get expense categories sorted by usage
CREATE OR REPLACE FUNCTION get_expense_categories_by_usage()
RETURNS TABLE (
  category TEXT,
  usage_count INTEGER,
  last_used TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ecu.category,
    ecu.usage_count,
    ecu.last_used
  FROM 
    expense_category_usage ecu
  WHERE 
    ecu.user_id = auth.uid()
  ORDER BY 
    ecu.usage_count DESC,
    ecu.last_used DESC;
END;
$$;
