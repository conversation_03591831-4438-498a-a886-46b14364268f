-- Migration to set up storage bucket for receipt photos with security policies

-- 1. Create the receipts storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('receipts', 'receipts', false)
ON CONFLICT (id) DO NOTHING;

-- 2. Create storage policies for the receipts bucket

-- Allow users to view their own receipt files
-- This policy checks that the authenticated user ID matches the user_id in the file path
-- File path pattern: user_id/filename.jpg
CREATE POLICY "Users can view their own receipts" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to insert their own receipt files
CREATE POLICY "Users can upload their own receipts" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to update their own receipt files
CREATE POLICY "Users can update their own receipts" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to delete their own receipt files
CREATE POLICY "Users can delete their own receipts" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- 3. Create an index on storage.objects to optimize policy evaluation
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_id_name ON storage.objects(bucket_id, name);