-- Migration to update expense category usage tracking functions

-- 1. Update the function to increment user expense category usage
CREATE OR REPLACE FUNCTION increment_user_expense_category_usage(category_name TEXT, user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Try to update existing record
  UPDATE expense_category_usage
  SET 
    usage_count = usage_count + 1,
    last_used = NOW(),
    updated_at = NOW()
  WHERE 
    user_id = user_id AND 
    category = category_name
  RETURNING usage_count INTO current_count;
  
  -- If no record was updated (no rows affected), insert a new one
  IF current_count IS NULL THEN
    INSERT INTO expense_category_usage (user_id, category, usage_count)
    VALUES (user_id, category_name, 1)
    RETURNING usage_count INTO current_count;
  END IF;
  
  RETURN current_count;
END;
$$;

-- 2. Update the function to get user expense categories by usage
CREATE OR REPLACE FUNCTION get_user_expense_categories_by_usage(user_id UUID)
RETURNS TABLE (
  category TEXT,
  count INTEGER,
  last_used TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ecu.category,
    ecu.usage_count,
    ecu.last_used
  FROM 
    expense_category_usage ecu
  WHERE 
    ecu.user_id = user_id
  ORDER BY 
    ecu.usage_count DESC,
    ecu.last_used DESC;
END;
$$;

-- 3. Create a function to reset a user's expense category usage counts
CREATE OR REPLACE FUNCTION reset_user_expense_category_usage(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM expense_category_usage
  WHERE user_id = user_id;
END;
$$;

-- 4. Create a function to get the most frequently used expense category for a user
CREATE OR REPLACE FUNCTION get_user_most_used_expense_category(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  most_used_category TEXT;
BEGIN
  SELECT category INTO most_used_category
  FROM expense_category_usage
  WHERE user_id = user_id
  ORDER BY usage_count DESC, last_used DESC
  LIMIT 1;
  
  RETURN most_used_category;
END;
$$;
