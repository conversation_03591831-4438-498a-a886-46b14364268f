-- Migration to set up document signing functionality

-- 1. Create a storage bucket for documents if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', false)
ON CONFLICT (id) DO NOTHING;

-- 2. Create storage policies for the documents bucket

-- Allow users to view their own document files
CREATE POLICY "Users can view their own documents" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to insert their own document files
CREATE POLICY "Users can upload their own documents" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to update their own document files
CREATE POLICY "Users can update their own documents" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Allow users to delete their own document files
CREATE POLICY "Users can delete their own documents" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- 3. Create a table for document signing requests
CREATE TABLE IF NOT EXISTS document_signing_requests (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL CHECK (document_type IN ('estimate', 'contract')),
  document_id TEXT NOT NULL,
  customer_id TEXT NOT NULL,
  customer_email TEXT NOT NULL,
  customer_name TEXT NOT NULL,
  document_url TEXT NOT NULL,
  signed_document_url TEXT,
  signed_document_customer_url TEXT, -- URL for customer version (without certification)
  signed_document_contractor_url TEXT, -- URL for contractor version (with certification)
  certification_url TEXT, -- URL for the certification document
  signature_data JSONB,
  status TEXT NOT NULL CHECK (status IN ('pending', 'viewed', 'signed', 'expired', 'cancelled')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  signed_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),
  job_id TEXT,
  job_title TEXT,
  job_address TEXT,
  job_city TEXT,
  job_state TEXT,
  job_zip_code TEXT
);

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_signing_requests_user_id ON document_signing_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_document_signing_requests_document_id ON document_signing_requests(document_id);
CREATE INDEX IF NOT EXISTS idx_document_signing_requests_status ON document_signing_requests(status);

-- 5. Set up Row Level Security (RLS) for the document_signing_requests table
ALTER TABLE document_signing_requests ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies for document_signing_requests

-- Users can view their own signing requests
CREATE POLICY "Users can view their own signing requests" ON document_signing_requests
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

-- Users can insert their own signing requests
CREATE POLICY "Users can insert their own signing requests" ON document_signing_requests
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Users can update their own signing requests
CREATE POLICY "Users can update their own signing requests" ON document_signing_requests
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

-- Users can delete their own signing requests
CREATE POLICY "Users can delete their own signing requests" ON document_signing_requests
  FOR DELETE TO authenticated
  USING (user_id = auth.uid());

-- 7. Create a function to handle document signing
CREATE OR REPLACE FUNCTION handle_document_signing(
  signing_id UUID,
  signature_data JSONB
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  request_record document_signing_requests;
  result JSONB;
BEGIN
  -- Get the signing request
  SELECT * INTO request_record
  FROM document_signing_requests
  WHERE id = signing_id;

  -- Check if the request exists
  IF request_record IS NULL THEN
    RETURN jsonb_build_object('success', false, 'message', 'Signing request not found');
  END IF;

  -- Check if the request is already signed
  IF request_record.status = 'signed' THEN
    RETURN jsonb_build_object('success', false, 'message', 'Document already signed');
  END IF;

  -- Check if the request is expired
  IF request_record.expires_at < NOW() THEN
    RETURN jsonb_build_object('success', false, 'message', 'Signing request has expired');
  END IF;

  -- Update the signing request
  UPDATE document_signing_requests
  SET
    status = 'signed',
    signature_data = handle_document_signing.signature_data,
    signed_at = NOW(),
    updated_at = NOW()
  WHERE id = signing_id;

  -- Return success
  RETURN jsonb_build_object('success', true, 'message', 'Document signed successfully');
END;
$$;
