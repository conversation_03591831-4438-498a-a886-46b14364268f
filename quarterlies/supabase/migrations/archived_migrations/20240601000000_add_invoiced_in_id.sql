-- Add invoiced_in_id column to time_logs table
ALTER TABLE time_logs
ADD COLUMN invoiced_in_id UUID REFERENCES invoices(id) ON DELETE SET NULL;

-- Add invoiced_in_id column to expenses table
ALTER TABLE expenses
ADD COLUMN invoiced_in_id UUID REFERENCES invoices(id) ON DELETE SET NULL;

-- Create an index for faster lookups
CREATE INDEX idx_time_logs_invoiced_in_id ON time_logs(invoiced_in_id);
CREATE INDEX idx_expenses_invoiced_in_id ON expenses(invoiced_in_id);
