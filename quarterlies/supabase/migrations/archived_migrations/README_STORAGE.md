# Supabase Storage Configuration

## Overview

This document outlines the storage configuration implemented in the Quarterlies application through the migration file `20240301000000_storage_receipts.sql`. These changes follow best practices for Supabase Storage security.

## Storage Bucket: receipts

A private storage bucket named `receipts` has been created to store receipt photos uploaded by users. The bucket is configured with the following properties:

- **ID**: receipts
- **Name**: receipts
- **Public**: false (private bucket, requiring authentication to access)

## Security Policies

### Path-Based Security

The storage policies implement path-based security using the following pattern:

```
user_id/filename.jpg
```

Where:
- `user_id` is the authenticated user's ID
- `filename.jpg` is the name of the uploaded file

Note: The bucket name (`receipts`) is specified when accessing the storage bucket, not in the file path.

### Policy Implementation

All policies use the `storage.foldername(name)` function to parse the file path and extract the user ID component. This ensures users can only access files within their own directory.

```sql
auth.uid()::text = (storage.foldername(name))[1]
```

This expression checks that the authenticated user's ID matches the first folder name in the path after the bucket name.

### Policy Types

1. **SELECT Policy**: Users can only view receipt files in their own directory
2. **INSERT Policy**: Users can only upload receipt files to their own directory
3. **UPDATE Policy**: Users can only update receipt files in their own directory
4. **DELETE Policy**: Users can only delete receipt files from their own directory

All policies include the `TO authenticated` clause, which explicitly restricts access to authenticated users only, following the same security pattern used in the database RLS policies.

## Performance Optimization

An index has been created on the `bucket_id` and `name` columns of the `storage.objects` table to optimize policy evaluation:

```sql
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_id_name ON storage.objects(bucket_id, name);
```

## How to Use

When implementing file uploads in your application, ensure that files are saved with the correct path structure:

```
{user_id}/{filename}
```

For example, in JavaScript using the Supabase client:

```javascript
const userId = supabase.auth.user().id;
const filePath = `${userId}/${fileName}`;

const { data, error } = await supabase
  .storage
  .from('receipts')
  .upload(filePath, file);
```

## How to Apply

To apply these storage configurations to your Supabase project:

1. Set up a Supabase project at [https://supabase.com](https://supabase.com)
2. Install the Supabase CLI: `npm install -g supabase`
3. Link your project: `supabase link --project-ref your-project-ref`
4. Push the migrations: `supabase db push`

Alternatively, you can copy the SQL from the migration file and execute it directly in the Supabase SQL Editor in the web dashboard.