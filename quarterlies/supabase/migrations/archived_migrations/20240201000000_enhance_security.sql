-- Migration to enhance database security

-- 1. Drop existing policies
DROP POLICY IF EXISTS "Users can only access their own customers" ON customers;
DROP POLICY IF EXISTS "Users can only access their own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can only access their own estimates" ON estimates;
DROP POLICY IF EXISTS "Users can only access their own expenses" ON expenses;
DROP POLICY IF EXISTS "Users can only access their own time logs" ON time_logs;
DROP POLICY IF EXISTS "Users can only access their own invoices" ON invoices;
DROP POLICY IF EXISTS "Users can only access their own payments" ON payments;
DROP POLICY IF EXISTS "Users can only access their own mileage" ON mileage;
DROP POLICY IF EXISTS "Users can only access their own tax payments" ON tax_payments;

-- 2. Create enhanced policies with TO authenticated clause and SELECT for auth.uid()

-- Customers table policies
CREATE POLICY "Users can select their own customers" ON customers
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own customers" ON customers
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own customers" ON customers
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own customers" ON customers
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Jobs table policies
CREATE POLICY "Users can select their own jobs" ON jobs
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own jobs" ON jobs
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own jobs" ON jobs
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own jobs" ON jobs
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Estimates table policies
CREATE POLICY "Users can select their own estimates" ON estimates
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own estimates" ON estimates
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own estimates" ON estimates
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own estimates" ON estimates
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Expenses table policies
CREATE POLICY "Users can select their own expenses" ON expenses
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own expenses" ON expenses
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own expenses" ON expenses
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own expenses" ON expenses
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Time logs table policies
CREATE POLICY "Users can select their own time logs" ON time_logs
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own time logs" ON time_logs
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own time logs" ON time_logs
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own time logs" ON time_logs
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Invoices table policies
CREATE POLICY "Users can select their own invoices" ON invoices
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own invoices" ON invoices
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own invoices" ON invoices
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own invoices" ON invoices
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Payments table policies
CREATE POLICY "Users can select their own payments" ON payments
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own payments" ON payments
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own payments" ON payments
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own payments" ON payments
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Mileage table policies
CREATE POLICY "Users can select their own mileage" ON mileage
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own mileage" ON mileage
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own mileage" ON mileage
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own mileage" ON mileage
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- Tax payments table policies
CREATE POLICY "Users can select their own tax payments" ON tax_payments
  FOR SELECT TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert their own tax payments" ON tax_payments
  FOR INSERT TO authenticated
  WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own tax payments" ON tax_payments
  FOR UPDATE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own tax payments" ON tax_payments
  FOR DELETE TO authenticated
  USING ((SELECT auth.uid()) = user_id);

-- 3. Verify indexes on user_id columns (these were already created in the initial migration)
-- But let's make sure they exist by creating them if they don't

CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);
CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_estimates_user_id ON estimates(user_id);
CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON expenses(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_mileage_user_id ON mileage(user_id);
CREATE INDEX IF NOT EXISTS idx_tax_payments_user_id ON tax_payments(user_id);