-- Comprehensive Schema Migration for Quarterlies Application
-- Generated: 2025-01-01
-- Description: Complete database schema based on current Flutter codebase analysis
-- This migration brings the Supabase database into alignment with current application requirements

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==================== CORE BUSINESS TABLES ====================

-- 1. CUSTOMERS TABLE
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  notes TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. JOBS TABLE
CREATE TABLE IF NOT EXISTS jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  estimated_price DECIMAL(12,2),
  estimated_expenses_budget DECIMAL(12,2),
  actual_income DECIMAL(12,2) DEFAULT 0.0,
  actual_expenses DECIMAL(12,2) DEFAULT 0.0,
  actual_labor_cost DECIMAL(12,2) DEFAULT 0.0,
  status TEXT NOT NULL DEFAULT 'estimate',
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  start_date DATE,
  end_date DATE,
  live_cost_sync_enabled BOOLEAN NOT NULL DEFAULT false,
  sync_expenses BOOLEAN NOT NULL DEFAULT false,
  sync_mileage BOOLEAN NOT NULL DEFAULT false,
  sync_labor_costs BOOLEAN NOT NULL DEFAULT false,
  sync_estimate_items BOOLEAN NOT NULL DEFAULT true,
  summarize_mileage BOOLEAN NOT NULL DEFAULT true,
  summarize_hours BOOLEAN NOT NULL DEFAULT false,
  default_invoice_due_days INTEGER,
  voice_note_url TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ESTIMATES TABLE
CREATE TABLE IF NOT EXISTS estimates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  line_items JSONB DEFAULT '[]'::jsonb,
  total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.0,
  status TEXT NOT NULL DEFAULT 'draft',
  notes TEXT,
  template_id UUID,
  is_template BOOLEAN NOT NULL DEFAULT false,
  template_name TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. CONTRACTS TABLE (new table)
CREATE TABLE IF NOT EXISTS contracts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  estimate_id UUID REFERENCES estimates(id),
  line_items JSONB DEFAULT '[]'::jsonb,
  total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.0,
  status TEXT NOT NULL DEFAULT 'draft',
  notes TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. INVOICES TABLE
CREATE TABLE IF NOT EXISTS invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  issue_date DATE NOT NULL,
  due_date DATE NOT NULL,
  total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.0,
  amount_paid DECIMAL(12,2) NOT NULL DEFAULT 0.0,
  status TEXT NOT NULL DEFAULT 'open',
  line_items JSONB DEFAULT '[]'::jsonb,
  notes TEXT,
  voice_note_url TEXT,
  is_template BOOLEAN NOT NULL DEFAULT false,
  template_name TEXT,
  template_id UUID,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. EXPENSES TABLE (includes mileage as unified table)
-- First ensure the expenses table exists with base structure
CREATE TABLE IF NOT EXISTS expenses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  job_id UUID REFERENCES jobs(id),
  description TEXT NOT NULL,
  amount DECIMAL(12,2) NOT NULL,
  date DATE NOT NULL,
  receipt_photo_url TEXT,
  category TEXT,
  voice_note_url TEXT,
  is_overhead BOOLEAN DEFAULT false,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new columns to expenses table if they don't exist
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS tags JSONB;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS invoiced_in_id UUID;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS pending_invoice_id UUID;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS pending_invoice_number TEXT;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS type TEXT DEFAULT 'Expense';
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS start_location TEXT;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS end_location TEXT;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS miles DECIMAL(8,2);
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS rate_per_mile DECIMAL(6,4);
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS is_auto_tracked BOOLEAN DEFAULT false;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS start_coordinates JSONB;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS end_coordinates JSONB;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS purpose TEXT;
ALTER TABLE expenses ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';

-- 7. TIME_LOGS TABLE
-- First ensure the time_logs table exists with base structure
CREATE TABLE IF NOT EXISTS time_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration INT,
  description TEXT,
  hourly_rate DECIMAL(10,2),
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new columns to time_logs table if they don't exist
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS date DATE;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS hours DECIMAL(8,2);
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS labor_cost DECIMAL(12,2);
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS is_flat_rate BOOLEAN DEFAULT false;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS voice_note_url TEXT;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS invoiced_in_id UUID;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS pending_invoice_id UUID;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS pending_invoice_number TEXT;
ALTER TABLE time_logs ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';

-- 8. PAYMENTS TABLE
-- First ensure the payments table exists with base structure
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  payment_method TEXT,
  reference_number TEXT,
  notes TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new columns to payments table if they don't exist
ALTER TABLE payments ADD COLUMN IF NOT EXISTS amount_received DECIMAL(12,2);
ALTER TABLE payments ADD COLUMN IF NOT EXISTS payment_date DATE;
ALTER TABLE payments ADD COLUMN IF NOT EXISTS voice_note_url TEXT;
ALTER TABLE payments ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';

-- Update constraints to make invoice_id and job_id nullable for flexibility
ALTER TABLE payments ALTER COLUMN invoice_id DROP NOT NULL;
ALTER TABLE payments ALTER COLUMN job_id DROP NOT NULL;

-- 9. TAX_PAYMENTS TABLE
-- First ensure the tax_payments table exists with base structure
CREATE TABLE IF NOT EXISTS tax_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  tax_period_start DATE NOT NULL,
  tax_period_end DATE NOT NULL,
  tax_authority TEXT NOT NULL,
  reference_number TEXT,
  notes TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new columns to tax_payments table if they don't exist
ALTER TABLE tax_payments ADD COLUMN IF NOT EXISTS tax_period TEXT;
ALTER TABLE tax_payments ADD COLUMN IF NOT EXISTS payment_method TEXT;
ALTER TABLE tax_payments ADD COLUMN IF NOT EXISTS confirmation_number TEXT;
ALTER TABLE tax_payments ADD COLUMN IF NOT EXISTS voice_note_url TEXT;
ALTER TABLE tax_payments ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';

-- ==================== ADD SYNC STATUS COLUMNS ====================

-- Add sync_status columns to existing tables if they don't exist
ALTER TABLE customers ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';
ALTER TABLE estimates ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'pending';
-- Note: expenses sync_status was already added in the expenses table modifications above
-- Note: time_logs sync_status was already added in the time_logs table modifications above
-- Note: payments sync_status was already added in the payments table modifications above
-- Note: tax_payments sync_status was already added in the tax_payments table modifications above

-- ==================== USER MANAGEMENT TABLES ====================

-- 10. USER_PROFILES TABLE
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip_code TEXT,
  country TEXT,
  profile_photo_url TEXT,
  signature_image_url TEXT,
  is_onboarding_complete BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- 11. USER_SETTINGS TABLE
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  default_live_job_cost_sync BOOLEAN NOT NULL DEFAULT false,
  sync_expenses BOOLEAN NOT NULL DEFAULT false,
  sync_mileage BOOLEAN NOT NULL DEFAULT false,
  sync_labor_costs BOOLEAN NOT NULL DEFAULT false,
  sync_estimate_items BOOLEAN NOT NULL DEFAULT true,
  default_invoice_due_days INTEGER NOT NULL DEFAULT 30,
  enable_due_date_notifications BOOLEAN NOT NULL DEFAULT true,
  due_date_notification_days INTEGER NOT NULL DEFAULT 3,
  enable_mileage_tracking BOOLEAN NOT NULL DEFAULT false,
  mileage_idle_timeout_minutes INTEGER NOT NULL DEFAULT 5,
  enable_voice_input BOOLEAN NOT NULL DEFAULT true,
  enable_offline_mode BOOLEAN NOT NULL DEFAULT true,
  wifi_only_sync BOOLEAN NOT NULL DEFAULT true,
  business_name TEXT,
  business_address TEXT,
  business_phone TEXT,
  business_email TEXT,
  business_logo TEXT,
  default_invoice_notes TEXT,
  default_invoice_terms TEXT,
  show_mileage_as_summary BOOLEAN NOT NULL DEFAULT true,
  show_hours_as_individual BOOLEAN NOT NULL DEFAULT true,
  automatic_brightness_detection BOOLEAN NOT NULL DEFAULT true,
  dynamic_color_adjustment BOOLEAN NOT NULL DEFAULT true,
  enhanced_contrast_mode BOOLEAN NOT NULL DEFAULT true,
  display_mode TEXT NOT NULL DEFAULT 'field',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- ==================== DOCUMENT SIGNING TABLES ====================

-- 12. DOCUMENT_SIGNING_REQUESTS TABLE
CREATE TABLE IF NOT EXISTS document_signing_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL CHECK (document_type IN ('estimate', 'contract')),
  document_id TEXT NOT NULL,
  customer_id TEXT NOT NULL,
  customer_email TEXT NOT NULL,
  customer_name TEXT NOT NULL,
  job_id TEXT NOT NULL,
  signing_link TEXT NOT NULL,
  expiration_date TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  local_pdf_path TEXT,
  remote_pdf_url TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 13. SIGNED_DOCUMENTS TABLE
CREATE TABLE IF NOT EXISTS signed_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  signing_request_id TEXT NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN ('estimate', 'contract')),
  document_id TEXT NOT NULL,
  customer_name TEXT NOT NULL,
  customer_email TEXT NOT NULL,
  signed_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address TEXT,
  device_info TEXT,
  customer_pdf_path TEXT,
  contractor_pdf_path TEXT,
  customer_pdf_url TEXT,
  contractor_pdf_url TEXT,
  certification_pdf_path TEXT,
  sync_status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== SUPPORTING TABLES ====================

-- 14. EMAIL_LOGS TABLE
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email_type TEXT NOT NULL,
  recipient TEXT NOT NULL,
  subject TEXT NOT NULL,
  document_type TEXT,
  document_id TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 15. EXPENSE_CATEGORY_USAGE TABLE
CREATE TABLE IF NOT EXISTS expense_category_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  category TEXT NOT NULL,
  usage_count INTEGER NOT NULL DEFAULT 1,
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, category)
);

-- 16. APP_SETTINGS TABLE
CREATE TABLE IF NOT EXISTS app_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==================== STORAGE BUCKETS ====================

-- Create receipts storage bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('receipts', 'receipts', false)
ON CONFLICT (id) DO NOTHING;

-- Create documents storage bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', false)
ON CONFLICT (id) DO NOTHING;

-- Create signatures storage bucket for user signature images
INSERT INTO storage.buckets (id, name, public)
VALUES ('signatures', 'signatures', false)
ON CONFLICT (id) DO NOTHING;

-- ==================== INDEXES FOR PERFORMANCE ====================

-- Customer indexes
CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);

-- Job indexes
CREATE INDEX IF NOT EXISTS idx_jobs_user_id ON jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_jobs_customer_id ON jobs(customer_id);
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);

-- Estimate indexes
CREATE INDEX IF NOT EXISTS idx_estimates_user_id ON estimates(user_id);
CREATE INDEX IF NOT EXISTS idx_estimates_job_id ON estimates(job_id);
CREATE INDEX IF NOT EXISTS idx_estimates_customer_id ON estimates(customer_id);
CREATE INDEX IF NOT EXISTS idx_estimates_status ON estimates(status);

-- Contract indexes
CREATE INDEX IF NOT EXISTS idx_contracts_user_id ON contracts(user_id);
CREATE INDEX IF NOT EXISTS idx_contracts_job_id ON contracts(job_id);
CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id);

-- Invoice indexes
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_job_id ON invoices(job_id);
CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);

-- Expense indexes
CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON expenses(user_id);
CREATE INDEX IF NOT EXISTS idx_expenses_job_id ON expenses(job_id);
CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
CREATE INDEX IF NOT EXISTS idx_expenses_type ON expenses(type);
CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category);

-- Time log indexes
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_job_id ON time_logs(job_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_date ON time_logs(date);

-- Payment indexes
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_payments_job_id ON payments(job_id);
CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date);

-- Tax payment indexes
CREATE INDEX IF NOT EXISTS idx_tax_payments_user_id ON tax_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_tax_payments_date ON tax_payments(date);
CREATE INDEX IF NOT EXISTS idx_tax_payments_period ON tax_payments(tax_period);

-- Document signing indexes
CREATE INDEX IF NOT EXISTS idx_document_signing_requests_user_id ON document_signing_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_document_signing_requests_status ON document_signing_requests(status);
CREATE INDEX IF NOT EXISTS idx_signed_documents_user_id ON signed_documents(user_id);

-- Email log indexes
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Expense category usage indexes
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_user_id ON expense_category_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_category ON expense_category_usage(category);
CREATE INDEX IF NOT EXISTS idx_expense_category_usage_count ON expense_category_usage(usage_count);

-- ==================== ENABLE ROW LEVEL SECURITY ====================

-- Enable RLS on all tables
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE estimates ENABLE ROW LEVEL SECURITY;
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE time_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE tax_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_signing_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE signed_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE expense_category_usage ENABLE ROW LEVEL SECURITY;

-- ==================== ROW LEVEL SECURITY POLICIES ====================

-- Customer policies
CREATE POLICY "Users can only access their own customers" ON customers
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Job policies
CREATE POLICY "Users can only access their own jobs" ON jobs
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Estimate policies
CREATE POLICY "Users can only access their own estimates" ON estimates
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Contract policies
CREATE POLICY "Users can only access their own contracts" ON contracts
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Invoice policies
CREATE POLICY "Users can only access their own invoices" ON invoices
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Expense policies
CREATE POLICY "Users can only access their own expenses" ON expenses
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Time log policies
CREATE POLICY "Users can only access their own time logs" ON time_logs
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Payment policies
CREATE POLICY "Users can only access their own payments" ON payments
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Tax payment policies
CREATE POLICY "Users can only access their own tax payments" ON tax_payments
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- User profile policies
CREATE POLICY "Users can only access their own profile" ON user_profiles
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- User settings policies
CREATE POLICY "Users can only access their own settings" ON user_settings
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Document signing request policies
CREATE POLICY "Users can only access their own document signing requests" ON document_signing_requests
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Signed document policies
CREATE POLICY "Users can only access their own signed documents" ON signed_documents
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Email log policies
CREATE POLICY "Users can only access their own email logs" ON email_logs
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- Expense category usage policies
CREATE POLICY "Users can only access their own expense category usage" ON expense_category_usage
  FOR ALL TO authenticated
  USING (auth.uid() = user_id);

-- ==================== STORAGE POLICIES ====================

-- Drop existing storage policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own receipts" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own receipts" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own receipts" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own receipts" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own documents" ON storage.objects;

-- Receipts bucket policies
CREATE POLICY "Users can view their own receipts" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their own receipts" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own receipts" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own receipts" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'receipts' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Documents bucket policies
CREATE POLICY "Users can view their own documents" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their own documents" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own documents" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own documents" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Signatures bucket policies
CREATE POLICY "Users can view their own signatures" ON storage.objects
  FOR SELECT TO authenticated
  USING (
    bucket_id = 'signatures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their own signatures" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (
    bucket_id = 'signatures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own signatures" ON storage.objects
  FOR UPDATE TO authenticated
  USING (
    bucket_id = 'signatures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own signatures" ON storage.objects
  FOR DELETE TO authenticated
  USING (
    bucket_id = 'signatures' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- ==================== DATABASE FUNCTIONS ====================

-- Function to increment expense category usage
CREATE OR REPLACE FUNCTION increment_user_expense_category_usage(category_name TEXT, user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Try to update existing record
  UPDATE expense_category_usage
  SET
    usage_count = usage_count + 1,
    last_used = NOW(),
    updated_at = NOW()
  WHERE
    user_id = user_id AND
    category = category_name
  RETURNING usage_count INTO current_count;

  -- If no record was updated, insert a new one
  IF current_count IS NULL THEN
    INSERT INTO expense_category_usage (user_id, category, usage_count)
    VALUES (user_id, category_name, 1)
    RETURNING usage_count INTO current_count;
  END IF;

  RETURN current_count;
END;
$$;

-- Function to get expense categories by usage
CREATE OR REPLACE FUNCTION get_user_expense_categories_by_usage(user_id UUID)
RETURNS TABLE (
  category TEXT,
  count INTEGER,
  last_used TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    ecu.category,
    ecu.usage_count,
    ecu.last_used
  FROM
    expense_category_usage ecu
  WHERE
    ecu.user_id = user_id
  ORDER BY
    ecu.usage_count DESC,
    ecu.last_used DESC;
END;
$$;

-- ==================== INITIAL DATA ====================

-- Insert default IRS mileage rate
INSERT INTO app_settings (key, value, description)
VALUES ('irs_mileage_rate', '0.655', 'Current IRS standard mileage rate per mile')
ON CONFLICT (key) DO NOTHING;

-- ==================== CONSTRAINTS AND VALIDATIONS ====================

-- Add check constraints for display mode
ALTER TABLE user_settings
ADD CONSTRAINT check_display_mode
CHECK (display_mode IN ('field', 'office'));

-- Add check constraints for sync status
ALTER TABLE customers
ADD CONSTRAINT check_customers_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE jobs
ADD CONSTRAINT check_jobs_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE estimates
ADD CONSTRAINT check_estimates_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE contracts
ADD CONSTRAINT check_contracts_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE invoices
ADD CONSTRAINT check_invoices_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE expenses
ADD CONSTRAINT check_expenses_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE time_logs
ADD CONSTRAINT check_time_logs_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE payments
ADD CONSTRAINT check_payments_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

ALTER TABLE tax_payments
ADD CONSTRAINT check_tax_payments_sync_status
CHECK (sync_status IN ('pending', 'synced', 'error'));

-- ==================== COMMENTS FOR DOCUMENTATION ====================

COMMENT ON TABLE customers IS 'Customer information and contact details';
COMMENT ON TABLE jobs IS 'Job/project management with cost tracking and sync settings';
COMMENT ON TABLE estimates IS 'Estimates with line items and template support';
COMMENT ON TABLE contracts IS 'Contracts derived from estimates with electronic signing';
COMMENT ON TABLE invoices IS 'Invoices with line items and payment tracking';
COMMENT ON TABLE expenses IS 'Expenses and mileage tracking (unified table)';
COMMENT ON TABLE time_logs IS 'Time tracking with labor cost calculation';
COMMENT ON TABLE payments IS 'Payment records linked to invoices and jobs';
COMMENT ON TABLE tax_payments IS 'Quarterly tax payment tracking';
COMMENT ON TABLE user_profiles IS 'Personal user information and onboarding status';
COMMENT ON TABLE user_settings IS 'App preferences and business settings';
COMMENT ON TABLE document_signing_requests IS 'Electronic signature requests';
COMMENT ON TABLE signed_documents IS 'Completed electronic signature records';
COMMENT ON TABLE email_logs IS 'Email sending tracking and status';
COMMENT ON TABLE expense_category_usage IS 'User-specific expense category usage tracking';
COMMENT ON TABLE app_settings IS 'Application-wide settings and configuration';

-- Migration completed successfully
-- All tables, indexes, RLS policies, storage policies, and functions have been created
-- The database schema is now aligned with the current Flutter application requirements