# Comprehensive Schema Migration Summary

## Overview
This document summarizes the comprehensive database schema migration performed on the Quarterlies application. The migration was generated based on analysis of the current Flutter codebase and brings the Supabase database into complete alignment with application requirements.

## Migration Details
- **Migration File**: `20250101000000_comprehensive_schema_migration.sql`
- **Generated**: 2025-01-01
- **Purpose**: Complete database schema alignment with current Flutter application

## Archived Files
All existing historical migration files have been moved to `archived_migrations/` folder:
- 20240101000000_initial_schema.sql
- 20240201000000_enhance_security.sql
- 20240301000000_storage_receipts.sql
- 20240401000000_security_enhancements.sql
- 20240601000000_add_invoiced_in_id.sql
- 20240601000000_document_signing.sql
- 20240602000000_add_pending_invoice_fields.sql
- 20240701000000_add_expense_category_usage.sql
- 20240801000000_remove_expense_type.sql
- 20240901000000_update_expense_category_usage.sql
- 20241201000000_add_glare_resistance_settings.sql
- 20241201000000_update_document_signing_schema.sql
- 20241202000000_add_display_mode_setting.sql
- 20241202000000_add_email_logs.sql
- 20241203000000_add_overhead_expense_fields.sql
- 20241215000000_add_job_budget_field.sql
- 20241220000000_create_user_profiles_and_settings.sql
- README_SECURITY.md
- README_STORAGE.md
- SECURITY_ENHANCEMENTS.md

## Database Schema

### Core Business Tables

#### 1. customers
- **Purpose**: Customer information and contact details
- **Key Fields**: name, email, phone, address fields, notes
- **Relationships**: Referenced by jobs, estimates, contracts, invoices
- **Features**: Sync status tracking, user isolation

#### 2. jobs
- **Purpose**: Job/project management with comprehensive cost tracking
- **Key Fields**: title, description, estimated/actual costs, status, address fields
- **Sync Settings**: Individual control for expenses, mileage, labor costs, estimate items
- **Display Options**: Summarization preferences for mileage and hours
- **Features**: Budget tracking, voice notes, sync status

#### 3. estimates
- **Purpose**: Estimates with line items and template support
- **Key Fields**: line_items (JSONB), total_amount, status, template fields
- **Features**: Template creation/cloning, sync status tracking

#### 4. contracts
- **Purpose**: Contracts derived from estimates with electronic signing
- **Key Fields**: estimate_id reference, line_items (JSONB), status
- **Features**: Links to estimates, electronic signature workflow

#### 5. invoices
- **Purpose**: Invoices with line items and payment tracking
- **Key Fields**: issue_date, due_date, total_amount, amount_paid, status
- **Features**: Template support, voice notes, payment tracking

#### 6. expenses (Unified Table)
- **Purpose**: Expenses and mileage tracking in unified table
- **Key Fields**: amount, date, category, is_overhead, type
- **Mileage Fields**: start/end locations, miles, rate_per_mile, coordinates
- **Features**: Receipt photos, voice notes, invoice tracking, overhead allocation

#### 7. time_logs
- **Purpose**: Time tracking with labor cost calculation
- **Key Fields**: date, hours, hourly_rate, labor_cost, is_flat_rate
- **Features**: Voice notes, invoice tracking, automatic cost calculation

#### 8. payments
- **Purpose**: Payment records linked to invoices and jobs
- **Key Fields**: amount_received, payment_date, payment_method
- **Features**: Voice notes, flexible linking to invoices/jobs

#### 9. tax_payments
- **Purpose**: Quarterly tax payment tracking
- **Key Fields**: date, amount, tax_period, payment_method, confirmation_number
- **Features**: Voice notes, quarterly organization

### User Management Tables

#### 10. user_profiles
- **Purpose**: Personal user information and onboarding status
- **Key Fields**: first_name, last_name, contact info, signature_image_url
- **Features**: Onboarding completion tracking, signature storage

#### 11. user_settings
- **Purpose**: App preferences and business settings
- **Sync Defaults**: Control for expenses, mileage, labor costs, estimate items
- **Business Info**: Company details, invoice defaults
- **UI Preferences**: Display mode (field/office), glare resistance settings
- **Features**: Comprehensive preference management

### Document Management Tables

#### 12. document_signing_requests
- **Purpose**: Electronic signature requests
- **Key Fields**: document_type, customer info, signing_link, expiration_date
- **Features**: Offline support, status tracking

#### 13. signed_documents
- **Purpose**: Completed electronic signature records
- **Key Fields**: signed_at, ip_address, device_info, PDF paths/URLs
- **Features**: Certification tracking, multiple PDF versions

### Supporting Tables

#### 14. email_logs
- **Purpose**: Email sending tracking and status
- **Key Fields**: email_type, recipient, subject, status, error_message
- **Features**: Comprehensive email audit trail

#### 15. expense_category_usage
- **Purpose**: User-specific expense category usage tracking
- **Key Fields**: category, usage_count, last_used
- **Features**: Personalized category ordering

#### 16. app_settings
- **Purpose**: Application-wide settings and configuration
- **Key Fields**: key, value, description
- **Features**: Global configuration management

## Storage Buckets

### receipts
- **Purpose**: Receipt photo storage
- **Security**: User-specific path-based access control
- **Path Pattern**: `{user_id}/{filename}`

### documents
- **Purpose**: Document signing PDF storage
- **Security**: User-specific path-based access control
- **Path Pattern**: `{user_id}/{filename}`

### signatures
- **Purpose**: User signature image storage
- **Security**: User-specific path-based access control
- **Path Pattern**: `{user_id}/{filename}`

## Security Implementation

### Row Level Security (RLS)
- **Enabled**: All application tables
- **Policy**: Users can only access their own data (`auth.uid() = user_id`)
- **Scope**: SELECT, INSERT, UPDATE, DELETE operations

### Storage Security
- **Path-based isolation**: Files organized by user ID
- **Authentication required**: All storage operations require authentication
- **Granular permissions**: Separate policies for view, upload, update, delete

## Performance Optimizations

### Indexes
- **User-based queries**: All tables indexed on `user_id`
- **Relationship queries**: Foreign key columns indexed
- **Common filters**: Status, date, type fields indexed
- **Search optimization**: Name and category fields indexed

### Database Functions
- **increment_user_expense_category_usage()**: Efficient category usage tracking
- **get_user_expense_categories_by_usage()**: Optimized category retrieval

## Data Integrity

### Constraints
- **Foreign Keys**: Proper relationships between all related tables
- **Check Constraints**: Valid values for sync_status, display_mode
- **Unique Constraints**: User-specific uniqueness where appropriate

### Validation
- **Sync Status**: Limited to 'pending', 'synced', 'error'
- **Display Mode**: Limited to 'field', 'office'
- **Document Types**: Limited to 'estimate', 'contract'

## Initial Data
- **IRS Mileage Rate**: Default rate of $0.655 per mile
- **App Settings**: Configurable application-wide settings

## Migration Benefits
1. **Complete Alignment**: Database schema matches current application requirements
2. **Security**: Comprehensive RLS and storage policies
3. **Performance**: Optimized indexes for common query patterns
4. **Maintainability**: Single source of truth for schema
5. **Documentation**: Comprehensive table and column comments
6. **Flexibility**: Support for all current and planned features

## Next Steps
1. Apply migration to Supabase project: `supabase db push`
2. Verify all application functionality works with new schema
3. Run comprehensive tests to ensure data integrity
4. Monitor performance and adjust indexes if needed

This migration provides a solid foundation for the Quarterlies application with proper security, performance, and maintainability considerations.
