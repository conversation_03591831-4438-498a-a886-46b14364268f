// Follow this setup guide to integrate the Deno runtime into your Supabase project:
// https://supabase.com/docs/guides/functions/deno-runtime

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { EmailClient } from '../_shared/email-client.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      to, 
      subject, 
      documentType, 
      customerName, 
      jobName, 
      signedDocumentUrl, 
      documentId 
    } = await req.json()

    // Validate required fields
    if (!to || !subject || !documentType || !customerName || !jobName || !signedDocumentUrl || !documentId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Initialize email client
    const emailClient = new EmailClient()

    // Create email content
    const documentTypeCapitalized = documentType.charAt(0).toUpperCase() + documentType.slice(1)
    const emailContent = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4a86e8; color: white; padding: 10px 20px; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px; }
            .button { display: inline-block; background-color: #4a86e8; color: white; padding: 10px 20px; 
                      text-decoration: none; border-radius: 5px; margin-top: 20px; }
            .footer { margin-top: 20px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Your Signed ${documentTypeCapitalized}</h2>
            </div>
            <div class="content">
              <p>Dear ${customerName},</p>
              <p>Thank you for signing the ${documentType} for the project "${jobName}".</p>
              <p>You can view and download your signed document by clicking the button below:</p>
              <p><a href="${signedDocumentUrl}" class="button">View Signed Document</a></p>
              <p>Please keep this document for your records.</p>
              <p>If you have any questions, please contact us directly.</p>
              <p>Thank you for your business!</p>
              <div class="footer">
                <p>This is an automated message. Please do not reply to this email.</p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `

    // Send the email
    const emailResult = await emailClient.sendEmail({
      to,
      subject,
      html: emailContent,
    })

    // Log the email sending in the database
    await supabaseClient
      .from('email_logs')
      .insert({
        email_type: 'signed_document_to_customer',
        recipient: to,
        subject,
        document_type: documentType,
        document_id: documentId,
        status: emailResult.success ? 'sent' : 'failed',
        error_message: emailResult.success ? null : emailResult.error,
      })

    return new Response(
      JSON.stringify({ success: emailResult.success, message: emailResult.success ? 'Email sent successfully' : emailResult.error }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: emailResult.success ? 200 : 500 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
