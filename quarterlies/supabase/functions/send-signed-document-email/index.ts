// Follow this setup guide to integrate the Deno runtime into your Supabase project:
// https://supabase.com/docs/guides/functions/deno-runtime

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { EmailClient } from '../_shared/email-client.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      customerEmail, 
      customerName, 
      documentType, 
      documentUrl, 
      jobName,
      jobDescription,
      serviceAddress,
      documentId,
      signedAt
    } = await req.json()

    // Validate required fields
    if (!customerEmail || !customerName || !documentType || !documentUrl || !jobName || !documentId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Initialize email client
    const emailClient = new EmailClient()

    // Capitalize document type for display
    const documentTypeCapitalized = documentType.charAt(0).toUpperCase() + documentType.slice(1)

    // Create email subject
    const subject = `Your Signed ${documentTypeCapitalized} - ${jobName}`

    // Create HTML email content with professional styling
    const emailContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
            h2 { color: #007bff; margin-top: 0; }
            .highlight { background-color: #fff3cd; padding: 10px; border-radius: 4px; border-left: 4px solid #ffc107; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Your Signed ${documentTypeCapitalized}</h2>
            </div>
            <div class="content">
              <p>Dear ${customerName},</p>
              
              <p>Thank you for signing your ${documentType} for the project "${jobName}". Your signed document is now ready and attached to this email.</p>
              
              <div class="info-box">
                <strong>Project Details:</strong><br>
                <strong>Project:</strong> ${jobName}<br>
                ${jobDescription ? `<strong>Description:</strong> ${jobDescription}<br>` : ''}
                ${serviceAddress ? `<strong>Service Address:</strong> ${serviceAddress}<br>` : ''}
                ${signedAt ? `<strong>Signed On:</strong> ${new Date(signedAt).toLocaleDateString()}<br>` : ''}
              </div>

              <p>You can also download your signed document using the link below:</p>
              <p><a href="${documentUrl}" class="button">Download Signed Document</a></p>
              
              <div class="highlight">
                <strong>Important:</strong> Please keep this signed document for your records. This is a legally binding agreement.
              </div>

              <p>If you have any questions about this document or the project, please don't hesitate to contact us.</p>
              
              <p>Thank you for your business!</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `

    // Send the email
    const emailResult = await emailClient.sendEmail({
      to: customerEmail,
      subject,
      html: emailContent,
    })

    // Log the email sending in the database
    await supabaseClient
      .from('email_logs')
      .insert({
        email_type: 'signed_customer',
        recipient: customerEmail,
        subject,
        document_type: documentType,
        document_id: documentId,
        status: emailResult.success ? 'sent' : 'failed',
        error_message: emailResult.success ? null : emailResult.error,
      })

    return new Response(
      JSON.stringify({ 
        success: emailResult.success, 
        message: emailResult.success ? 'Email sent successfully' : emailResult.error 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: emailResult.success ? 200 : 500 }
    )

  } catch (error) {
    console.error('Error in send-signed-document-email function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
