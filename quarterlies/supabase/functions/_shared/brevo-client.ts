// Brevo (formerly Sendinblue) client for sending emails
// Free tier: 300 emails/day (9,000/month)
// No credit card required for free tier

interface BrevoEmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
  fromName?: string;
}

export class BrevoClient {
  private apiKey: string;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.apiKey = Deno.env.get('BREVO_API_KEY') ?? '';
    this.fromEmail = Deno.env.get('FROM_EMAIL') ?? '<EMAIL>';
    this.fromName = Deno.env.get('FROM_NAME') ?? 'Quarterlies';

    if (!this.apiKey) {
      throw new Error('Brevo API key missing. Please set BREVO_API_KEY environment variable.');
    }
  }

  async sendEmail(emailData: BrevoEmailData): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'api-key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sender: {
            email: emailData.from || this.fromEmail,
            name: emailData.fromName || this.fromName,
          },
          to: [
            {
              email: emailData.to,
            }
          ],
          subject: emailData.subject,
          htmlContent: emailData.html,
        }),
      });

      if (response.ok) {
        return { success: true };
      } else {
        const errorData = await response.json();
        return { 
          success: false, 
          error: `Brevo API error: ${response.status} - ${errorData.message || JSON.stringify(errorData)}` 
        };
      }
    } catch (error) {
      return { success: false, error: `Network error: ${error.message}` };
    }
  }

  // Helper method to create signing request HTML
  createSigningRequestHTML(data: {
    customerName: string;
    documentType: string;
    jobName: string;
    signingLink: string;
    jobDescription?: string;
    serviceAddress?: string;
    expirationDate?: string;
  }): string {
    const documentTypeCapitalized = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
            h2 { color: #007bff; margin-top: 0; }
            .link-text { word-break: break-all; color: #007bff; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>${documentTypeCapitalized} Signature Request</h2>
            </div>
            <div class="content">
              <p>Dear ${data.customerName},</p>
              <p>Your ${data.documentType} for the project "${data.jobName}" is ready for your review and electronic signature.</p>
              
              <div class="info-box">
                <strong>Project Details:</strong><br>
                <strong>Project:</strong> ${data.jobName}<br>
                ${data.jobDescription ? `<strong>Description:</strong> ${data.jobDescription}<br>` : ''}
                ${data.serviceAddress ? `<strong>Service Address:</strong> ${data.serviceAddress}<br>` : ''}
              </div>

              <p>Please click the button below to view and sign the document:</p>
              <p style="text-align: center;">
                <a href="${data.signingLink}" class="button">View and Sign Document</a>
              </p>
              
              <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
              <p class="link-text">${data.signingLink}</p>
              
              <p><strong>Important:</strong> This link will expire ${data.expirationDate || 'in 7 days'}.</p>
              <p>This electronic signature is legally binding and equivalent to signing a physical document.</p>
              <p>Thank you for your business!</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  // Helper method to create signed document HTML
  createSignedDocumentHTML(data: {
    customerName: string;
    documentType: string;
    jobName: string;
    documentUrl: string;
    jobDescription?: string;
    serviceAddress?: string;
    signedAt?: string;
  }): string {
    const documentTypeCapitalized = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background-color: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .highlight { background-color: #fff3cd; padding: 10px; border-radius: 4px; border-left: 4px solid #ffc107; }
            h2 { color: #28a745; margin-top: 0; }
            .link-text { word-break: break-all; color: #28a745; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Your Signed ${documentTypeCapitalized}</h2>
            </div>
            <div class="content">
              <p>Dear ${data.customerName},</p>
              <p>Thank you for signing your ${data.documentType} for the project "${data.jobName}". Your signed document is now ready.</p>
              
              <div class="info-box">
                <strong>Project Details:</strong><br>
                <strong>Project:</strong> ${data.jobName}<br>
                ${data.jobDescription ? `<strong>Description:</strong> ${data.jobDescription}<br>` : ''}
                ${data.serviceAddress ? `<strong>Service Address:</strong> ${data.serviceAddress}<br>` : ''}
                ${data.signedAt ? `<strong>Signed On:</strong> ${new Date(data.signedAt).toLocaleDateString()}<br>` : ''}
              </div>

              <p>You can download your signed document using the link below:</p>
              <p style="text-align: center;">
                <a href="${data.documentUrl}" class="button">Download Signed Document</a>
              </p>
              
              <p>Direct link: <span class="link-text">${data.documentUrl}</span></p>
              
              <div class="highlight">
                <strong>Important:</strong> Please keep this signed document for your records. This is a legally binding agreement.
              </div>

              <p>If you have any questions about this document or the project, please don't hesitate to contact us.</p>
              <p>Thank you for your business!</p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  // Helper method to create contractor notification HTML
  createContractorNotificationHTML(data: {
    contractorName: string;
    customerName: string;
    documentType: string;
    jobName: string;
    documentUrl: string;
    jobDescription?: string;
    serviceAddress?: string;
    signedAt?: string;
  }): string {
    const documentTypeCapitalized = data.documentType.charAt(0).toUpperCase() + data.documentType.slice(1);
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background-color: #f8f9fa; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background-color: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
            .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .success-box { background-color: #d4edda; padding: 15px; border-radius: 4px; border-left: 4px solid #28a745; margin: 15px 0; }
            h2 { color: #17a2b8; margin-top: 0; }
            .link-text { word-break: break-all; color: #17a2b8; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>${documentTypeCapitalized} Signed!</h2>
            </div>
            <div class="content">
              <p>Hello ${data.contractorName},</p>
              
              <div class="success-box">
                <strong>Great news!</strong> ${data.customerName} has signed the ${data.documentType} for "${data.jobName}".
              </div>
              
              <div class="info-box">
                <strong>Project Details:</strong><br>
                <strong>Customer:</strong> ${data.customerName}<br>
                <strong>Project:</strong> ${data.jobName}<br>
                ${data.jobDescription ? `<strong>Description:</strong> ${data.jobDescription}<br>` : ''}
                ${data.serviceAddress ? `<strong>Service Address:</strong> ${data.serviceAddress}<br>` : ''}
                ${data.signedAt ? `<strong>Signed On:</strong> ${new Date(data.signedAt).toLocaleDateString()}<br>` : ''}
              </div>

              <p>You can download the signed document (with certification) using the link below:</p>
              <p style="text-align: center;">
                <a href="${data.documentUrl}" class="button">Download Signed Document</a>
              </p>
              
              <p>Direct link: <span class="link-text">${data.documentUrl}</span></p>
              
              <p>This document includes the digital signature certification for your records.</p>
              <p>You can now proceed with the project as outlined in the signed ${data.documentType}.</p>
            </div>
            <div class="footer">
              <p>This is an automated notification from Quarterlies.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }
}
