// Email client using Brevo (formerly Sendinblue) for reliable email delivery
// Free tier: 300 emails/day (9,000/month), no credit card required

export class EmailClient {
  private apiKey: string;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.apiKey = Deno.env.get('BREVO_API_KEY') ?? '';
    this.fromEmail = Deno.env.get('FROM_EMAIL') ?? '<EMAIL>';
    this.fromName = Deno.env.get('FROM_NAME') ?? 'Quarterlies';

    if (!this.apiKey) {
      throw new Error('Brevo API key missing. Please set BREVO_API_KEY environment variable.');
    }
  }

  // Send an email using Brevo API
  async sendEmail({ to, subject, html }: { to: string; subject: string; html: string }) {
    try {
      const response = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'api-key': this.api<PERSON>ey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sender: {
            email: this.fromEmail,
            name: this.fromName,
          },
          to: [
            {
              email: to,
            }
          ],
          subject: subject,
          htmlContent: html,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        return { success: true, messageId: result.messageId };
      } else {
        const errorData = await response.json();
        return {
          success: false,
          error: `Brevo API error: ${response.status} - ${errorData.message || JSON.stringify(errorData)}`
        };
      }
    } catch (error) {
      console.error('Brevo Error:', error);
      return { success: false, error: `Error sending email: ${error.message}` };
    }
  }
}
