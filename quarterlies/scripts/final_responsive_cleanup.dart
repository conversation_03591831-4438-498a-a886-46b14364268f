#!/usr/bin/env dart

import 'dart:io';

/// Final cleanup script to handle remaining responsive design issues
/// Focuses on the specific patterns that the first script missed

void main() async {
  print('🔧 Starting final responsive design cleanup...\n');
  
  final screenDirectories = [
    'lib/screens',
  ];
  
  var totalFilesProcessed = 0;
  var totalReplacements = 0;
  
  for (final dir in screenDirectories) {
    final results = await processDirectory(dir);
    totalFilesProcessed += results['files'] as int;
    totalReplacements += results['replacements'] as int;
  }
  
  print('\n✅ FINAL CLEANUP COMPLETED');
  print('=' * 50);
  print('Files processed: $totalFilesProcessed');
  print('Total replacements made: $totalReplacements');
}

Future<Map<String, int>> processDirectory(String dirPath) async {
  final dir = Directory(dirPath);
  var filesProcessed = 0;
  var totalReplacements = 0;
  
  if (!await dir.exists()) {
    print('⚠️  Directory not found: $dirPath');
    return {'files': 0, 'replacements': 0};
  }
  
  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final replacements = await processFile(entity);
      if (replacements > 0) {
        filesProcessed++;
        totalReplacements += replacements;
        print('✅ ${entity.path.replaceAll('quarterlies/', '')}: $replacements fixes');
      }
    }
  }
  
  return {'files': filesProcessed, 'replacements': totalReplacements};
}

Future<int> processFile(File file) async {
  final content = await file.readAsString();
  var modifiedContent = content;
  var replacementCount = 0;

  // Replace remaining fontSize patterns
  final fontSizePatterns = [
    // fontSize: number pattern
    {
      'from': RegExp(r'fontSize:\s*(\d+(?:\.\d+)?)\s*,?'),
      'to': '',
    },
    // TextStyle with only fontSize
    {
      'from': RegExp(r'TextStyle\s*\(\s*fontSize:\s*\d+(?:\.\d+)?\s*\)'),
      'to': 'TextStyle()',
    },
    // Complex TextStyle patterns with fontSize
    {
      'from': RegExp(r'fontSize:\s*\d+(?:\.\d+)?\s*,\s*'),
      'to': '',
    },
  ];

  for (final pattern in fontSizePatterns) {
    final regex = pattern['from'] as RegExp;
    final replacement = pattern['to'] as String;
    
    final matches = regex.allMatches(modifiedContent).length;
    if (matches > 0) {
      modifiedContent = modifiedContent.replaceAll(regex, replacement);
      replacementCount += matches;
    }
  }

  // Replace hardcoded sizes with ResponsiveSpacing
  final sizePatterns = [
    // size: number
    {
      'from': RegExp(r'size:\s*(\d+(?:\.\d+)?)\s*,?'),
      'to': (Match m) => 'size: ResponsiveSpacing.getIconSize(context, base: ${m.group(1)}),',
    },
    // height: number (for buttons and containers)
    {
      'from': RegExp(r'height:\s*(\d+(?:\.\d+)?)\s*,?'),
      'to': (Match m) => 'height: ResponsiveSpacing.getButtonHeight(context, base: ${m.group(1)}),',
    },
    // width: number (for specific cases)
    {
      'from': RegExp(r'width:\s*(\d+(?:\.\d+)?)\s*,?'),
      'to': (Match m) => 'width: ResponsiveSpacing.getSpacing(context, base: ${m.group(1)}),',
    },
  ];

  for (final pattern in sizePatterns) {
    final regex = pattern['from'] as RegExp;
    final replacementFunc = pattern['to'] as String Function(Match);
    
    final matches = regex.allMatches(modifiedContent).length;
    if (matches > 0) {
      modifiedContent = modifiedContent.replaceAllMapped(regex, replacementFunc);
      replacementCount += matches;
    }
  }

  // Replace remaining EdgeInsets patterns
  final edgeInsetsPatterns = [
    // EdgeInsets.all(number)
    {
      'from': RegExp(r'EdgeInsets\.all\s*\(\s*(\d+(?:\.\d+)?)\s*\)'),
      'to': (Match m) => 'ResponsiveSpacing.getPadding(context, base: ${m.group(1)})',
    },
    // EdgeInsets.symmetric(horizontal: number)
    {
      'from': RegExp(r'EdgeInsets\.symmetric\s*\(\s*horizontal:\s*(\d+(?:\.\d+)?)\s*\)'),
      'to': (Match m) => 'ResponsiveSpacing.getPadding(context, base: ${m.group(1)})',
    },
    // EdgeInsets.symmetric(vertical: number)
    {
      'from': RegExp(r'EdgeInsets\.symmetric\s*\(\s*vertical:\s*(\d+(?:\.\d+)?)\s*\)'),
      'to': (Match m) => 'ResponsiveSpacing.getPadding(context, base: ${m.group(1)})',
    },
    // EdgeInsets.only patterns
    {
      'from': RegExp(r'EdgeInsets\.only\s*\([^)]*\)'),
      'to': (Match m) => 'ResponsiveSpacing.getPadding(context)',
    },
  ];

  for (final pattern in edgeInsetsPatterns) {
    final regex = pattern['from'] as RegExp;
    final replacementFunc = pattern['to'] as String Function(Match);
    
    final matches = regex.allMatches(modifiedContent).length;
    if (matches > 0) {
      modifiedContent = modifiedContent.replaceAllMapped(regex, replacementFunc);
      replacementCount += matches;
    }
  }

  // Convert remaining Text widgets to ResponsiveText
  final textPatterns = [
    // Text with style but no ResponsiveText
    {
      'from': RegExp(r'(?<!Responsive)Text\s*\(\s*([^,\)]+),\s*style:\s*[^)]+\)'),
      'to': (Match m) => 'ResponsiveBody(${m.group(1)})',
    },
    // Simple Text widgets
    {
      'from': RegExp(r'(?<!Responsive)Text\s*\(\s*([^,\)]+)\s*\)'),
      'to': (Match m) => 'ResponsiveBody(${m.group(1)})',
    },
  ];

  for (final pattern in textPatterns) {
    final regex = pattern['from'] as RegExp;
    final replacementFunc = pattern['to'] as String Function(Match);
    
    // Only apply if the file doesn't already have many ResponsiveText widgets
    final responsiveTextCount = 'ResponsiveText'.allMatches(modifiedContent).length;
    final totalTextCount = RegExp(r'\bText\s*\(').allMatches(modifiedContent).length;
    
    if (responsiveTextCount < totalTextCount * 0.5) {
      final matches = regex.allMatches(modifiedContent).length;
      if (matches > 0) {
        modifiedContent = modifiedContent.replaceAllMapped(regex, replacementFunc);
        replacementCount += matches;
      }
    }
  }

  // Fix TextOverflow.ellipsis
  if (modifiedContent.contains('TextOverflow.ellipsis')) {
    modifiedContent = modifiedContent.replaceAll('TextOverflow.ellipsis', 'TextOverflow.visible');
    replacementCount++;
  }

  // Clean up empty TextStyle()
  modifiedContent = modifiedContent.replaceAll(RegExp(r'style:\s*TextStyle\s*\(\s*\)\s*,?'), '');

  // Clean up trailing commas and extra spaces
  modifiedContent = modifiedContent.replaceAll(RegExp(r',\s*,'), ',');
  modifiedContent = modifiedContent.replaceAll(RegExp(r',\s*\)'), ')');

  // Write the modified content back to the file
  if (replacementCount > 0) {
    await file.writeAsString(modifiedContent);
  }

  return replacementCount;
}
