#!/usr/bin/env dart

import 'dart:io';

/// <PERSON><PERSON><PERSON> to systematically fix all 62 screens for responsive design issues
/// This script identifies and reports issues that need manual fixing

void main() async {
  print('🔍 Starting comprehensive screen audit...\n');
  
  final screenDirectories = [
    'lib/screens',
  ];
  
  final issues = <String, List<String>>{};
  
  for (final dir in screenDirectories) {
    await auditDirectory(dir, issues);
  }
  
  print('\n📊 AUDIT SUMMARY');
  print('=' * 50);
  
  var totalScreens = 0;
  var totalIssues = 0;
  
  for (final entry in issues.entries) {
    final file = entry.key;
    final fileIssues = entry.value;
    
    if (fileIssues.isNotEmpty) {
      totalScreens++;
      totalIssues += fileIssues.length;
      
      print('\n❌ $file');
      for (final issue in fileIssues) {
        print('   • $issue');
      }
    }
  }
  
  print('\n📈 STATISTICS');
  print('Total screens with issues: $totalScreens');
  print('Total issues found: $totalIssues');
  
  print('\n🔧 RECOMMENDED FIXES');
  print('1. Replace all fixed fontSize with ResponsiveText widgets');
  print('2. Replace TextOverflow.ellipsis with proper responsive sizing');
  print('3. Wrap all screen bodies with ResponsiveLayout');
  print('4. Ensure proper SafeArea usage');
  print('5. Add proper scrolling for all content');
}

Future<void> auditDirectory(String dirPath, Map<String, List<String>> issues) async {
  final dir = Directory(dirPath);
  
  if (!await dir.exists()) {
    print('⚠️  Directory not found: $dirPath');
    return;
  }
  
  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      await auditFile(entity, issues);
    }
  }
}

Future<void> auditFile(File file, Map<String, List<String>> issues) async {
  final content = await file.readAsString();
  final lines = content.split('\n');
  final fileName = file.path.replaceAll('quarterlies/', '');
  
  final fileIssues = <String>[];
  
  // Check for fixed font sizes
  for (int i = 0; i < lines.length; i++) {
    final line = lines[i];
    
    // Check for fixed fontSize
    if (line.contains('fontSize:') && line.contains(RegExp(r'\d+'))) {
      fileIssues.add('Line ${i + 1}: Fixed fontSize found - needs ResponsiveText');
    }
    
    // Check for TextOverflow.ellipsis
    if (line.contains('TextOverflow.ellipsis')) {
      fileIssues.add('Line ${i + 1}: TextOverflow.ellipsis found - needs responsive solution');
    }
    
    // Check for missing SafeArea in Scaffold body
    if (line.contains('body:') && i < lines.length - 5) {
      final nextFewLines = lines.skip(i).take(5).join(' ');
      if (!nextFewLines.contains('SafeArea') && !nextFewLines.contains('ResponsiveLayout')) {
        fileIssues.add('Line ${i + 1}: Scaffold body may need SafeArea or ResponsiveLayout');
      }
    }
    
    // Check for hardcoded padding/margin values
    if (line.contains(RegExp(r'EdgeInsets\.(all|symmetric|only)\(\s*\d+'))) {
      fileIssues.add('Line ${i + 1}: Hardcoded EdgeInsets - should be responsive');
    }
    
    // Check for hardcoded sizes
    if (line.contains(RegExp(r'size:\s*\d+')) && !line.contains('Icons.')) {
      fileIssues.add('Line ${i + 1}: Hardcoded size - should be responsive');
    }
  }
  
  // Check for missing imports
  if (!content.contains('responsive_layout.dart') && 
      !content.contains('responsive_text.dart') &&
      content.contains('class ') && 
      content.contains('Screen') &&
      content.contains('Widget build')) {
    fileIssues.add('Missing responsive imports - needs ResponsiveLayout and ResponsiveText');
  }
  
  if (fileIssues.isNotEmpty) {
    issues[fileName] = fileIssues;
  }
}
