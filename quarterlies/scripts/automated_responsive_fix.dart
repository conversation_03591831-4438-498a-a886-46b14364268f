#!/usr/bin/env dart

import 'dart:io';

/// Automated script to fix responsive design issues across all screens
/// This script performs systematic replacements to implement responsive design

void main() async {
  print('🚀 Starting automated responsive design fixes...\n');

  final screenDirectories = ['lib/screens'];

  var totalFilesProcessed = 0;
  var totalReplacements = 0;

  for (final dir in screenDirectories) {
    final results = await processDirectory(dir);
    totalFilesProcessed += results['files'] as int;
    totalReplacements += results['replacements'] as int;
  }

  print('\n✅ AUTOMATED FIXES COMPLETED');
  print('=' * 50);
  print('Files processed: $totalFilesProcessed');
  print('Total replacements made: $totalReplacements');
  print('\n📋 MANUAL REVIEW REQUIRED');
  print('Please review all changes and test thoroughly.');
}

Future<Map<String, int>> processDirectory(String dirPath) async {
  final dir = Directory(dirPath);
  var filesProcessed = 0;
  var totalReplacements = 0;

  if (!await dir.exists()) {
    print('⚠️  Directory not found: $dirPath');
    return {'files': 0, 'replacements': 0};
  }

  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final replacements = await processFile(entity);
      if (replacements > 0) {
        filesProcessed++;
        totalReplacements += replacements;
        print(
          '✅ ${entity.path.replaceAll('quarterlies/', '')}: $replacements fixes',
        );
      }
    }
  }

  return {'files': filesProcessed, 'replacements': totalReplacements};
}

Future<int> processFile(File file) async {
  final content = await file.readAsString();
  var modifiedContent = content;
  var replacementCount = 0;

  // Skip if already has responsive imports
  if (content.contains('responsive_layout.dart') &&
      content.contains('responsive_text.dart')) {
    return 0;
  }

  // Add responsive imports if this is a screen file
  if (content.contains('class ') &&
      content.contains('Screen') &&
      content.contains('Widget build')) {
    // Find the last import statement
    final lines = modifiedContent.split('\n');
    var lastImportIndex = -1;

    for (int i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        lastImportIndex = i;
      }
    }

    if (lastImportIndex != -1) {
      // Add responsive imports after the last import
      final newImports = [
        "import 'package:quarterlies/widgets/responsive_layout.dart';",
        "import 'package:quarterlies/widgets/responsive_text.dart';",
        "import 'package:quarterlies/utils/responsive_spacing.dart';",
      ];

      for (final import in newImports) {
        if (!modifiedContent.contains(import)) {
          lines.insert(lastImportIndex + 1, import);
          lastImportIndex++;
          replacementCount++;
        }
      }

      modifiedContent = lines.join('\n');
    }
  }

  // Replace specific fontSize patterns with ResponsiveText
  final specificReplacements = [
    // AppBar title with fontSize
    {
      'from': RegExp(
        r'title:\s*Text\s*\(\s*([^,\)]+),\s*style:\s*TextStyle\s*\([^}]*fontSize:\s*[^,}]+[^}]*\)\s*\)',
      ),
      'to': (Match m) => 'title: ResponsiveTitle(${m.group(1)})',
    },
    // Text with fontSize in TextStyle
    {
      'from': RegExp(
        r'Text\s*\(\s*([^,\)]+),\s*style:\s*TextStyle\s*\([^}]*fontSize:\s*[^,}]+[^}]*\)\s*\)',
      ),
      'to': (Match m) => 'ResponsiveBody(${m.group(1)})',
    },
    // Simple fontSize property removal (will be handled by ResponsiveText)
    {
      'from': RegExp(r'fontSize:\s*\d+(?:\.\d+)?\s*,?\s*'),
      'to': (Match m) => '',
    },
  ];

  for (final replacement in specificReplacements) {
    final pattern = replacement['from'] as RegExp;
    final replacementFunc = replacement['to'] as String Function(Match);

    final matches = pattern.allMatches(modifiedContent).length;
    if (matches > 0) {
      modifiedContent = modifiedContent.replaceAllMapped(
        pattern,
        replacementFunc,
      );
      replacementCount += matches;
    }
  }

  // Replace TextOverflow.ellipsis
  modifiedContent = modifiedContent.replaceAll(
    'TextOverflow.ellipsis',
    'TextOverflow.visible',
  );
  if (content.contains('TextOverflow.ellipsis')) {
    replacementCount++;
  }

  // Replace hardcoded EdgeInsets with ResponsiveSpacing
  final edgeInsetsReplacements = [
    {
      'pattern': RegExp(r'EdgeInsets\.all\s*\(\s*(\d+(?:\.\d+)?)\s*\)'),
      'replacement':
          (Match match) =>
              'ResponsiveSpacing.getPadding(context, base: ${match.group(1)})',
    },
    {
      'pattern': RegExp(
        r'EdgeInsets\.symmetric\s*\(\s*horizontal:\s*(\d+(?:\.\d+)?)\s*\)',
      ),
      'replacement':
          (Match match) =>
              'ResponsiveSpacing.getPadding(context, base: ${match.group(1)})',
    },
    {
      'pattern': RegExp(
        r'EdgeInsets\.symmetric\s*\(\s*vertical:\s*(\d+(?:\.\d+)?)\s*\)',
      ),
      'replacement':
          (Match match) =>
              'ResponsiveSpacing.getPadding(context, base: ${match.group(1)})',
    },
  ];

  for (final replacement in edgeInsetsReplacements) {
    final pattern = replacement['pattern'] as RegExp;
    final replacementFunc =
        replacement['replacement'] as String Function(Match);

    final matches = pattern.allMatches(modifiedContent).length;
    modifiedContent = modifiedContent.replaceAllMapped(
      pattern,
      replacementFunc,
    );
    replacementCount += matches;
  }

  // Wrap Scaffold body with ResponsiveLayout if not already wrapped
  if (modifiedContent.contains('body:') &&
      !modifiedContent.contains('ResponsiveLayout') &&
      !modifiedContent.contains('SafeArea')) {
    modifiedContent = modifiedContent.replaceAllMapped(
      RegExp(r'body:\s*([^,\n]+)'),
      (match) {
        replacementCount++;
        return 'body: ResponsiveLayout(child: ${match.group(1)})';
      },
    );
  }

  // Replace common Text widgets with ResponsiveText variants
  final textReplacements = [
    // Simple Text widgets
    {
      'pattern': RegExp(r'Text\s*\(\s*([^,\)]+)\s*\)'),
      'replacement': (Match match) => 'ResponsiveBody(${match.group(1)})',
    },
  ];

  for (final replacement in textReplacements) {
    final pattern = replacement['pattern'] as RegExp;
    final replacementFunc =
        replacement['replacement'] as String Function(Match);

    // Only replace if it's not already a ResponsiveText widget
    if (!modifiedContent.contains('Responsive')) {
      final matches = pattern.allMatches(modifiedContent).length;
      modifiedContent = modifiedContent.replaceAllMapped(
        pattern,
        replacementFunc,
      );
      replacementCount += matches;
    }
  }

  // Write the modified content back to the file
  if (replacementCount > 0) {
    await file.writeAsString(modifiedContent);
  }

  return replacementCount;
}
