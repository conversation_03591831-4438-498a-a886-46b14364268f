#!/bin/bash

# Brevo Setup Script for Quarterlies App
# This script helps you set up Brevo for email functionality
# Free tier: 300 emails/day (9,000/month), no credit card required

echo "🚀 Brevo Setup for Quarterlies App"
echo "=================================="
echo ""
echo "Brevo (formerly Sendinblue) offers a generous free tier:"
echo "✅ 300 emails per day (9,000 per month)"
echo "✅ No credit card required"
echo "✅ Professional email templates"
echo "✅ Reliable delivery"
echo ""

# Check if user wants to proceed
echo "📧 Email Service Setup"
echo "====================="
echo "Would you like to set up Brevo now? (y/n)"
read -r SETUP_BREVO

if [ "$SETUP_BREVO" != "y" ] && [ "$SETUP_BREVO" != "Y" ]; then
    echo "Setup cancelled. You can run this script again anytime."
    exit 0
fi

echo ""
echo "📝 Step-by-step Brevo setup:"
echo "1. Go to https://www.brevo.com"
echo "2. Click 'Sign up free'"
echo "3. Create your account (no credit card required)"
echo "4. Verify your email address"
echo "5. Complete the onboarding process"
echo ""
echo "Press Enter when you've completed the account setup..."
read -r

echo ""
echo "🔑 Getting your API key:"
echo "1. In your Brevo dashboard, go to 'SMTP & API'"
echo "2. Click on 'API Keys'"
echo "3. Click 'Generate a new API key'"
echo "4. Give it a name like 'Quarterlies App'"
echo "5. Copy the API key"
echo ""
echo "Press Enter when you have your API key ready..."
read -r

echo ""
echo "📧 Configuration"
echo "==============="
read -p "Enter your Brevo API key: " BREVO_API_KEY
read -p "Enter your sender email (e.g., <EMAIL>): " FROM_EMAIL
read -p "Enter your sender name (e.g., Quarterlies): " FROM_NAME

# Validate API key by making a test call
echo ""
echo "🧪 Testing API key..."
TEST_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/brevo_test.json \
  -X GET \
  -H "api-key: $BREVO_API_KEY" \
  "https://api.brevo.com/v3/account")

if [ "$TEST_RESPONSE" = "200" ]; then
    echo "✅ API key is valid!"
    ACCOUNT_EMAIL=$(cat /tmp/brevo_test.json | grep -o '"email":"[^"]*"' | cut -d'"' -f4)
    echo "   Account email: $ACCOUNT_EMAIL"
else
    echo "❌ API key validation failed. Please check your API key."
    echo "   HTTP Status: $TEST_RESPONSE"
    exit 1
fi

# Clean up test file
rm -f /tmp/brevo_test.json

echo ""
echo "✅ Setup Complete!"
echo "=================="
echo ""
echo "📝 Your Brevo configuration:"
echo ""
echo "BREVO_API_KEY=$BREVO_API_KEY"
echo "FROM_EMAIL=$FROM_EMAIL"
echo "FROM_NAME=$FROM_NAME"
echo ""
echo "⚠️  IMPORTANT: Save these credentials securely!"
echo ""
echo "📧 Next steps:"
echo "1. Add these environment variables to Supabase Edge Functions"
echo "2. Run the deployment script: ./scripts/deploy_supabase.sh"
echo "3. Test email sending functionality"
echo ""
echo "💡 Tips:"
echo "• Your free tier includes 300 emails/day (9,000/month)"
echo "• Monitor your usage in the Brevo dashboard"
echo "• Verify your sender domain for better deliverability"
echo ""
echo "🎉 Brevo setup completed successfully!"
