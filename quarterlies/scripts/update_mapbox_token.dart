#!/usr/bin/env dart

import 'dart:io';

/// <PERSON><PERSON><PERSON> to update the Mapbox access token in the AddressService
/// Usage: dart scripts/update_mapbox_token.dart YOUR_MAPBOX_TOKEN

void main(List<String> args) {
  if (args.isEmpty) {
    stderr.writeln(
      'Usage: dart scripts/update_mapbox_token.dart YOUR_MAPBOX_TOKEN',
    );
    stderr.writeln(
      'Example: dart scripts/update_mapbox_token.dart pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV4YW1wbGUifQ.example',
    );
    exit(1);
  }

  final token = args[0];
  final addressServicePath = 'lib/services/address_service.dart';

  try {
    // Read the current file
    final file = File(addressServicePath);
    if (!file.existsSync()) {
      stderr.writeln('Error: $addressServicePath not found');
      exit(1);
    }

    String content = file.readAsStringSync();

    // Replace the placeholder token
    const placeholder = 'YOUR_MAPBOX_ACCESS_TOKEN';
    if (!content.contains(placeholder)) {
      stderr.writeln(
        'Warning: Placeholder token not found. The token may already be set.',
      );
      stderr.writeln('Looking for: $placeholder');
      exit(1);
    }

    content = content.replaceAll(placeholder, token);

    // Write the updated content back
    file.writeAsStringSync(content);

    stdout.writeln(
      '✅ Successfully updated Mapbox access token in $addressServicePath',
    );
    stdout.writeln(
      '🔒 Remember to keep your token secure and never commit it to version control',
    );
  } catch (e) {
    stderr.writeln('❌ Error updating token: $e');
    exit(1);
  }
}
