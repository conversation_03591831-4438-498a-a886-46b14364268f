#!/bin/bash

# Supabase Deployment Script for Quarterlies App
# This script deploys migrations and edge functions to Supabase

echo "🚀 Supabase Deployment for Quarterlies App"
echo "=========================================="
echo ""

# Configuration
PROJECT_REF="hoeagvrddekfmeqqkxca"
SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhvZWFndnJkZGVrZm1lcXFreGNhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI4MzI1MCwiZXhwIjoyMDYxODU5MjUwfQ.u_AmabaIzNdbBLlpj3rh_7tYQTvdL5-7HXKuEXSlsDo"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Installing..."
    npm install -g supabase
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Supabase CLI. Please install manually:"
        echo "   npm install -g supabase"
        exit 1
    fi
fi

echo "✅ Supabase CLI found"

# Check if we're in the right directory
if [ ! -d "supabase" ]; then
    echo "❌ Please run this script from the quarterlies directory"
    exit 1
fi

echo "✅ Found supabase directory"

# Link to the project
echo "🔗 Linking to Supabase project..."
supabase link --project-ref $PROJECT_REF

if [ $? -ne 0 ]; then
    echo "❌ Failed to link to Supabase project"
    exit 1
fi

echo "✅ Linked to Supabase project"

# Deploy database migrations
echo "📊 Deploying database migrations..."
supabase db push

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy database migrations"
    exit 1
fi

echo "✅ Database migrations deployed"

# Set up environment variables for edge functions
echo "🔧 Setting up environment variables..."

# Check if user has Brevo configured
echo ""
echo "📧 Brevo Email Configuration"
echo "============================"
echo "Do you have a Brevo account? (y/n)"
read -r HAS_BREVO

if [ "$HAS_BREVO" = "y" ] || [ "$HAS_BREVO" = "Y" ]; then
    echo "Please enter your Brevo configuration:"
    read -p "Brevo API Key: " BREVO_API_KEY
    read -p "From Email (e.g., <EMAIL>): " FROM_EMAIL
    read -p "From Name (e.g., Quarterlies): " FROM_NAME

    # Set environment variables
    supabase secrets set BREVO_API_KEY="$BREVO_API_KEY"
    supabase secrets set FROM_EMAIL="$FROM_EMAIL"
    supabase secrets set FROM_NAME="$FROM_NAME"

    echo "✅ Brevo environment variables set"
else
    echo "⚠️  Skipping Brevo configuration. Email functions will not work until configured."
    echo ""
    echo "📝 To set up Brevo (free - 300 emails/day):"
    echo "1. Go to https://www.brevo.com"
    echo "2. Create a free account (no credit card required)"
    echo "3. Go to SMTP & API > API Keys"
    echo "4. Create a new API key"
    echo "5. Re-run this script with your API key"
fi

# Set Supabase environment variables
echo "🔧 Setting Supabase environment variables..."
supabase secrets set SUPABASE_URL="https://hoeagvrddekfmeqqkxca.supabase.co"
supabase secrets set SUPABASE_SERVICE_ROLE_KEY="$SERVICE_ROLE_KEY"

echo "✅ Supabase environment variables set"

# Deploy edge functions
echo "📡 Deploying edge functions..."

# Deploy each function individually
FUNCTIONS=("send-signing-request-email" "send-signed-document-notification" "send-signed-document-to-customer" "send-signed-document-email")

for func in "${FUNCTIONS[@]}"; do
    echo "📡 Deploying function: $func"
    supabase functions deploy $func

    if [ $? -ne 0 ]; then
        echo "❌ Failed to deploy function: $func"
        exit 1
    fi

    echo "✅ Function deployed: $func"
done

echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo ""
echo "✅ Database migrations deployed"
echo "✅ Edge functions deployed"
echo "✅ Environment variables configured"
echo ""
echo "🔗 Your Supabase project: https://supabase.com/dashboard/project/$PROJECT_REF"
echo ""
echo "📝 Next steps:"
echo "1. Test the email functions in the Supabase dashboard"
echo "2. Verify database tables are created correctly"
echo "3. Test the app functionality"
echo ""

if [ "$HAS_BREVO" != "y" ] && [ "$HAS_BREVO" != "Y" ]; then
    echo "⚠️  Remember to configure Brevo for email functionality:"
    echo "   1. Go to https://www.brevo.com"
    echo "   2. Create a free account (no credit card required)"
    echo "   3. Get your API key and re-run this script"
    echo ""
fi

echo "🎉 All done! Your Quarterlies app server features are now deployed!"
