#!/usr/bin/env dart

import 'dart:io';

/// Final script to eliminate the last remaining fontSize issues

void main() async {
  print('🎯 Targeting final fontSize issues...\n');
  
  final problematicFiles = [
    'lib/screens/tax_export/tax_export_screen.dart',
    'lib/screens/settings/mileage_settings_section.dart',
    'lib/screens/settings/sync_settings_section.dart',
    'lib/screens/settings/about_section.dart',
    'lib/screens/settings/signature_settings_screen.dart',
    'lib/screens/dashboard_screen.dart',
    'lib/screens/invoices/invoice_item_form_screen.dart',
    'lib/screens/expenses/expense_conflict_screen.dart',
    'lib/screens/contracts/contract_detail_screen.dart',
    'lib/screens/document_signing/document_signing_screen.dart',
    'lib/screens/document_signing/create_signing_request_screen.dart',
    'lib/screens/onboarding/signature_step.dart',
    'lib/screens/onboarding/business_info_step.dart',
  ];
  
  var totalReplacements = 0;
  
  for (final filePath in problematicFiles) {
    final file = File(filePath);
    if (await file.exists()) {
      final replacements = await processFile(file);
      if (replacements > 0) {
        totalReplacements += replacements;
        print('✅ ${filePath.replaceAll('lib/screens/', '')}: $replacements fixes');
      }
    }
  }
  
  print('\n🎯 FINAL FONTSIZE CLEANUP COMPLETED');
  print('Total replacements made: $totalReplacements');
}

Future<int> processFile(File file) async {
  final content = await file.readAsString();
  var modifiedContent = content;
  var replacementCount = 0;

  // Very aggressive fontSize removal patterns
  final patterns = [
    // Any fontSize: followed by number
    RegExp(r'fontSize:\s*\d+(?:\.\d+)?\s*,?\s*'),
    // fontSize property in any context
    RegExp(r',\s*fontSize:\s*\d+(?:\.\d+)?'),
    // fontSize at start of TextStyle
    RegExp(r'TextStyle\s*\(\s*fontSize:\s*\d+(?:\.\d+)?\s*,?'),
  ];

  for (final pattern in patterns) {
    final matches = pattern.allMatches(modifiedContent).length;
    if (matches > 0) {
      modifiedContent = modifiedContent.replaceAll(pattern, '');
      replacementCount += matches;
    }
  }

  // Clean up empty TextStyle() and malformed syntax
  modifiedContent = modifiedContent.replaceAll(RegExp(r'TextStyle\s*\(\s*\)'), 'TextStyle()');
  modifiedContent = modifiedContent.replaceAll(RegExp(r'style:\s*TextStyle\s*\(\s*\)\s*,?'), '');
  modifiedContent = modifiedContent.replaceAll(RegExp(r',\s*,'), ',');
  modifiedContent = modifiedContent.replaceAll(RegExp(r',\s*\)'), ')');
  modifiedContent = modifiedContent.replaceAll(RegExp(r'\(\s*,'), '(');

  // Write the modified content back to the file
  if (replacementCount > 0) {
    await file.writeAsString(modifiedContent);
  }

  return replacementCount;
}
