// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in quarterlies/test/services/report_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:typed_data' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:quarterlies/models/models.dart' as _i2;
import 'package:quarterlies/services/data_repository.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeContract_0 extends _i1.SmartFake implements _i2.Contract {
  _FakeContract_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserSettings_1 extends _i1.SmartFake implements _i2.UserSettings {
  _FakeUserSettings_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DataRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockDataRepository extends _i1.Mock implements _i3.DataRepository {
  MockDataRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.SyncStatus> get syncStatus =>
      (super.noSuchMethod(
            Invocation.getter(#syncStatus),
            returnValue: _i4.Stream<_i2.SyncStatus>.empty(),
          )
          as _i4.Stream<_i2.SyncStatus>);

  @override
  _i4.Stream<bool> get connectionStatus =>
      (super.noSuchMethod(
            Invocation.getter(#connectionStatus),
            returnValue: _i4.Stream<bool>.empty(),
          )
          as _i4.Stream<bool>);

  @override
  _i4.Stream<bool> get networkStatus =>
      (super.noSuchMethod(
            Invocation.getter(#networkStatus),
            returnValue: _i4.Stream<bool>.empty(),
          )
          as _i4.Stream<bool>);

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> isOnline() =>
      (super.noSuchMethod(
            Invocation.method(#isOnline, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> syncData() =>
      (super.noSuchMethod(
            Invocation.method(#syncData, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<T>> search<T>({
    required String? keyword,
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#search, [], {
              #keyword: keyword,
              #filters: filters,
            }),
            returnValue: _i4.Future<List<T>>.value(<T>[]),
          )
          as _i4.Future<List<T>>);

  @override
  _i4.Future<void> addCustomer(_i2.Customer? customer) =>
      (super.noSuchMethod(
            Invocation.method(#addCustomer, [customer]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Customer>> getCustomers() =>
      (super.noSuchMethod(
            Invocation.method(#getCustomers, []),
            returnValue: _i4.Future<List<_i2.Customer>>.value(<_i2.Customer>[]),
          )
          as _i4.Future<List<_i2.Customer>>);

  @override
  _i4.Future<List<_i2.Customer>> getCustomersPaginated(
    int? page,
    int? pageSize, [
    String? searchQuery,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomersPaginated, [
              page,
              pageSize,
              searchQuery,
            ]),
            returnValue: _i4.Future<List<_i2.Customer>>.value(<_i2.Customer>[]),
          )
          as _i4.Future<List<_i2.Customer>>);

  @override
  _i4.Future<int> getCustomersCount([String? searchQuery]) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomersCount, [searchQuery]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<int> getJobsCount() =>
      (super.noSuchMethod(
            Invocation.method(#getJobsCount, []),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<List<_i2.Job>> getJobsPaginated(
    int? page,
    int? pageSize, [
    String? searchQuery,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getJobsPaginated, [page, pageSize, searchQuery]),
            returnValue: _i4.Future<List<_i2.Job>>.value(<_i2.Job>[]),
          )
          as _i4.Future<List<_i2.Job>>);

  @override
  _i4.Future<List<_i2.Invoice>> getInvoicesPaginated(
    int? page,
    int? pageSize, [
    String? searchQuery,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoicesPaginated, [
              page,
              pageSize,
              searchQuery,
            ]),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<List<_i2.Expense>> getExpensesPaginated(
    int? page,
    int? pageSize, [
    String? searchQuery,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getExpensesPaginated, [
              page,
              pageSize,
              searchQuery,
            ]),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<_i2.Customer?> getCustomerById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getCustomerById, [id]),
            returnValue: _i4.Future<_i2.Customer?>.value(),
          )
          as _i4.Future<_i2.Customer?>);

  @override
  _i4.Future<void> updateCustomer(_i2.Customer? customer) =>
      (super.noSuchMethod(
            Invocation.method(#updateCustomer, [customer]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteCustomer(String? customerId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteCustomer, [customerId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> addJob(_i2.Job? job) =>
      (super.noSuchMethod(
            Invocation.method(#addJob, [job]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Job>> getJobs() =>
      (super.noSuchMethod(
            Invocation.method(#getJobs, []),
            returnValue: _i4.Future<List<_i2.Job>>.value(<_i2.Job>[]),
          )
          as _i4.Future<List<_i2.Job>>);

  @override
  _i4.Future<_i2.Job?> getJobById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [id]),
            returnValue: _i4.Future<_i2.Job?>.value(),
          )
          as _i4.Future<_i2.Job?>);

  @override
  _i4.Future<void> updateJob(_i2.Job? job) =>
      (super.noSuchMethod(
            Invocation.method(#updateJob, [job]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteJob, [jobId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> addInvoice(_i2.Invoice? invoice) =>
      (super.noSuchMethod(
            Invocation.method(#addInvoice, [invoice]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Invoice>> getInvoices() =>
      (super.noSuchMethod(
            Invocation.method(#getInvoices, []),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<_i2.Invoice?> getInvoiceById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoiceById, [id]),
            returnValue: _i4.Future<_i2.Invoice?>.value(),
          )
          as _i4.Future<_i2.Invoice?>);

  @override
  _i4.Future<void> updateInvoice(_i2.Invoice? invoice) =>
      (super.noSuchMethod(
            Invocation.method(#updateInvoice, [invoice]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteInvoice(String? invoiceId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteInvoice, [invoiceId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> addPayment(_i2.Payment? payment) =>
      (super.noSuchMethod(
            Invocation.method(#addPayment, [payment]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Payment>> getPayments() =>
      (super.noSuchMethod(
            Invocation.method(#getPayments, []),
            returnValue: _i4.Future<List<_i2.Payment>>.value(<_i2.Payment>[]),
          )
          as _i4.Future<List<_i2.Payment>>);

  @override
  _i4.Future<List<_i2.Payment>> getPaymentsByInvoice(String? invoiceId) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentsByInvoice, [invoiceId]),
            returnValue: _i4.Future<List<_i2.Payment>>.value(<_i2.Payment>[]),
          )
          as _i4.Future<List<_i2.Payment>>);

  @override
  _i4.Future<List<_i2.Payment>> getPaymentsByJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentsByJob, [jobId]),
            returnValue: _i4.Future<List<_i2.Payment>>.value(<_i2.Payment>[]),
          )
          as _i4.Future<List<_i2.Payment>>);

  @override
  _i4.Future<void> addExpense(_i2.Expense? expense) =>
      (super.noSuchMethod(
            Invocation.method(#addExpense, [expense]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Expense>> getExpenses() =>
      (super.noSuchMethod(
            Invocation.method(#getExpenses, []),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Expense>> getExpensesByJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getExpensesByJob, [jobId]),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Expense>> getOverheadExpenses() =>
      (super.noSuchMethod(
            Invocation.method(#getOverheadExpenses, []),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Expense>> getOverheadExpensesByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getOverheadExpensesByDateRange, [
              startDate,
              endDate,
            ]),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Expense>> getExpensesByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getExpensesByDateRange, [startDate, endDate]),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Payment>> getPaymentsByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentsByDateRange, [startDate, endDate]),
            returnValue: _i4.Future<List<_i2.Payment>>.value(<_i2.Payment>[]),
          )
          as _i4.Future<List<_i2.Payment>>);

  @override
  _i4.Future<void> updatePayment(_i2.Payment? payment) =>
      (super.noSuchMethod(
            Invocation.method(#updatePayment, [payment]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deletePayment(String? paymentId) =>
      (super.noSuchMethod(
            Invocation.method(#deletePayment, [paymentId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateExpense(_i2.Expense? expense) =>
      (super.noSuchMethod(
            Invocation.method(#updateExpense, [expense]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> addTimeLog(_i2.TimeLog? timeLog) =>
      (super.noSuchMethod(
            Invocation.method(#addTimeLog, [timeLog]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.TimeLog>> getTimeLogs() =>
      (super.noSuchMethod(
            Invocation.method(#getTimeLogs, []),
            returnValue: _i4.Future<List<_i2.TimeLog>>.value(<_i2.TimeLog>[]),
          )
          as _i4.Future<List<_i2.TimeLog>>);

  @override
  _i4.Future<List<_i2.TimeLog>> getTimeLogsByJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getTimeLogsByJob, [jobId]),
            returnValue: _i4.Future<List<_i2.TimeLog>>.value(<_i2.TimeLog>[]),
          )
          as _i4.Future<List<_i2.TimeLog>>);

  @override
  _i4.Future<void> updateTimeLog(_i2.TimeLog? timeLog) =>
      (super.noSuchMethod(
            Invocation.method(#updateTimeLog, [timeLog]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteTimeLog(String? timeLogId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTimeLog, [timeLogId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<Map<String, double>> allocateOverheadExpenses({
    String? method = 'proportional_income',
    int? year,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#allocateOverheadExpenses, [], {
              #method: method,
              #year: year,
            }),
            returnValue: _i4.Future<Map<String, double>>.value(
              <String, double>{},
            ),
          )
          as _i4.Future<Map<String, double>>);

  @override
  _i4.Future<double> getTotalAllocatedOverheadForYear(int? year) =>
      (super.noSuchMethod(
            Invocation.method(#getTotalAllocatedOverheadForYear, [year]),
            returnValue: _i4.Future<double>.value(0.0),
          )
          as _i4.Future<double>);

  @override
  _i4.Future<Map<String, dynamic>> getJobCostSummary(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobCostSummary, [jobId]),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> getYearToDateFinancials(int? year) =>
      (super.noSuchMethod(
            Invocation.method(#getYearToDateFinancials, [year]),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> getQuarterlyFinancials(
    int? year,
    int? quarter,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getQuarterlyFinancials, [year, quarter]),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<List<_i2.Job>> getActiveJobs() =>
      (super.noSuchMethod(
            Invocation.method(#getActiveJobs, []),
            returnValue: _i4.Future<List<_i2.Job>>.value(<_i2.Job>[]),
          )
          as _i4.Future<List<_i2.Job>>);

  @override
  _i4.Future<List<_i2.Expense>> getExpensesSince(DateTime? startDate) =>
      (super.noSuchMethod(
            Invocation.method(#getExpensesSince, [startDate]),
            returnValue: _i4.Future<List<_i2.Expense>>.value(<_i2.Expense>[]),
          )
          as _i4.Future<List<_i2.Expense>>);

  @override
  _i4.Future<List<_i2.Invoice>> getInvoicesSince(DateTime? startDate) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoicesSince, [startDate]),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<List<_i2.Invoice>> getInvoicesByJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoicesByJob, [jobId]),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<_i2.Expense?> getExpenseById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getExpenseById, [id]),
            returnValue: _i4.Future<_i2.Expense?>.value(),
          )
          as _i4.Future<_i2.Expense?>);

  @override
  _i4.Future<void> deleteExpense(String? expenseId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteExpense, [expenseId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Estimate>> getEstimatesByStatus(String? status) =>
      (super.noSuchMethod(
            Invocation.method(#getEstimatesByStatus, [status]),
            returnValue: _i4.Future<List<_i2.Estimate>>.value(<_i2.Estimate>[]),
          )
          as _i4.Future<List<_i2.Estimate>>);

  @override
  _i4.Future<List<_i2.Invoice>> getInvoicesByStatus(String? status) =>
      (super.noSuchMethod(
            Invocation.method(#getInvoicesByStatus, [status]),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<List<_i2.Invoice>> getOverdueInvoices() =>
      (super.noSuchMethod(
            Invocation.method(#getOverdueInvoices, []),
            returnValue: _i4.Future<List<_i2.Invoice>>.value(<_i2.Invoice>[]),
          )
          as _i4.Future<List<_i2.Invoice>>);

  @override
  _i4.Future<void> addTaxPayment(_i2.TaxPayment? payment) =>
      (super.noSuchMethod(
            Invocation.method(#addTaxPayment, [payment]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateTaxPayment(_i2.TaxPayment? payment) =>
      (super.noSuchMethod(
            Invocation.method(#updateTaxPayment, [payment]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.TaxPayment>> getTaxPayments() =>
      (super.noSuchMethod(
            Invocation.method(#getTaxPayments, []),
            returnValue: _i4.Future<List<_i2.TaxPayment>>.value(
              <_i2.TaxPayment>[],
            ),
          )
          as _i4.Future<List<_i2.TaxPayment>>);

  @override
  _i4.Future<List<_i2.TaxPayment>> getTaxPaymentsByPeriod(String? taxPeriod) =>
      (super.noSuchMethod(
            Invocation.method(#getTaxPaymentsByPeriod, [taxPeriod]),
            returnValue: _i4.Future<List<_i2.TaxPayment>>.value(
              <_i2.TaxPayment>[],
            ),
          )
          as _i4.Future<List<_i2.TaxPayment>>);

  @override
  _i4.Future<List<_i2.TaxPayment>> getTaxPaymentsByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getTaxPaymentsByDateRange, [startDate, endDate]),
            returnValue: _i4.Future<List<_i2.TaxPayment>>.value(
              <_i2.TaxPayment>[],
            ),
          )
          as _i4.Future<List<_i2.TaxPayment>>);

  @override
  _i4.Future<_i2.TaxPayment?> getTaxPaymentById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getTaxPaymentById, [id]),
            returnValue: _i4.Future<_i2.TaxPayment?>.value(),
          )
          as _i4.Future<_i2.TaxPayment?>);

  @override
  _i4.Future<void> deleteTaxPayment(String? taxPaymentId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTaxPayment, [taxPaymentId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.TaxPayment>> getTaxPaymentsSince(DateTime? startDate) =>
      (super.noSuchMethod(
            Invocation.method(#getTaxPaymentsSince, [startDate]),
            returnValue: _i4.Future<List<_i2.TaxPayment>>.value(
              <_i2.TaxPayment>[],
            ),
          )
          as _i4.Future<List<_i2.TaxPayment>>);

  @override
  _i4.Future<void> createEstimateTemplate(
    _i2.Estimate? estimate,
    String? templateName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createEstimateTemplate, [
              estimate,
              templateName,
            ]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<String> getCurrentUserId() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUserId, []),
            returnValue: _i4.Future<String>.value(
              _i5.dummyValue<String>(
                this,
                Invocation.method(#getCurrentUserId, []),
              ),
            ),
          )
          as _i4.Future<String>);

  @override
  _i4.Future<void> addEstimate(_i2.Estimate? estimate) =>
      (super.noSuchMethod(
            Invocation.method(#addEstimate, [estimate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Estimate>> getEstimates() =>
      (super.noSuchMethod(
            Invocation.method(#getEstimates, []),
            returnValue: _i4.Future<List<_i2.Estimate>>.value(<_i2.Estimate>[]),
          )
          as _i4.Future<List<_i2.Estimate>>);

  @override
  _i4.Future<_i2.Estimate?> getEstimateById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getEstimateById, [id]),
            returnValue: _i4.Future<_i2.Estimate?>.value(),
          )
          as _i4.Future<_i2.Estimate?>);

  @override
  _i4.Future<List<_i2.Estimate>> getEstimatesByJobId(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getEstimatesByJobId, [jobId]),
            returnValue: _i4.Future<List<_i2.Estimate>>.value(<_i2.Estimate>[]),
          )
          as _i4.Future<List<_i2.Estimate>>);

  @override
  _i4.Future<List<_i2.Estimate>> getEstimateTemplates() =>
      (super.noSuchMethod(
            Invocation.method(#getEstimateTemplates, []),
            returnValue: _i4.Future<List<_i2.Estimate>>.value(<_i2.Estimate>[]),
          )
          as _i4.Future<List<_i2.Estimate>>);

  @override
  _i4.Future<void> updateEstimate(_i2.Estimate? estimate) =>
      (super.noSuchMethod(
            Invocation.method(#updateEstimate, [estimate]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteEstimate(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteEstimate, [id]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<double?> getMileageRate() =>
      (super.noSuchMethod(
            Invocation.method(#getMileageRate, []),
            returnValue: _i4.Future<double?>.value(),
          )
          as _i4.Future<double?>);

  @override
  _i4.Future<void> addContract(_i2.Contract? contract) =>
      (super.noSuchMethod(
            Invocation.method(#addContract, [contract]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.Contract>> getContracts() =>
      (super.noSuchMethod(
            Invocation.method(#getContracts, []),
            returnValue: _i4.Future<List<_i2.Contract>>.value(<_i2.Contract>[]),
          )
          as _i4.Future<List<_i2.Contract>>);

  @override
  _i4.Future<_i2.Contract?> getContractById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getContractById, [id]),
            returnValue: _i4.Future<_i2.Contract?>.value(),
          )
          as _i4.Future<_i2.Contract?>);

  @override
  _i4.Future<void> updateContract(_i2.Contract? contract) =>
      (super.noSuchMethod(
            Invocation.method(#updateContract, [contract]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.Contract> createContractFromEstimate(_i2.Estimate? estimate) =>
      (super.noSuchMethod(
            Invocation.method(#createContractFromEstimate, [estimate]),
            returnValue: _i4.Future<_i2.Contract>.value(
              _FakeContract_0(
                this,
                Invocation.method(#createContractFromEstimate, [estimate]),
              ),
            ),
          )
          as _i4.Future<_i2.Contract>);

  @override
  _i4.Future<void> addDocumentSigningRequest(
    _i2.DocumentSigningRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addDocumentSigningRequest, [request]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.DocumentSigningRequest>> getDocumentSigningRequests() =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentSigningRequests, []),
            returnValue: _i4.Future<List<_i2.DocumentSigningRequest>>.value(
              <_i2.DocumentSigningRequest>[],
            ),
          )
          as _i4.Future<List<_i2.DocumentSigningRequest>>);

  @override
  _i4.Future<_i2.DocumentSigningRequest?> getDocumentSigningRequestById(
    String? id,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getDocumentSigningRequestById, [id]),
            returnValue: _i4.Future<_i2.DocumentSigningRequest?>.value(),
          )
          as _i4.Future<_i2.DocumentSigningRequest?>);

  @override
  _i4.Future<void> addSignedDocument(_i2.SignedDocument? document) =>
      (super.noSuchMethod(
            Invocation.method(#addSignedDocument, [document]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.SignedDocument>> getSignedDocuments() =>
      (super.noSuchMethod(
            Invocation.method(#getSignedDocuments, []),
            returnValue: _i4.Future<List<_i2.SignedDocument>>.value(
              <_i2.SignedDocument>[],
            ),
          )
          as _i4.Future<List<_i2.SignedDocument>>);

  @override
  _i4.Future<_i2.SignedDocument?> getSignedDocumentById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getSignedDocumentById, [id]),
            returnValue: _i4.Future<_i2.SignedDocument?>.value(),
          )
          as _i4.Future<_i2.SignedDocument?>);

  @override
  _i4.Future<_i2.UserSettings> getUserSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getUserSettings, []),
            returnValue: _i4.Future<_i2.UserSettings>.value(
              _FakeUserSettings_1(
                this,
                Invocation.method(#getUserSettings, []),
              ),
            ),
          )
          as _i4.Future<_i2.UserSettings>);

  @override
  _i4.Future<void> updateUserSettings(_i2.UserSettings? settings) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserSettings, [settings]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.UserProfile?> getUserProfile() =>
      (super.noSuchMethod(
            Invocation.method(#getUserProfile, []),
            returnValue: _i4.Future<_i2.UserProfile?>.value(),
          )
          as _i4.Future<_i2.UserProfile?>);

  @override
  _i4.Future<void> createUserProfile(_i2.UserProfile? profile) =>
      (super.noSuchMethod(
            Invocation.method(#createUserProfile, [profile]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateUserProfile(_i2.UserProfile? profile) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserProfile, [profile]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> isOnboardingComplete() =>
      (super.noSuchMethod(
            Invocation.method(#isOnboardingComplete, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> storeUserSignatureLocally(_i6.Uint8List? signatureBytes) =>
      (super.noSuchMethod(
            Invocation.method(#storeUserSignatureLocally, [signatureBytes]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i6.Uint8List?> getUserSignatureBytes() =>
      (super.noSuchMethod(
            Invocation.method(#getUserSignatureBytes, []),
            returnValue: _i4.Future<_i6.Uint8List?>.value(),
          )
          as _i4.Future<_i6.Uint8List?>);

  @override
  _i4.Future<bool> hasUserSignature() =>
      (super.noSuchMethod(
            Invocation.method(#hasUserSignature, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);
}
