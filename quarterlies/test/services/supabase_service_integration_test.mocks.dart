// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in quarterlies/test/services/supabase_service_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoTrueAdminApi_9 extends _i1.SmartFake
    implements _i2.GoTrueAdminApi {
  _FakeGoTrueAdminApi_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoTrueMFAApi_10 extends _i1.SmartFake implements _i2.GoTrueMFAApi {
  _FakeGoTrueMFAApi_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthResponse_11 extends _i1.SmartFake implements _i2.AuthResponse {
  _FakeAuthResponse_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeOAuthResponse_12 extends _i1.SmartFake implements _i2.OAuthResponse {
  _FakeOAuthResponse_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthSessionUrlResponse_13 extends _i1.SmartFake
    implements _i2.AuthSessionUrlResponse {
  _FakeAuthSessionUrlResponse_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResendResponse_14 extends _i1.SmartFake
    implements _i2.ResendResponse {
  _FakeResendResponse_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserResponse_15 extends _i1.SmartFake implements _i2.UserResponse {
  _FakeUserResponse_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeObject_16 extends _i1.SmartFake implements Object {
  _FakeObject_16(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestQueryBuilder_17<T> extends _i1.SmartFake
    implements _i2.PostgrestQueryBuilder<T> {
  _FakePostgrestQueryBuilder_17(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions =>
      (super.noSuchMethod(
            Invocation.getter(#functions),
            returnValue: _FakeFunctionsClient_0(
              this,
              Invocation.getter(#functions),
            ),
          )
          as _i2.FunctionsClient);

  @override
  _i2.SupabaseStorageClient get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeSupabaseStorageClient_1(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i2.SupabaseStorageClient);

  @override
  _i2.RealtimeClient get realtime =>
      (super.noSuchMethod(
            Invocation.getter(#realtime),
            returnValue: _FakeRealtimeClient_2(
              this,
              Invocation.getter(#realtime),
            ),
          )
          as _i2.RealtimeClient);

  @override
  _i2.PostgrestClient get rest =>
      (super.noSuchMethod(
            Invocation.getter(#rest),
            returnValue: _FakePostgrestClient_3(this, Invocation.getter(#rest)),
          )
          as _i2.PostgrestClient);

  @override
  Map<String, String> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  _i2.GoTrueClient get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeGoTrueClient_4(this, Invocation.getter(#auth)),
          )
          as _i2.GoTrueClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
    Invocation.setter(#functions, _functions),
    returnValueForMissingStub: null,
  );

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
    Invocation.setter(#storage, _storage),
    returnValueForMissingStub: null,
  );

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
    Invocation.setter(#realtime, _realtime),
    returnValueForMissingStub: null,
  );

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
    Invocation.setter(#rest, _rest),
    returnValueForMissingStub: null,
  );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
    Invocation.setter(#headers, headers),
    returnValueForMissingStub: null,
  );

  @override
  _i2.SupabaseQueryBuilder from(String? table) =>
      (super.noSuchMethod(
            Invocation.method(#from, [table]),
            returnValue: _FakeSupabaseQueryBuilder_5(
              this,
              Invocation.method(#from, [table]),
            ),
          )
          as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) =>
      (super.noSuchMethod(
            Invocation.method(#schema, [schema]),
            returnValue: _FakeSupabaseQuerySchema_6(
              this,
              Invocation.method(#schema, [schema]),
            ),
          )
          as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#channel, [name], {#opts: opts}),
            returnValue: _FakeRealtimeChannel_8(
              this,
              Invocation.method(#channel, [name], {#opts: opts}),
            ),
          )
          as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() =>
      (super.noSuchMethod(
            Invocation.method(#getChannels, []),
            returnValue: <_i2.RealtimeChannel>[],
          )
          as List<_i2.RealtimeChannel>);

  @override
  _i3.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
            Invocation.method(#removeChannel, [channel]),
            returnValue: _i3.Future<String>.value(
              _i4.dummyValue<String>(
                this,
                Invocation.method(#removeChannel, [channel]),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<List<String>> removeAllChannels() =>
      (super.noSuchMethod(
            Invocation.method(#removeAllChannels, []),
            returnValue: _i3.Future<List<String>>.value(<String>[]),
          )
          as _i3.Future<List<String>>);

  @override
  _i3.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

/// A class which mocks [GoTrueClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoTrueClient extends _i1.Mock implements _i2.GoTrueClient {
  MockGoTrueClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.GoTrueAdminApi get admin =>
      (super.noSuchMethod(
            Invocation.getter(#admin),
            returnValue: _FakeGoTrueAdminApi_9(this, Invocation.getter(#admin)),
          )
          as _i2.GoTrueAdminApi);

  @override
  _i2.GoTrueMFAApi get mfa =>
      (super.noSuchMethod(
            Invocation.getter(#mfa),
            returnValue: _FakeGoTrueMFAApi_10(this, Invocation.getter(#mfa)),
          )
          as _i2.GoTrueMFAApi);

  @override
  _i3.Stream<_i2.AuthState> get onAuthStateChange =>
      (super.noSuchMethod(
            Invocation.getter(#onAuthStateChange),
            returnValue: _i3.Stream<_i2.AuthState>.empty(),
          )
          as _i3.Stream<_i2.AuthState>);

  @override
  _i3.Stream<_i2.AuthState> get onAuthStateChangeSync =>
      (super.noSuchMethod(
            Invocation.getter(#onAuthStateChangeSync),
            returnValue: _i3.Stream<_i2.AuthState>.empty(),
          )
          as _i3.Stream<_i2.AuthState>);

  @override
  Map<String, String> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  set admin(_i2.GoTrueAdminApi? _admin) => super.noSuchMethod(
    Invocation.setter(#admin, _admin),
    returnValueForMissingStub: null,
  );

  @override
  set mfa(_i2.GoTrueMFAApi? _mfa) => super.noSuchMethod(
    Invocation.setter(#mfa, _mfa),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<_i2.AuthResponse> signInAnonymously({
    Map<String, dynamic>? data,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInAnonymously, [], {
              #data: data,
              #captchaToken: captchaToken,
            }),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#signInAnonymously, [], {
                  #data: data,
                  #captchaToken: captchaToken,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<_i2.AuthResponse> signUp({
    String? email,
    String? phone,
    required String? password,
    String? emailRedirectTo,
    Map<String, dynamic>? data,
    String? captchaToken,
    _i2.OtpChannel? channel = _i2.OtpChannel.sms,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUp, [], {
              #email: email,
              #phone: phone,
              #password: password,
              #emailRedirectTo: emailRedirectTo,
              #data: data,
              #captchaToken: captchaToken,
              #channel: channel,
            }),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#signUp, [], {
                  #email: email,
                  #phone: phone,
                  #password: password,
                  #emailRedirectTo: emailRedirectTo,
                  #data: data,
                  #captchaToken: captchaToken,
                  #channel: channel,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<_i2.AuthResponse> signInWithPassword({
    String? email,
    String? phone,
    required String? password,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPassword, [], {
              #email: email,
              #phone: phone,
              #password: password,
              #captchaToken: captchaToken,
            }),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#signInWithPassword, [], {
                  #email: email,
                  #phone: phone,
                  #password: password,
                  #captchaToken: captchaToken,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<_i2.OAuthResponse> getOAuthSignInUrl({
    required _i2.OAuthProvider? provider,
    String? redirectTo,
    String? scopes,
    Map<String, String>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getOAuthSignInUrl, [], {
              #provider: provider,
              #redirectTo: redirectTo,
              #scopes: scopes,
              #queryParams: queryParams,
            }),
            returnValue: _i3.Future<_i2.OAuthResponse>.value(
              _FakeOAuthResponse_12(
                this,
                Invocation.method(#getOAuthSignInUrl, [], {
                  #provider: provider,
                  #redirectTo: redirectTo,
                  #scopes: scopes,
                  #queryParams: queryParams,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.OAuthResponse>);

  @override
  _i3.Future<_i2.AuthSessionUrlResponse> exchangeCodeForSession(
    String? authCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#exchangeCodeForSession, [authCode]),
            returnValue: _i3.Future<_i2.AuthSessionUrlResponse>.value(
              _FakeAuthSessionUrlResponse_13(
                this,
                Invocation.method(#exchangeCodeForSession, [authCode]),
              ),
            ),
          )
          as _i3.Future<_i2.AuthSessionUrlResponse>);

  @override
  _i3.Future<_i2.AuthResponse> signInWithIdToken({
    required _i2.OAuthProvider? provider,
    required String? idToken,
    String? accessToken,
    String? nonce,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithIdToken, [], {
              #provider: provider,
              #idToken: idToken,
              #accessToken: accessToken,
              #nonce: nonce,
              #captchaToken: captchaToken,
            }),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#signInWithIdToken, [], {
                  #provider: provider,
                  #idToken: idToken,
                  #accessToken: accessToken,
                  #nonce: nonce,
                  #captchaToken: captchaToken,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<void> signInWithOtp({
    String? email,
    String? phone,
    String? emailRedirectTo,
    bool? shouldCreateUser,
    Map<String, dynamic>? data,
    String? captchaToken,
    _i2.OtpChannel? channel = _i2.OtpChannel.sms,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithOtp, [], {
              #email: email,
              #phone: phone,
              #emailRedirectTo: emailRedirectTo,
              #shouldCreateUser: shouldCreateUser,
              #data: data,
              #captchaToken: captchaToken,
              #channel: channel,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.AuthResponse> verifyOTP({
    String? email,
    String? phone,
    String? token,
    required _i2.OtpType? type,
    String? redirectTo,
    String? captchaToken,
    String? tokenHash,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyOTP, [], {
              #email: email,
              #phone: phone,
              #token: token,
              #type: type,
              #redirectTo: redirectTo,
              #captchaToken: captchaToken,
              #tokenHash: tokenHash,
            }),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#verifyOTP, [], {
                  #email: email,
                  #phone: phone,
                  #token: token,
                  #type: type,
                  #redirectTo: redirectTo,
                  #captchaToken: captchaToken,
                  #tokenHash: tokenHash,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<String> getSSOSignInUrl({
    String? providerId,
    String? domain,
    String? redirectTo,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getSSOSignInUrl, [], {
              #providerId: providerId,
              #domain: domain,
              #redirectTo: redirectTo,
              #captchaToken: captchaToken,
            }),
            returnValue: _i3.Future<String>.value(
              _i4.dummyValue<String>(
                this,
                Invocation.method(#getSSOSignInUrl, [], {
                  #providerId: providerId,
                  #domain: domain,
                  #redirectTo: redirectTo,
                  #captchaToken: captchaToken,
                }),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<_i2.AuthResponse> refreshSession([String? refreshToken]) =>
      (super.noSuchMethod(
            Invocation.method(#refreshSession, [refreshToken]),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#refreshSession, [refreshToken]),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<void> reauthenticate() =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticate, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.ResendResponse> resend({
    String? email,
    String? phone,
    required _i2.OtpType? type,
    String? emailRedirectTo,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resend, [], {
              #email: email,
              #phone: phone,
              #type: type,
              #emailRedirectTo: emailRedirectTo,
              #captchaToken: captchaToken,
            }),
            returnValue: _i3.Future<_i2.ResendResponse>.value(
              _FakeResendResponse_14(
                this,
                Invocation.method(#resend, [], {
                  #email: email,
                  #phone: phone,
                  #type: type,
                  #emailRedirectTo: emailRedirectTo,
                  #captchaToken: captchaToken,
                }),
              ),
            ),
          )
          as _i3.Future<_i2.ResendResponse>);

  @override
  _i3.Future<_i2.UserResponse> getUser([String? jwt]) =>
      (super.noSuchMethod(
            Invocation.method(#getUser, [jwt]),
            returnValue: _i3.Future<_i2.UserResponse>.value(
              _FakeUserResponse_15(this, Invocation.method(#getUser, [jwt])),
            ),
          )
          as _i3.Future<_i2.UserResponse>);

  @override
  _i3.Future<_i2.UserResponse> updateUser(
    _i2.UserAttributes? attributes, {
    String? emailRedirectTo,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #updateUser,
              [attributes],
              {#emailRedirectTo: emailRedirectTo},
            ),
            returnValue: _i3.Future<_i2.UserResponse>.value(
              _FakeUserResponse_15(
                this,
                Invocation.method(
                  #updateUser,
                  [attributes],
                  {#emailRedirectTo: emailRedirectTo},
                ),
              ),
            ),
          )
          as _i3.Future<_i2.UserResponse>);

  @override
  _i3.Future<_i2.AuthResponse> setSession(String? refreshToken) =>
      (super.noSuchMethod(
            Invocation.method(#setSession, [refreshToken]),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#setSession, [refreshToken]),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  _i3.Future<_i2.AuthSessionUrlResponse> getSessionFromUrl(
    Uri? originUrl, {
    bool? storeSession = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getSessionFromUrl,
              [originUrl],
              {#storeSession: storeSession},
            ),
            returnValue: _i3.Future<_i2.AuthSessionUrlResponse>.value(
              _FakeAuthSessionUrlResponse_13(
                this,
                Invocation.method(
                  #getSessionFromUrl,
                  [originUrl],
                  {#storeSession: storeSession},
                ),
              ),
            ),
          )
          as _i3.Future<_i2.AuthSessionUrlResponse>);

  @override
  _i3.Future<void> signOut({
    _i2.SignOutScope? scope = _i2.SignOutScope.local,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signOut, [], {#scope: scope}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> resetPasswordForEmail(
    String? email, {
    String? redirectTo,
    String? captchaToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #resetPasswordForEmail,
              [email],
              {#redirectTo: redirectTo, #captchaToken: captchaToken},
            ),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<List<_i2.UserIdentity>> getUserIdentities() =>
      (super.noSuchMethod(
            Invocation.method(#getUserIdentities, []),
            returnValue: _i3.Future<List<_i2.UserIdentity>>.value(
              <_i2.UserIdentity>[],
            ),
          )
          as _i3.Future<List<_i2.UserIdentity>>);

  @override
  _i3.Future<_i2.OAuthResponse> getLinkIdentityUrl(
    _i2.OAuthProvider? provider, {
    String? redirectTo,
    String? scopes,
    Map<String, String>? queryParams,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getLinkIdentityUrl,
              [provider],
              {
                #redirectTo: redirectTo,
                #scopes: scopes,
                #queryParams: queryParams,
              },
            ),
            returnValue: _i3.Future<_i2.OAuthResponse>.value(
              _FakeOAuthResponse_12(
                this,
                Invocation.method(
                  #getLinkIdentityUrl,
                  [provider],
                  {
                    #redirectTo: redirectTo,
                    #scopes: scopes,
                    #queryParams: queryParams,
                  },
                ),
              ),
            ),
          )
          as _i3.Future<_i2.OAuthResponse>);

  @override
  _i3.Future<void> unlinkIdentity(_i2.UserIdentity? identity) =>
      (super.noSuchMethod(
            Invocation.method(#unlinkIdentity, [identity]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> setInitialSession(String? jsonStr) =>
      (super.noSuchMethod(
            Invocation.method(#setInitialSession, [jsonStr]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i2.AuthResponse> recoverSession(String? jsonStr) =>
      (super.noSuchMethod(
            Invocation.method(#recoverSession, [jsonStr]),
            returnValue: _i3.Future<_i2.AuthResponse>.value(
              _FakeAuthResponse_11(
                this,
                Invocation.method(#recoverSession, [jsonStr]),
              ),
            ),
          )
          as _i3.Future<_i2.AuthResponse>);

  @override
  void startAutoRefresh() => super.noSuchMethod(
    Invocation.method(#startAutoRefresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void stopAutoRefresh() => super.noSuchMethod(
    Invocation.method(#stopAutoRefresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyAllSubscribers(
    _i2.AuthChangeEvent? event, {
    _i2.Session? session,
    bool? broadcast = true,
  }) => super.noSuchMethod(
    Invocation.method(
      #notifyAllSubscribers,
      [event],
      {#session: session, #broadcast: broadcast},
    ),
    returnValueForMissingStub: null,
  );

  @override
  Object notifyException(Object? exception, [StackTrace? stackTrace]) =>
      (super.noSuchMethod(
            Invocation.method(#notifyException, [exception, stackTrace]),
            returnValue: _FakeObject_16(
              this,
              Invocation.method(#notifyException, [exception, stackTrace]),
            ),
          )
          as Object);
}

/// A class which mocks [PostgrestClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockPostgrestClient extends _i1.Mock implements _i2.PostgrestClient {
  MockPostgrestClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get url =>
      (super.noSuchMethod(
            Invocation.getter(#url),
            returnValue: _i4.dummyValue<String>(this, Invocation.getter(#url)),
          )
          as String);

  @override
  Map<String, String> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  _i2.PostgrestClient auth(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#auth, [token]),
            returnValue: _FakePostgrestClient_3(
              this,
              Invocation.method(#auth, [token]),
            ),
          )
          as _i2.PostgrestClient);

  @override
  _i2.PostgrestClient setAuth(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#setAuth, [token]),
            returnValue: _FakePostgrestClient_3(
              this,
              Invocation.method(#setAuth, [token]),
            ),
          )
          as _i2.PostgrestClient);

  @override
  _i2.PostgrestQueryBuilder<void> from(String? table) =>
      (super.noSuchMethod(
            Invocation.method(#from, [table]),
            returnValue: _FakePostgrestQueryBuilder_17<void>(
              this,
              Invocation.method(#from, [table]),
            ),
          )
          as _i2.PostgrestQueryBuilder<void>);

  @override
  _i2.PostgrestClient schema(String? schema) =>
      (super.noSuchMethod(
            Invocation.method(#schema, [schema]),
            returnValue: _FakePostgrestClient_3(
              this,
              Invocation.method(#schema, [schema]),
            ),
          )
          as _i2.PostgrestClient);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<dynamic, dynamic>? params,
    bool? get = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i3.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}
