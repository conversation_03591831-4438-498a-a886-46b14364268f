import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/models.dart';

void main() {
  group('Business Logic Calculation Tests', () {
    // These tests focus on calculation logic that can be tested independently
    // without complex service mocking

    group('Job cost calculations', () {
      test('should calculate direct expenses correctly', () {
        // Arrange
        final expenses = [
          Expense(
            userId: 'user-1',
            jobId: 'job-1',
            description: 'Materials',
            amount: 500.0,
            date: DateTime.now(),
            category: 'Materials',
          ),
          Expense(
            userId: 'user-1',
            jobId: 'job-1',
            description: 'Equipment',
            amount: 300.0,
            date: DateTime.now(),
            category: 'Equipment',
          ),
          Expense(
            userId: 'user-1',
            jobId: 'job-1',
            description: 'Tools',
            amount: 150.0,
            date: DateTime.now(),
            category: 'Tools',
          ),
        ];

        // Act
        final totalExpenses = expenses.fold(
          0.0,
          (sum, expense) => sum + expense.amount,
        );

        // Assert
        expect(totalExpenses, equals(950.0)); // 500 + 300 + 150
      });

      test('should calculate labor costs correctly', () {
        // Arrange
        final timeLogs = [
          TimeLog(
            userId: 'user-1',
            jobId: 'job-1',
            date: DateTime.now(),
            hours: 8.0,
            hourlyRate: 75.0,
            laborCost: 600.0,
            isFlatRate: false,
          ),
          TimeLog(
            userId: 'user-1',
            jobId: 'job-1',
            date: DateTime.now(),
            hours: 6.0,
            hourlyRate: 80.0,
            laborCost: 480.0,
            isFlatRate: false,
          ),
          TimeLog(
            userId: 'user-1',
            jobId: 'job-1',
            date: DateTime.now(),
            hours: 0.0,
            hourlyRate: 0.0,
            laborCost: 500.0,
            isFlatRate: true,
          ),
        ];

        // Act
        final totalLaborCost = timeLogs.fold(
          0.0,
          (sum, log) => sum + log.laborCost,
        );
        final totalHours = timeLogs.fold(0.0, (sum, log) {
          return log.isFlatRate ? sum : sum + log.hours;
        });

        // Assert
        expect(totalLaborCost, equals(1580.0)); // 600 + 480 + 500
        expect(totalHours, equals(14.0)); // 8 + 6 (excluding flat rate)
      });

      test('should calculate total income correctly', () {
        // Arrange
        final payments = [
          Payment(
            userId: 'user-1',
            jobId: 'job-1',
            amountReceived: 2000.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Check',
          ),
          Payment(
            userId: 'user-1',
            jobId: 'job-1',
            amountReceived: 1500.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Bank Transfer',
          ),
          Payment(
            userId: 'user-1',
            jobId: 'job-1',
            amountReceived: 800.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Cash',
          ),
        ];

        // Act
        final totalIncome = payments.fold(
          0.0,
          (sum, payment) => sum + payment.amountReceived,
        );

        // Assert
        expect(totalIncome, equals(4300.0)); // 2000 + 1500 + 800
      });

      test('should calculate profit margin correctly', () {
        // Arrange
        const totalIncome = 10000.0;
        const totalExpenses = 3000.0;
        const totalLaborCost = 2000.0;

        // Act
        final netProfit = totalIncome - totalExpenses - totalLaborCost;
        final profitMargin =
            totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0.0;

        // Assert
        expect(netProfit, equals(5000.0)); // 10000 - 3000 - 2000
        expect(profitMargin, equals(50.0)); // 50% profit margin
      });

      test('should handle zero income for profit margin calculation', () {
        // Arrange
        const totalIncome = 0.0;
        const totalExpenses = 1000.0;
        const totalLaborCost = 500.0;

        // Act
        final netProfit = totalIncome - totalExpenses - totalLaborCost;
        final profitMargin =
            totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0.0;

        // Assert
        expect(netProfit, equals(-1500.0)); // Loss
        expect(profitMargin, equals(0.0)); // Avoid division by zero
      });
    });

    group('Expense categorization', () {
      test('should categorize expenses correctly', () {
        // Arrange
        final expenses = [
          Expense(
            userId: 'user-1',
            description: 'Paper and pens',
            amount: 50.0,
            date: DateTime.now(),
            category: 'Office Supplies',
          ),
          Expense(
            userId: 'user-1',
            description: 'Printer',
            amount: 300.0,
            date: DateTime.now(),
            category: 'Office Supplies',
          ),
          Expense(
            userId: 'user-1',
            description: 'Laptop',
            amount: 1500.0,
            date: DateTime.now(),
            category: 'Equipment',
          ),
          Expense(
            userId: 'user-1',
            description: 'Flight to conference',
            amount: 400.0,
            date: DateTime.now(),
            category: 'Travel',
          ),
        ];

        // Act
        final categoryTotals = <String, double>{};
        for (final expense in expenses) {
          final category = expense.category ?? 'Uncategorized';
          categoryTotals[category] =
              (categoryTotals[category] ?? 0.0) + expense.amount;
        }

        // Assert
        expect(categoryTotals['Office Supplies'], equals(350.0)); // 50 + 300
        expect(categoryTotals['Equipment'], equals(1500.0));
        expect(categoryTotals['Travel'], equals(400.0));

        final totalExpenses = categoryTotals.values.fold(
          0.0,
          (sum, amount) => sum + amount,
        );
        expect(totalExpenses, equals(2250.0));
      });
    });

    group('Time log aggregations', () {
      test('should aggregate time logs correctly', () {
        // Arrange
        final timeLogs = [
          TimeLog(
            userId: 'user-1',
            jobId: 'job-1',
            date: DateTime.now(),
            hours: 8.0,
            hourlyRate: 75.0,
            laborCost: 600.0,
            isFlatRate: false,
          ),
          TimeLog(
            userId: 'user-1',
            jobId: 'job-1',
            date: DateTime.now(),
            hours: 6.5,
            hourlyRate: 80.0,
            laborCost: 520.0,
            isFlatRate: false,
          ),
          TimeLog(
            userId: 'user-1',
            jobId: 'job-2',
            date: DateTime.now(),
            hours: 0.0,
            hourlyRate: 0.0,
            laborCost: 1000.0,
            isFlatRate: true,
          ),
        ];

        // Act
        final totalHours = timeLogs.fold(0.0, (sum, log) {
          return log.isFlatRate ? sum : sum + log.hours;
        });

        final totalLaborCost = timeLogs.fold(0.0, (sum, log) {
          return sum + log.laborCost;
        });

        final averageHourlyRate =
            totalHours > 0
                ? (timeLogs
                        .where((log) => !log.isFlatRate)
                        .fold(0.0, (sum, log) => sum + log.laborCost)) /
                    totalHours
                : 0.0;

        // Assert
        expect(totalHours, equals(14.5)); // 8 + 6.5 (excluding flat rate)
        expect(totalLaborCost, equals(2120.0)); // 600 + 520 + 1000
        expect(averageHourlyRate, closeTo(77.24, 0.01)); // (600 + 520) / 14.5
      });
    });

    group('Payment aggregations', () {
      test('should aggregate payments by method', () {
        // Arrange
        final payments = [
          Payment(
            userId: 'user-1',
            amountReceived: 1000.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Check',
          ),
          Payment(
            userId: 'user-1',
            amountReceived: 1500.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Check',
          ),
          Payment(
            userId: 'user-1',
            amountReceived: 2000.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Bank Transfer',
          ),
          Payment(
            userId: 'user-1',
            amountReceived: 500.0,
            paymentDate: DateTime.now(),
            paymentMethod: 'Cash',
          ),
        ];

        // Act
        final paymentsByMethod = <String, double>{};
        for (final payment in payments) {
          paymentsByMethod[payment.paymentMethod] =
              (paymentsByMethod[payment.paymentMethod] ?? 0.0) +
              payment.amountReceived;
        }

        final totalPayments = payments.fold(
          0.0,
          (sum, payment) => sum + payment.amountReceived,
        );

        // Assert
        expect(paymentsByMethod['Check'], equals(2500.0)); // 1000 + 1500
        expect(paymentsByMethod['Bank Transfer'], equals(2000.0));
        expect(paymentsByMethod['Cash'], equals(500.0));
        expect(totalPayments, equals(5000.0));
      });
    });
  });
}
