// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in quarterlies/test/services/voice_recording_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;
import 'dart:typed_data' as _i6;

import 'package:flutter_sound/flutter_sound.dart' as _i4;
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart'
    as _i5;
import 'package:logger/logger.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:speech_to_text/speech_to_text.dart' as _i8;
import 'package:speech_to_text_platform_interface/speech_to_text_platform_interface.dart'
    as _i10;
import 'package:supabase/supabase.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLogger_0 extends _i1.SmartFake implements _i2.Logger {
  _FakeLogger_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDuration_1 extends _i1.SmartFake implements Duration {
  _FakeDuration_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFunctionsClient_2 extends _i1.SmartFake
    implements _i3.FunctionsClient {
  _FakeFunctionsClient_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseStorageClient_3 extends _i1.SmartFake
    implements _i3.SupabaseStorageClient {
  _FakeSupabaseStorageClient_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeClient_4 extends _i1.SmartFake
    implements _i3.RealtimeClient {
  _FakeRealtimeClient_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestClient_5 extends _i1.SmartFake
    implements _i3.PostgrestClient {
  _FakePostgrestClient_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoTrueClient_6 extends _i1.SmartFake implements _i3.GoTrueClient {
  _FakeGoTrueClient_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQueryBuilder_7 extends _i1.SmartFake
    implements _i3.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQuerySchema_8 extends _i1.SmartFake
    implements _i3.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestFilterBuilder_9<T1> extends _i1.SmartFake
    implements _i3.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeChannel_10 extends _i1.SmartFake
    implements _i3.RealtimeChannel {
  _FakeRealtimeChannel_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FlutterSoundRecorder].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterSoundRecorder extends _i1.Mock
    implements _i4.FlutterSoundRecorder {
  MockFlutterSoundRecorder() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.Logger get logger =>
      (super.noSuchMethod(
            Invocation.getter(#logger),
            returnValue: _FakeLogger_0(this, Invocation.getter(#logger)),
          )
          as _i2.Logger);

  @override
  _i5.RecorderState get recorderState =>
      (super.noSuchMethod(
            Invocation.getter(#recorderState),
            returnValue: _i5.RecorderState.isStopped,
          )
          as _i5.RecorderState);

  @override
  bool get isRecording =>
      (super.noSuchMethod(Invocation.getter(#isRecording), returnValue: false)
          as bool);

  @override
  bool get isStopped =>
      (super.noSuchMethod(Invocation.getter(#isStopped), returnValue: false)
          as bool);

  @override
  bool get isPaused =>
      (super.noSuchMethod(Invocation.getter(#isPaused), returnValue: false)
          as bool);

  @override
  void setLogLevel(_i2.Level? aLevel) => super.noSuchMethod(
    Invocation.method(#setLogLevel, [aLevel]),
    returnValueForMissingStub: null,
  );

  @override
  void interleavedRecording({required _i6.Uint8List? data}) =>
      super.noSuchMethod(
        Invocation.method(#interleavedRecording, [], {#data: data}),
        returnValueForMissingStub: null,
      );

  @override
  void recordingDataFloat32({required List<_i6.Float32List>? data}) =>
      super.noSuchMethod(
        Invocation.method(#recordingDataFloat32, [], {#data: data}),
        returnValueForMissingStub: null,
      );

  @override
  void recordingDataInt16({required List<_i6.Int16List>? data}) =>
      super.noSuchMethod(
        Invocation.method(#recordingDataInt16, [], {#data: data}),
        returnValueForMissingStub: null,
      );

  @override
  void updateRecorderProgress({int? duration, double? dbPeakLevel}) =>
      super.noSuchMethod(
        Invocation.method(#updateRecorderProgress, [], {
          #duration: duration,
          #dbPeakLevel: dbPeakLevel,
        }),
        returnValueForMissingStub: null,
      );

  @override
  void openRecorderCompleted(int? state, bool? success) => super.noSuchMethod(
    Invocation.method(#openRecorderCompleted, [state, success]),
    returnValueForMissingStub: null,
  );

  @override
  void pauseRecorderCompleted(int? state, bool? success) => super.noSuchMethod(
    Invocation.method(#pauseRecorderCompleted, [state, success]),
    returnValueForMissingStub: null,
  );

  @override
  void resumeRecorderCompleted(int? state, bool? success) => super.noSuchMethod(
    Invocation.method(#resumeRecorderCompleted, [state, success]),
    returnValueForMissingStub: null,
  );

  @override
  void startRecorderCompleted(int? state, bool? success) => super.noSuchMethod(
    Invocation.method(#startRecorderCompleted, [state, success]),
    returnValueForMissingStub: null,
  );

  @override
  void stopRecorderCompleted(int? state, bool? success, String? url) =>
      super.noSuchMethod(
        Invocation.method(#stopRecorderCompleted, [state, success, url]),
        returnValueForMissingStub: null,
      );

  @override
  void log(_i2.Level? logLevel, String? msg) => super.noSuchMethod(
    Invocation.method(#log, [logLevel, msg]),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<_i4.FlutterSoundRecorder?> openRecorder({
    dynamic isBGService = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#openRecorder, [], {#isBGService: isBGService}),
            returnValue: _i7.Future<_i4.FlutterSoundRecorder?>.value(),
          )
          as _i7.Future<_i4.FlutterSoundRecorder?>);

  @override
  _i7.Future<void> closeRecorder() =>
      (super.noSuchMethod(
            Invocation.method(#closeRecorder, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<bool> isEncoderSupported(_i4.Codec? codec) =>
      (super.noSuchMethod(
            Invocation.method(#isEncoderSupported, [codec]),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<void> setSubscriptionDuration(Duration? duration) =>
      (super.noSuchMethod(
            Invocation.method(#setSubscriptionDuration, [duration]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  Duration getSubscriptionDuration() =>
      (super.noSuchMethod(
            Invocation.method(#getSubscriptionDuration, []),
            returnValue: _FakeDuration_1(
              this,
              Invocation.method(#getSubscriptionDuration, []),
            ),
          )
          as Duration);

  @override
  _i7.Future<void> startRecorder({
    _i4.Codec? codec = _i4.Codec.defaultCodec,
    String? toFile,
    _i7.StreamSink<List<_i6.Float32List>>? toStreamFloat32,
    _i7.StreamSink<List<_i6.Int16List>>? toStreamInt16,
    _i7.StreamSink<_i6.Uint8List>? toStream,
    int? sampleRate,
    int? numChannels = 1,
    int? bitRate = 16000,
    int? bufferSize = 8192,
    bool? enableVoiceProcessing = false,
    _i4.AudioSource? audioSource = _i4.AudioSource.defaultSource,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startRecorder, [], {
              #codec: codec,
              #toFile: toFile,
              #toStreamFloat32: toStreamFloat32,
              #toStreamInt16: toStreamInt16,
              #toStream: toStream,
              #sampleRate: sampleRate,
              #numChannels: numChannels,
              #bitRate: bitRate,
              #bufferSize: bufferSize,
              #enableVoiceProcessing: enableVoiceProcessing,
              #audioSource: audioSource,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<String?> stopRecorder() =>
      (super.noSuchMethod(
            Invocation.method(#stopRecorder, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  void requestData() => super.noSuchMethod(
    Invocation.method(#requestData, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<int> getSampleRate() =>
      (super.noSuchMethod(
            Invocation.method(#getSampleRate, []),
            returnValue: _i7.Future<int>.value(0),
          )
          as _i7.Future<int>);

  @override
  _i7.Future<void> pauseRecorder() =>
      (super.noSuchMethod(
            Invocation.method(#pauseRecorder, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> resumeRecorder() =>
      (super.noSuchMethod(
            Invocation.method(#resumeRecorder, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<bool?> deleteRecord({required String? fileName}) =>
      (super.noSuchMethod(
            Invocation.method(#deleteRecord, [], {#fileName: fileName}),
            returnValue: _i7.Future<bool?>.value(),
          )
          as _i7.Future<bool?>);

  @override
  _i7.Future<String?> getRecordURL({required String? path}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecordURL, [], {#path: path}),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);
}

/// A class which mocks [SpeechToText].
///
/// See the documentation for Mockito's code generation for more information.
class MockSpeechToText extends _i1.Mock implements _i8.SpeechToText {
  MockSpeechToText() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasRecognized =>
      (super.noSuchMethod(Invocation.getter(#hasRecognized), returnValue: false)
          as bool);

  @override
  String get lastRecognizedWords =>
      (super.noSuchMethod(
            Invocation.getter(#lastRecognizedWords),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#lastRecognizedWords),
            ),
          )
          as String);

  @override
  String get lastStatus =>
      (super.noSuchMethod(
            Invocation.getter(#lastStatus),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#lastStatus),
            ),
          )
          as String);

  @override
  double get lastSoundLevel =>
      (super.noSuchMethod(Invocation.getter(#lastSoundLevel), returnValue: 0.0)
          as double);

  @override
  bool get isAvailable =>
      (super.noSuchMethod(Invocation.getter(#isAvailable), returnValue: false)
          as bool);

  @override
  bool get isListening =>
      (super.noSuchMethod(Invocation.getter(#isListening), returnValue: false)
          as bool);

  @override
  bool get isNotListening =>
      (super.noSuchMethod(
            Invocation.getter(#isNotListening),
            returnValue: false,
          )
          as bool);

  @override
  bool get hasError =>
      (super.noSuchMethod(Invocation.getter(#hasError), returnValue: false)
          as bool);

  @override
  _i7.Future<bool> get hasPermission =>
      (super.noSuchMethod(
            Invocation.getter(#hasPermission),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  set errorListener(_i8.SpeechErrorListener? _errorListener) =>
      super.noSuchMethod(
        Invocation.setter(#errorListener, _errorListener),
        returnValueForMissingStub: null,
      );

  @override
  set statusListener(_i8.SpeechStatusListener? _statusListener) =>
      super.noSuchMethod(
        Invocation.setter(#statusListener, _statusListener),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<bool> initialize({
    _i8.SpeechErrorListener? onError,
    _i8.SpeechStatusListener? onStatus,
    dynamic debugLogging = false,
    Duration? finalTimeout = const Duration(milliseconds: 2000),
    List<_i10.SpeechConfigOption>? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #onError: onError,
              #onStatus: onStatus,
              #debugLogging: debugLogging,
              #finalTimeout: finalTimeout,
              #options: options,
            }),
            returnValue: _i7.Future<bool>.value(false),
          )
          as _i7.Future<bool>);

  @override
  _i7.Future<void> stop() =>
      (super.noSuchMethod(
            Invocation.method(#stop, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancel() =>
      (super.noSuchMethod(
            Invocation.method(#cancel, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<dynamic> listen({
    _i8.SpeechResultListener? onResult,
    Duration? listenFor,
    Duration? pauseFor,
    String? localeId,
    _i8.SpeechSoundLevelChange? onSoundLevelChange,
    dynamic cancelOnError = false,
    dynamic partialResults = true,
    dynamic onDevice = false,
    _i10.ListenMode? listenMode = _i10.ListenMode.confirmation,
    dynamic sampleRate = 0,
    _i10.SpeechListenOptions? listenOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#listen, [], {
              #onResult: onResult,
              #listenFor: listenFor,
              #pauseFor: pauseFor,
              #localeId: localeId,
              #onSoundLevelChange: onSoundLevelChange,
              #cancelOnError: cancelOnError,
              #partialResults: partialResults,
              #onDevice: onDevice,
              #listenMode: listenMode,
              #sampleRate: sampleRate,
              #listenOptions: listenOptions,
            }),
            returnValue: _i7.Future<dynamic>.value(),
          )
          as _i7.Future<dynamic>);

  @override
  void changePauseFor(Duration? pauseFor) => super.noSuchMethod(
    Invocation.method(#changePauseFor, [pauseFor]),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<List<_i8.LocaleName>> locales() =>
      (super.noSuchMethod(
            Invocation.method(#locales, []),
            returnValue: _i7.Future<List<_i8.LocaleName>>.value(
              <_i8.LocaleName>[],
            ),
          )
          as _i7.Future<List<_i8.LocaleName>>);

  @override
  _i7.Future<_i8.LocaleName?> systemLocale() =>
      (super.noSuchMethod(
            Invocation.method(#systemLocale, []),
            returnValue: _i7.Future<_i8.LocaleName?>.value(),
          )
          as _i7.Future<_i8.LocaleName?>);
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i3.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.FunctionsClient get functions =>
      (super.noSuchMethod(
            Invocation.getter(#functions),
            returnValue: _FakeFunctionsClient_2(
              this,
              Invocation.getter(#functions),
            ),
          )
          as _i3.FunctionsClient);

  @override
  _i3.SupabaseStorageClient get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeSupabaseStorageClient_3(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i3.SupabaseStorageClient);

  @override
  _i3.RealtimeClient get realtime =>
      (super.noSuchMethod(
            Invocation.getter(#realtime),
            returnValue: _FakeRealtimeClient_4(
              this,
              Invocation.getter(#realtime),
            ),
          )
          as _i3.RealtimeClient);

  @override
  _i3.PostgrestClient get rest =>
      (super.noSuchMethod(
            Invocation.getter(#rest),
            returnValue: _FakePostgrestClient_5(this, Invocation.getter(#rest)),
          )
          as _i3.PostgrestClient);

  @override
  Map<String, String> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  _i3.GoTrueClient get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeGoTrueClient_6(this, Invocation.getter(#auth)),
          )
          as _i3.GoTrueClient);

  @override
  set functions(_i3.FunctionsClient? _functions) => super.noSuchMethod(
    Invocation.setter(#functions, _functions),
    returnValueForMissingStub: null,
  );

  @override
  set storage(_i3.SupabaseStorageClient? _storage) => super.noSuchMethod(
    Invocation.setter(#storage, _storage),
    returnValueForMissingStub: null,
  );

  @override
  set realtime(_i3.RealtimeClient? _realtime) => super.noSuchMethod(
    Invocation.setter(#realtime, _realtime),
    returnValueForMissingStub: null,
  );

  @override
  set rest(_i3.PostgrestClient? _rest) => super.noSuchMethod(
    Invocation.setter(#rest, _rest),
    returnValueForMissingStub: null,
  );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
    Invocation.setter(#headers, headers),
    returnValueForMissingStub: null,
  );

  @override
  _i3.SupabaseQueryBuilder from(String? table) =>
      (super.noSuchMethod(
            Invocation.method(#from, [table]),
            returnValue: _FakeSupabaseQueryBuilder_7(
              this,
              Invocation.method(#from, [table]),
            ),
          )
          as _i3.SupabaseQueryBuilder);

  @override
  _i3.SupabaseQuerySchema schema(String? schema) =>
      (super.noSuchMethod(
            Invocation.method(#schema, [schema]),
            returnValue: _FakeSupabaseQuerySchema_8(
              this,
              Invocation.method(#schema, [schema]),
            ),
          )
          as _i3.SupabaseQuerySchema);

  @override
  _i3.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            returnValue: _FakePostgrestFilterBuilder_9<T>(
              this,
              Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            ),
          )
          as _i3.PostgrestFilterBuilder<T>);

  @override
  _i3.RealtimeChannel channel(
    String? name, {
    _i3.RealtimeChannelConfig? opts = const _i3.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#channel, [name], {#opts: opts}),
            returnValue: _FakeRealtimeChannel_10(
              this,
              Invocation.method(#channel, [name], {#opts: opts}),
            ),
          )
          as _i3.RealtimeChannel);

  @override
  List<_i3.RealtimeChannel> getChannels() =>
      (super.noSuchMethod(
            Invocation.method(#getChannels, []),
            returnValue: <_i3.RealtimeChannel>[],
          )
          as List<_i3.RealtimeChannel>);

  @override
  _i7.Future<String> removeChannel(_i3.RealtimeChannel? channel) =>
      (super.noSuchMethod(
            Invocation.method(#removeChannel, [channel]),
            returnValue: _i7.Future<String>.value(
              _i9.dummyValue<String>(
                this,
                Invocation.method(#removeChannel, [channel]),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  _i7.Future<List<String>> removeAllChannels() =>
      (super.noSuchMethod(
            Invocation.method(#removeAllChannels, []),
            returnValue: _i7.Future<List<String>>.value(<String>[]),
          )
          as _i7.Future<List<String>>);

  @override
  _i7.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}
