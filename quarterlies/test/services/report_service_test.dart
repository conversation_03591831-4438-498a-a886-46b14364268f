import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:quarterlies/services/report_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/models/report_models.dart' as report_models;
import 'package:quarterlies/models/models.dart';

// Generate mocks
@GenerateMocks([DataRepository])
import 'report_service_test.mocks.dart';

void main() {
  group('ReportService', () {
    late ReportService reportService;
    late MockDataRepository mockDataRepository;

    setUp(() {
      mockDataRepository = MockDataRepository();
      reportService = ReportService(mockDataRepository);
    });

    group('generateJobReport', () {
      test('should generate job report successfully', () async {
        // Arrange
        const jobId = 'test-job-id';
        final filters = report_models.ReportFilters(
          timePeriod: report_models.TimePeriod.currentYear,
          selectedJobIds: [jobId],
          includeAllJobs: false,
        );

        final mockJob = Job(
          userId: 'user-id',
          customerId: 'customer-id',
          title: 'Test Job',
          status: 'active',
          estimatedPrice: 1000.0,
        );

        final mockCustomer = Customer(
          userId: 'user-id',
          name: 'Test Customer',
          email: '<EMAIL>',
        );

        // Mock the data repository calls
        when(
          mockDataRepository.getJobById(jobId),
        ).thenAnswer((_) async => mockJob);
        when(
          mockDataRepository.getCustomerById('customer-id'),
        ).thenAnswer((_) async => mockCustomer);
        when(
          mockDataRepository.getExpensesByJob(jobId),
        ).thenAnswer((_) async => <Expense>[]);
        when(
          mockDataRepository.getTimeLogsByJob(jobId),
        ).thenAnswer((_) async => <TimeLog>[]);
        when(
          mockDataRepository.getInvoices(),
        ).thenAnswer((_) async => <Invoice>[]);
        when(
          mockDataRepository.getPaymentsByJob(jobId),
        ).thenAnswer((_) async => <Payment>[]);
        when(
          mockDataRepository.getEstimatesByJobId(jobId),
        ).thenAnswer((_) async => <Estimate>[]);
        when(mockDataRepository.getJobCostSummary(jobId)).thenAnswer(
          (_) async => {'total_expenses': 200.0, 'total_labor_cost': 100.0},
        );

        // Act
        final result = await reportService.generateJobReport(
          jobId: jobId,
          filters: filters,
        );

        // Assert
        expect(result, isA<report_models.JobReportData>());
        expect(result.jobDetails['title'], equals('Test Job'));
        expect(result.jobDetails['customer_name'], equals('Test Customer'));
        expect(result.expenses, isEmpty);
        expect(result.timeLogs, isEmpty);
        expect(result.invoices, isEmpty);
        expect(result.payments, isEmpty);
        expect(result.estimates, isEmpty);
      });

      test('should throw exception when job not found', () async {
        // Arrange
        const jobId = 'non-existent-job';
        final filters = report_models.ReportFilters(
          timePeriod: report_models.TimePeriod.currentYear,
          selectedJobIds: [jobId],
          includeAllJobs: false,
        );

        when(
          mockDataRepository.getJobById(jobId),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () async => await reportService.generateJobReport(
            jobId: jobId,
            filters: filters,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('generateReportPdf', () {
      test('should generate PDF for job report', () async {
        // Arrange
        final jobReportData = report_models.JobReportData(
          reportType: report_models.ReportType.jobProfitability,
          filters: report_models.ReportFilters(
            timePeriod: report_models.TimePeriod.currentYear,
            selectedJobIds: ['test-job'],
            includeAllJobs: false,
          ),
          generatedAt: DateTime.now(),
          jobDetails: {
            'title': 'Test Job',
            'customer_name': 'Test Customer',
            'status': 'active',
            'estimated_price': 1000.0,
          },
          financialSummary: {
            'total_income': 800.0,
            'total_expenses': 200.0,
            'total_labor_cost': 100.0,
            'profit_loss': 500.0,
          },
          expenses: [],
          timeLogs: [],
          invoices: [],
          payments: [],
          estimates: [],
        );

        // Act
        final pdfBytes = await reportService.generateReportPdf(jobReportData);

        // Assert
        expect(pdfBytes, isNotEmpty);
        expect(pdfBytes, isA<List<int>>());
      });
    });
  });
}
