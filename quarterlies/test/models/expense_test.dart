import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/expense.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('Expense Model Tests', () {
    group('Expense creation and properties', () {
      test('should create expense with required fields', () {
        // Arrange & Act
        final expense = Expense(
          userId: 'user-1',
          amount: 150.50,
          description: 'Office supplies',
          category: 'Office Supplies',
          date: DateTime(2024, 1, 15),
        );

        // Assert
        expect(expense.userId, equals('user-1'));
        expect(expense.amount, equals(150.50));
        expect(expense.description, equals('Office supplies'));
        expect(expense.category, equals('Office Supplies'));
        expect(expense.date, equals(DateTime(2024, 1, 15)));
        expect(expense.id, isNotNull);
        expect(expense.createdAt, isNotNull);
        expect(expense.updatedAt, isNotNull);
        expect(expense.syncStatus, equals(SyncStatus.pending));
        expect(expense.isOverhead, isFalse); // Default value
      });

      test('should create expense with optional fields', () {
        // Arrange & Act
        final expense = Expense(
          userId: 'user-1',
          jobId: 'job-1',
          amount: 250.75,
          description: 'Equipment purchase',
          category: 'Equipment',
          date: DateTime(2024, 2, 20),
          isOverhead: true,
          receiptPhotoUrl: 'https://example.com/receipt.pdf',
          tags: ['equipment', 'office'],
          voiceNoteUrl: 'https://example.com/voice-note.mp3',
        );

        // Assert
        expect(expense.jobId, equals('job-1'));
        expect(expense.isOverhead, isTrue);
        expect(
          expense.receiptPhotoUrl,
          equals('https://example.com/receipt.pdf'),
        );
        expect(expense.tags, equals(['equipment', 'office']));
        expect(
          expense.voiceNoteUrl,
          equals('https://example.com/voice-note.mp3'),
        );
      });

      test('should handle zero amount expense', () {
        // Arrange & Act
        final expense = Expense(
          userId: 'user-1',
          amount: 0.0,
          description: 'Free sample',
          category: 'Marketing',
          date: DateTime.now(),
        );

        // Assert
        expect(expense.amount, equals(0.0));
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final date = DateTime(2024, 1, 15, 10, 30);
        final expense = Expense(
          id: 'expense-1',
          userId: 'user-1',
          jobId: 'job-1',
          amount: 125.50,
          description: 'Gas for work trip',
          category: 'Transportation',
          date: date,
          isOverhead: false,
          receiptPhotoUrl: 'https://example.com/receipt.jpg',
          tags: ['transportation', 'gas'],
          voiceNoteUrl: 'https://example.com/voice-note.mp3',
        );

        // Act
        final json = expense.toJson();

        // Assert
        expect(json['id'], equals('expense-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['job_id'], equals('job-1'));
        expect(json['amount'], equals(125.50));
        expect(json['description'], equals('Gas for work trip'));
        expect(json['category'], equals('Transportation'));
        expect(json['date'], equals(date.toIso8601String()));
        expect(json['is_overhead'], isFalse);
        expect(
          json['receipt_photo_url'],
          equals('https://example.com/receipt.jpg'),
        );
        expect(json['tags'], equals('transportation,gas'));
        expect(
          json['voice_note_url'],
          equals('https://example.com/voice-note.mp3'),
        );
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'expense-1',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'amount': 89.99,
          'description': 'Software subscription',
          'category': 'Software',
          'date': '2024-01-15T14:30:00.000Z',
          'is_overhead': true,
          'receipt_photo_url': 'https://example.com/invoice.pdf',
          'tags': 'software,subscription',
          'voice_note_url': 'https://example.com/voice.mp3',
          'created_at': '2024-01-15T14:30:00.000Z',
          'updated_at': '2024-01-15T14:30:00.000Z',
        };

        // Act
        final expense = Expense.fromJson(json);

        // Assert
        expect(expense.id, equals('expense-1'));
        expect(expense.userId, equals('user-1'));
        expect(expense.jobId, equals('job-1'));
        expect(expense.amount, equals(89.99));
        expect(expense.description, equals('Software subscription'));
        expect(expense.category, equals('Software'));
        expect(expense.isOverhead, isTrue);
        expect(
          expense.receiptPhotoUrl,
          equals('https://example.com/invoice.pdf'),
        );
        expect(expense.tags, equals(['software', 'subscription']));
        expect(expense.voiceNoteUrl, equals('https://example.com/voice.mp3'));
      });
    });

    group('copyWith method', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalExpense = Expense(
          userId: 'user-1',
          amount: 100.0,
          description: 'Original description',
          category: 'Original Category',
          date: DateTime(2024, 1, 1),
          isOverhead: false,
        );

        // Act
        final updatedExpense = originalExpense.copyWith(
          amount: 150.0,
          description: 'Updated description',
          category: 'Updated Category',
          isOverhead: true,
          tags: ['updated', 'test'],
        );

        // Assert
        expect(updatedExpense.amount, equals(150.0));
        expect(updatedExpense.description, equals('Updated description'));
        expect(updatedExpense.category, equals('Updated Category'));
        expect(updatedExpense.isOverhead, isTrue);
        expect(updatedExpense.tags, equals(['updated', 'test']));
        expect(updatedExpense.userId, equals('user-1')); // Unchanged
        expect(updatedExpense.id, equals(originalExpense.id)); // Unchanged
        expect(updatedExpense.date, equals(DateTime(2024, 1, 1))); // Unchanged
      });
    });

    group('Business logic validation', () {
      test('should validate expense categories', () {
        // Arrange - Common Schedule C expense categories
        final validCategories = [
          'Office Supplies',
          'Equipment',
          'Transportation',
          'Meals',
          'Travel',
          'Professional Services',
          'Software',
          'Insurance',
          'Utilities',
          'Rent',
          'Marketing',
          'Training',
        ];

        // Act & Assert
        for (final category in validCategories) {
          final expense = Expense(
            userId: 'user-1',
            amount: 100.0,
            description: 'Test expense',
            category: category,
            date: DateTime.now(),
          );

          expect(expense.category, equals(category));
        }
      });

      test('should handle currency precision', () {
        // Arrange
        const amount = 123.456789;

        // Act
        final expense = Expense(
          userId: 'user-1',
          amount: amount,
          description: 'Precision test',
          category: 'Test',
          date: DateTime.now(),
        );

        // Assert - Amount should maintain precision as stored
        expect(expense.amount, equals(123.456789));

        // For display/calculation purposes, round to 2 decimal places
        final displayAmount = (expense.amount * 100).round() / 100;
        expect(displayAmount, equals(123.46));
      });
    });
  });
}
