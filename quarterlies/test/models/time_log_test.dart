import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/time_log.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('TimeLog Model Tests', () {
    group('Labor cost calculations', () {
      test('should calculate labor cost correctly for hourly rate', () {
        // Arrange
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 8.0,
          hourlyRate: 75.0,
          laborCost: 8.0 * 75.0, // Should be calculated
          isFlatRate: false,
        );

        // Act & Assert
        expect(timeLog.hours, equals(8.0));
        expect(timeLog.hourlyRate, equals(75.0));
        expect(timeLog.laborCost, equals(600.0)); // 8 * 75
        expect(timeLog.isFlatRate, isFalse);
      });

      test('should handle flat rate correctly', () {
        // Arrange
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 0.0, // Hours might be 0 for flat rate
          hourlyRate: 0.0, // Hourly rate might be 0 for flat rate
          laborCost: 500.0, // Fixed flat rate amount
          isFlatRate: true,
        );

        // Act & Assert
        expect(timeLog.hours, equals(0.0));
        expect(timeLog.hourlyRate, equals(0.0));
        expect(timeLog.laborCost, equals(500.0));
        expect(timeLog.isFlatRate, isTrue);
      });

      test('should handle fractional hours', () {
        // Arrange
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 6.5,
          hourlyRate: 80.0,
          laborCost: 6.5 * 80.0,
          isFlatRate: false,
        );

        // Act & Assert
        expect(timeLog.hours, equals(6.5));
        expect(timeLog.hourlyRate, equals(80.0));
        expect(timeLog.laborCost, equals(520.0)); // 6.5 * 80
      });

      test('should handle zero hours', () {
        // Arrange
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 0.0,
          hourlyRate: 75.0,
          laborCost: 0.0,
          isFlatRate: false,
        );

        // Act & Assert
        expect(timeLog.hours, equals(0.0));
        expect(timeLog.hourlyRate, equals(75.0));
        expect(timeLog.laborCost, equals(0.0));
      });
    });

    group('TimeLog creation and properties', () {
      test('should create time log with required fields', () {
        // Arrange
        final date = DateTime.now();
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: date,
          hours: 4.0,
          hourlyRate: 100.0,
          laborCost: 400.0,
        );

        // Assert
        expect(timeLog.userId, equals('user-1'));
        expect(timeLog.jobId, equals('job-1'));
        expect(timeLog.date, equals(date));
        expect(timeLog.hours, equals(4.0));
        expect(timeLog.hourlyRate, equals(100.0));
        expect(timeLog.laborCost, equals(400.0));
        expect(timeLog.isFlatRate, isFalse);
        expect(timeLog.id, isNotNull);
        expect(timeLog.createdAt, isNotNull);
        expect(timeLog.updatedAt, isNotNull);
        expect(timeLog.syncStatus, equals(SyncStatus.pending));
      });

      test('should create time log with optional fields', () {
        // Arrange
        final timeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 8.0,
          hourlyRate: 75.0,
          laborCost: 600.0,
          notes: 'Worked on project setup',
          voiceNoteUrl: 'https://example.com/voice.mp3',
          isFlatRate: false,
          invoicedInId: 'invoice-1',
          pendingInvoiceId: 'pending-invoice-1',
          pendingInvoiceNumber: 'INV-001',
        );

        // Assert
        expect(timeLog.notes, equals('Worked on project setup'));
        expect(timeLog.voiceNoteUrl, equals('https://example.com/voice.mp3'));
        expect(timeLog.invoicedInId, equals('invoice-1'));
        expect(timeLog.pendingInvoiceId, equals('pending-invoice-1'));
        expect(timeLog.pendingInvoiceNumber, equals('INV-001'));
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final date = DateTime(2024, 1, 15, 9, 0);
        final timeLog = TimeLog(
          id: 'timelog-1',
          userId: 'user-1',
          jobId: 'job-1',
          date: date,
          hours: 7.5,
          hourlyRate: 85.0,
          laborCost: 637.5,
          notes: 'Development work',
          voiceNoteUrl: 'https://example.com/voice.mp3',
          isFlatRate: false,
          invoicedInId: 'invoice-1',
          pendingInvoiceId: 'pending-invoice-1',
          pendingInvoiceNumber: 'INV-001',
        );

        // Act
        final json = timeLog.toJson();

        // Assert
        expect(json['id'], equals('timelog-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['job_id'], equals('job-1'));
        expect(json['date'], equals(date.toIso8601String()));
        expect(json['hours'], equals(7.5));
        expect(json['hourly_rate'], equals(85.0));
        expect(json['labor_cost'], equals(637.5));
        expect(json['notes'], equals('Development work'));
        expect(json['voice_note_url'], equals('https://example.com/voice.mp3'));
        expect(json['is_flat_rate'], isFalse);
        expect(json['invoiced_in_id'], equals('invoice-1'));
        expect(json['pending_invoice_id'], equals('pending-invoice-1'));
        expect(json['pending_invoice_number'], equals('INV-001'));
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'timelog-1',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'date': '2024-01-15T14:30:00.000Z',
          'hours': 5.0,
          'hourly_rate': 90.0,
          'labor_cost': 450.0,
          'notes': 'Testing and debugging',
          'voice_note_url': 'https://example.com/voice2.mp3',
          'is_flat_rate': false,
          'invoiced_in_id': 'invoice-2',
          'pending_invoice_id': null,
          'pending_invoice_number': null,
          'created_at': '2024-01-15T14:30:00.000Z',
          'updated_at': '2024-01-15T14:30:00.000Z',
        };

        // Act
        final timeLog = TimeLog.fromJson(json);

        // Assert
        expect(timeLog.id, equals('timelog-1'));
        expect(timeLog.userId, equals('user-1'));
        expect(timeLog.jobId, equals('job-1'));
        expect(timeLog.hours, equals(5.0));
        expect(timeLog.hourlyRate, equals(90.0));
        expect(timeLog.laborCost, equals(450.0));
        expect(timeLog.notes, equals('Testing and debugging'));
        expect(timeLog.voiceNoteUrl, equals('https://example.com/voice2.mp3'));
        expect(timeLog.isFlatRate, isFalse);
        expect(timeLog.invoicedInId, equals('invoice-2'));
        expect(timeLog.pendingInvoiceId, isNull);
        expect(timeLog.pendingInvoiceNumber, isNull);
      });

      test('should handle flat rate in JSON', () {
        // Arrange
        final json = {
          'id': 'timelog-flat',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'date': '2024-01-15T10:00:00.000Z',
          'hours': 0.0,
          'hourly_rate': 0.0,
          'labor_cost': 1000.0,
          'notes': 'Project completion bonus',
          'is_flat_rate': true,
          'created_at': '2024-01-15T10:00:00.000Z',
          'updated_at': '2024-01-15T10:00:00.000Z',
        };

        // Act
        final timeLog = TimeLog.fromJson(json);

        // Assert
        expect(timeLog.hours, equals(0.0));
        expect(timeLog.hourlyRate, equals(0.0));
        expect(timeLog.laborCost, equals(1000.0));
        expect(timeLog.isFlatRate, isTrue);
        expect(timeLog.notes, equals('Project completion bonus'));
      });
    });

    group('copyWith method', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalTimeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 6.0,
          hourlyRate: 70.0,
          laborCost: 420.0,
          notes: 'Original notes',
          isFlatRate: false,
        );

        // Act
        final updatedTimeLog = originalTimeLog.copyWith(
          hours: 8.0,
          hourlyRate: 80.0,
          laborCost: 640.0,
          notes: 'Updated notes',
          isFlatRate: false,
        );

        // Assert
        expect(updatedTimeLog.hours, equals(8.0));
        expect(updatedTimeLog.hourlyRate, equals(80.0));
        expect(updatedTimeLog.laborCost, equals(640.0));
        expect(updatedTimeLog.notes, equals('Updated notes'));
        expect(updatedTimeLog.isFlatRate, isFalse);
        expect(updatedTimeLog.userId, equals('user-1')); // Unchanged
        expect(updatedTimeLog.jobId, equals('job-1')); // Unchanged
        expect(updatedTimeLog.id, equals(originalTimeLog.id)); // Unchanged
      });

      test('should update from hourly to flat rate', () {
        // Arrange
        final originalTimeLog = TimeLog(
          userId: 'user-1',
          jobId: 'job-1',
          date: DateTime.now(),
          hours: 8.0,
          hourlyRate: 75.0,
          laborCost: 600.0,
          isFlatRate: false,
        );

        // Act
        final updatedTimeLog = originalTimeLog.copyWith(
          hours: 0.0,
          hourlyRate: 0.0,
          laborCost: 800.0,
          isFlatRate: true,
        );

        // Assert
        expect(updatedTimeLog.hours, equals(0.0));
        expect(updatedTimeLog.hourlyRate, equals(0.0));
        expect(updatedTimeLog.laborCost, equals(800.0));
        expect(updatedTimeLog.isFlatRate, isTrue);
      });
    });
  });
}
