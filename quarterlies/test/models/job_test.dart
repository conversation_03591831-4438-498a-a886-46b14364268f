import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/job.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('Job Model Tests', () {
    group('profitLoss calculation', () {
      test('should calculate profit correctly with positive values', () {
        // Arrange
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          actualIncome: 1000.0,
          actualExpenses: 300.0,
          actualLaborCost: 200.0,
        );

        // Act
        final profitLoss = job.profitLoss;

        // Assert
        expect(profitLoss, equals(500.0)); // 1000 - 300 - 200
      });

      test('should calculate loss correctly when expenses exceed income', () {
        // Arrange
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          actualIncome: 500.0,
          actualExpenses: 400.0,
          actualLaborCost: 300.0,
        );

        // Act
        final profitLoss = job.profitLoss;

        // Assert
        expect(profitLoss, equals(-200.0)); // 500 - 400 - 300
      });

      test('should handle null values as zero', () {
        // Arrange
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          // All financial fields are null
        );

        // Act
        final profitLoss = job.profitLoss;

        // Assert
        expect(profitLoss, equals(0.0));
      });

      test('should handle partial null values', () {
        // Arrange
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          actualIncome: 1000.0,
          // actualExpenses and actualLaborCost are null
        );

        // Act
        final profitLoss = job.profitLoss;

        // Assert
        expect(profitLoss, equals(1000.0)); // 1000 - 0 - 0
      });
    });

    group('Job creation and properties', () {
      test('should create job with required fields', () {
        // Arrange & Act
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
        );

        // Assert
        expect(job.userId, equals('user-1'));
        expect(job.customerId, equals('customer-1'));
        expect(job.title, equals('Test Job'));
        expect(job.status, equals('active'));
        expect(job.id, isNotNull);
        expect(job.createdAt, isNotNull);
        expect(job.updatedAt, isNotNull);
        expect(job.syncStatus, equals(SyncStatus.pending));
      });

      test('should create job with default sync settings', () {
        // Arrange & Act
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
        );

        // Assert
        expect(job.syncExpenses, isFalse); // Default is false
        expect(job.syncMileage, isFalse); // Default is false
        expect(job.syncLaborCosts, isFalse); // Default is false
        expect(job.syncEstimateItems, isTrue); // Default is true
        expect(job.summarizeMileage, isTrue);
        expect(job.summarizeHours, isFalse);
      });

      test('should create job with custom sync settings', () {
        // Arrange & Act
        final job = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          syncExpenses: false,
          syncMileage: false,
          syncLaborCosts: false,
          syncEstimateItems: false,
          summarizeMileage: false,
          summarizeHours: true,
        );

        // Assert
        expect(job.syncExpenses, isFalse);
        expect(job.syncMileage, isFalse);
        expect(job.syncLaborCosts, isFalse);
        expect(job.syncEstimateItems, isFalse);
        expect(job.summarizeMileage, isFalse);
        expect(job.summarizeHours, isTrue);
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final job = Job(
          id: 'job-1',
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Test Job',
          status: 'active',
          estimatedPrice: 1000.0,
          actualIncome: 800.0,
          actualExpenses: 200.0,
          actualLaborCost: 150.0,
        );

        // Act
        final json = job.toJson();

        // Assert
        expect(json['id'], equals('job-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['customer_id'], equals('customer-1'));
        expect(json['title'], equals('Test Job'));
        expect(json['status'], equals('active'));
        expect(json['estimated_price'], equals(1000.0));
        expect(json['actual_income'], equals(800.0));
        expect(json['actual_expenses'], equals(200.0));
        expect(json['actual_labor_cost'], equals(150.0));
        expect(json['sync_expenses'], isFalse); // Default value
        expect(json['sync_mileage'], isFalse); // Default value
        expect(json['sync_labor_costs'], isFalse); // Default value
        expect(json['sync_estimate_items'], isTrue); // Default value
        expect(json['summarize_mileage'], isTrue);
        expect(json['summarize_hours'], isFalse);
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'job-1',
          'user_id': 'user-1',
          'customer_id': 'customer-1',
          'title': 'Test Job',
          'status': 'active',
          'estimated_price': 1000.0,
          'actual_income': 800.0,
          'actual_expenses': 200.0,
          'actual_labor_cost': 150.0,
          'sync_expenses': true,
          'sync_mileage': false,
          'sync_labor_costs': true,
          'sync_estimate_items': true,
          'summarize_mileage': false,
          'summarize_hours': true,
          'created_at': '2024-01-01T00:00:00.000Z',
          'updated_at': '2024-01-01T00:00:00.000Z',
        };

        // Act
        final job = Job.fromJson(json);

        // Assert
        expect(job.id, equals('job-1'));
        expect(job.userId, equals('user-1'));
        expect(job.customerId, equals('customer-1'));
        expect(job.title, equals('Test Job'));
        expect(job.status, equals('active'));
        expect(job.estimatedPrice, equals(1000.0));
        expect(job.actualIncome, equals(800.0));
        expect(job.actualExpenses, equals(200.0));
        expect(job.actualLaborCost, equals(150.0));
        expect(job.syncExpenses, isTrue); // From JSON
        expect(job.syncMileage, isFalse); // From JSON
        expect(job.syncLaborCosts, isTrue); // From JSON
        expect(job.syncEstimateItems, isTrue); // From JSON
        expect(job.summarizeMileage, isFalse);
        expect(job.summarizeHours, isTrue);
        expect(job.profitLoss, equals(450.0)); // 800 - 200 - 150
      });
    });

    group('copyWith method', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalJob = Job(
          userId: 'user-1',
          customerId: 'customer-1',
          title: 'Original Job',
          status: 'active',
          estimatedPrice: 1000.0,
        );

        // Act
        final updatedJob = originalJob.copyWith(
          title: 'Updated Job',
          status: 'completed',
          actualIncome: 1200.0,
        );

        // Assert
        expect(updatedJob.title, equals('Updated Job'));
        expect(updatedJob.status, equals('completed'));
        expect(updatedJob.actualIncome, equals(1200.0));
        expect(updatedJob.estimatedPrice, equals(1000.0)); // Unchanged
        expect(updatedJob.userId, equals('user-1')); // Unchanged
        expect(updatedJob.id, equals(originalJob.id)); // Unchanged
      });
    });
  });
}
