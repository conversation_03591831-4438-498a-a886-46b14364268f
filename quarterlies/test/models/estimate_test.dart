import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/estimate.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('Estimate Model Tests', () {
    group('calculateTotal method', () {
      test('should calculate total from line items correctly', () {
        // Arrange
        final lineItems = [
          EstimateItem(
            description: 'Item 1',
            quantity: 2.0,
            unit: 'hours',
            unitPrice: 50.0,
          ),
          EstimateItem(
            description: 'Item 2',
            quantity: 1.0,
            unit: 'each',
            unitPrice: 100.0,
          ),
          EstimateItem(
            description: 'Item 3',
            quantity: 3.0,
            unit: 'units',
            unitPrice: 25.0,
          ),
        ];

        final estimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: lineItems,
          totalAmount: 0.0, // Will be calculated
        );

        // Act
        final total = estimate.calculateTotal();

        // Assert
        expect(
          total,
          equals(275.0),
        ); // (2*50) + (1*100) + (3*25) = 100 + 100 + 75
      });

      test('should return zero for empty line items', () {
        // Arrange
        final estimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: [],
          totalAmount: 0.0,
        );

        // Act
        final total = estimate.calculateTotal();

        // Assert
        expect(total, equals(0.0));
      });

      test('should handle fractional quantities and prices', () {
        // Arrange
        final lineItems = [
          EstimateItem(
            description: 'Fractional Item',
            quantity: 2.5,
            unit: 'hours',
            unitPrice: 75.50,
          ),
        ];

        final estimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: lineItems,
          totalAmount: 0.0,
        );

        // Act
        final total = estimate.calculateTotal();

        // Assert
        expect(total, equals(188.75)); // 2.5 * 75.50
      });
    });

    group('cloneAsTemplate method', () {
      test('should create template clone correctly', () {
        // Arrange
        final originalLineItems = [
          EstimateItem(
            description: 'Original Item',
            quantity: 1.0,
            unit: 'each',
            unitPrice: 100.0,
          ),
        ];

        final originalEstimate = Estimate(
          id: 'original-id',
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: originalLineItems,
          totalAmount: 100.0,
          status: 'approved',
          notes: 'Original notes',
        );

        // Act
        final template = originalEstimate.cloneAsTemplate();

        // Assert
        expect(template.id, isNot(equals('original-id')));
        expect(template.userId, equals('user-1'));
        expect(template.jobId, equals('job-1'));
        expect(template.customerId, equals('customer-1'));
        expect(template.totalAmount, equals(100.0));
        expect(template.status, equals('draft'));
        expect(template.notes, equals('Original notes'));
        expect(template.templateId, equals('original-id'));
        expect(template.lineItems.length, equals(1));
        expect(template.lineItems.first.description, equals('Original Item'));
      });
    });

    group('Estimate creation and properties', () {
      test('should create estimate with required fields', () {
        // Arrange & Act
        final estimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: [],
          totalAmount: 500.0,
        );

        // Assert
        expect(estimate.userId, equals('user-1'));
        expect(estimate.jobId, equals('job-1'));
        expect(estimate.customerId, equals('customer-1'));
        expect(estimate.totalAmount, equals(500.0));
        expect(estimate.status, equals('draft'));
        expect(estimate.id, isNotNull);
        expect(estimate.createdAt, isNotNull);
        expect(estimate.updatedAt, isNotNull);
        expect(estimate.syncStatus, equals(SyncStatus.pending));
      });

      test('should create estimate with optional fields', () {
        // Arrange & Act
        final estimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: [],
          totalAmount: 500.0,
          status: 'sent',
          notes: 'Test notes',
          isTemplate: true,
          templateName: 'Standard Template',
        );

        // Assert
        expect(estimate.status, equals('sent'));
        expect(estimate.notes, equals('Test notes'));
        expect(estimate.isTemplate, isTrue);
        expect(estimate.templateName, equals('Standard Template'));
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final lineItems = [
          EstimateItem(
            id: 'item-1',
            description: 'Test Item',
            quantity: 2.0,
            unit: 'hours',
            unitPrice: 50.0,
          ),
        ];

        final estimate = Estimate(
          id: 'estimate-1',
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: lineItems,
          totalAmount: 100.0,
          status: 'sent',
          notes: 'Test notes',
        );

        // Act
        final json = estimate.toJson();

        // Assert
        expect(json['id'], equals('estimate-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['job_id'], equals('job-1'));
        expect(json['customer_id'], equals('customer-1'));
        expect(json['total_amount'], equals(100.0));
        expect(json['status'], equals('sent'));
        expect(json['notes'], equals('Test notes'));
        expect(json['line_items'], isA<List>());
        expect(json['line_items'].length, equals(1));
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'estimate-1',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'customer_id': 'customer-1',
          'total_amount': 150.0,
          'status': 'approved',
          'notes': 'JSON notes',
          'is_template': false,
          'template_name': null,
          'template_id': null,
          'created_at': '2024-01-15T10:00:00.000Z',
          'updated_at': '2024-01-15T10:00:00.000Z',
          'line_items': [
            {
              'id': 'item-1',
              'description': 'JSON Item',
              'quantity': 3.0,
              'unit': 'each',
              'unit_price': 50.0,
            },
          ],
        };

        // Act
        final estimate = Estimate.fromJson(json);

        // Assert
        expect(estimate.id, equals('estimate-1'));
        expect(estimate.userId, equals('user-1'));
        expect(estimate.jobId, equals('job-1'));
        expect(estimate.customerId, equals('customer-1'));
        expect(estimate.totalAmount, equals(150.0));
        expect(estimate.status, equals('approved'));
        expect(estimate.notes, equals('JSON notes'));
        expect(estimate.isTemplate, isFalse);
        expect(estimate.lineItems.length, equals(1));
        expect(estimate.lineItems.first.description, equals('JSON Item'));
        expect(estimate.lineItems.first.quantity, equals(3.0));
        expect(estimate.lineItems.first.unitPrice, equals(50.0));
      });
    });

    group('copyWith method', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalEstimate = Estimate(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          lineItems: [],
          totalAmount: 100.0,
          status: 'draft',
          notes: 'Original notes',
        );

        // Act
        final updatedEstimate = originalEstimate.copyWith(
          status: 'sent',
          totalAmount: 200.0,
          notes: 'Updated notes',
        );

        // Assert
        expect(updatedEstimate.status, equals('sent'));
        expect(updatedEstimate.totalAmount, equals(200.0));
        expect(updatedEstimate.notes, equals('Updated notes'));
        expect(updatedEstimate.userId, equals('user-1')); // Unchanged
        expect(updatedEstimate.jobId, equals('job-1')); // Unchanged
        expect(updatedEstimate.id, equals(originalEstimate.id)); // Unchanged
      });
    });
  });

  group('EstimateItem Model Tests', () {
    group('Calculation properties', () {
      test('should calculate total correctly', () {
        // Arrange
        final item = EstimateItem(
          description: 'Test Item',
          quantity: 3.0,
          unit: 'hours',
          unitPrice: 75.0,
        );

        // Act
        final total = item.total;

        // Assert
        expect(total, equals(225.0)); // 3 * 75
      });

      test('should calculate tax amount correctly', () {
        // Arrange
        final item = EstimateItem(
          description: 'Taxable Item',
          quantity: 2.0,
          unit: 'each',
          unitPrice: 100.0,
          taxRate: 8.5, // 8.5% tax
        );

        // Act
        final taxAmount = item.taxAmount;
        final totalWithTax = item.totalWithTax;

        // Assert
        expect(item.total, equals(200.0)); // 2 * 100
        expect(taxAmount, equals(17.0)); // 200 * 0.085
        expect(totalWithTax, equals(217.0)); // 200 + 17
      });

      test('should handle zero tax rate', () {
        // Arrange
        final item = EstimateItem(
          description: 'Non-taxable Item',
          quantity: 1.0,
          unit: 'each',
          unitPrice: 50.0,
          // taxRate is null (no tax)
        );

        // Act
        final taxAmount = item.taxAmount;
        final totalWithTax = item.totalWithTax;

        // Assert
        expect(item.total, equals(50.0));
        expect(taxAmount, equals(0.0));
        expect(totalWithTax, equals(50.0));
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final item = EstimateItem(
          id: 'item-1',
          description: 'Test Item',
          quantity: 2.5,
          unit: 'hours',
          unitPrice: 80.0,
          taxRate: 7.5,
        );

        // Act
        final json = item.toJson();

        // Assert
        expect(json['id'], equals('item-1'));
        expect(json['description'], equals('Test Item'));
        expect(json['quantity'], equals(2.5));
        expect(json['unit'], equals('hours'));
        expect(json['unit_price'], equals(80.0));
        expect(json['tax_rate'], equals(7.5));
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'item-1',
          'description': 'JSON Item',
          'quantity': 1.5,
          'unit': 'days',
          'unit_price': 200.0,
          'tax_rate': 10.0,
        };

        // Act
        final item = EstimateItem.fromJson(json);

        // Assert
        expect(item.id, equals('item-1'));
        expect(item.description, equals('JSON Item'));
        expect(item.quantity, equals(1.5));
        expect(item.unit, equals('days'));
        expect(item.unitPrice, equals(200.0));
        expect(item.taxRate, equals(10.0));
        expect(item.total, equals(300.0)); // 1.5 * 200
        expect(item.taxAmount, equals(30.0)); // 300 * 0.10
        expect(item.totalWithTax, equals(330.0)); // 300 + 30
      });
    });
  });
}
