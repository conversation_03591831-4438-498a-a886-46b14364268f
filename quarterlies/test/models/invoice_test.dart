import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/invoice.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('Invoice Model Tests', () {
    group('Balance calculations', () {
      test('should calculate balance due correctly', () {
        // Arrange
        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 1000.0,
          amountPaid: 300.0,
        );

        // Act
        final balanceDue = invoice.balanceDue;

        // Assert
        expect(balanceDue, equals(700.0)); // 1000 - 300
      });

      test('should handle fully paid invoice', () {
        // Arrange
        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 500.0,
          amountPaid: 500.0,
          status: 'paid',
        );

        // Act
        final balanceDue = invoice.balanceDue;

        // Assert
        expect(balanceDue, equals(0.0));
      });

      test('should handle overpaid invoice', () {
        // Arrange
        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 800.0,
          amountPaid: 850.0,
        );

        // Act
        final balanceDue = invoice.balanceDue;

        // Assert
        expect(balanceDue, equals(-50.0)); // Credit balance
      });

      test('should handle zero amount invoice', () {
        // Arrange
        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 0.0,
          amountPaid: 0.0,
        );

        // Act
        final balanceDue = invoice.balanceDue;

        // Assert
        expect(balanceDue, equals(0.0));
      });
    });

    group('Invoice creation and properties', () {
      test('should create invoice with required fields', () {
        // Arrange
        final issueDate = DateTime.now();
        final dueDate = DateTime.now().add(const Duration(days: 30));

        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: issueDate,
          dueDate: dueDate,
          totalAmount: 1500.0,
        );

        // Assert
        expect(invoice.userId, equals('user-1'));
        expect(invoice.jobId, equals('job-1'));
        expect(invoice.customerId, equals('customer-1'));
        expect(invoice.issueDate, equals(issueDate));
        expect(invoice.dueDate, equals(dueDate));
        expect(invoice.totalAmount, equals(1500.0));
        expect(invoice.amountPaid, equals(0.0)); // Default
        expect(invoice.status, equals('open')); // Default
        expect(invoice.isTemplate, isFalse); // Default
        expect(invoice.syncStatus, equals(SyncStatus.pending));
        expect(invoice.balanceDue, equals(1500.0));
      });

      test('should create invoice with optional fields', () {
        // Arrange
        final lineItems = [
          InvoiceItem(
            type: 'service',
            description: 'Service 1',
            quantity: 2.0,
            unit: 'hours',
            unitPrice: 100.0,
          ),
          InvoiceItem(
            type: 'service',
            description: 'Service 2',
            quantity: 1.0,
            unit: 'each',
            unitPrice: 200.0,
          ),
        ];

        final invoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 400.0,
          amountPaid: 100.0,
          status: 'partial',
          lineItems: lineItems,
          notes: 'Payment terms: Net 30',
          voiceNoteUrl: 'https://example.com/voice.mp3',
          isTemplate: true,
          templateName: 'Standard Service Invoice',
        );

        // Assert
        expect(invoice.amountPaid, equals(100.0));
        expect(invoice.status, equals('partial'));
        expect(invoice.lineItems?.length, equals(2));
        expect(invoice.notes, equals('Payment terms: Net 30'));
        expect(invoice.voiceNoteUrl, equals('https://example.com/voice.mp3'));
        expect(invoice.isTemplate, isTrue);
        expect(invoice.templateName, equals('Standard Service Invoice'));
        expect(invoice.balanceDue, equals(300.0)); // 400 - 100
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final issueDate = DateTime(2024, 1, 15);
        final dueDate = DateTime(2024, 2, 15);

        final lineItems = [
          InvoiceItem(
            id: 'item-1',
            type: 'labor',
            description: 'Consulting',
            quantity: 8.0,
            unit: 'hours',
            unitPrice: 125.0,
          ),
        ];

        final invoice = Invoice(
          id: 'invoice-1',
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: issueDate,
          dueDate: dueDate,
          totalAmount: 1000.0,
          amountPaid: 250.0,
          status: 'partial',
          lineItems: lineItems,
          notes: 'Thank you for your business',
        );

        // Act
        final json = invoice.toJson();

        // Assert
        expect(json['id'], equals('invoice-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['job_id'], equals('job-1'));
        expect(json['customer_id'], equals('customer-1'));
        expect(json['issue_date'], equals(issueDate.toIso8601String()));
        expect(json['due_date'], equals(dueDate.toIso8601String()));
        expect(json['total_amount'], equals(1000.0));
        expect(json['amount_paid'], equals(250.0));
        expect(json['status'], equals('partial'));
        expect(json['notes'], equals('Thank you for your business'));
        expect(json['line_items'], isA<List>());
        expect(json['line_items'].length, equals(1));
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'invoice-1',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'customer_id': 'customer-1',
          'issue_date': '2024-01-15T00:00:00.000Z',
          'due_date': '2024-02-15T00:00:00.000Z',
          'total_amount': 750.0,
          'amount_paid': 200.0,
          'status': 'partial',
          'notes': 'JSON invoice notes',
          'voice_note_url': 'https://example.com/voice.mp3',
          'is_template': false,
          'template_name': null,
          'template_id': null,
          'created_at': '2024-01-15T00:00:00.000Z',
          'updated_at': '2024-01-15T00:00:00.000Z',
          'line_items': [
            {
              'id': 'item-1',
              'description': 'Development',
              'quantity': 10.0,
              'unit_price': 75.0,
            },
          ],
        };

        // Act
        final invoice = Invoice.fromJson(json);

        // Assert
        expect(invoice.id, equals('invoice-1'));
        expect(invoice.userId, equals('user-1'));
        expect(invoice.jobId, equals('job-1'));
        expect(invoice.customerId, equals('customer-1'));
        expect(invoice.totalAmount, equals(750.0));
        expect(invoice.amountPaid, equals(200.0));
        expect(invoice.status, equals('partial'));
        expect(invoice.notes, equals('JSON invoice notes'));
        expect(invoice.voiceNoteUrl, equals('https://example.com/voice.mp3'));
        expect(invoice.isTemplate, isFalse);
        expect(invoice.lineItems?.length, equals(1));
        expect(invoice.lineItems?.first.description, equals('Development'));
        expect(invoice.balanceDue, equals(550.0)); // 750 - 200
      });
    });

    group('copyWith method', () {
      test('should create copy with updated fields', () {
        // Arrange
        final originalInvoice = Invoice(
          userId: 'user-1',
          jobId: 'job-1',
          customerId: 'customer-1',
          issueDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 30)),
          totalAmount: 1000.0,
          amountPaid: 0.0,
          status: 'open',
          notes: 'Original notes',
        );

        // Act
        final updatedInvoice = originalInvoice.copyWith(
          amountPaid: 500.0,
          status: 'partial',
          notes: 'Updated notes',
        );

        // Assert
        expect(updatedInvoice.amountPaid, equals(500.0));
        expect(updatedInvoice.status, equals('partial'));
        expect(updatedInvoice.notes, equals('Updated notes'));
        expect(updatedInvoice.totalAmount, equals(1000.0)); // Unchanged
        expect(updatedInvoice.userId, equals('user-1')); // Unchanged
        expect(updatedInvoice.id, equals(originalInvoice.id)); // Unchanged
        expect(updatedInvoice.balanceDue, equals(500.0)); // 1000 - 500
      });
    });
  });

  group('InvoiceItem Model Tests', () {
    group('Calculation properties', () {
      test('should calculate total correctly', () {
        // Arrange
        final item = InvoiceItem(
          type: 'labor',
          description: 'Consulting Hours',
          quantity: 6.0,
          unit: 'hours',
          unitPrice: 150.0,
        );

        // Act
        final total = item.total;

        // Assert
        expect(total, equals(900.0)); // 6 * 150
      });

      test('should handle fractional quantities', () {
        // Arrange
        final item = InvoiceItem(
          type: 'service',
          description: 'Partial Service',
          quantity: 2.5,
          unit: 'hours',
          unitPrice: 80.0,
        );

        // Act
        final total = item.total;

        // Assert
        expect(total, equals(200.0)); // 2.5 * 80
      });

      test('should handle zero quantity', () {
        // Arrange
        final item = InvoiceItem(
          type: 'service',
          description: 'Free Service',
          quantity: 0.0,
          unit: 'each',
          unitPrice: 100.0,
        );

        // Act
        final total = item.total;

        // Assert
        expect(total, equals(0.0));
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final item = InvoiceItem(
          id: 'item-1',
          type: 'design',
          description: 'Design Work',
          quantity: 4.0,
          unit: 'hours',
          unitPrice: 125.0,
        );

        // Act
        final json = item.toJson();

        // Assert
        expect(json['id'], equals('item-1'));
        expect(json['description'], equals('Design Work'));
        expect(json['quantity'], equals(4.0));
        expect(json['unit_price'], equals(125.0));
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'item-1',
          'description': 'Testing Services',
          'quantity': 3.0,
          'unit_price': 90.0,
        };

        // Act
        final item = InvoiceItem.fromJson(json);

        // Assert
        expect(item.id, equals('item-1'));
        expect(item.description, equals('Testing Services'));
        expect(item.quantity, equals(3.0));
        expect(item.unitPrice, equals(90.0));
        expect(item.total, equals(270.0)); // 3 * 90
      });
    });
  });
}
