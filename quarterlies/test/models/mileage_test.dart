import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/models/mileage.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('Mileage Model Tests', () {
    group('Mileage deduction calculation', () {
      test('should calculate mileage deduction correctly', () {
        // Arrange
        const miles = 100.0;
        const ratePerMile = 0.655; // 2024 IRS standard mileage rate
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'Home',
          endLocation: 'Client Site',
          miles: miles,
          ratePerMile: ratePerMile,
          date: DateTime.now(),
        );

        // Act
        final deduction = mileage.amount;

        // Assert
        expect(deduction, equals(65.5)); // 100 * 0.655
      });

      test('should calculate deduction with different rates', () {
        // Arrange
        const miles = 50.0;
        const customRate = 0.70; // Custom rate
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'Office',
          endLocation: 'Meeting',
          miles: miles,
          ratePerMile: customRate,
          date: DateTime.now(),
        );

        // Act
        final deduction = mileage.amount;

        // Assert
        expect(deduction, equals(35.0)); // 50 * 0.70
      });

      test('should handle zero miles', () {
        // Arrange
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'Same Location',
          endLocation: 'Same Location',
          miles: 0.0,
          ratePerMile: 0.655,
          date: DateTime.now(),
        );

        // Act
        final deduction = mileage.amount;

        // Assert
        expect(deduction, equals(0.0));
      });

      test('should handle fractional miles', () {
        // Arrange
        const miles = 12.5;
        const ratePerMile = 0.655;
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'Start',
          endLocation: 'End',
          miles: miles,
          ratePerMile: ratePerMile,
          date: DateTime.now(),
        );

        // Act
        final deduction = mileage.amount;

        // Assert
        expect(deduction, equals(8.1875)); // 12.5 * 0.655
      });
    });

    group('Mileage creation and properties', () {
      test('should create mileage with required fields', () {
        // Arrange
        final date = DateTime.now();
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'Home',
          endLocation: 'Work',
          miles: 25.0,
          ratePerMile: 0.655,
          date: date,
        );

        // Assert
        expect(mileage.userId, equals('user-1'));
        expect(mileage.startLocation, equals('Home'));
        expect(mileage.endLocation, equals('Work'));
        expect(mileage.miles, equals(25.0));
        expect(mileage.ratePerMile, equals(0.655));
        expect(mileage.date, equals(date));
        expect(mileage.isAutoTracked, isFalse);
        expect(mileage.id, isNotNull);
        expect(mileage.description, equals('Mileage: Home to Work'));
        expect(mileage.amount, equals(16.375)); // 25 * 0.655
      });

      test('should create mileage with optional fields', () {
        // Arrange
        final mileage = Mileage(
          userId: 'user-1',
          jobId: 'job-1',
          startLocation: 'Office',
          endLocation: 'Client',
          miles: 50.0,
          ratePerMile: 0.655,
          date: DateTime.now(),
          isAutoTracked: true,
          startCoordinates: '40.7128,-74.0060',
          endCoordinates: '40.7589,-73.9851',
          purpose: 'Client meeting',
          description: 'Custom description',
          category: 'Transportation',
          isOverhead: false,
        );

        // Assert
        expect(mileage.jobId, equals('job-1'));
        expect(mileage.isAutoTracked, isTrue);
        expect(mileage.startCoordinates, equals('40.7128,-74.0060'));
        expect(mileage.endCoordinates, equals('40.7589,-73.9851'));
        expect(mileage.purpose, equals('Client meeting'));
        expect(mileage.description, equals('Custom description'));
        expect(mileage.category, equals('Transportation'));
        expect(mileage.isOverhead, isFalse);
      });

      test('should inherit from Expense correctly', () {
        // Arrange
        final mileage = Mileage(
          userId: 'user-1',
          startLocation: 'A',
          endLocation: 'B',
          miles: 10.0,
          ratePerMile: 0.655,
          date: DateTime.now(),
        );

        // Assert
        expect(mileage, isA<Mileage>());
        expect(mileage.syncStatus, equals(SyncStatus.pending));
        expect(mileage.createdAt, isNotNull);
        expect(mileage.updatedAt, isNotNull);
      });
    });

    group('JSON serialization', () {
      test('should convert to JSON correctly', () {
        // Arrange
        final date = DateTime(2024, 1, 15);
        final mileage = Mileage(
          id: 'mileage-1',
          userId: 'user-1',
          jobId: 'job-1',
          startLocation: 'Home',
          endLocation: 'Office',
          miles: 30.0,
          ratePerMile: 0.655,
          date: date,
          isAutoTracked: true,
          startCoordinates: '40.7128,-74.0060',
          endCoordinates: '40.7589,-73.9851',
          purpose: 'Commute',
          description: 'Daily commute',
          category: 'Transportation',
        );

        // Act
        final json = mileage.toJson();

        // Assert
        expect(json['id'], equals('mileage-1'));
        expect(json['user_id'], equals('user-1'));
        expect(json['job_id'], equals('job-1'));
        expect(json['start_location'], equals('Home'));
        expect(json['end_location'], equals('Office'));
        expect(json['miles'], equals(30.0));
        expect(json['rate_per_mile'], equals(0.655));
        expect(json['is_auto_tracked'], isTrue);
        expect(json['start_coordinates'], equals('40.7128,-74.0060'));
        expect(json['end_coordinates'], equals('40.7589,-73.9851'));
        expect(json['purpose'], equals('Commute'));
        expect(json['description'], equals('Daily commute'));
        expect(json['category'], equals('Transportation'));
        expect(json['amount'], closeTo(19.65, 0.01)); // 30 * 0.655
      });

      test('should create from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'mileage-1',
          'user_id': 'user-1',
          'job_id': 'job-1',
          'start_location': 'Start Point',
          'end_location': 'End Point',
          'miles': 45.5,
          'rate_per_mile': 0.655,
          'date': '2024-01-15T10:00:00.000Z',
          'is_auto_tracked': false,
          'start_coordinates': '40.7128,-74.0060',
          'end_coordinates': '40.7589,-73.9851',
          'purpose': 'Business trip',
          'description': 'Client visit',
          'category': 'Transportation',
          'amount': 29.8025, // 45.5 * 0.655
          'is_overhead': false,
          'created_at': '2024-01-15T10:00:00.000Z',
          'updated_at': '2024-01-15T10:00:00.000Z',
        };

        // Act
        final mileage = Mileage.fromJson(json);

        // Assert
        expect(mileage.id, equals('mileage-1'));
        expect(mileage.userId, equals('user-1'));
        expect(mileage.jobId, equals('job-1'));
        expect(mileage.startLocation, equals('Start Point'));
        expect(mileage.endLocation, equals('End Point'));
        expect(mileage.miles, equals(45.5));
        expect(mileage.ratePerMile, equals(0.655));
        expect(mileage.isAutoTracked, isFalse);
        expect(mileage.startCoordinates, equals('40.7128,-74.0060'));
        expect(mileage.endCoordinates, equals('40.7589,-73.9851'));
        expect(mileage.purpose, equals('Business trip'));
        expect(mileage.description, equals('Client visit'));
        expect(mileage.category, equals('Transportation'));
        expect(mileage.amount, closeTo(29.8025, 0.01));
        expect(mileage.isOverhead, isFalse);
      });
    });

    group('copyWith method', () {
      test('should create copy with updated mileage-specific fields', () {
        // Arrange
        final originalMileage = Mileage(
          userId: 'user-1',
          startLocation: 'Original Start',
          endLocation: 'Original End',
          miles: 20.0,
          ratePerMile: 0.655,
          date: DateTime.now(),
          purpose: 'Original purpose',
        );

        // Act
        final updatedMileage = originalMileage.copyWith(
          startLocation: 'New Start',
          endLocation: 'New End',
          miles: 35.0,
          ratePerMile: 0.70,
          purpose: 'Updated purpose',
          isAutoTracked: true,
        );

        // Assert
        expect(updatedMileage.startLocation, equals('New Start'));
        expect(updatedMileage.endLocation, equals('New End'));
        expect(updatedMileage.miles, equals(35.0));
        expect(updatedMileage.ratePerMile, equals(0.70));
        expect(updatedMileage.purpose, equals('Updated purpose'));
        expect(updatedMileage.isAutoTracked, isTrue);
        expect(updatedMileage.amount, equals(24.5)); // 35 * 0.70
        expect(updatedMileage.userId, equals('user-1')); // Unchanged
        expect(updatedMileage.id, equals(originalMileage.id)); // Unchanged
      });

      test('should create copy with updated expense fields', () {
        // Arrange
        final originalMileage = Mileage(
          userId: 'user-1',
          startLocation: 'Start',
          endLocation: 'End',
          miles: 10.0,
          ratePerMile: 0.655,
          date: DateTime.now(),
        );

        // Act
        final updatedMileage = originalMileage.copyWith(
          description: 'Updated description',
          category: 'Updated category',
          isOverhead: true,
        );

        // Assert
        expect(updatedMileage.description, equals('Updated description'));
        expect(updatedMileage.category, equals('Updated category'));
        expect(updatedMileage.isOverhead, isTrue);
        expect(updatedMileage.miles, equals(10.0)); // Unchanged
        expect(
          updatedMileage.amount,
          closeTo(6.55, 0.01),
        ); // 10 * 0.655 (unchanged)
      });
    });
  });
}
