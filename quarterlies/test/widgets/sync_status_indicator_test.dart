import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/widgets/sync_status_indicator.dart';
import 'package:quarterlies/models/sync_status.dart';

void main() {
  group('SyncStatusIndicator Widget Tests', () {
    Widget createTestWidget(Widget child) {
      return MaterialApp(home: Scaffold(body: child));
    }

    group('Sync status display', () {
      testWidgets('should display pending sync status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        expect(find.byType(SyncStatusIndicator), findsOneWidget);

        // Look for pending indicator (cloud_upload icon)
        expect(find.byIcon(Icons.cloud_upload), findsOneWidget);
      });

      testWidgets('should display synced status', (WidgetTester tester) async {
        // Arrange
        const syncStatus = SyncStatus.synced;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        expect(find.byType(SyncStatusIndicator), findsOneWidget);

        // Look for synced indicator (cloud_done icon)
        expect(find.byIcon(Icons.cloud_done), findsOneWidget);
      });

      testWidgets('should display error sync status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.error;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        expect(find.byType(SyncStatusIndicator), findsOneWidget);

        // Look for error indicator (cloud_off icon)
        expect(find.byIcon(Icons.cloud_off), findsOneWidget);
      });

      testWidgets('should display conflict sync status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.conflict;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        expect(find.byType(SyncStatusIndicator), findsOneWidget);

        // Look for conflict indicator (sync_problem icon)
        expect(find.byIcon(Icons.sync_problem), findsOneWidget);
      });
    });

    group('Sync status colors', () {
      testWidgets('should use correct color for pending status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        final iconWidget = tester.widget<Icon>(find.byIcon(Icons.cloud_upload));
        expect(iconWidget.color, equals(Colors.orange));
      });

      testWidgets('should use correct color for synced status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.synced;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        final iconWidget = tester.widget<Icon>(find.byIcon(Icons.cloud_done));
        expect(iconWidget.color, equals(Colors.green));
      });

      testWidgets('should use correct color for error status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.error;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        final iconWidget = tester.widget<Icon>(find.byIcon(Icons.cloud_off));
        expect(iconWidget.color, equals(Colors.red));
      });

      testWidgets('should use correct color for conflict status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.conflict;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert
        final iconWidget = tester.widget<Icon>(find.byIcon(Icons.sync_problem));
        expect(iconWidget.color, equals(Colors.deepOrange));
      });
    });

    group('Sync status interactions', () {
      testWidgets('should handle tap on conflict status', (
        WidgetTester tester,
      ) async {
        // Arrange
        bool tapped = false;
        const syncStatus = SyncStatus.conflict;

        // Act
        await tester.pumpWidget(
          createTestWidget(
            SyncStatusIndicator(status: syncStatus, onTap: () => tapped = true),
          ),
        );

        await tester.tap(find.byType(InkWell));
        await tester.pumpAndSettle();

        // Assert
        expect(tapped, isTrue);
      });

      testWidgets('should show tooltip on hover', (WidgetTester tester) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert - Just verify the widget renders without errors
        expect(find.byType(SyncStatusIndicator), findsOneWidget);
        expect(find.byType(Tooltip), findsOneWidget);
      });
    });

    group('Sync status animations', () {
      testWidgets('should animate error status', (WidgetTester tester) async {
        // Arrange
        const syncStatus = SyncStatus.error;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Check for animation (TweenAnimationBuilder)
        expect(find.byType(TweenAnimationBuilder<double>), findsOneWidget);
      });

      testWidgets('should not animate synced status', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.synced;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert - No animation for synced status
        expect(find.byType(TweenAnimationBuilder<double>), findsNothing);
      });
    });

    group('Sync status accessibility', () {
      testWidgets('should have tooltip for accessibility', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus)),
        );

        // Assert - Just verify tooltip exists
        expect(find.byType(Tooltip), findsOneWidget);
      });
    });

    group('Sync status size variants', () {
      testWidgets('should render with custom size', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.synced;

        // Act
        await tester.pumpWidget(
          createTestWidget(SyncStatusIndicator(status: syncStatus, size: 24.0)),
        );

        // Assert
        final iconWidget = tester.widget<Icon>(find.byIcon(Icons.cloud_done));
        expect(iconWidget.size, equals(24.0));
      });
    });

    group('Sync status with text', () {
      testWidgets('should show text when showLabel is true', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(
            SyncStatusIndicator(status: syncStatus, showLabel: true),
          ),
        );

        // Assert
        expect(find.text('PENDING'), findsOneWidget);
        expect(find.byIcon(Icons.cloud_upload), findsOneWidget);
      });

      testWidgets('should hide text when showLabel is false', (
        WidgetTester tester,
      ) async {
        // Arrange
        const syncStatus = SyncStatus.pending;

        // Act
        await tester.pumpWidget(
          createTestWidget(
            SyncStatusIndicator(status: syncStatus, showLabel: false),
          ),
        );

        // Assert
        expect(find.text('PENDING'), findsNothing);
        expect(find.byIcon(Icons.cloud_upload), findsOneWidget);
      });
    });
  });
}
