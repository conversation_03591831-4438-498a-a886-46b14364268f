import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:quarterlies/widgets/budget_chart.dart';
import 'package:quarterlies/services/budget_service.dart';

void main() {
  group('BudgetChart Widget Tests', () {
    Widget createTestWidget(Widget child) {
      return MaterialApp(home: Scaffold(body: child));
    }

    group('Budget chart rendering', () {
      testWidgets('should render budget chart with valid data', (
        WidgetTester tester,
      ) async {
        // Arrange
        final budgetData = BudgetData(
          jobId: 'test-job',
          budget: 10000.0,
          totalActualCosts: 5000.0, // Well under budget
          totalExpenses: 2500.0,
          totalLaborCost: 2500.0,
          budgetStatus: BudgetStatus.underBudget,
          expenses: [],
          timeLogs: [],
        );

        // Act
        await tester.pumpWidget(
          createTestWidget(BudgetChart(budgetData: budgetData)),
        );

        // Assert
        expect(find.byType(BudgetChart), findsOneWidget);

        // Check that the chart widget is present
        // The exact chart implementation may use fl_chart or other charting library
        expect(find.byType(Container), findsWidgets);
      });

      testWidgets('should handle zero values gracefully', (
        WidgetTester tester,
      ) async {
        // Arrange
        final budgetData = BudgetData(
          jobId: 'test-job',
          budget: 0.0,
          totalActualCosts: 0.0,
          totalExpenses: 0.0,
          totalLaborCost: 0.0,
          budgetStatus: BudgetStatus.noBudget,
          expenses: [],
          timeLogs: [],
        );

        // Act
        await tester.pumpWidget(
          createTestWidget(BudgetChart(budgetData: budgetData)),
        );

        // Assert
        expect(find.byType(BudgetChart), findsOneWidget);

        // Should render without errors even with zero values
        expect(tester.takeException(), isNull);
      });

      testWidgets('should render without errors', (WidgetTester tester) async {
        // Arrange - Simple test data that won't trigger alerts
        final budgetData = BudgetData(
          jobId: 'test-job',
          budget: 5000.0,
          totalActualCosts: 3000.0, // Under budget
          totalExpenses: 1500.0,
          totalLaborCost: 1500.0,
          budgetStatus: BudgetStatus.underBudget,
          expenses: [],
          timeLogs: [],
        );

        // Act
        await tester.pumpWidget(
          createTestWidget(BudgetChart(budgetData: budgetData)),
        );

        // Assert
        expect(find.byType(BudgetChart), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });
  });
}
