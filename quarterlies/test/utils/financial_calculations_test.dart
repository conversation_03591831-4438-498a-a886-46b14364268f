import 'package:flutter_test/flutter_test.dart';
import 'dart:math' as math;

void main() {
  group('Financial Calculations Utility Tests', () {
    group('Tax calculations', () {
      test('should calculate self-employment tax correctly', () {
        // Arrange
        const netEarnings = 50000.0;
        const selfEmploymentTaxRate = 0.1413; // 14.13% for 2024

        // Act
        final selfEmploymentTax = netEarnings * selfEmploymentTaxRate;

        // Assert
        expect(selfEmploymentTax, equals(7065.0));
      });

      test('should calculate estimated quarterly tax', () {
        // Arrange
        const annualIncome = 80000.0;
        const annualDeductions = 20000.0;
        const taxableIncome = annualIncome - annualDeductions;
        const estimatedTaxRate = 0.22; // 22% tax bracket

        // Act
        final annualTax = taxableIncome * estimatedTaxRate;
        final quarterlyTax = annualTax / 4;

        // Assert
        expect(annualTax, equals(13200.0)); // 60000 * 0.22
        expect(quarterlyTax, equals(3300.0)); // 13200 / 4
      });

      test('should handle tax calculation with no income', () {
        // Arrange
        const netEarnings = 0.0;
        const taxRate = 0.15;

        // Act
        final tax = math.max(0, netEarnings * taxRate);

        // Assert
        expect(tax, equals(0.0));
      });

      test('should handle tax calculation with losses', () {
        // Arrange
        const netEarnings = -5000.0; // Loss
        const taxRate = 0.15;

        // Act
        final tax = math.max(0, netEarnings * taxRate);

        // Assert
        expect(tax, equals(0.0)); // No tax on losses
      });
    });

    group('Profit margin calculations', () {
      test('should calculate profit margin correctly', () {
        // Arrange
        const revenue = 100000.0;
        const expenses = 60000.0;
        const profit = revenue - expenses;

        // Act
        final profitMargin = (profit / revenue) * 100;

        // Assert
        expect(profitMargin, equals(40.0)); // 40% profit margin
      });

      test('should handle zero revenue for profit margin', () {
        // Arrange
        const revenue = 0.0;
        const expenses = 1000.0;
        const profit = revenue - expenses;

        // Act
        final profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0.0;

        // Assert
        expect(profitMargin, equals(0.0)); // Avoid division by zero
      });

      test('should calculate negative profit margin', () {
        // Arrange
        const revenue = 50000.0;
        const expenses = 70000.0;
        const profit = revenue - expenses; // -20000

        // Act
        final profitMargin = (profit / revenue) * 100;

        // Assert
        expect(profitMargin, equals(-40.0)); // -40% (loss)
      });
    });

    group('Overhead allocation calculations', () {
      test('should allocate overhead by revenue percentage', () {
        // Arrange
        const totalRevenue = 200000.0;
        const jobRevenue = 30000.0;
        const totalOverhead = 24000.0;

        // Act
        final revenuePercentage = jobRevenue / totalRevenue;
        final allocatedOverhead = totalOverhead * revenuePercentage;

        // Assert
        expect(revenuePercentage, equals(0.15)); // 15%
        expect(allocatedOverhead, equals(3600.0)); // 24000 * 0.15
      });

      test('should allocate overhead by time percentage', () {
        // Arrange
        const totalHours = 2000.0;
        const jobHours = 250.0;
        const totalOverhead = 20000.0;

        // Act
        final timePercentage = jobHours / totalHours;
        final allocatedOverhead = totalOverhead * timePercentage;

        // Assert
        expect(timePercentage, equals(0.125)); // 12.5%
        expect(allocatedOverhead, equals(2500.0)); // 20000 * 0.125
      });

      test('should handle zero total for overhead allocation', () {
        // Arrange
        const totalRevenue = 0.0;
        const jobRevenue = 10000.0;
        const totalOverhead = 5000.0;

        // Act
        final revenuePercentage =
            totalRevenue > 0 ? jobRevenue / totalRevenue : 0.0;
        final allocatedOverhead = totalOverhead * revenuePercentage;

        // Assert
        expect(revenuePercentage, equals(0.0));
        expect(allocatedOverhead, equals(0.0));
      });
    });

    group('Mileage deduction calculations', () {
      test('should calculate standard mileage deduction', () {
        // Arrange
        const miles = 1000.0;
        const irsRate2024 = 0.655; // 65.5 cents per mile for 2024

        // Act
        final deduction = miles * irsRate2024;

        // Assert
        expect(deduction, equals(655.0));
      });

      test('should calculate mileage deduction with mixed rates', () {
        // Arrange - Different rates for different periods
        final mileageEntries = [
          {'miles': 500.0, 'rate': 0.655}, // 2024 rate
          {'miles': 300.0, 'rate': 0.625}, // 2023 rate
          {'miles': 200.0, 'rate': 0.585}, // 2022 rate
        ];

        // Act
        final totalDeduction = mileageEntries.fold(0.0, (sum, entry) {
          return sum + (entry['miles']! * entry['rate']!);
        });

        // Assert
        expect(
          totalDeduction,
          equals(632.0),
        ); // (500*0.655) + (300*0.625) + (200*0.585) = 327.5 + 187.5 + 117 = 632.0
      });

      test('should handle zero miles', () {
        // Arrange
        const miles = 0.0;
        const rate = 0.655;

        // Act
        final deduction = miles * rate;

        // Assert
        expect(deduction, equals(0.0));
      });
    });

    group('Depreciation calculations', () {
      test('should calculate straight-line depreciation', () {
        // Arrange
        const assetCost = 10000.0;
        const salvageValue = 1000.0;
        const usefulLife = 5.0; // years

        // Act
        final annualDepreciation = (assetCost - salvageValue) / usefulLife;

        // Assert
        expect(annualDepreciation, equals(1800.0)); // (10000 - 1000) / 5
      });

      test('should calculate declining balance depreciation', () {
        // Arrange
        const assetCost = 10000.0;
        const depreciationRate = 0.20; // 20% declining balance

        // Act
        final firstYearDepreciation = assetCost * depreciationRate;

        // Assert
        expect(firstYearDepreciation, equals(2000.0)); // 10000 * 0.20
      });
    });

    group('Interest and loan calculations', () {
      test('should calculate simple interest', () {
        // Arrange
        const principal = 10000.0;
        const rate = 0.05; // 5% annual rate
        const time = 2.0; // 2 years

        // Act
        final interest = principal * rate * time;
        final totalAmount = principal + interest;

        // Assert
        expect(interest, equals(1000.0)); // 10000 * 0.05 * 2
        expect(totalAmount, equals(11000.0));
      });

      test('should calculate compound interest', () {
        // Arrange
        const principal = 10000.0;
        const rate = 0.05; // 5% annual rate
        const compoundingPeriods = 12.0; // Monthly compounding
        const time = 2.0; // 2 years

        // Act
        final amount =
            principal *
            math.pow(
              1 + (rate / compoundingPeriods),
              compoundingPeriods * time,
            );
        final interest = amount - principal;

        // Assert
        expect(amount, closeTo(11049.41, 0.01)); // Compound interest result
        expect(interest, closeTo(1049.41, 0.01));
      });

      test('should calculate monthly payment for loan', () {
        // Arrange
        const principal = 100000.0; // Loan amount
        const annualRate = 0.06; // 6% annual rate
        const monthlyRate = annualRate / 12;
        const numberOfPayments = 30 * 12; // 30 years

        // Act
        final monthlyPayment =
            principal *
            (monthlyRate * math.pow(1 + monthlyRate, numberOfPayments)) /
            (math.pow(1 + monthlyRate, numberOfPayments) - 1);

        // Assert
        expect(
          monthlyPayment,
          closeTo(599.55, 0.01),
        ); // Standard mortgage calculation
      });
    });

    group('Break-even analysis', () {
      test('should calculate break-even point in units', () {
        // Arrange
        const fixedCosts = 50000.0;
        const pricePerUnit = 100.0;
        const variableCostPerUnit = 60.0;

        // Act
        final contributionMargin = pricePerUnit - variableCostPerUnit;
        final breakEvenUnits = fixedCosts / contributionMargin;

        // Assert
        expect(contributionMargin, equals(40.0));
        expect(breakEvenUnits, equals(1250.0)); // 50000 / 40
      });

      test('should calculate break-even point in revenue', () {
        // Arrange
        const fixedCosts = 50000.0;
        const pricePerUnit = 100.0;
        const variableCostPerUnit = 60.0;

        // Act
        final contributionMarginRatio =
            (pricePerUnit - variableCostPerUnit) / pricePerUnit;
        final breakEvenRevenue = fixedCosts / contributionMarginRatio;

        // Assert
        expect(contributionMarginRatio, equals(0.4)); // 40%
        expect(breakEvenRevenue, equals(125000.0)); // 50000 / 0.4
      });
    });

    group('ROI and ROE calculations', () {
      test('should calculate return on investment (ROI)', () {
        // Arrange
        const initialInvestment = 10000.0;
        const finalValue = 12000.0;

        // Act
        final roi =
            ((finalValue - initialInvestment) / initialInvestment) * 100;

        // Assert
        expect(roi, equals(20.0)); // 20% ROI
      });

      test('should calculate return on equity (ROE)', () {
        // Arrange
        const netIncome = 50000.0;
        const shareholderEquity = 200000.0;

        // Act
        final roe = (netIncome / shareholderEquity) * 100;

        // Assert
        expect(roe, equals(25.0)); // 25% ROE
      });
    });

    group('Currency and rounding', () {
      test('should round currency to 2 decimal places', () {
        // Arrange
        const amount = 123.456789;

        // Act
        final rounded = (amount * 100).round() / 100;

        // Assert
        expect(rounded, equals(123.46));
      });

      test('should handle very small amounts', () {
        // Arrange
        const amount = 0.001;

        // Act
        final rounded = (amount * 100).round() / 100;

        // Assert
        expect(rounded, equals(0.0)); // Rounds to 0
      });

      test('should handle negative amounts', () {
        // Arrange
        const amount = -123.456;

        // Act
        final rounded = (amount * 100).round() / 100;

        // Assert
        expect(rounded, equals(-123.46));
      });
    });
  });
}
