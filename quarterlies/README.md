# Quarterlies

A comprehensive business management application built with Flutter, designed for field workers and office professionals. Features offline capabilities, electronic signatures, voice integration, and field-friendly design optimized for outdoor visibility.

## Features

- **Customer & Job Management**: Complete CRM with job tracking and cost analysis
- **Estimates & Invoices**: Professional PDF generation with electronic signatures
- **Expense Tracking**: IRS Schedule C categorization with receipt OCR scanning
- **Time & Mileage Tracking**: GPS-enabled tracking with automatic calculations
- **Offline Capabilities**: Full offline functionality with intelligent sync
- **Voice Integration**: Voice search and note recording throughout the app
- **Field-Friendly Design**: High contrast colors and large touch targets for outdoor use

## Design System

The application follows a comprehensive design blueprint optimized for both field and office environments. See [Design Blueprint](../docs/blueprint.md) for detailed design specifications including:

- Field-friendly color scheme with high contrast ratios
- Dual display modes (Field Mode and Office Mode)
- Material Design 3 implementation with accessibility enhancements
- Context-sensitive workflow patterns

## Getting Started

### Prerequisites

- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / VS Code with Flutter extensions
- Supabase account for backend services

### Installation

1. Clone the repository
2. Install dependencies: `flutter pub get`
3. Configure Supabase credentials in environment files
4. Run the application: `flutter run`

## Documentation

- [Design Blueprint](../docs/blueprint.md) - Complete design system and UI guidelines
- [Implementation Analysis](../IMPLEMENTATION_ANALYSIS.md) - Current implementation status
- [Server Implementation](SERVER_IMPLEMENTATION.md) - Backend setup and configuration
- [Security Implementation](SECURITY_IMPLEMENTATION.md) - Security features and best practices

## Architecture

The application uses a clean architecture pattern with:

- **Provider** for state management
- **Repository pattern** for data access
- **Service layer** for business logic
- **SQLite** for offline storage
- **Supabase** for backend services

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
