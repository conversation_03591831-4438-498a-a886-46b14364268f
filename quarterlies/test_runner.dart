#!/usr/bin/env dart

import 'dart:io';
import 'dart:developer' as developer;

// Utility function to replace print statements for test runner
void log(String message) {
  developer.log(message, name: 'TestRunner');
  // Also output to stdout for visibility
  stdout.writeln(message);
}

/// Test runner script for Quarterlies app
/// Provides convenient commands to run different test categories
///
/// Usage:
///   dart test_runner.dart [command]
///
/// Commands:
///   unit        - Run all unit tests (models, services, utils)
///   widget      - Run all widget tests
///   models      - Run model tests only
///   services    - Run service tests only
///   calculations - Run calculation tests only
///   all         - Run all tests
///   coverage    - Run tests with coverage report
///   watch       - Run tests in watch mode

void main(List<String> args) async {
  if (args.isEmpty) {
    printUsage();
    exit(1);
  }

  final command = args[0].toLowerCase();

  switch (command) {
    case 'unit':
      await runUnitTests();
      break;
    case 'widget':
      await runWidgetTests();
      break;
    case 'models':
      await runModelTests();
      break;
    case 'services':
      await runServiceTests();
      break;
    case 'auth':
      await runAuthServiceTests();
      break;
    case 'email':
      await runEmailServiceTests();
      break;
    case 'sync':
      await runSyncServiceTests();
      break;
    case 'voice':
      await runVoiceServiceTests();
      break;
    case 'calculations':
      await runCalculationTests();
      break;
    case 'all':
      await runAllTests();
      break;
    case 'coverage':
      await runTestsWithCoverage();
      break;
    case 'watch':
      await runTestsInWatchMode();
      break;
    default:
      log('Unknown command: $command');
      printUsage();
      exit(1);
  }
}

void printUsage() {
  log('Quarterlies Test Runner');
  log('=======================');
  log('');
  log('Usage: dart test_runner.dart [command]');
  log('');
  log('Commands:');
  log('  unit         - Run all unit tests (models, services, utils)');
  log('  widget       - Run all widget tests');
  log('  models       - Run model tests only');
  log('  services     - Run service tests only');
  log('  auth         - Run authentication service tests only');
  log('  email        - Run email service tests only');
  log('  sync         - Run sync service tests only');
  log('  voice        - Run voice service tests only');
  log('  calculations - Run calculation tests only');
  log('  all          - Run all tests');
  log('  coverage     - Run tests with coverage report');
  log('  watch        - Run tests in watch mode');
  log('');
  log('Examples:');
  log('  dart test_runner.dart models');
  log('  dart test_runner.dart coverage');
  log('  dart test_runner.dart all');
}

Future<void> runUnitTests() async {
  log('🧪 Running unit tests...');
  await runFlutterTest(['test/models/', 'test/services/', 'test/utils/']);
}

Future<void> runWidgetTests() async {
  log('🎨 Running widget tests...');
  await runFlutterTest(['test/widgets/']);
}

Future<void> runModelTests() async {
  log('📊 Running model tests...');
  await runFlutterTest(['test/models/']);
}

Future<void> runServiceTests() async {
  log('⚙️ Running service tests...');
  await runFlutterTest(['test/services/']);
}

Future<void> runCalculationTests() async {
  log('🧮 Running calculation tests...');
  await runFlutterTest(['test/utils/financial_calculations_test.dart']);
}

Future<void> runAuthServiceTests() async {
  log('🔐 Running authentication service tests...');
  await runFlutterTest(['test/services/auth_service_test.dart']);
}

Future<void> runEmailServiceTests() async {
  log('📧 Running email service tests...');
  await runFlutterTest(['test/services/email_service_test.dart']);
}

Future<void> runSyncServiceTests() async {
  log('🔄 Running sync service tests...');
  await runFlutterTest(['test/services/sync_service_test.dart']);
}

Future<void> runVoiceServiceTests() async {
  log('🎤 Running voice service tests...');
  await runFlutterTest(['test/services/voice_recording_service_test.dart']);
}

Future<void> runAllTests() async {
  log('🚀 Running all tests...');
  await runFlutterTest(['test/']);
}

Future<void> runTestsWithCoverage() async {
  log('📈 Running tests with coverage...');
  await runFlutterTest(['test/'], coverage: true);

  // Generate coverage report
  log('📊 Generating coverage report...');
  await runCommand('genhtml', ['coverage/lcov.info', '-o', 'coverage/html']);

  log('✅ Coverage report generated at coverage/html/index.html');
}

Future<void> runTestsInWatchMode() async {
  log('👀 Running tests in watch mode...');
  await runFlutterTest(['test/'], watch: true);
}

Future<void> runFlutterTest(
  List<String> paths, {
  bool coverage = false,
  bool watch = false,
}) async {
  final args = ['test'];

  if (coverage) {
    args.add('--coverage');
  }

  if (watch) {
    args.add('--watch');
  }

  args.addAll(paths);

  await runCommand('flutter', args);
}

Future<void> runCommand(String command, List<String> args) async {
  log('Running: $command ${args.join(' ')}');

  final process = await Process.start(command, args);

  // Stream output
  process.stdout.listen((data) {
    stdout.add(data);
  });

  process.stderr.listen((data) {
    stderr.add(data);
  });

  final exitCode = await process.exitCode;

  if (exitCode != 0) {
    log('❌ Command failed with exit code: $exitCode');
    exit(exitCode);
  }
}
