

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE OR REPLACE FUNCTION "public"."get_user_expense_categories_by_usage"("user_id" "uuid") RETURNS TABLE("category" "text", "count" integer, "last_used" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    ecu.category,
    ecu.usage_count,
    ecu.last_used
  FROM
    expense_category_usage ecu
  WHERE
    ecu.user_id = user_id
  ORDER BY
    ecu.usage_count DESC,
    ecu.last_used DESC;
END;
$$;


ALTER FUNCTION "public"."get_user_expense_categories_by_usage"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_user_expense_category_usage"("category_name" "text", "user_id" "uuid") RETURNS integer
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Try to update existing record
  UPDATE expense_category_usage
  SET
    usage_count = usage_count + 1,
    last_used = NOW(),
    updated_at = NOW()
  WHERE
    user_id = user_id AND
    category = category_name
  RETURNING usage_count INTO current_count;

  -- If no record was updated, insert a new one
  IF current_count IS NULL THEN
    INSERT INTO expense_category_usage (user_id, category, usage_count)
    VALUES (user_id, category_name, 1)
    RETURNING usage_count INTO current_count;
  END IF;

  RETURN current_count;
END;
$$;


ALTER FUNCTION "public"."increment_user_expense_category_usage"("category_name" "text", "user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."log_financial_transaction"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO audit_logs (user_id, action_type, table_name, record_id, old_data, new_data, ip_address)
  VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    CASE
      WHEN TG_OP = 'DELETE' THEN OLD.id
      ELSE NEW.id
    END,
    CASE
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN to_jsonb(OLD)
      ELSE NULL
    END,
    CASE
      WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW)
      ELSE NULL
    END,
    current_setting('request.headers', true)::json->>'x-forwarded-for'
  );
  RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."log_financial_transaction"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."app_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "value" "text" NOT NULL,
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."app_settings" OWNER TO "postgres";


COMMENT ON TABLE "public"."app_settings" IS 'Application-wide settings and configuration';



CREATE TABLE IF NOT EXISTS "public"."audit_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "action_type" "text" NOT NULL,
    "table_name" "text",
    "record_id" "uuid",
    "old_data" "jsonb",
    "new_data" "jsonb",
    "ip_address" "text",
    "user_agent" "text",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."audit_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."contracts" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "job_id" "uuid" NOT NULL,
    "customer_id" "uuid" NOT NULL,
    "estimate_id" "uuid",
    "line_items" "jsonb" DEFAULT '[]'::"jsonb",
    "total_amount" numeric(12,2) DEFAULT 0.0 NOT NULL,
    "status" "text" DEFAULT 'draft'::"text" NOT NULL,
    "notes" "text",
    "sync_status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "check_contracts_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."contracts" OWNER TO "postgres";


COMMENT ON TABLE "public"."contracts" IS 'Contracts derived from estimates with electronic signing';



CREATE TABLE IF NOT EXISTS "public"."customers" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "email" "text",
    "phone" "text",
    "address" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_customers_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."customers" OWNER TO "postgres";


COMMENT ON TABLE "public"."customers" IS 'Customer information and contact details';



CREATE TABLE IF NOT EXISTS "public"."document_signing_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "document_type" "text" NOT NULL,
    "document_id" "text" NOT NULL,
    "customer_id" "text" NOT NULL,
    "customer_email" "text" NOT NULL,
    "customer_name" "text" NOT NULL,
    "job_id" "text" NOT NULL,
    "signing_link" "text" NOT NULL,
    "expiration_date" timestamp with time zone NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "local_pdf_path" "text",
    "remote_pdf_url" "text",
    "sync_status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "document_signing_requests_document_type_check" CHECK (("document_type" = ANY (ARRAY['estimate'::"text", 'contract'::"text"])))
);


ALTER TABLE "public"."document_signing_requests" OWNER TO "postgres";


COMMENT ON TABLE "public"."document_signing_requests" IS 'Electronic signature requests';



CREATE TABLE IF NOT EXISTS "public"."email_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "email_type" "text" NOT NULL,
    "recipient" "text" NOT NULL,
    "subject" "text" NOT NULL,
    "document_type" "text",
    "document_id" "text",
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "error_message" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."email_logs" OWNER TO "postgres";


COMMENT ON TABLE "public"."email_logs" IS 'Email sending tracking and status';



CREATE TABLE IF NOT EXISTS "public"."estimates" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "job_id" "uuid" NOT NULL,
    "customer_id" "uuid" NOT NULL,
    "estimate_number" "text" NOT NULL,
    "date" "date" NOT NULL,
    "expiry_date" "date",
    "status" "text" DEFAULT 'draft'::"text" NOT NULL,
    "subtotal" numeric(10,2) DEFAULT 0 NOT NULL,
    "tax_rate" numeric(5,2) DEFAULT 0,
    "tax_amount" numeric(10,2) DEFAULT 0,
    "total" numeric(10,2) DEFAULT 0 NOT NULL,
    "notes" "text",
    "line_items" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_estimates_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."estimates" OWNER TO "postgres";


COMMENT ON TABLE "public"."estimates" IS 'Estimates with line items and template support';



CREATE TABLE IF NOT EXISTS "public"."expense_category_usage" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "category" "text" NOT NULL,
    "usage_count" integer DEFAULT 1 NOT NULL,
    "last_used" timestamp with time zone DEFAULT "now"(),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."expense_category_usage" OWNER TO "postgres";


COMMENT ON TABLE "public"."expense_category_usage" IS 'User-specific expense category usage tracking';



CREATE TABLE IF NOT EXISTS "public"."expenses" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "job_id" "uuid",
    "date" "date" NOT NULL,
    "amount" numeric(10,2) NOT NULL,
    "description" "text" NOT NULL,
    "category" "text",
    "receipt_url" "text",
    "tax_deductible" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "invoiced_in_id" "uuid",
    "tags" "jsonb",
    "pending_invoice_id" "uuid",
    "pending_invoice_number" "text",
    "type" "text" DEFAULT 'Expense'::"text",
    "start_location" "text",
    "end_location" "text",
    "miles" numeric(8,2),
    "rate_per_mile" numeric(6,4),
    "is_auto_tracked" boolean DEFAULT false,
    "start_coordinates" "jsonb",
    "end_coordinates" "jsonb",
    "purpose" "text",
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_expenses_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."expenses" OWNER TO "postgres";


COMMENT ON TABLE "public"."expenses" IS 'Expenses and mileage tracking (unified table)';



CREATE TABLE IF NOT EXISTS "public"."invoices" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "job_id" "uuid" NOT NULL,
    "customer_id" "uuid" NOT NULL,
    "invoice_number" "text" NOT NULL,
    "date" "date" NOT NULL,
    "due_date" "date" NOT NULL,
    "status" "text" DEFAULT 'draft'::"text" NOT NULL,
    "subtotal" numeric(10,2) DEFAULT 0 NOT NULL,
    "tax_rate" numeric(5,2) DEFAULT 0,
    "tax_amount" numeric(10,2) DEFAULT 0,
    "total" numeric(10,2) DEFAULT 0 NOT NULL,
    "notes" "text",
    "line_items" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_invoices_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."invoices" OWNER TO "postgres";


COMMENT ON TABLE "public"."invoices" IS 'Invoices with line items and payment tracking';



CREATE TABLE IF NOT EXISTS "public"."jobs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "customer_id" "uuid" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text",
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "start_date" "date",
    "end_date" "date",
    "location" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_jobs_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."jobs" OWNER TO "postgres";


COMMENT ON TABLE "public"."jobs" IS 'Job/project management with cost tracking and sync settings';



CREATE TABLE IF NOT EXISTS "public"."mileage" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "expense_id" "uuid",
    "job_id" "uuid",
    "date" "date" NOT NULL,
    "start_location" "text" NOT NULL,
    "end_location" "text" NOT NULL,
    "distance" real NOT NULL,
    "rate" numeric(10,2) NOT NULL,
    "total" numeric(10,2) NOT NULL,
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."mileage" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "invoice_id" "uuid",
    "job_id" "uuid",
    "date" "date" NOT NULL,
    "amount" numeric(10,2) NOT NULL,
    "payment_method" "text",
    "reference_number" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "amount_received" numeric(12,2),
    "payment_date" "date",
    "voice_note_url" "text",
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_payments_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."payments" OWNER TO "postgres";


COMMENT ON TABLE "public"."payments" IS 'Payment records linked to invoices and jobs';



CREATE TABLE IF NOT EXISTS "public"."signed_documents" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "signing_request_id" "text" NOT NULL,
    "document_type" "text" NOT NULL,
    "document_id" "text" NOT NULL,
    "customer_name" "text" NOT NULL,
    "customer_email" "text" NOT NULL,
    "signed_at" timestamp with time zone NOT NULL,
    "ip_address" "text",
    "device_info" "text",
    "customer_pdf_path" "text",
    "contractor_pdf_path" "text",
    "customer_pdf_url" "text",
    "contractor_pdf_url" "text",
    "certification_pdf_path" "text",
    "sync_status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "signed_documents_document_type_check" CHECK (("document_type" = ANY (ARRAY['estimate'::"text", 'contract'::"text"])))
);


ALTER TABLE "public"."signed_documents" OWNER TO "postgres";


COMMENT ON TABLE "public"."signed_documents" IS 'Completed electronic signature records';



CREATE TABLE IF NOT EXISTS "public"."tax_payments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "date" "date" NOT NULL,
    "amount" numeric(10,2) NOT NULL,
    "tax_period_start" "date" NOT NULL,
    "tax_period_end" "date" NOT NULL,
    "tax_authority" "text" NOT NULL,
    "reference_number" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "tax_period" "text",
    "payment_method" "text",
    "confirmation_number" "text",
    "voice_note_url" "text",
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_tax_payments_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."tax_payments" OWNER TO "postgres";


COMMENT ON TABLE "public"."tax_payments" IS 'Quarterly tax payment tracking';



CREATE TABLE IF NOT EXISTS "public"."time_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "job_id" "uuid" NOT NULL,
    "start_time" timestamp with time zone NOT NULL,
    "end_time" timestamp with time zone,
    "duration" integer,
    "description" "text",
    "hourly_rate" numeric(10,2),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "invoiced_in_id" "uuid",
    "date" "date",
    "hours" numeric(8,2),
    "notes" "text",
    "labor_cost" numeric(12,2),
    "is_flat_rate" boolean DEFAULT false,
    "voice_note_url" "text",
    "pending_invoice_id" "uuid",
    "pending_invoice_number" "text",
    "sync_status" "text" DEFAULT 'pending'::"text",
    CONSTRAINT "check_time_logs_sync_status" CHECK (("sync_status" = ANY (ARRAY['pending'::"text", 'synced'::"text", 'error'::"text"])))
);


ALTER TABLE "public"."time_logs" OWNER TO "postgres";


COMMENT ON TABLE "public"."time_logs" IS 'Time tracking with labor cost calculation';



CREATE TABLE IF NOT EXISTS "public"."user_profiles" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "first_name" "text",
    "last_name" "text",
    "email" "text",
    "phone" "text",
    "address" "text",
    "city" "text",
    "state" "text",
    "zip_code" "text",
    "country" "text",
    "profile_photo_url" "text",
    "signature_image_url" "text",
    "is_onboarding_complete" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_profiles" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_profiles" IS 'Personal user information and onboarding status';



CREATE TABLE IF NOT EXISTS "public"."user_settings" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "default_live_job_cost_sync" boolean DEFAULT false NOT NULL,
    "sync_expenses" boolean DEFAULT false NOT NULL,
    "sync_mileage" boolean DEFAULT false NOT NULL,
    "sync_labor_costs" boolean DEFAULT false NOT NULL,
    "sync_estimate_items" boolean DEFAULT true NOT NULL,
    "default_invoice_due_days" integer DEFAULT 30 NOT NULL,
    "enable_due_date_notifications" boolean DEFAULT true NOT NULL,
    "due_date_notification_days" integer DEFAULT 3 NOT NULL,
    "enable_mileage_tracking" boolean DEFAULT false NOT NULL,
    "mileage_idle_timeout_minutes" integer DEFAULT 5 NOT NULL,
    "enable_voice_input" boolean DEFAULT true NOT NULL,
    "enable_offline_mode" boolean DEFAULT true NOT NULL,
    "wifi_only_sync" boolean DEFAULT true NOT NULL,
    "business_name" "text",
    "business_address" "text",
    "business_phone" "text",
    "business_email" "text",
    "business_logo" "text",
    "default_invoice_notes" "text",
    "default_invoice_terms" "text",
    "show_mileage_as_summary" boolean DEFAULT true NOT NULL,
    "show_hours_as_individual" boolean DEFAULT true NOT NULL,
    "automatic_brightness_detection" boolean DEFAULT true NOT NULL,
    "dynamic_color_adjustment" boolean DEFAULT true NOT NULL,
    "enhanced_contrast_mode" boolean DEFAULT true NOT NULL,
    "display_mode" "text" DEFAULT 'field'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "check_display_mode" CHECK (("display_mode" = ANY (ARRAY['field'::"text", 'office'::"text"])))
);


ALTER TABLE "public"."user_settings" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_settings" IS 'App preferences and business settings';



ALTER TABLE ONLY "public"."app_settings"
    ADD CONSTRAINT "app_settings_key_key" UNIQUE ("key");



ALTER TABLE ONLY "public"."app_settings"
    ADD CONSTRAINT "app_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."contracts"
    ADD CONSTRAINT "contracts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."document_signing_requests"
    ADD CONSTRAINT "document_signing_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."email_logs"
    ADD CONSTRAINT "email_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."estimates"
    ADD CONSTRAINT "estimates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."expense_category_usage"
    ADD CONSTRAINT "expense_category_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."expense_category_usage"
    ADD CONSTRAINT "expense_category_usage_user_id_category_key" UNIQUE ("user_id", "category");



ALTER TABLE ONLY "public"."expenses"
    ADD CONSTRAINT "expenses_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."mileage"
    ADD CONSTRAINT "mileage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."signed_documents"
    ADD CONSTRAINT "signed_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tax_payments"
    ADD CONSTRAINT "tax_payments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."time_logs"
    ADD CONSTRAINT "time_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_user_id_key" UNIQUE ("user_id");



CREATE INDEX "idx_audit_logs_created_at" ON "public"."audit_logs" USING "btree" ("created_at");



CREATE INDEX "idx_audit_logs_table_record" ON "public"."audit_logs" USING "btree" ("table_name", "record_id");



CREATE INDEX "idx_audit_logs_user_id" ON "public"."audit_logs" USING "btree" ("user_id");



CREATE INDEX "idx_contracts_customer_id" ON "public"."contracts" USING "btree" ("customer_id");



CREATE INDEX "idx_contracts_job_id" ON "public"."contracts" USING "btree" ("job_id");



CREATE INDEX "idx_contracts_user_id" ON "public"."contracts" USING "btree" ("user_id");



CREATE INDEX "idx_customers_name" ON "public"."customers" USING "btree" ("name");



CREATE INDEX "idx_customers_user_id" ON "public"."customers" USING "btree" ("user_id");



CREATE INDEX "idx_document_signing_requests_status" ON "public"."document_signing_requests" USING "btree" ("status");



CREATE INDEX "idx_document_signing_requests_user_id" ON "public"."document_signing_requests" USING "btree" ("user_id");



CREATE INDEX "idx_email_logs_email_type" ON "public"."email_logs" USING "btree" ("email_type");



CREATE INDEX "idx_email_logs_status" ON "public"."email_logs" USING "btree" ("status");



CREATE INDEX "idx_email_logs_user_id" ON "public"."email_logs" USING "btree" ("user_id");



CREATE INDEX "idx_estimates_customer_id" ON "public"."estimates" USING "btree" ("customer_id");



CREATE INDEX "idx_estimates_job_id" ON "public"."estimates" USING "btree" ("job_id");



CREATE INDEX "idx_estimates_status" ON "public"."estimates" USING "btree" ("status");



CREATE INDEX "idx_estimates_user_id" ON "public"."estimates" USING "btree" ("user_id");



CREATE INDEX "idx_expense_category_usage_category" ON "public"."expense_category_usage" USING "btree" ("category");



CREATE INDEX "idx_expense_category_usage_count" ON "public"."expense_category_usage" USING "btree" ("usage_count");



CREATE INDEX "idx_expense_category_usage_user_id" ON "public"."expense_category_usage" USING "btree" ("user_id");



CREATE INDEX "idx_expenses_category" ON "public"."expenses" USING "btree" ("category");



CREATE INDEX "idx_expenses_date" ON "public"."expenses" USING "btree" ("date");



CREATE INDEX "idx_expenses_invoiced_in_id" ON "public"."expenses" USING "btree" ("invoiced_in_id");



CREATE INDEX "idx_expenses_job_id" ON "public"."expenses" USING "btree" ("job_id");



CREATE INDEX "idx_expenses_type" ON "public"."expenses" USING "btree" ("type");



CREATE INDEX "idx_expenses_user_id" ON "public"."expenses" USING "btree" ("user_id");



CREATE INDEX "idx_invoices_customer_id" ON "public"."invoices" USING "btree" ("customer_id");



CREATE INDEX "idx_invoices_due_date" ON "public"."invoices" USING "btree" ("due_date");



CREATE INDEX "idx_invoices_job_id" ON "public"."invoices" USING "btree" ("job_id");



CREATE INDEX "idx_invoices_status" ON "public"."invoices" USING "btree" ("status");



CREATE INDEX "idx_invoices_user_id" ON "public"."invoices" USING "btree" ("user_id");



CREATE INDEX "idx_jobs_customer_id" ON "public"."jobs" USING "btree" ("customer_id");



CREATE INDEX "idx_jobs_status" ON "public"."jobs" USING "btree" ("status");



CREATE INDEX "idx_jobs_user_id" ON "public"."jobs" USING "btree" ("user_id");



CREATE INDEX "idx_mileage_expense_id" ON "public"."mileage" USING "btree" ("expense_id");



CREATE INDEX "idx_mileage_job_id" ON "public"."mileage" USING "btree" ("job_id");



CREATE INDEX "idx_mileage_user_id" ON "public"."mileage" USING "btree" ("user_id");



CREATE INDEX "idx_payments_date" ON "public"."payments" USING "btree" ("payment_date");



CREATE INDEX "idx_payments_invoice_id" ON "public"."payments" USING "btree" ("invoice_id");



CREATE INDEX "idx_payments_job_id" ON "public"."payments" USING "btree" ("job_id");



CREATE INDEX "idx_payments_user_id" ON "public"."payments" USING "btree" ("user_id");



CREATE INDEX "idx_signed_documents_user_id" ON "public"."signed_documents" USING "btree" ("user_id");



CREATE INDEX "idx_tax_payments_date" ON "public"."tax_payments" USING "btree" ("date");



CREATE INDEX "idx_tax_payments_period" ON "public"."tax_payments" USING "btree" ("tax_period");



CREATE INDEX "idx_tax_payments_user_id" ON "public"."tax_payments" USING "btree" ("user_id");



CREATE INDEX "idx_time_logs_date" ON "public"."time_logs" USING "btree" ("date");



CREATE INDEX "idx_time_logs_invoiced_in_id" ON "public"."time_logs" USING "btree" ("invoiced_in_id");



CREATE INDEX "idx_time_logs_job_id" ON "public"."time_logs" USING "btree" ("job_id");



CREATE INDEX "idx_time_logs_user_id" ON "public"."time_logs" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "log_expenses_audit" AFTER INSERT OR DELETE OR UPDATE ON "public"."expenses" FOR EACH ROW EXECUTE FUNCTION "public"."log_financial_transaction"();



CREATE OR REPLACE TRIGGER "log_invoices_audit" AFTER INSERT OR DELETE OR UPDATE ON "public"."invoices" FOR EACH ROW EXECUTE FUNCTION "public"."log_financial_transaction"();



CREATE OR REPLACE TRIGGER "log_payments_audit" AFTER INSERT OR DELETE OR UPDATE ON "public"."payments" FOR EACH ROW EXECUTE FUNCTION "public"."log_financial_transaction"();



CREATE OR REPLACE TRIGGER "log_tax_payments_audit" AFTER INSERT OR DELETE OR UPDATE ON "public"."tax_payments" FOR EACH ROW EXECUTE FUNCTION "public"."log_financial_transaction"();



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."contracts"
    ADD CONSTRAINT "contracts_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."contracts"
    ADD CONSTRAINT "contracts_estimate_id_fkey" FOREIGN KEY ("estimate_id") REFERENCES "public"."estimates"("id");



ALTER TABLE ONLY "public"."contracts"
    ADD CONSTRAINT "contracts_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."contracts"
    ADD CONSTRAINT "contracts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."customers"
    ADD CONSTRAINT "customers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."document_signing_requests"
    ADD CONSTRAINT "document_signing_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."email_logs"
    ADD CONSTRAINT "email_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."estimates"
    ADD CONSTRAINT "estimates_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."estimates"
    ADD CONSTRAINT "estimates_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."estimates"
    ADD CONSTRAINT "estimates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."expense_category_usage"
    ADD CONSTRAINT "expense_category_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."expenses"
    ADD CONSTRAINT "expenses_invoiced_in_id_fkey" FOREIGN KEY ("invoiced_in_id") REFERENCES "public"."invoices"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."expenses"
    ADD CONSTRAINT "expenses_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."expenses"
    ADD CONSTRAINT "expenses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."invoices"
    ADD CONSTRAINT "invoices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."mileage"
    ADD CONSTRAINT "mileage_expense_id_fkey" FOREIGN KEY ("expense_id") REFERENCES "public"."expenses"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."mileage"
    ADD CONSTRAINT "mileage_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."mileage"
    ADD CONSTRAINT "mileage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "public"."invoices"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."signed_documents"
    ADD CONSTRAINT "signed_documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."tax_payments"
    ADD CONSTRAINT "tax_payments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."time_logs"
    ADD CONSTRAINT "time_logs_invoiced_in_id_fkey" FOREIGN KEY ("invoiced_in_id") REFERENCES "public"."invoices"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."time_logs"
    ADD CONSTRAINT "time_logs_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "public"."jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."time_logs"
    ADD CONSTRAINT "time_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_profiles"
    ADD CONSTRAINT "user_profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_settings"
    ADD CONSTRAINT "user_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "System can insert audit logs" ON "public"."audit_logs" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Users can delete their own customers" ON "public"."customers" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own estimates" ON "public"."estimates" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own expenses" ON "public"."expenses" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own invoices" ON "public"."invoices" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own jobs" ON "public"."jobs" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own mileage" ON "public"."mileage" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own payments" ON "public"."payments" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own tax payments" ON "public"."tax_payments" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can delete their own time logs" ON "public"."time_logs" FOR DELETE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own customers" ON "public"."customers" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own estimates" ON "public"."estimates" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own expenses" ON "public"."expenses" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own invoices" ON "public"."invoices" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own jobs" ON "public"."jobs" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own mileage" ON "public"."mileage" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own payments" ON "public"."payments" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own tax payments" ON "public"."tax_payments" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can insert their own time logs" ON "public"."time_logs" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can only access their own contracts" ON "public"."contracts" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own customers" ON "public"."customers" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own document signing requests" ON "public"."document_signing_requests" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own email logs" ON "public"."email_logs" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own estimates" ON "public"."estimates" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own expense category usage" ON "public"."expense_category_usage" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own expenses" ON "public"."expenses" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own invoices" ON "public"."invoices" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own jobs" ON "public"."jobs" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own payments" ON "public"."payments" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own profile" ON "public"."user_profiles" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own settings" ON "public"."user_settings" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own signed documents" ON "public"."signed_documents" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own tax payments" ON "public"."tax_payments" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can only access their own time logs" ON "public"."time_logs" TO "authenticated" USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can select their own customers" ON "public"."customers" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own estimates" ON "public"."estimates" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own expenses" ON "public"."expenses" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own invoices" ON "public"."invoices" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own jobs" ON "public"."jobs" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own mileage" ON "public"."mileage" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own payments" ON "public"."payments" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own tax payments" ON "public"."tax_payments" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can select their own time logs" ON "public"."time_logs" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own customers" ON "public"."customers" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own estimates" ON "public"."estimates" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own expenses" ON "public"."expenses" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own invoices" ON "public"."invoices" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own jobs" ON "public"."jobs" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own mileage" ON "public"."mileage" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own payments" ON "public"."payments" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own tax payments" ON "public"."tax_payments" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can update their own time logs" ON "public"."time_logs" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Users can view their own audit logs" ON "public"."audit_logs" FOR SELECT TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



ALTER TABLE "public"."audit_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."contracts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."customers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."document_signing_requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."email_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."estimates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."expense_category_usage" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."expenses" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."invoices" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."jobs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."mileage" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."signed_documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tax_payments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."time_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_profiles" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_settings" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_expense_categories_by_usage"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_expense_categories_by_usage"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_expense_categories_by_usage"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_user_expense_category_usage"("category_name" "text", "user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."increment_user_expense_category_usage"("category_name" "text", "user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_user_expense_category_usage"("category_name" "text", "user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."log_financial_transaction"() TO "anon";
GRANT ALL ON FUNCTION "public"."log_financial_transaction"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."log_financial_transaction"() TO "service_role";



GRANT ALL ON TABLE "public"."app_settings" TO "anon";
GRANT ALL ON TABLE "public"."app_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."app_settings" TO "service_role";



GRANT ALL ON TABLE "public"."audit_logs" TO "anon";
GRANT ALL ON TABLE "public"."audit_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."audit_logs" TO "service_role";



GRANT ALL ON TABLE "public"."contracts" TO "anon";
GRANT ALL ON TABLE "public"."contracts" TO "authenticated";
GRANT ALL ON TABLE "public"."contracts" TO "service_role";



GRANT ALL ON TABLE "public"."customers" TO "anon";
GRANT ALL ON TABLE "public"."customers" TO "authenticated";
GRANT ALL ON TABLE "public"."customers" TO "service_role";



GRANT ALL ON TABLE "public"."document_signing_requests" TO "anon";
GRANT ALL ON TABLE "public"."document_signing_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."document_signing_requests" TO "service_role";



GRANT ALL ON TABLE "public"."email_logs" TO "anon";
GRANT ALL ON TABLE "public"."email_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."email_logs" TO "service_role";



GRANT ALL ON TABLE "public"."estimates" TO "anon";
GRANT ALL ON TABLE "public"."estimates" TO "authenticated";
GRANT ALL ON TABLE "public"."estimates" TO "service_role";



GRANT ALL ON TABLE "public"."expense_category_usage" TO "anon";
GRANT ALL ON TABLE "public"."expense_category_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."expense_category_usage" TO "service_role";



GRANT ALL ON TABLE "public"."expenses" TO "anon";
GRANT ALL ON TABLE "public"."expenses" TO "authenticated";
GRANT ALL ON TABLE "public"."expenses" TO "service_role";



GRANT ALL ON TABLE "public"."invoices" TO "anon";
GRANT ALL ON TABLE "public"."invoices" TO "authenticated";
GRANT ALL ON TABLE "public"."invoices" TO "service_role";



GRANT ALL ON TABLE "public"."jobs" TO "anon";
GRANT ALL ON TABLE "public"."jobs" TO "authenticated";
GRANT ALL ON TABLE "public"."jobs" TO "service_role";



GRANT ALL ON TABLE "public"."mileage" TO "anon";
GRANT ALL ON TABLE "public"."mileage" TO "authenticated";
GRANT ALL ON TABLE "public"."mileage" TO "service_role";



GRANT ALL ON TABLE "public"."payments" TO "anon";
GRANT ALL ON TABLE "public"."payments" TO "authenticated";
GRANT ALL ON TABLE "public"."payments" TO "service_role";



GRANT ALL ON TABLE "public"."signed_documents" TO "anon";
GRANT ALL ON TABLE "public"."signed_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."signed_documents" TO "service_role";



GRANT ALL ON TABLE "public"."tax_payments" TO "anon";
GRANT ALL ON TABLE "public"."tax_payments" TO "authenticated";
GRANT ALL ON TABLE "public"."tax_payments" TO "service_role";



GRANT ALL ON TABLE "public"."time_logs" TO "anon";
GRANT ALL ON TABLE "public"."time_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."time_logs" TO "service_role";



GRANT ALL ON TABLE "public"."user_profiles" TO "anon";
GRANT ALL ON TABLE "public"."user_profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_profiles" TO "service_role";



GRANT ALL ON TABLE "public"."user_settings" TO "anon";
GRANT ALL ON TABLE "public"."user_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."user_settings" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
