# Implementation Plan for Estimate and Invoice Templates

This document outlines the implementation plan for adding template functionality to Estimates and Invoices in the Quarterlies app.

## 1. Model Updates

### Estimate Model
The Estimate model already has a `templateId` field, but we need to add an `isTemplate` flag to identify templates.

### Invoice Model
The Invoice model needs to be updated to add both `isTemplate` and `templateId` fields.

## 2. Supabase Service Updates

### Estimate Templates
- Add `createEstimateTemplate` method
- Add `getInvoiceTemplates` method (similar to existing `getEstimateTemplates`)

### Invoice Templates
- Add `createInvoiceTemplate` method
- Add `getInvoiceTemplates` method

## 3. UI Updates

### Estimate Detail Screen
- Add "Save as Template" action in the options menu
- Implement dialog for template name input

### Invoice Detail Screen
- Add "Save as Template" action in the options menu
- Implement dialog for template name input

### Estimate Form Screen
- Add "Create from Template" option
- Implement template selection dialog

### Invoice Form Screen
- Add "Create from Template" option
- Implement template selection dialog

## 4. Implementation Steps

1. Update models
2. Add Supabase service methods
3. Update detail screens to add "Save as Template" functionality
4. Update form screens to add "Create from Template" functionality
5. Test all functionality

## 5. Testing Plan

- Test creating templates from existing Estimates and Invoices
- Test creating new Estimates and Invoices from templates
- Verify that templates retain line items but remove customer/job-specific information
- Verify that templates are properly displayed in selection dialogs